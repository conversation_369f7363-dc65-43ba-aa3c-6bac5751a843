import { ComponentFixture, TestBed } from '@angular/core/testing';

import { WrapperSuivisCommandesComponent } from './wrapper-suivis-commandes.component';

describe('WrapperSuivisCommandesComponent', () => {
  let component: WrapperSuivisCommandesComponent;
  let fixture: ComponentFixture<WrapperSuivisCommandesComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [WrapperSuivisCommandesComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(WrapperSuivisCommandesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
