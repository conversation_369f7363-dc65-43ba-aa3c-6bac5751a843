import { Component, Input, OnInit } from '@angular/core';

import { ToastrService } from 'ngx-toastr';
import { InvoiceService } from '../../../../../../services';
import { FluxService } from '../../../../../../services/flux.service';
import { EntreposageService } from '../../../../../../services/entreposage.service';
import { FactureService } from '../../../../../../services/facture.service';
import { CommonModule } from '@angular/common';
import { SmartContainerComponent } from '../../../../../../shared/components/smart-container/smart-container.component';
import { SpinnerComponent } from '../../../../../../shared/components/spinner/spinner.component';
import { ImpotService } from 'src/services/impot.service';
@Component({
  selector: 'app-facture-impression',
  standalone: true,
  imports: [CommonModule,SmartContainerComponent,SpinnerComponent],
  templateUrl: './facture-impression.component.html',
  styleUrl: './facture-impression.component.scss'
})
export class FactureImpressionComponent implements OnInit {
  @Input() prefacture :boolean = false
  @Input() set source(value: any) {
    this._source = value;
    if (!this.prefacture) { 
      this.loading = true;
      this.showFacture(this._source);
    }
    else{
      this.loading = true;
      this.getImpot()
      setInterval(() => {
        this.showPrefacture(this._source);
      }, 100);
    }
    console.log(this.prefacture)
  }
  get source(): any {
    return this._source;
  }
  loading: boolean = true;
  private _source: any = {};
  listImpot: any = [];

  pdf!: {
    dateFac?: any,
    code?: any,
    adress?: any,
    mat_fisc?: any,
    nom_client?: any,
    tva?: any,
    commandes?: any[],
    timbre?: any,
    pref?: any[],
    verif?: any[],
    HT?: any,
    TTC?: any,
    TVA?: any,
    decEnLettres?: any,
    foot?: any,
    montantEnLettres?: any,
    prix?: any
  };

  constructor(
      private factureService: FactureService,
      private entreposageService: EntreposageService,
      private fluxService: FluxService,
      private invoiceService: InvoiceService,
      private toastr: ToastrService,
      private impotService: ImpotService) {
  
    }

  ngOnInit(): void {
    this.initializePDF();
  }
  async getImpot() {
    this.impotService.getImpot().subscribe({
      next: (response: any) => {
        this.listImpot = response;
        console.log(this.listImpot)
      },
      error: (error: any) => {
        console.error(error);
      }
    })
  }

  initializePDF(){
    this.pdf = {
      dateFac: null,
      code: null,
      adress: null,
      mat_fisc: null,
      nom_client: null,
      tva: null,
      commandes: [],
      timbre: null,
      pref: [],
      verif: [],
      HT: null,
      TTC: null,
      TVA: null,
      decEnLettres: null,
      foot: null,
      montantEnLettres: null,
      prix: null
    };
  }

  showPrefacture(Data : any) {
    this.initializePDF()
    this.loading = true;
    this.pdf.pref = []
    let qte = 0;
    let price = 0;
    let prix = 0;
    let volume = 0;
    let priceHors = 0
    let priceDelivery = 0
    let Qte = 0
    this.pdf.HT = 0
    this.pdf.TTC = 0
    this.pdf.TVA = 0

    Data.forEach((line :any) => {
      this.pdf.adress = line.adresse;
      this.pdf.mat_fisc = line.mat_fisc;
      this.pdf.nom_client = line.nom_client;
      if (line.type_ligne === 'Transfert administratif ZEN Group-Magasin ou dépôt' || line.type_ligne === 'Transfert Administratif inter magasin') {
        prix += line.prix_tot;
        qte ++
      } else if (line.type_ligne === 'Transfert administratif technique et matériel informatique' || line.type_ligne === 'Livraison fourniture') {
        price += line.prix_tot;
        Qte += line.quantite
      } else {
        volume += line.adapted_volume != null ? line.adapted_volume : line.volume;
        priceDelivery += line.prix_tot ;
       
      }
    });

    this.generatePref(prix, price, qte, Qte, volume, priceDelivery);

    // if (prix !== 0) {
    //   this.pdf.pref.push({
    //     designation: "Transfert administratif",
    //     unite: "colis",
    //     volume: qte,
    //     prix: prix.toFixed(3)
    //   });
    // }

    // if (price !== 0) {
    //   this.pref.push({
    //     designation: "Transfert administratif technique et matériel informatique",
    //     unite: "colis",
    //     volume: Qte,
    //     prix: price.toFixed(3)
    //   });
    // }

    // if (priceDelivery !== 0) {
    //   this.pref.push({
    //     designation: "Livraison",
    //     unite: "m³",
    //     volume: volume,
    //     prix: priceDelivery.toFixed(3)
    //   });
    // }

    
    for (let i = 0; i < this.pdf.pref.length; i++) {
      const prixHT = parseFloat(this.pdf.pref[i].prix);
      this.pdf.HT += isNaN(prixHT) ? 0 : prixHT;
    }

    //console.log('Somme des prixHT:', this.HT.toFixed(3));
    this.pdf.HT = parseFloat(this.pdf.HT.toFixed(3));

    const TVA_PERCENTAGE =this.listImpot[0].tva_tr ;
    this.pdf.timbre = this.listImpot[0].timbre;
    this.pdf.TVA = (this.pdf.HT * TVA_PERCENTAGE) / 100;
    this.pdf.TTC = this.pdf.HT + this.pdf.TVA + this.pdf.timbre;
    this.pdf.foot = [{ HT: this.pdf.HT.toFixed(3), TVA: this.pdf.TVA.toFixed(3), timbre: this.pdf.timbre.toFixed(3), TTC: this.pdf.TTC.toFixed(3) }]
    const partieEntiere = Math.floor(this.pdf.TTC);
    const partieDecimale = (this.pdf.TTC - partieEntiere).toFixed(3).split('.')[1];
    this.invoiceService.convertNumberToText(partieEntiere).subscribe({
      next: (response: any) => {
        this.pdf.decEnLettres = response.enLettres;
        //console.log("///////////////", response.enLettres)
        //console.log("///////////////", response)
        this.pdf.montantEnLettres = this.pdf.decEnLettres + ` dinars et ${partieDecimale}  Millimes `
      },
      error: (error: any) => {
        console.error(error);
      }
    });
    this.loading = false;
  }
  async showFacture(row: any) {
    this.initializePDF(); 
    let dateObject = new Date(row.created_at);
    let jour = dateObject.getDate();
    let mois = dateObject.getMonth() + 1; // Janvier est 0
    let annee = dateObject.getFullYear();
    this.pdf.dateFac = row.invoice_date;

    // Assignation des valeurs
    this.pdf.code = row.code;
    this.pdf.adress = row.adresse;
    this.pdf.mat_fisc = row.mat_fisc;
    this.pdf.nom_client = row.nom_fournisseur;
    this.pdf.tva = row.tva;
    this.pdf.timbre = row.timbre;
    console.log('verif', this.pdf.verif)
    if (this.pdf.verif?.length === 0) {
      this.pdf.verif.push(row.id_facture);

      // Vérification du type
      if (row.type === "transport" || row.type === undefined || row.type === null) {
        this.handleTransportPDF(row);
      } else if (row.type === "flux") {
        this.handleFluxPDF(row);
      } else if (row.type === "Entreposage") {
        this.handleEntreposagePDF(row);
      }
      else
        this.toastr.error("erreur type")

    }
  }

  handleTransportPDF(row: any) {
    let qte = 0;
    let price = 0;
    let prix = 0;
    let volume = 0;
    let priceDelivery = 0;
    let Qte = 0;
    this.pdf.HT = 0;
    this.pdf.TTC = 0;
    this.pdf.TVA = 0;

    let slectedLines: any
    this.factureService.getFacture(row.id_facture).subscribe({
      next: (response: any) => {
        console.log('transportPDF', this.pdf);
        console.log('response', response); 

        response.forEach((line: any) => {
          if (line.type_ligne === 'Transfert administratif ZEN Group-Magasin ou dépôt' ||
            line.type_ligne === 'Transfert Administratif inter magasin') {
            prix += line.prix_tot;
            qte++;
          } else if (line.type_ligne === 'Transfert administratif technique et matériel informatique' ||
            line.type_ligne === 'Livraison fourniture') {
            price += line.prix_tot;
            Qte += line.quantite;
          } else {
            volume += line.adapted_volume != null ? line.adapted_volume : line.volume;
            priceDelivery += line.prix_tot;
          }

          
        });
        console.log('prix', prix);
        console.log('price', price);
        console.log('qte', qte);
        console.log('Qte', Qte);
        console.log('volume', volume);
        console.log('priceDelivery', priceDelivery);

        this.generatePref(prix, price, qte, Qte, volume, priceDelivery);
        this.calculateInvoice();
      },
      error: (error : any) => {
        console.error('Error loading details:', error);
      },
      complete: () => {
        this.loading = false;
      }
    });

    
  }

  handleFluxPDF(row: any) {

    let qteMP = 0, prixMP = 0;
    let qtePF = 0, prixPF = 0;
    let qteFournitureMP = 0, prixFournitureMP = 0;
    let qteFourniturePF = 0, prixFourniturePF = 0;


    let puMP = 0, puPF = 0
    let puFourniturePF = 0, puFournitureMP = 0
    row.HT = 0;
    row.TVA = 0;
    row.TTC = 0;


    this.fluxService.getFluxByidFacture(row.id_facture).subscribe({
      next: (selectedLines: any[]) => {
        console.log('selectedLines', selectedLines);
        selectedLines.forEach((line: any) => {
          if (line.type === 'MP') {
            prixMP += line.prix_tot;
            qteMP += line.qte;
            if (line.PU > puMP) {
              puMP = line.PU;
            }
          } else if (line.type === 'PF') {
            prixPF += line.prix_tot;
            qtePF += line.qte;
            if (line.PU > puPF) {
              puPF = line.PU;
            }
          } else if (line.type === 'MP_F') {
            prixFournitureMP += line.prix_tot;
            qteFournitureMP += line.qte;
            if (line.PU > puFournitureMP) {
              puFournitureMP = line.PU;
            }
          } else if (line.type === 'PF_F') {
            prixFourniturePF += line.prix_tot;
            qteFourniturePF += line.qte;
            if (line.PU > puFourniturePF) {
              puFourniturePF = line.PU;
            }
          }
        });
        // Ajouter MP
        if (qteMP > 0) {
          this.pdf.pref!.push({
            designation: "GESTION DE FLUX MP",
            unite: "ROULEAU",
            volume: parseFloat(qteMP.toFixed(3)),
            prix: (parseFloat(qteMP.toFixed(3)) * puMP).toFixed(3),
            code: "SRV-10108",
            un: "M3",
            pu: puMP
          });
        }

        // Ajouter PF
        if (qtePF > 0) {
          this.pdf.pref!.push({
            designation: "GESTION DE FLUX PF",
            unite: "pièces",
            volume: parseFloat(qtePF.toFixed(3)),
            prix: (parseFloat(qtePF.toFixed(3)) * puPF).toFixed(3),
            code: "SRV-10005",
            un: "UN",
            pu: puPF

          });
        }

        // Ajouter fourniture MP
        if (qteFournitureMP > 0) {
          this.pdf.pref!.push({
            designation: "FOURNITURE FLUX MP",
            unite: "m³",
            volume: parseFloat(qteFournitureMP.toFixed(3)),
            prix: (parseFloat(qteFournitureMP.toFixed(3)) * puFournitureMP).toFixed(3),
            code: "FRN-0108",
            un: "M3",
            pu: puFournitureMP

          });
        }

        // Ajouter fourniture PF
        if (qteFourniturePF > 0) {
          this.pdf.pref!.push({
            designation: "FOURNITURE FLUX PF",
            unite: "pièces",
            volume: parseFloat(qteFourniturePF.toFixed(3)),
            prix: (parseFloat(qteFourniturePF.toFixed(3)) * puFourniturePF).toFixed(3),
            code: "FRN-0112",
            un: "UN",
            pu: puFourniturePF

          });
        }


        this.pdf.pref!.forEach((item: any) => {
          const prixHT = parseFloat(item.prix);
          this.pdf.HT += isNaN(prixHT) ? 0 : prixHT;
        });
        this.pdf.HT = parseFloat(this.pdf.HT.toFixed(3));
        this.pdf.TVA = (this.pdf.HT * this.pdf.tva) / 100;
        this.pdf.TTC = this.pdf.HT + this.pdf.TVA + this.pdf.timbre;

        this.pdf.foot = [{
          HT: this.pdf.HT.toFixed(3),
          TVA: this.pdf.TVA.toFixed(3),
          timbre: this.pdf.timbre?.toFixed(3),
          TTC: this.pdf.TTC.toFixed(3)
        }];

        const partieEntiere = Math.floor(this.pdf.TTC);
        const partieDecimale = (this.pdf.TTC - partieEntiere).toFixed(3).split('.')[1];
        this.invoiceService.convertNumberToText(partieEntiere).subscribe({
          next: (data) => {
            this.pdf.decEnLettres = data;
            this.pdf.montantEnLettres = this.pdf.decEnLettres + ` dinars et ${partieDecimale} Millimes `;
          },
          error: (error) =>
            this.toastr.error('erreur')
        });

      },
      error: (error) => {
        console.error('Error loading flux data:', error);
        // Handle error appropriately
      },
      complete: () => {
        this.loading = false;
      }
    });
  }

  handleEntreposagePDF(row: any) {
    let qteMP = 0, prixMP = 0;
    let qtePF = 0, prixPF = 0;
    let qteFournitureMP = 0, prixFournitureMP = 0;
    let qteFourniturePF = 0, prixFourniturePF = 0;


    let totalVolumeMP = 0, totalVolumePF = 0
    let totalVolumeFourniturePF = 0, totalVolumeFournitureMP = 0

    let puMP = 0, puPF = 0
    let puFourniturePF = 0, puFournitureMP = 0

    this.pdf.HT = 0;
    this.pdf.TTC = 0;
    this.pdf.TVA = 0;
    let selectedLines: any;
    // Convert subscribe to Promise
    this.entreposageService.getEntreposageByIdfacture(row.id_facture).subscribe({
      next: (data : any) => {
        console.log('data', data)
        selectedLines = data;
        selectedLines.forEach((line: any) => {
          if (line.type === 'MP') {
            prixMP += line.prix_tot;
            qteMP += line.qte;
            totalVolumeMP += line.volume_history
            if (line.PU_Month > puMP) {
              puMP = line.PU_Month
            }
          } else if (line.type === 'PF') {
            prixPF += line.prix_tot;
            qtePF += line.qte;
            totalVolumePF += line.volume_history

            if (line.PU_Month > puPF) {
              puPF = line.PU_Month
            }

          } else if (line.type === 'MP_F') {
            prixFournitureMP += line.prix_tot;
            qteFournitureMP += line.qte;
            totalVolumeFournitureMP += line.volume_history

            if (line.PU_Month > puFournitureMP) {
              puFournitureMP = line.PU_Month
            }

          } else if (line.type === 'PF_F') {
            prixFourniturePF += line.prix_tot;
            qteFourniturePF += line.qte;
            totalVolumeFourniturePF += line.volume_history

            if (line.PU_Month > puFourniturePF) {
              puFourniturePF = line.PU_Month
            }

          }
        });

        // Ajouter MP
        if (qteMP > 0) {
          this.pdf.pref!.push({
            designation: "GESTION ENTREPOSAGE MP",
            unite: "m³",
            volume: parseFloat(qteMP.toFixed(3)),
            prix: ((parseFloat((totalVolumeMP / 300).toFixed(3)) * puMP * 10).toFixed(3)),

            code: "SRV-10004",
            un: "M3",
            pu: puMP * 10,
            totVolume: (totalVolumeMP / 300)

          });
        }

        // Ajouter PF
        if (qtePF > 0) {
          this.pdf.pref!.push({
            designation: "GESTION ENTREPOSAGE PF",
            unite: "m³",
            volume: parseFloat(qtePF.toFixed(3)),
            prix: ((parseFloat((totalVolumePF / 30).toFixed(3)) * puPF).toFixed(3)),
            code: "SRV-10062",
            un: "UN",
            pu: puPF,
            totVolume: (totalVolumePF / 30)

          });
        }

        // Ajouter fourniture MP
        if (qteFournitureMP > 0) {
          this.pdf.pref!.push({
            designation: "FOURNITURE ENTREPOSAGE MP",
            unite: "Rouleau",
            volume: parseFloat(qteFournitureMP.toFixed(3)),
            prix: ((parseFloat((totalVolumeFournitureMP / 30).toFixed(3)) * puFournitureMP).toFixed(3)),
            code: "FRN-0113",
            un: "M3",
            pu: puFournitureMP,
            totVolume: (totalVolumeFournitureMP / 30)

          });
        }

        // Ajouter fourniture PF
        if (qteFourniturePF > 0) {
          this.pdf.pref!.push({
            designation: "FOURNITURE ENTREPOSAGE PF",
            unite: "m³",
            volume: parseFloat(qteFourniturePF.toFixed(3)),
            prix: ((parseFloat((totalVolumeFourniturePF / 30).toFixed(3)) * puFourniturePF).toFixed(3)),
            code: "FRN-0114",
            un: "UN",
            pu: puFourniturePF,
            totVolume: (totalVolumeFourniturePF / 30)

          });
        }
        console.log('pref', this.pdf.pref)
        this.pdf.pref!.forEach((item: any) => {
          const prixHT = parseFloat(item.prix);
          this.pdf.HT += isNaN(prixHT) ? 0 : prixHT;
        });
        this.pdf.HT = parseFloat(this.pdf.HT.toFixed(3));
        this.pdf.TVA = (this.pdf.HT * this.pdf.tva) / 100;
        this.pdf.TTC = this.pdf.HT + this.pdf.TVA + this.pdf.timbre;

        this.pdf.foot = [{
          HT: this.pdf.HT.toFixed(3),
          TVA: this.pdf.TVA.toFixed(3),
          timbre: this.pdf.timbre.toFixed(3),
          TTC: this.pdf.TTC.toFixed(3)
        }];

        const partieEntiere = Math.floor(this.pdf.TTC);
        const partieDecimale = (this.pdf.TTC - partieEntiere).toFixed(3).split('.')[1];

        this.pdf.decEnLettres = this.invoiceService.convertNumberToText(partieEntiere).subscribe({
          next: (data :any) => {
            this.pdf.decEnLettres = data;
            this.pdf.montantEnLettres = this.pdf.decEnLettres + ` dinars et ${partieDecimale} Millimes `;
          }
        })

      },
      error: (error : any) => {
        console.error('Error loading details:', error);
        this.toastr.error('Erreur loading details');
      },
      complete: () => {
        this.loading = false;
      }
    });







  }

  // Méthode pour générer les pref (transfert administratif)
  generatePref(prix: any, price: any, qte: any, Qte: any, volume: any, priceDelivery: any) {
    if (prix !== 0) {
      this.pdf.pref!.push({
        designation: "Transfert administratif",
        unite: "colis",
        volume: qte,
        prix: prix.toFixed(3),
        code: "SRV-10371",
        un: "COL"
      });
    }
    if (price !== 0) {
      this.pdf.pref!.push({
        designation: "Transfert administratif technique et matériel informatique",
        unite: "colis",
        volume: Qte,
        prix: price.toFixed(3),
        code: "SRV-10372",
        un: "COL"
      });
    }
    if (priceDelivery !== 0) {
      this.pdf.pref!.push({
        designation: "Livraison",
        unite: "m³",
        volume: parseFloat(volume.toFixed(3)),
        prix: parseFloat(priceDelivery.toFixed(3)),
        code: "SRV-10007",
        un: "M3"
      });
    }

  }

  // Méthode pour calculer la facture
  calculateInvoice() {
    console.log('Calculating invoice...');
    for (let i = 0; i < this.pdf.pref!.length; i++) {
      const prixHT = parseFloat(this.pdf.pref![i].prix);
      this.pdf.HT += isNaN(prixHT) ? 0 : prixHT;
    }

    console.log('Somme des prixHT:', this.pdf.HT.toFixed(3));
    this.pdf.HT = parseFloat(this.pdf.HT.toFixed(3));
    this.pdf.timbre = 1;
    this.pdf.TVA = (this.pdf.HT * this.pdf.tva) / 100;
    this.pdf.TTC = this.pdf.HT + this.pdf.TVA + this.pdf.timbre;
    this.pdf.foot = [{ HT: this.pdf.HT.toFixed(3), TVA: this.pdf.TVA.toFixed(3), timbre: this.pdf.timbre.toFixed(3), TTC: this.pdf.TTC.toFixed(3) }];

    const partieEntiere = Math.floor(this.pdf.TTC);
    const partieDecimale = (this.pdf.TTC - partieEntiere).toFixed(3).split('.')[1];

    this.invoiceService.convertNumberToText(partieEntiere).subscribe({
      next: (response: any) => {
        this.pdf.decEnLettres = response.enLettres;
        this.pdf.montantEnLettres = this.pdf.decEnLettres + ` dinars et ${partieDecimale} Millimes `;
      },
      error: (error: any) => {
        this.toastr.error('Error converting number to text');
      }
    });

    console.log('pdf: ', this.pdf)
  }





  // formatDateJJMMAAAA = (dateString: any) => {
  //   const date = new Date(dateString);
  //   const day = String(date.getDate()).padStart(2, '0');
  //   const month = String(date.getMonth() + 1).padStart(2, '0');
  //   const year = date.getFullYear();

  //   return `${day}${month}${year}`;
  // };



  // Méthode pour imprimer la facture (PDF)
  imprimerFacture() {
    const printContents = document.getElementById('invoice-pdf')?.innerHTML;
    if (!printContents) return;
    
    // Get the logo source
    const logo = document.querySelector('#invoice-pdf img')?.getAttribute('src');
    
    const printWindow = window.open('', '', 'height=800,width=800');
    printWindow?.document.write(`
      <html>
        <head>
          <style>
            @media print {
              body {
                font-size: 12pt;
              }
              body { margin: 0; padding: 0; }
                .invoice-container { max-width: 100% !important; margin: 0 !important; }
                .invoice-card { box-shadow: none !important; border: none !important; }
  
              body * {
                visibility: hidden;
              }
  
              #facture-content, #facture-content * {
                visibility: visible;
                margin-top: 20px;
              }
  
              img {
                max-width: 100%;
                height: auto;
              }
              .ttc {
                color: black !important;
              }
            }
          </style>
        </head>
        <body onload="window.print(); window.close();">
          <div id="facture-content">
          ${printContents.replace(/src="([^"]+)"/g, (match, p1) => {
            return p1.startsWith('assets') ? `src="${window.location.origin}/${p1}"` : match;
          })}
          </div>
        </body>
      </html>
    `);
    printWindow?.document.close();
  }
}
