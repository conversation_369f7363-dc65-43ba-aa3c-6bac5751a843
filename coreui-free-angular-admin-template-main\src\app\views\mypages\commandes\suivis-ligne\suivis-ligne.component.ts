import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { ButtonDirective, ButtonGroupComponent, FormCheckLabelDirective } from '@coreui/angular';
import { ToastrService } from 'ngx-toastr';
import { ContextService } from 'src/services/strategy/access-context.service';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';
import { TableColumn, TableConfig } from 'src/shared/models/table.models';

@Component({
  selector: 'app-suivis-ligne',
  standalone: true,
  imports: [
    SmartContainerComponent,
    SmartTableComponent,
    FormsModule,
    CommonModule,
    ReactiveFormsModule,
    ButtonGroupComponent,
    FormCheckLabelDirective,
    ButtonDirective,
  ],
  templateUrl: './suivis-ligne.component.html',
  styleUrl: './suivis-ligne.component.scss'
})
export class SuivisLigneComponent implements OnInit {

  loading:boolean =false
  source:any[] = []
  title: string = 'Mes Commandes';
  formRadio = new UntypedFormGroup({
    radio: new UntypedFormControl('Mes Commandes')
  });

  TableColumns: TableColumn[] = [
    {name:'id',displayName:'ID',sortable:true,filterable:true},
    {name:'date_depart',displayName:'Date Départ',sortable:true,filterable:true},
    {name:'nom_depart',displayName:'Départ',sortable:true,filterable:true},
    {name:'nom_arrivee',displayName:'Arrivée',sortable:true,filterable:true},
    {name:'type_ligne',displayName:'Type',sortable:true,filterable:true},
    {name:'volume',displayName:'Volume',sortable:true,filterable:true},
    {name:'quantite',displayName:'Quantité',sortable:true,filterable:true},
    {name:'ajoutee_par',displayName:'Demandeur',sortable:true,filterable:true},
    {name:'conducteur_nom_prenom',displayName:'Nom Conducteur',sortable:true,filterable:true},
    {name:'mobile',displayName:'Conducteur Tel',sortable:true,filterable:true},
  ]

  TableConfig: TableConfig = {
    emptyMessage: "Aucune ligne trouvée",
    
  }

  constructor(
    private contextService: ContextService,
    private toastr: ToastrService,
  ) { }


  ngOnInit(): void {
    this.setRadioValue('Mes Commandes réservées');
  }
  setRadioValue(value: string): void {
    this.formRadio.setValue({ radio: value });
    this.title = this.formRadio.value['radio']
    switch(this.formRadio.value['radio']){
      case 'Mes Commandes réservées':
        this.title = 'Mes Commandes Réservées';
        this.loadReservedLine();
        break;
      case 'Mes Lignes Expédiées':
        this.title = 'Mes Lignes Expédiées';
        this.loadExpediedLine();
        break;
      case 'Mes Lignes Livrées':
        this.title = 'Mes Lignes Livrées';
        this.loadDeliveredLine();
        break;
    }
  }
  loadReservedLine() {
    this.loading =true;
    this.contextService.findReservedLineWithByUserId().subscribe({
      next: (data) => {
        this.source = data;
        console.log('data',data)
        this.loading = false;
      },
      error: (error) => {
        this.toastr.error('Error loading lignes to facture ');
        console.error(error);
        this.loading =false
      }
    })
  }
  loadExpediedLine() {
    this.loading =true;
    this.contextService.findExpediedLineWithByUserId().subscribe({
      next: (data) => {
        this.source = data;
        console.log('data',data)
        this.loading = false;
      },
      error: (error) => {
        this.toastr.error('Error loading lignes to facture ');
        console.error(error);
        this.loading =false
      }
    })
  }
  loadDeliveredLine() {
    this.loading =true;
    this.contextService.findDeliveredLineWithByUserId().subscribe({
      next: (data) => {
        this.source = data;
        console.log('data',data)
        this.loading = false;
      },
      error: (error) => {
        this.toastr.error('Error loading lignes to facture ');
        console.error(error);
        this.loading =false
      }
    })
  }
}



