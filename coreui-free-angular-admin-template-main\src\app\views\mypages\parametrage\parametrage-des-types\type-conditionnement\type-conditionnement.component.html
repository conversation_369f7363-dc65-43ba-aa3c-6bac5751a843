<app-smart-container title="Type Conditionnement">
    <div slot="content">
        <div class="d-flex flex-column w-100">
            <div class="d-flex flex-wrap align-items-center justify-content-between mb-3">
                <div class="d-flex flex-grow-1 align-items-center">
                    <input type="text" placeholder="Type Conditionnement" class="form-control me-2" name="typeConditionnement">
                    <button type="button" class="btn btn-raised btn-primary me-3" (click)="addConditionnement()">
                        Ajouter
                    </button>
                </div>
            </div>  
        </div>
        <div *ngIf="showTable">
            <app-smart-table 
            [data]="conditionnement" 
            [columns]="tableColumns"
            [actionButtons]="tableaction" 
            [isLoading]="loading"></app-smart-table>
        </div>
    </div>
    <div slot="actions">
        <app-smart-button [label]="'Détails'" [color]="'primary'" (onClick)="showTableConditionnement()"></app-smart-button>
    </div>
</app-smart-container>