import { Component, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { NgStyle, CommonModule } from '@angular/common';
import { IconDirective } from '@coreui/icons-angular';
import { ContainerComponent, RowComponent, ColComponent, CardGroupComponent, TextColorDirective, CardComponent, CardBodyComponent, FormDirective, InputGroupComponent, InputGroupTextDirective, FormControlDirective, ButtonDirective } from '@coreui/angular';
import { AuthService } from '../../../../services/auth.service';
import { Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { map, Subject, take, takeUntil } from 'rxjs';
import { Md5 } from 'md5-typescript';
import { LoginRequest, AuthError } from '../../../../models/auth.interfaces';
import { RoleService } from '../../../../services/role.service';
import { ContextService } from '../../../../services/strategy/access-context.service';


@Component({
    selector: 'app-login',
    templateUrl: './login.component.html',
    styleUrls: ['./login.component.scss'],
    imports: [
        ContainerComponent,
        RowComponent,
        ColComponent,
        CardGroupComponent,
        TextColorDirective,
        CardComponent,
        CardBodyComponent,
        FormDirective,
        InputGroupComponent,
        InputGroupTextDirective,
        IconDirective,
        FormControlDirective,
        ButtonDirective,
        NgStyle,
        CommonModule,
        ReactiveFormsModule,
    ]
})
export class LoginComponent implements OnInit, OnDestroy {
  loginForm!: FormGroup;
  errorMessage: string = '';
  isLoading: boolean = false;
  private destroy$ = new Subject<void>();

  constructor(
    private authService: AuthService,
    private roleService: RoleService,
    private router: Router,
    private formBuilder: FormBuilder,

    
    private ContextService: ContextService
  ) {}

  ngOnInit(): void {
    this.initializeForm();

    // Subscribe to authentication state
    this.authService.isAuthenticated$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isAuthenticated => {
        if (isAuthenticated) {
          this.router.navigate(['/dashboard']);
        }
      });

    // Subscribe to loading state
    this.authService.loading$
      .pipe(takeUntil(this.destroy$))
      .subscribe(loading => {
        this.isLoading = loading;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.loginForm = this.formBuilder.group({
      email: ['', [
        Validators.required,
        Validators.email,
        Validators.minLength(5)
      ]],
      password: ['', [
        Validators.required,
        Validators.minLength(6)
      ]]
    });
  }

  onSubmit(): void {
    if (this.loginForm.valid) {
      this.errorMessage = '';
      const loginData: LoginRequest = {
        email: this.loginForm.get('email')?.value,
        mot_de_passe: Md5.init(this.loginForm.get('password')?.value)
      };

      this.authService.login(loginData).subscribe({
        next: (response) => {
          console.log('Login successful:', response);
          this.errorMessage = '';

          // Check for redirect URL from auth guard
          const redirectUrl = sessionStorage.getItem('redirectUrl');
          if (redirectUrl) {
            sessionStorage.removeItem('redirectUrl');
            this.router.navigate([redirectUrl]);
          } else {
            this.router.navigate([this.ContextService.getLandingPage()]);
            console.log(this.roleService.getRole())
          }
        },
        error: (error: AuthError) => {
          console.error('Login failed:', error);
          this.handleLoginError(error);
        }
      });
    } else {
      this.markFormGroupTouched();
    }
    
  }

  private handleLoginError(error: AuthError): void {
    switch (error.type) {
      case 'INVALID_CREDENTIALS':
        this.errorMessage = 'Invalid email or password. Please check your credentials and try again.';
        break;
      case 'NETWORK_ERROR':
        this.errorMessage = 'Network error. Please check your internet connection and try again.';
        break;
      case 'SERVER_ERROR':
        this.errorMessage = 'Server error. Please try again later or contact support.';
        break;
      case 'VALIDATION_ERROR':
        this.errorMessage = 'Invalid request format. Please check your input and try again.';
        break;
      default:
        this.errorMessage = 'An unexpected error occurred. Please try again.';
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.loginForm.controls).forEach(key => {
      const control = this.loginForm.get(key);
      control?.markAsTouched();
    });
  }

  // Getter methods for template access
  get email() { return this.loginForm.get('email'); }
  get password() { return this.loginForm.get('password'); }

  // Validation helper methods
  isFieldInvalid(fieldName: string): boolean {
    const field = this.loginForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.loginForm.get(fieldName);
    if (field && field.errors && (field.dirty || field.touched)) {
      if (field.errors['required']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
      }
      if (field.errors['email']) {
        return 'Please enter a valid email address';
      }
      if (field.errors['minlength']) {
        const requiredLength = field.errors['minlength'].requiredLength;
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${requiredLength} characters`;
      }
    }
    return '';
  }
}
