import { Component, Input } from '@angular/core';
import { ShipmentService } from 'src/services/shipment.service';
import { DynamicModalComponent } from 'src/shared/components/dynamic-modal/dynamic-modal.component';
import { TableColumn } from 'src/shared/models/table.models';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';

@Component({
  selector: 'app-inspection-colis-details-modal',
  standalone: true,
  imports: [DynamicModalComponent,SmartTableComponent],
  templateUrl: './inspection-colis-details-modal.component.html',
  styleUrl: './inspection-colis-details-modal.component.scss'
})
export class InspectionColisDetailsModalComponent {

  @Input() ColisId!: number;

  loading: boolean = false;
  showModal: boolean = false;

  statusList: any[] = [];
  tableColumn :TableColumn[] = [
    { name:'created_date', displayName: 'Date de réception', dataType: 'date' },
    { name:'receptor_name', displayName: 'Nom du récepteur' },
  ]

  constructor(
    private shipmentService: ShipmentService,
  ){}

  ngOnInit(): void {
    this.loadLignes();
  }

  loadLignes() {
    this.loading = true;
    this.shipmentService.getStatusByPackage(this.ColisId).subscribe({
      next: (data: any) => {
        this.statusList = data;
      },
      error: (error: any) => {
        console.error(error);
      },
      complete: () => {
        this.loading = false;
      }
    })
  }
  openModal() {
    this.showModal = true;
    this.loadLignes();
  }
  closeModal() {
    this.showModal = false;
  }
}
