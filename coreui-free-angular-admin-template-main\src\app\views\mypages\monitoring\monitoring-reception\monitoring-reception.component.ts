import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { ReactiveFormsModule, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { ButtonDirective, ButtonGroupComponent, FormCheckLabelDirective } from '@coreui/angular';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { ReceptionColisComponent } from './reception-colis/reception-colis.component';
import { ReceptionCommandeComponent } from './reception-commande/reception-commande.component';

@Component({
  selector: 'app-monitoring-reception',
  imports: [
    SmartContainerComponent,
    CommonModule,
    ButtonGroupComponent,
    FormCheckLabelDirective,
    ButtonDirective,
    ReactiveFormsModule,
    ReceptionColisComponent,
    ReceptionCommandeComponent
  ],
  templateUrl: './monitoring-reception.component.html',
  styleUrl: './monitoring-reception.component.scss'
})
export class MonitoringReceptionComponent {
  title : string = 'Liste des Livraisons';
  formRadio = new UntypedFormGroup({
    radio: new UntypedFormControl('Liste des livraisons')
  });

  constructor(
  ) { }

  setRadioValue(value: string): void {
    this.formRadio.setValue({ radio: value });
    console.log(this.formRadio.value)
    switch(this.formRadio.value['radio']){
      case 'Liste des colis':
        this.title = 'Liste des Colis';
        break;
      case 'Liste des livraisons':
        this.title = 'Liste des Livraisons';
        break;
    }
  }
}
