import { Component } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { SmartContainerComponent } from '../../../../../../shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from '../../../../../../shared/components/smart-table/smart-table.component';
import { SmartButtonComponent } from '../../../../../../shared/components/smart-button/smart-button.component';
import { ActionButton, TableColumn } from '../../../../../../shared/models/table.models';
import { ConfirmationDialogService } from '../../../../../../services/confirmation-dialog.service';
import { MarchandiseService } from '../../../../../../services/marchandise.service';
import { CommonModule } from '@angular/common';
@Component({
  selector: 'app-type-marchandise',
  imports: [SmartContainerComponent,SmartTableComponent,SmartButtonComponent,CommonModule],
  templateUrl: './type-marchandise.component.html',
  styleUrl: './type-marchandise.component.scss'
})
export class TypeMarchandiseComponent {
  
  loading: boolean = true;
  marchandise: any[] = [];
  showTable: boolean = false;
  tableColumns: TableColumn[] = [
    { name: 'nom_marchandise', displayName: 'Nom Marchandise' ,sortable: true,filterable: true},
  ];

  tableaction: ActionButton[] = [
    { icon: 'cil-trash', color: 'danger', tooltip: 'Supprimer', callback: (row: any) => this.deleteMarchandise(row) },
  ];
  
  constructor(private marchandiseService: MarchandiseService, 
    private toastr: ToastrService,
    private confirmDialogService: ConfirmationDialogService
  
  ) { }



  addMarchandise() {
    const input = document.querySelector('input[name="typeMarchandise"]') as HTMLInputElement;
    let typeMarchandise = input?.value || "";
    console.log(typeMarchandise);
    if (typeMarchandise !== "") {
      this.marchandiseService.addMerchandiseType(typeMarchandise).subscribe(
        (res) => {
          this.toastr.success('Le Type marchandise sauvegarde avec succès');
          input.value = "";
          typeMarchandise = "";
          if (this.showTable) {
            this.loadMarchandise();
          } 

        },
        (error) => {
          console.log(error)
          if (error.status === 500 && error.error && error.error.message.includes("ER_DUP_ENTRY")) {
            this.toastr.error('Le Type marchandise existe déjà');
          } else {
            console.log("Une erreur inattendue s'est produite.");
          }
        }
      );
    }
    else {
      this.toastr.error('Inseré le type à ajouté');

    }
  }
  deleteMarchandise(row: any): void {
    this.confirmDialogService.confirmDelete(`voulez-vous vraiment supprimer ce type de marchandise ${row.nom_marchandise} ?`)
    .then((res) => {
      if (res) {
        this.marchandiseService.deleteMerchandiseType(row.id).subscribe({
          next: (res) => {
            this.toastr.success('Le Type marchandise supprimé avec succès');
            this.loadMarchandise();
          },
          error: (error) => {
            console.log(error)
            this.toastr.error('Une erreur est survenue lors de la suppression du type marchandise');
          }
        });
      }
    });
  }

  showTableMarchandise() {
    this.showTable = true;
    this.loading = true;
    this.loadMarchandise();
  }


  loadMarchandise() {
    
    this.marchandiseService.getAllMerchandiseTypes().subscribe({
      next: (res:any) => {
        this.marchandise = res;
      },
      error: (error:any) => {
        this.toastr.error('Une erreur est survenue lors de la récupération des marchandises');
        console.log(error)
      },
      complete: () => {
        this.loading = false;
      }
    });
  }
}
