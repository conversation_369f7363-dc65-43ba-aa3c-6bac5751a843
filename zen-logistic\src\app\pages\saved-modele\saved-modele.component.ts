import { Component, OnInit,ViewChild  } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { RegisterServiceService } from 'app/services/register-service.service';
import { ToastrService } from 'ngx-toastr';
import { ModalDismissReasons } from '@ng-bootstrap/ng-bootstrap';
import { CommandeService } from 'app/services/commande.service';

@Component({
  selector: 'app-modele',
  templateUrl: './saved-modele.component.html',
  styleUrls: ['./saved-modele.component.scss']
})
export class SavedModeleComponent implements OnInit {
  @ViewChild('modalContent', { static: false }) modalContent: any;

  list_id = [];
  commandeEnr =[]
  // settings2 = {
  //   actions: {
  //     add: false,
  //     edit: false,
  //     delete: {
  //       confirmDelete: true,
  //       deleteButtonContent: '<i class="ft-x danger font-medium-1 mr-2"></i>',
  //     }
  //   },
  //   columns: {
  //     labelle: {
  //       title: 'Libelle',
  //       filter: true,
  //     },
  //   },
  //   // userRowSelect: (event) => {
  //   //   if (event.type === 'dblclick') {
  //   //     // Access the ID of the double-clicked row
  //   //     const selectedRowId = event.data.id;
  //   //     console.log('ID de la ligne double-cliquée :', selectedRowId);
  
  //   //     // Add your logic to display the modal with the ID here, for example:
  //   //     this.openModal(selectedRowId);
  //   //   }
  //   // },
  // };
  
    settings2 = {
      
      columns: {
        
        labelle: {
          title: 'Labelle',
          filter: true,
        },
        nom_utilisateur: {
          title: 'propriétaire ',
          filter: true,
        },

      },
      actions: {
        delete: true,   // Activer l'action de suppression
        add: false,     // Désactiver l'action d'ajout
        edit: false   ,  // Désactiver l'action d'édition

      },
      hideSearch: true, // Add this line to hide the search bar

      delete: {
        confirmDelete: true,
        deleteButtonContent: '<i class="ft-x danger font-medium-1 mr-2"></i>',
      }
      
    };
  
  settings = {
    actions: {
      add: false,
      edit: false,
      delete: false,
    },
    hideSearch: true, // Add this line to hide the search bar
    columns: {
      labelle: { title: 'Libelle', filter: false },
      ville_depart: { title: 'Ville de Départ' , filter: false },
      ville_arrivee: { title: 'Ville d\'Arrivée', filter: false },
      type_conditionnement: { title: 'Type de Conditionnement', filter: false },
      type_marchandise: { title: 'Type de Marchandise', filter: false },
      poids: { title: 'Poids', filter: false },
      quantite: { title: 'Quantité', filter: false },
      // Add other properties as needed
    },
  };
  
  

  constructor( private registerService: RegisterServiceService,
     private toastr: ToastrService,
     private commandeService : CommandeService,
     private modalService: NgbModal) { }

 async ngOnInit() {


  if(sessionStorage.getItem('userRole')=='Client'|| sessionStorage.getItem('userRole')=='GS'|| sessionStorage.getItem('userRole')=='GE'){
    console.log('test')
    await this.registerService
    .findAllCommEnrg(sessionStorage.getItem("iduser"))
    .subscribe((data) => {
      console.log(data);
      this.list_id = data;
    });
    }else if(sessionStorage.getItem('userRole')=='Chef Departement'){
      this.commandeService.findEnrgBychef(sessionStorage.getItem('iduser')).then(data => {
        this.list_id = data;
        console.log(data)
      })
    }

  }

  async onDeleteConfirm(event: any): Promise<void> {
    console.log('Deleting ID:', event.data.id);
  
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ?')) {
      try {
        if(sessionStorage.getItem('userRole')=='Chef Departement'){

          await this.registerService.deleteEnrgistredCommande(event.data.id);

        }

        console.log('Delete successful');
        event.confirm.resolve();
      } catch (error) {
        console.error('Delete error:', error);
        // Handle error, if needed
        event.confirm.reject();
      }
    } else {
      event.confirm.reject();
    }
  }


  async openModal(content) {
    try {
      const data = await this.registerService.findOneCommEnrg(content).toPromise();
      this.commandeEnr = data;
      console.log('Commande enregistrée: ', data);
      this.modalService.open(this.modalContent, { centered: true });
    } catch (error) {
      console.error('Erreur lors de la récupération de la commande enregistrée: ', error);
    }
  }
  
   

    onUserRowSelect(event): void {
      const selectedRowId = event.data.id;
      console.log('ID de la ligne sélectionnée :', selectedRowId);
      this.openModal(selectedRowId);
    }

}
