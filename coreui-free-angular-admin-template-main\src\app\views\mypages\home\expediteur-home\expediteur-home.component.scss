// ExpediteurHome Component Styles - Adapted from zen-logistic ProfileexpediteurComponent with CoreUI compatibility

#user-profile {
  .profile-with-cover {
    position: relative;

    .profil-cover-details {
      margin-top: -50px;

      .profile-image {
        img.img-border {
          border: 5px solid #fff;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }

    .profile-cover-buttons {
      position: absolute;
      top: 250px;
      right: 20px;
      z-index: 10;
    }
  }
}

.profile-section {
  padding: 30px 20px;

  .welcome-text {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--cui-primary);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  }

  .profile-menu {
    position: relative;
    top: -30px;
    padding-bottom: 0;
    padding-left: 15px;
    display: flex;
    align-items: center;
    justify-content: space-around;

    li {
      a {
        display: block;
        padding: 10px 15px;
        text-decoration: none;
        color: var(--cui-body-color);
        border-radius: 6px;
        transition: all 0.3s ease;

        &:hover {
          background-color: var(--cui-primary);
          color: white;
        }
      }
    }
  }
}

// Cover image styling
.bg-cover {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.height-300 {
  height: 300px;
}

// Profile image styling
.width-100 {
  width: 100px;
  height: 100px;
  object-fit: cover;
}

.gradient-summer {
  border: 5px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

// Button styling
.btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;

  &.btn-danger {
    background-color: var(--cui-danger);
    border-color: var(--cui-danger);

    &:hover {
      background-color: var(--cui-danger-hover, #dc3545);
      border-color: var(--cui-danger-hover, #dc3545);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
    }
  }

  &.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
  }
}

// Card styling
.card {
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--cui-border-color);

  .card-header {
    background-color: var(--cui-card-cap-bg);
    border-bottom: 1px solid var(--cui-border-color);
    border-radius: 10px 10px 0 0;

    .card-title {
      color: var(--cui-heading-color);
      font-weight: 600;
    }
  }

  .card-body {
    background-color: var(--cui-card-bg);

    strong {
      color: var(--cui-primary);
      font-weight: 600;
    }

    p {
      color: var(--cui-body-color);
      margin-bottom: 0.5rem;
    }
  }
}

// Badge styling
.badge {
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;

  &.bg-success {
    background-color: var(--cui-success) !important;
  }

  &.bg-warning {
    background-color: var(--cui-warning) !important;
    color: var(--cui-dark) !important;
  }

  &.bg-danger {
    background-color: var(--cui-danger) !important;
  }
}

// Alert styling
.alert {
  border-radius: 8px;
  border: none;

  &.alert-warning {
    background-color: rgba(var(--cui-warning-rgb), 0.1);
    color: var(--cui-warning);
    border-left: 4px solid var(--cui-warning);
  }
}

// Loading spinner
.spinner-border {
  width: 3rem;
  height: 3rem;
}

// Responsive Design
@media (max-width: 768px) {
  #user-profile {
    .profile-section {
      .profile-menu {
        top: 0px;
        flex-direction: column;
        gap: 10px;
      }
    }

    .profile-with-cover {
      .profile-cover-buttons {
        top: 15px;
        right: 15px;
      }
    }
  }

  .welcome-text {
    font-size: 1.2rem !important;
  }

  .card-title {
    font-size: 1rem !important;
  }
}

@media (max-width: 576px) {
  .profile-section {
    padding: 20px 15px;

    .welcome-text {
      font-size: 1rem !important;
    }
  }

  .width-100 {
    width: 80px;
    height: 80px;
  }

  .btn-lg {
    padding: 0.5rem 1rem !important;
    font-size: 1rem !important;
  }

  .card-body {
    padding: 1rem;
  }
}

// RTL Support
[dir="rtl"] {
  #user-profile {
    .profile-with-cover {
      .profile-cover-buttons {
        left: 20px;
        right: auto;
      }
    }
  }

  .profile-section {
    .profile-menu {
      padding-right: 15px;
      padding-left: auto;
    }
  }

  @media (max-width: 768px) {
    #user-profile {
      .profile-with-cover {
        .profile-cover-buttons {
          left: 15px;
          right: auto;
        }
      }
    }
  }
}

// Icon styling
.fas {
  color: var(--cui-primary);
}

// Text utilities
.text-muted {
  color: var(--cui-secondary) !important;
}

.text-uppercase {
  text-transform: uppercase;
}

.font-medium-2 {
  font-size: 1.1rem;
  font-weight: 500;
}