<app-smart-container
title="Data Export Example">
    <div slot="content">

        <div class="export-controls">
            <app-export-button [data]="sampleData" [fileName]="'product-data-' + today"
                [buttonText]="'Export Product Data'" (exportStarted)="onExportStart()"
                (exportCompleted)="onExportSuccess()" (exportFailed)="onExportError($event)"></app-export-button>
        </div>

        <div *ngIf="exportStatus" class="status" [class.success]="exportStatus === 'success'"
            [class.error]="exportStatus === 'error'">
            {{ statusMessage }}
        </div>
    </div>
</app-smart-container>