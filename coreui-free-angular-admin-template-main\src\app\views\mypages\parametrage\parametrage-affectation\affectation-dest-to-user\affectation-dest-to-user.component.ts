import { Component, OnInit } from '@angular/core';
import { SmartContainerComponent } from '../../../../../../shared/components/smart-container/smart-container.component';
import { DynamicFormComponent } from '../../../../../../shared/components/dynamic-form/dynamic-form.component';
import { SmartTableComponent } from '../../../../../../shared/components/smart-table/smart-table.component';
import { CustomerService, DestinationService, } from '../../../../../../services';
import { ToastrService } from 'ngx-toastr';
import { FormButton, FormConfig } from '../../../../../../shared/models/form.models';
import { CommonModule } from '@angular/common';
import { ActionButton, TableColumn } from '../../../../../../shared/models/table.models';
import { FournisseurService } from '../../../../../../services/fournisseur.service';
import { ConfirmationDialogService } from '../../../../../../services/confirmation-dialog.service';

@Component({
  selector: 'app-affectation-dest-to-user',
  standalone: true,
  imports: [SmartContainerComponent,SmartTableComponent,DynamicFormComponent,CommonModule],
  templateUrl: './affectation-dest-to-user.component.html',
  styleUrl: './affectation-dest-to-user.component.scss'
})
export class AffectationDestToUserComponent implements OnInit {
  loading:boolean=false
  showTable:boolean= false
  users:any[]=[]
  selectedUserDestinations: any[] =[]
  selectedUser:any= {}

  fournisseurDestinations: any[] =[]

  fournisseurList :any ={}

  formConfig:FormConfig = {
    title: "Affecter Utilisateur à un Chef",
    fieldGroups: [],
    buttons: []
  }
  tableColumns: TableColumn[] = [
      {
        name:'nom_locale',
        displayName:'Nom',
        sortable:true,
      }
    ]
    tableActions: ActionButton[] = [
      {
        icon:'cil-trash',
        tooltip:'Supprimer',
        color:'danger',
        callback: (row: any) => this.removeDestinationFromClient(row)
      },   
    ]
  constructor(
    private cutomerService: CustomerService,
    private destinationService:DestinationService,
    private fournisseurService:FournisseurService,
    private confirmationDialogService:ConfirmationDialogService,
    private toastr: ToastrService
  ){}
  
  ngOnInit(): void {
    this.loadusers()
    this.loadFournisseurs()
    this.buildFormConfig()
  }
  loadFournisseurs(){
    this.fournisseurService.getAllFournisseur().subscribe({
      next: (data) => {
        this.fournisseurList = data
        const fournisseurField = this.formConfig.fieldGroups?.[1].fields?.find((f: any) => f.name === 'fournisseur');
        if (fournisseurField && fournisseurField.options) {
          fournisseurField.options.objectArray = this.fournisseurList;
        }
      },
      error: (error) => {
        this.toastr.error("error")
      }
    })
  }
  buildFormConfig() {
    this.formConfig = {
      fieldGroups: [
        {
          fields: [
            {
              name: 'userId',
              label: 'Utilisateur',
              type: 'select',
              placeholder: 'Selectionner un utilisateur',
              onChange: (value:any) => this.onSelectedClientChange(value),
              options: {
                objectArray: this.users,
                valueAttribute: 'id',
                labelAttribute: 'nom_utilisateur'
              }
            }
          ]
        },
        {
          fields: [
            {
              name: 'fournisseur',
              label: 'Fournisseur',
              type: 'select',
              placeholder: 'Selectionner un fournisseur',
              onChange: (value:any) => this.onSelectedFournisseurChange(value),
              options: {
                objectArray: this.fournisseurList,
                valueAttribute: 'id',
                labelAttribute: 'nom_fournisseur'
              }
            }
          ]
        },
        {
          fields: [
            {
              name: 'newDestinations',
              label: 'Destination',
              type: 'select',
              placeholder: 'Selectionner une destination',
              options: {
                objectArray: this.fournisseurDestinations,
                valueAttribute: 'id',
                labelAttribute: 'nom_locale'
              }
            }
          ]
        }
      ],
      buttons: [
        {
          label: 'Affecter',
          color: 'primary',
          onClick: (formData: any) => this.affecterDestinationAuClient(formData)
        }
      ]
    }
  }
  removeDestinationFromClient(row:any){
    this.confirmationDialogService.confirmDelete(
      'Voulez-vous vraiment supprimer cette destination ?'
    ).then((result) => {
      if (result) {
        this.destinationService.deleteDestinationByUser(row.id).subscribe({
          next: (data) => {
            this.toastr.success("success")
            this.loadUserDestinations(this.selectedUser.id)
          },
          error: (error) => {
            this.toastr.error("error")
          }
        })
      }
    })
  }

  onSelectedClientChange(value:any) {
    this.showTable = true
    this.selectedUser = this.users.find((user:any) => user.id==value)
    this.loadUserDestinations(value)
  }

  loadUserDestinations(value:any){
    this.loading = true
    this.destinationService.findDestinationsByUser(value).subscribe({
      next: (data) => {
        
        this.selectedUserDestinations = data.destination
        this.loading = false
      },
      error: (error) => {
        this.toastr.error("error")
        this.loading = false
      }
    })
  }
  onSelectedFournisseurChange(value:any){
    this.destinationService.findDestinationsByCompany(value).subscribe({
      next: (data) => {
        this.fournisseurDestinations = data.destination
        const destinationField = this.formConfig.fieldGroups?.[2].fields?.find((f: any) => f.name === 'newDestinations');
        if (destinationField && destinationField.options) {
          destinationField.options.objectArray = this.fournisseurDestinations;
        }
      },
      error: (error) => {
        this.toastr.error("error")
      }
    })
  }
  loadusers(){
    this.cutomerService.getAllCustomers().subscribe({
      next: (data) => {
        this.users = data
        this.users.forEach((user:any)=>{
          user.nom_utilisateur = user.nom + ' ' + user.prenom
        })
        const userField = this.formConfig.fieldGroups?.[0].fields?.find((f: any) => f.name === 'userId');
        if (userField && userField.options) {
          userField.options.objectArray = this.users;
        }
      },
      error: (error) => {
        this.toastr.error("error")
      }
    })
  }
  affecterDestinationAuClient(data:any){
    let formattedData ={
      id_user:data.userId,
      id_destination:[data.newDestinations]
    }
    this.destinationService.assignDestinationToUser(formattedData.id_user,formattedData.id_destination).subscribe({
      next: (data) => {
        this.toastr.success("success")
        this.loadUserDestinations(data.userId)
      },
      error: (error) => {
        this.toastr.error("error")
      }
    })
  }
}
