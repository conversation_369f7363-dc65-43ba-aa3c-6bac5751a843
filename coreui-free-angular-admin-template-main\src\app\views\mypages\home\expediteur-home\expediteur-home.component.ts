import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { SessionStorageService, CustomerService } from '../../../../../services';
import { Subject, takeUntil, catchError, of } from 'rxjs';
import { SmartContainerComponent } from '../../../../../shared/components/smart-container/smart-container.component';

@Component({
  selector: 'app-expediteur-home',
  imports: [CommonModule,SmartContainerComponent],
  templateUrl: './expediteur-home.component.html',
  styleUrl: './expediteur-home.component.scss'
})
export class ExpediteurHomeComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // User data
  userDetail: any = null;
  isLoading = false;

  constructor(
    private sessionStorageService: SessionStorageService,
    private customerService: CustomerService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadUserDetails();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load user details from session
   */
  private loadUserDetails(): void {
    this.isLoading = true;
    const session = this.sessionStorageService.getSession();

    if (session?.iduser) {
      this.customerService.findCustomerById(session.iduser.toString())
        .pipe(
          takeUntil(this.destroy$),
          catchError(error => {
            console.error('Error loading user details:', error);
            return of(null);
          })
        )
        .subscribe(data => {
          this.userDetail = data;
          this.isLoading = false;
        });
    } else {
      this.isLoading = false;
    }
  }

  /**
   * Logout user and redirect to login
   */

}
