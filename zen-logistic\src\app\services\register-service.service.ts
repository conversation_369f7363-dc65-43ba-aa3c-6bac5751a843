import { HttpClient, HttpEvent, HttpHeaders, HttpParams, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { environment } from 'environments/environment';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

const httpOptions = {
  headers: new HttpHeaders({ 'Content-Type': 'application/json', 'Authorization': 'Basic ' + btoa('med:123456') })
};

@Injectable({
  providedIn: 'root'
})
export class RegisterServiceService {
  [x: string]: any;
  apiURL = environment.apiURL;

  constructor(private http: HttpClient,private router : Router
    ) { }
  getFacture(id): Observable<any> {
    console.log(this.apiURL + 'facture/' + id)
    return this.http.get<any>(this.apiURL + 'facture/' + id, httpOptions)
  }
  updateNote(data, id): Observable<any> {
    return this.http.put<any>(this.apiURL + 'comm/updatenote/' + id, data, httpOptions);
  }
  getFactureE(id): Observable<any> {
    return this.http.get<any>(this.apiURL + 'facturebyexpediteur/' + id, httpOptions)
  }

  getAllFacture(page: number, pageSize: number, idClient?: any, invoiceDate?: string): Observable<any> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('pageSize', pageSize.toString());
  
    if (idClient) {
      params = params.set('id_client', idClient.toString());
    }
  
    if (invoiceDate) {
      params = params.set('invoice_date', invoiceDate);
    }
  
    return this.http.get<any>(`${this.apiURL}allfacture/`, { ...httpOptions, params });
  }
  

  genererFacture(facture): Observable<any> {
    return this.http.post<any>(this.apiURL + 'facture', facture, httpOptions);

  }
  listCustomers(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'customers', httpOptions);

  }
  addCustomers(data): Observable<any> {
    return this.http.post<any>(this.apiURL + 'customers', data, httpOptions);

  }

  sendEmail(data): Observable<any> {
    return this.http.post<any>(this.apiURL + 'email', data, httpOptions);
  }

  updateByCles(data, cle): Observable<any> {
    return this.http.put<any>(this.apiURL + 'customers/update/' + cle, data, httpOptions);
  }

  upCustomerTr(data, id): Observable<any> {
    return this.http.put<any>(this.apiURL + 'custumm/' + id, data, httpOptions);
  }
  getReclamationExp(id): Observable<any> {
    return this.http.get<any>(this.apiURL + 'reclamation/expediteur/' + id, httpOptions);
  }
  addConducteur(data): Observable<any> {
    return this.http.post<any>(this.apiURL + 'conducteur', data, httpOptions);

  }

  getReclamationTransporteur(id): Observable<any> {
    return this.http.get<any>(this.apiURL + 'reclamation/transporteur/' + id, httpOptions);
  }
  getReclamations(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'reclamations', httpOptions);
  }
  findAll(): Observable<any> {
    // console.log(this.apiURL+'customers')
    return this.http.get<any>(this.apiURL + 'customers', httpOptions);
  }

  findauth(data): Observable<any> {
    return this.http.post<any>(this.apiURL + 'customers/login/', data, httpOptions);
  }

  update(data, id): Observable<any> {
    return this.http.put<any>(this.apiURL + 'cust/' + id, data, httpOptions);
  }

  updateTr(data, id): Observable<any> {
    return this.http.put<any>(this.apiURL + 'custom/' + id, data, httpOptions);
  }

  delete(id): Observable<any> {
    return this.http.get<any>(this.apiURL + 'customers/delate/' + id, httpOptions);
  }

  findOne(id): Promise<any> {
    return this.http.get<any>(this.apiURL + 'customers/' + id, httpOptions)
      .toPromise()
      .catch(error => {
        if (error.status === 401 || error.status === 403 || error.status === 0) {
          // Si erreur de connexion refusée ou non autorisée, rediriger vers la page de connexion
          this.router.navigate(['pages/login']);
        }
        return Promise.reject(error);
      });
  }

  findAllTr(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'customer/Transporteur', httpOptions);
  }

  findAllExp(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'customer/Expediteur', httpOptions);
  }

  // pour vérifier le nom de l'utilisateur
  findNomCust(data): Observable<any> {
    return this.http.get<any>(this.apiURL + 'custo/' + data, httpOptions);
  }

  // conducteur

 

  async findAllConducteur(id): Promise<any> {
    return new Promise((resolve, reject) => {
      this.http.get(this.apiURL + 'conducteurTr/' + id, httpOptions).toPromise().then(res => {
        resolve(res)
      }).catch(err => {
        reject(err.error.message)
      })
    });
  }


  findConducteurAdmin(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'conducteur', httpOptions);
  }

  conduceurBloqued(id): Observable<any> {
    return this.http.get<any>(this.apiURL + 'conducteur/conduceurBloqued/' + id, httpOptions);
  }

  findConducteur(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'conducteur/', httpOptions);
  }

  findConducteurById(id): Promise<any> {
    const url = `${this.apiURL}conducteur/${id}`;

    return this.http.get(url, httpOptions).toPromise();

  }

  // camion

  addCamion(data): Observable<any> {
    return this.http.post<any>(this.apiURL + 'camion', data, httpOptions);

  }

  findAllCamion(): Observable<any> {

    return this.http.get<any>(this.apiURL + 'camion', httpOptions);
  }

  async getCustomerDirectly(id): Promise<any> {
    return new Promise((resolve, reject) => {
      this.http.get(this.apiURL + 'camionTr/' + id, httpOptions).toPromise().then(res => {
        resolve(res)
      }).catch(err => {
        reject(err.error.message)
      })
    });
  }

  deleteCamion(id): Observable<any> {
    return this.http.get<any>(this.apiURL + 'camion/delate/' + id, httpOptions);
  }

  findcamion(id): Observable<any> {
    return this.http.get<any>(this.apiURL + 'camion/' + id, httpOptions);
  }

  findAllCamionAdmin(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'camion', httpOptions);
  }
  // commande

  addCommande(data): Observable<any> {
    return this.http.post<any>(this.apiURL + 'commande', data, httpOptions);
  }

  findAllCommEnrg(id): Observable<any> {
    return this.http.get<any>(this.apiURL + 'getAllCommande/' + id, httpOptions);
  }

  findAllCommande(): Observable<any> {
    // console.log(this.apiURL+'commande')
    return this.http.get<any>(this.apiURL + 'commande', httpOptions);
  }

  findCommande(id): Observable<any> {
    return this.http.get<any>(this.apiURL + 'commande/' + id, httpOptions);
  }
  /* findCommande(id) : Observable<any> {
     return this.http.get<any>( this.apiURL+'commande/'+id, httpOptions ) ; }*/



  updateComm(data, id): Observable<any> {

    return this.http.put<any>(this.apiURL + 'comm/update/' + id, data, httpOptions);
  }

  findAllComm(id): Observable<any> {
    return this.http.get<any>(this.apiURL + 'comm/' + id, httpOptions);
  }


  // ( component "list-commande Expéditeur" )
  updatecomm(data, id): Observable<any> {
    return this.http.put<any>(this.apiURL + 'comm/up/' + id, data, httpOptions);
  }

  findAllCom(id): Observable<any> {
    return this.http.get<any>(this.apiURL + 'com/' + id, httpOptions)
      .pipe(
        catchError((err) => {
          // Handle the error here

          return throwError(err);    // Rethrow it back to component
        })
      )
  }
  findAllComTrpValide(id): Observable<any> {
    return this.http.get<any>(this.apiURL + 'comTrpValide/' + id, httpOptions)
      .pipe(
        catchError((err) => {
          // Handle the error here

          return throwError(err);    // Rethrow it back to component
        })
      )

  }

  findComARes(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'comareserve/', httpOptions)
      .pipe(
        catchError((err) => {
          // Handle the error here

          return throwError(err);    // Rethrow it back to component
        })
      )

  }
  // lescommandes a valider par expéditeur

  findComm(id): Observable<any> {
    return this.http.get<any>(this.apiURL + 'commande/exp/' + id, httpOptions);
  }

  // Update commande ( réserver commande (trp) )
  updateCommTr(data, id): Observable<any> {
    return this.http.put<any>(this.apiURL + 'comande/' + id, data, httpOptions);
  }

  // find commande resevées ( int trp)

  findAllCommRes(id): Observable<any> {
    return this.http.get<any>(this.apiURL + 'commande/Reservees/' + id, httpOptions);
  }

  // find commande resevées ( int exp)

  findCommRes(id): Observable<any> {
    return this.http.get<any>(this.apiURL + 'commande/Res/' + id, httpOptions);
  }



  // ville

  getVilles(): Promise<any[]> {
    return this.http.get<any[]>(this.apiURL + 'ville', httpOptions).toPromise();
  }

  // Région

  findRegion(id_ville): Observable<any> {
    return this.http.get<any>(this.apiURL + 'region/' + id_ville, httpOptions);
  }

  // point de chargement
  addPtChar(data): Observable<any> {
    return this.http.post<any>(this.apiURL + 'ptchargement', data, httpOptions);
  }

  addChar(data): Observable<any> {
    return this.http.post<any>(this.apiURL + 'ptchargement', data, httpOptions);
  }
  addCharDecharByIdCmd(id_commande): Observable<any> {
    return this.http.get<any>(this.apiURL + 'ptchargDecharg/' + id_commande, httpOptions);
  }

  getAllEntreprise(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'entreprises/', httpOptions)
  }

  getClientByExp(idExp): Promise<any> {
    return this.http.get<any>(this.apiURL + 'getClientByExp/' + idExp, httpOptions).toPromise();
  }

  getAllClients(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'getAllClients', httpOptions);
  }

  setclientToExp(idClient, idExp) {
    const  data = {
      idExp: idExp,
      idClient: idClient
    }
    return this.http.post<any>(this.apiURL + 'customers/setclient', data, httpOptions);
  }
  removeClientFromExp(idClient, idExp) {
    const  data = {
      idExp: idExp,
      idClient: idClient
    }
    return this.http.post<any>(this.apiURL + 'customers/removeClient', data, httpOptions);
  }

  disableClient(customerId) {

    return this.http.get<any>(this.apiURL + `customers/disable/${customerId}`, httpOptions);
  }

  getLienuxDecharByCustomer(idClient): Observable<any> {
    const  data = {
      user_id: idClient
    }
    return this.http.post<any>(this.apiURL + 'getLienuxDecharByCustomer', data, httpOptions);
  }
  addLieuxDechar(idClient, libelle): Observable<any> {
    const  data = {
      user_id: idClient,
      libelle: libelle
    }
    return this.http.post<any>(this.apiURL + 'addLieuxDechar', data, httpOptions);
  }

  addTypeMarchandise(data): Observable<any> {
    const toAdd ={nom_marchandise : data}
    return this.http.post<any>(this.apiURL + 'marchandise', toAdd, httpOptions);
  }

  addTypeCondition(data): Observable<any> {
    const toAdd ={nom_condition : data}
    return this.http.post<any>(this.apiURL + 'condition', toAdd, httpOptions);
  }

  getAllMarchandise(): Observable<any> {
    return this.http.get<any>(this.apiURL +'marchandise',httpOptions);
  }

  getAllCondition(): Observable<any> {
    return this.http.get<any>(this.apiURL +'condition',httpOptions);
  }

  getAlltypeCmd(): Observable<any> {
    return this.http.get<any>(this.apiURL +'typecmd',httpOptions);
  }

  deleteMarchandise(id: number): Promise<any> {
    const url = `${this.apiURL}marchandise/${id}`;
    console.log('DELETE URL:', url);

    return this.http.delete<any>(url, httpOptions)
      .toPromise()
      .then(response => response)
      .catch(error => {
        console.error('Error deleting marchandise:', error);
        throw error; // Rethrow the error for further handling
      });
  }

  deleteCondition(id: number): Promise<any> {
    const url = `${this.apiURL}condition/${id}`;
    console.log('DELETE URL:', url);

    return this.http.delete<any>(url, httpOptions)
      .toPromise()
      .then(response => response)
      .catch(error => {
        console.error('Error deleting condition:', error);
        throw error; // Rethrow the error for further handling
      });
  }

  deleteEnrgistredCommande(id: number): Promise<any> {
    return this.http.put<any>(`${this.apiURL}comm/deleteEnr/${id}`, null, httpOptions)
      .pipe(
        catchError(error => {
          throw new Error(error);
        })
      )
      .toPromise();
  }

  findCustomerByMail(email): Observable<any> {
    return this.http.get<any>(this.apiURL +`getUserByMail/${email}`,httpOptions);
  }

  findAllClient(): Observable<any> {
    return this.http.get<any>(this.apiURL +`findAllClient`,httpOptions);
  }

  findAllChef(): Observable<any> {
    return this.http.get<any>(this.apiURL +`findAllChef`,httpOptions);
  }


  createChefUser(data): Promise<any> {
    return this.http.post<any>(this.apiURL + 'createChefUser', data,  httpOptions)
      .toPromise();
  }

  findUsersByChef(selectedChefId): Observable<any> {
    return this.http.get<any>(this.apiURL +`findUsersByChef/${selectedChefId}`,httpOptions);
  }

  getAllRole(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'roles', httpOptions);
  }

  updateUserInfo(data): Observable<any> {
    return this.http.put<any>(this.apiURL + 'updateUserById/' + data.id, data, httpOptions);
  }
  
  lastConnexion(id): Observable<any> {
    return this.http.put<any>(this.apiURL + 'lastConnexion/' + id, null, httpOptions);
  }
  updateClientColor(id,data): Observable<any> {
    return this.http.put<any>(this.apiURL + 'updateClientColor/' + id, data, httpOptions);
  }

  findFactureByClientAndDate(id_client: any, invoiced_date: string, page: number, pageSize: number): Observable<any> {
    const params = new HttpParams()
      .set('id_client', id_client.toString())
      .set('invoiced_date', invoiced_date) // invoiced_date doit être sous format 'MMYYYY'
      .set('page', page.toString())
      .set('pageSize', pageSize.toString());
  
    return this.http.get<any>(`${this.apiURL}findFactureByClientAndDate`, { ...httpOptions, params });
  }
  



}



