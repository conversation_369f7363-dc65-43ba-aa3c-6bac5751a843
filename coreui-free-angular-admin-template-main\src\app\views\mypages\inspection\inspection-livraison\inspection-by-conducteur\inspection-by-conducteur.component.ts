import { Component, OnInit, ViewChild } from '@angular/core';
import { SessionStorageService, VoyageService } from 'src/services';
import { ConducteurService } from 'src/services/conducteur.service';
import { ActionButton, TableColumn ,TableConfig} from 'src/shared/models/table.models';
import { FormConfig } from 'src/shared/models/form.models';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';
import { DynamicExportButtonComponent } from 'src/shared/components/dynamic-export-button/dynamic-export-button.component';
import { CommonModule } from '@angular/common';
import { DynamicFormComponent } from 'src/shared/components/dynamic-form/dynamic-form.component';
import { ToastrService } from 'ngx-toastr';
import { LigneCmdService } from 'src/services/ligne-cmd.service';
import { ConfirmationDialogService } from 'src/services/confirmation-dialog.service';
import { InspectionSharedModalComponent } from '../shared/inspection-shared-modal/inspection-shared-modal.component';

@Component({
  selector: 'app-inspection-by-conducteur',
  standalone: true,
  imports: [SmartContainerComponent,SmartTableComponent,DynamicExportButtonComponent,CommonModule,DynamicFormComponent,InspectionSharedModalComponent],
  templateUrl: './inspection-by-conducteur.component.html',
  styleUrl: './inspection-by-conducteur.component.scss'
})
export class InspectionByConducteurComponent implements OnInit{

  @ViewChild('detailModal') detailModal!: InspectionSharedModalComponent;

  loading = false;
  showTable = false;
  DataExportLabel!: {
    nom_conducteur: string,
    date_debut: string,
    date_fin: string
  } 
  tableColumn : TableColumn[] =[
    {
      name: 'id',
      displayName: 'ID',
      sortable: true,
      filterable: true,
    },
    {
      name: 'nom_voyage',
      displayName: 'Nom Voyage',
      sortable: true,
      filterable: true,
    },
    {
      name: 'sell_price',
      displayName: 'Montant de vente',
      sortable: true,
      filterable: true,
    },
    {
      name: 'purchase_price',
      displayName: 'Montant à payer',
      sortable: true,
      filterable: true,
    },
    {
      name: 'date_voyage',
      displayName: 'Date du voyage',
      dataType: 'date',
      sortable: true,
      filterable: true,
    }
  ];
  tableConfig: TableConfig = {
    emptyMessage: 'Aucune ligne trouvée', 
  }
  tableActions: ActionButton[] = [
    {
      icon: 'cil-trash',
      color: 'danger',
      callback: (row: any) => {this.handleVoyageCancellation(row)}
    },
    {
      icon: 'cil-zoom',
      color: 'primary',
      callback: (row: any) => {this.ViewDetails(row)}
    }
  ]
  
  drivers: any[] = [];
  lignes: any[] = [];
  totalToPay: any;
  formConfig!: FormConfig 
  

  constructor(
    private conducteurService: ConducteurService,
    private voyageService: VoyageService,
    private LigneCmdService: LigneCmdService,
    private confirmDialogService: ConfirmationDialogService,
    private toastr: ToastrService,
    private sessionService: SessionStorageService
  ){}

  

  ngOnInit(): void {
    this.loadConducteur();
    this.buildFormConfig();
  }
  loadConducteur() {
    this.conducteurService.findConducteurAdmin().subscribe({
      next: (data: any) => {
        this.drivers = data;
        this.drivers.forEach((driver: any) => {
          driver.fullName = driver.nom + ' ' + driver.prenom;
        });
        const DriverFiels = this.formConfig?.fieldGroups?.find((fieldGroup: any) => fieldGroup.fields.find((field: any) => field.name === 'id_conducteur'));
        if (DriverFiels && DriverFiels.fields) {
          DriverFiels.fields[0].options = {
            objectArray: this.drivers,
            valueAttribute: 'id',
            labelAttribute: 'fullName'
          };
        }
        console.log(this.drivers);
      },
      error: (error: any) => {
        console.error(error);
      }
    });
  }
  buildFormConfig() {
    this.formConfig = {
      fieldGroups: [
        {
          fields: [
            {
              name: 'id_conducteur',
              type: 'select',
              label: 'Conducteur',
              required: true,
              options: 
                {
                  objectArray: this.drivers,
                  valueAttribute: 'id',
                  labelAttribute: 'fullName'
                }
            },
          ]
        }
      ],
      fields: [
        
        {
          name: 'date_debut',
          type: 'date',
          label: 'Date de début',
          required: true
        },
        {
          name: 'date_fin',
          type: 'date',
          label: 'Date de fin',
          required: true
        }
      ],
      buttons: [
        {
          label: 'Rechercher',
          color: 'primary',
          icon: 'cil-search',
          onClick: (formData: any) => this.onSubmit(formData)
        }
      ]
    }
  }

  onSubmit(formData: any) {
    if (this.sessionService.getSessionValue('userRole') !== 'SuperAdmin' && this.sessionService.getSessionValue('userRole') !== 'Administrateur') {
      this.toastr.error('Vous n\'avez pas accès à cette fonctionnalité');
      return;
    }
    this.showTable = true;
    this.loading = true;
    console.log(formData);
    const data = {
      id_conducteur: formData.id_conducteur,
      date_debut: formData.date_debut,
      date_fin: formData.date_fin
    }
    this.DataExportLabel = {
      nom_conducteur: this.drivers.find((driver: any) => driver.id === parseInt(formData.id_conducteur))?.fullName,
      date_debut: formData.date_debut,
      date_fin: formData.date_fin
    };
    this.LigneCmdService.findLigneDelivredByDriver(data).subscribe({
      next: (data: any) => {
        this.lignes = data.data;
        if (this.lignes.length > 0) {
          this.totalToPay = this.lignes.reduce((total: any, ligne: any) => total + ligne.purchase_price, 0);
        }
        console.log(this.lignes);
      },
      error: (error: any) => {
        console.error(error);
      },
      complete: () => {
        this.loading = false;
      }
    })
    this.loading = false;

  }
  handleVoyageCancellation(row: any): void {
    const voyageId = row.id;
    this.confirmDialogService.confirmDelete("Voulez-vous vraiment annuler ce voyage ?").then(
      (data: any) => {
        this.voyageService.cancelVoyage(voyageId).subscribe({
            next: (data: any) => {
              this.toastr.success('Voyage annulé avec succès');
              this.lignes = this.lignes.filter((ligne: any) => ligne.id !== voyageId);
            },
            error: (error: any) => {
              console.error(error);
            },
            complete: () => {
              this.loading = false;
            }
          })
        }
    )  
  }
  ViewDetails(row: any): void {
    this.detailModal.VoyageId = row.id;
    this.detailModal.openModal();
  }
}
