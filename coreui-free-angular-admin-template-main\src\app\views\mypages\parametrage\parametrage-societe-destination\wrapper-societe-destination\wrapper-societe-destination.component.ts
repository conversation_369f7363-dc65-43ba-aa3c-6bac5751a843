import { Component } from '@angular/core';
import { SmartContainerComponent } from '../../../../../../shared/components/smart-container/smart-container.component';
import { SmartButtonComponent } from '../../../../../../shared/components/smart-button/smart-button.component';
import { AjouterDestinationComponent } from '../ajouter-destination/ajouter-destination.component';
import { AjouterSocieteComponent } from '../ajouter-societe/ajouter-societe.component';
import { GestionDestinationComponent } from '../gestion-destination/gestion-destination.component';
import { CommonModule } from '@angular/common';
@Component({
  selector: 'app-wrapper-societe-destination',
  standalone: true,
  imports: 
  [
    SmartContainerComponent,SmartButtonComponent,
    AjouterDestinationComponent,
    AjouterSocieteComponent,
    GestionDestinationComponent,
    CommonModule
  ],
  templateUrl: './wrapper-societe-destination.component.html',
  styleUrl: './wrapper-societe-destination.component.scss'
})
export class WrapperSocieteDestinationComponent {
  showprimaryView: boolean = true;
  toggleView() {
    this.showprimaryView = !this.showprimaryView;
  }

}
