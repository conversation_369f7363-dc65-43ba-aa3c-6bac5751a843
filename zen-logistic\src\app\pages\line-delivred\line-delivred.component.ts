import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { LigneCmdService } from 'app/services/ligneCmd.service';

@Component({
  selector: 'app-line-delivred',
  templateUrl: './line-delivred.component.html',
  styleUrls: ['./line-delivred.component.scss']
})
export class LineDelivredComponent implements OnInit {

  source = [];

  constructor(private ligneCmdService : LigneCmdService,private router: Router) { }

  ngOnInit() {


    this.ligneCmdService.ligneCmdLivredByDemand(sessionStorage.getItem('iduser')).subscribe(data => {
      this.source = data;
      console.log(data);
    })
    if(sessionStorage.getItem('userRole')=='Client'|| sessionStorage.getItem('userRole')=='GS'|| sessionStorage.getItem('userRole')=='GE'){
      console.log('test')
      this.ligneCmdService.ligneCmdLivredByDemand(sessionStorage.getItem('iduser')).subscribe(data => {
        this.source = data;
        console.log(data)
      })}else if(sessionStorage.getItem('userRole')=='Chef Departement'){
        this.ligneCmdService.findDeliveredByChef(sessionStorage.getItem('iduser')).subscribe(data => {
          this.source = data.data;
          console.log(data)
        })
      }else{
        this.router.navigate[('/pages/login')]
      }


  }


  settings = {
    actions: {
      add: false,
      edit: false,
      delete: false,
    },


    columns: {
      id: {
        title: 'ID'
      },
     date_depart: {
        title: 'Date Départ'
      },
      date_arrivee: {
        title: 'Date Arrivée'
      },

      date_voyage: {
        title: 'Date Voyage'
      },
      nom_depart: {
        title: 'Départ'
      },

      nom_arrivee: {
        title: 'Arrivée'
      },

      type_ligne: {
        title: 'Type',
      },
      volume: {
        title: 'Volume',
      },
      quantite: {
        title: 'Quantité',
      },
      ajoutee_par: {
        title: 'Demandeur',
      },
      conducteur_nom_prenom: {
        title: 'Nom Conducteur',
      },
      mobile: {
        title: 'Conducteur Tel',
      },

      
    },

    // attr : les lignes dans le tableau 
    attr: {
      class: "table table-responsive"
    },

}}
