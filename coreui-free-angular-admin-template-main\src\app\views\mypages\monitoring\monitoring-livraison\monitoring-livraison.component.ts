import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { ReactiveFormsModule, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { ButtonDirective, ButtonGroupComponent, FormCheckLabelDirective } from '@coreui/angular';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { LivraisonColisComponent } from './livraison-colis/livraison-colis.component';
import { LivraisonCommandeComponent } from './livraison-commande/livraison-commande.component';

@Component({
  selector: 'app-monitoring-livraison',
  standalone:true,
  imports: [
    SmartContainerComponent,
    CommonModule,
    ButtonGroupComponent,
    FormCheckLabelDirective,
    ButtonDirective,
    ReactiveFormsModule,
    LivraisonColisComponent,
    LivraisonCommandeComponent
  ],
  templateUrl: './monitoring-livraison.component.html',
  styleUrl: './monitoring-livraison.component.scss'
})
export class MonitoringLivraisonComponent {
  title:string = 'Liste des livraisons';
  formRadio = new UntypedFormGroup({
    radio: new UntypedFormControl('Liste des livraisons')
  });

  constructor(
  ) { }

  setRadioValue(value: string): void {
    this.formRadio.setValue({ radio: value });
    console.log(this.formRadio.value)
    switch(this.formRadio.value['radio']){
      case 'Liste des colis':
        this.title = 'Liste des Colis';
        break;
      case 'Liste des livraisons':
        this.title = 'Liste des Livraisons';
        break;
    }
  }
}
