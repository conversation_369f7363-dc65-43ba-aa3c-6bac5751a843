<app-smart-container title="Transport">


    <div slot="content">
        <div class="card-body" style="display: flex;" *ngIf="!isPasswordCorrect">
            <input id="password" type="password" class="form-control" style="width: 300px;" placeholder="Entrez le mot de passe" name="password">
            <app-smart-button [label]="'Valider'" [color]="'primary'" (onClick)="checkPassword()"></app-smart-button>
        </div>

        <div *ngIf="isPasswordCorrect">
            <app-smart-table 
            [data]="prices" 
            [columns]="tableColumns"
            [actionButtons]="tableaction" 
            [isLoading]="loading">
        </app-smart-table>
        </div>
    </div>
</app-smart-container>

<app-form-modal 
[config]="formConfig"
[initialData]="initialData" 
[(visible)]="showModal" 
size="lg" 
(formChange)="onFormChange($event)"
>
</app-form-modal>
