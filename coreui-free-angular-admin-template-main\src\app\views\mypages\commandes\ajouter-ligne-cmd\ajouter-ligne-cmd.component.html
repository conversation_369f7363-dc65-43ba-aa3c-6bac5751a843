<app-smart-container>

    <div slot="content">

        
            <app-smart-container [title]="'Details Ligne'">
                <div slot="actions">
                    <app-smart-button
                        [label]="'Affecter'"
                        [icon]="'cilPlus'"
                        [disabled]="disableButton"
                        [color]="'primary'"
                        (onClick)="Affecter()">
                    </app-smart-button>
                </div>
                <div slot="content">
                    <app-dynamic-form
                        [config]="formConfig"
                        [initialData]="initialData">
                    </app-dynamic-form>
                    <div *ngIf="showDetailsTable">
                        <app-smart-table
                        [data]="sourceDetails"
                        [columns]="tableDetailsColumn"
                        [config]="tableDetailsConfig"
                        [isLoading]="loadingDetailsTable"
                        [actionButtons]="tableDetailsAction"
                        (selectionChange)="onSelectDetailsChange($event)">
                    </app-smart-table>
                    </div>
                </div>
            </app-smart-container>
        
        <app-smart-table 
            [data]="VoyageLineToAdjust" 
            [columns]="tableColumn" 
            [config]="tableConfig"
            (selectionChange)="onSelectChange($event)"></app-smart-table>
    </div>
</app-smart-container>