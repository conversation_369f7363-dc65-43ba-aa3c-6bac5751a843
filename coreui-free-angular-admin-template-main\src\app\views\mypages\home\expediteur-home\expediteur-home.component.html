<!-- Expediteur Profile Section -->
<section id="user-profile" class="container-fluid">
  <!-- Loading Indicator -->
  <div *ngIf="isLoading" class="d-flex justify-content-center align-items-center" style="height: 200px;">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Profile Content -->
  <div *ngIf="!isLoading" class="row">
    <div class="col-12">
      <div class="card profile-with-cover">


        <!-- Welcome Section -->
        <div class="profile-section text-center" *ngIf="userDetail">
          <span class="font-medium-2 text-uppercase welcome-text">
            BIENVENUE {{ userDetail.nom_utilisateur || (userDetail.prenom + ' ' + userDetail.nom) }} CHEZ ZEN LOGISTIC
          </span>
          <p class="text-muted font-medium-2 mt-2">Espace Expéditeur</p>
        </div>

        <!-- User Details Card -->
        <div *ngIf="userDetail" class="row mt-4">
          <div class="col-md-8 offset-md-2">

            <app-smart-container [title]="'Informations du Profil'">
              <div slot="content">
                <div class="row">
                  <div class="col-md-6 mb-3" *ngIf="userDetail.nom">
                    <strong>Nom:</strong>
                    <p class="mb-0">{{ userDetail.nom }}</p>
                  </div>
                  <div class="col-md-6 mb-3" *ngIf="userDetail.prenom">
                    <strong>Prénom:</strong>
                    <p class="mb-0">{{ userDetail.prenom }}</p>
                  </div>
                  <div class="col-md-6 mb-3" *ngIf="userDetail.email">
                    <strong>Email:</strong>
                    <p class="mb-0">{{ userDetail.email }}</p>
                  </div>
                  <div class="col-md-6 mb-3" *ngIf="userDetail.mobile">
                    <strong>Mobile:</strong>
                    <p class="mb-0">{{ userDetail.mobile }}</p>
                  </div>
                  <div class="col-md-6 mb-3" *ngIf="userDetail.type_utilisateur">
                    <strong>Type d'utilisateur:</strong>
                    <p class="mb-0">{{ userDetail.type_utilisateur }}</p>
                  </div>
                  <div class="col-md-6 mb-3" *ngIf="userDetail.statut">
                    <strong>Statut:</strong>
                    <span class="badge" [class.bg-success]="userDetail.statut === 'Actif'"
                      [class.bg-warning]="userDetail.statut === 'En attente'"
                      [class.bg-danger]="userDetail.statut === 'Inactif'">
                      {{ userDetail.statut }}
                    </span>
                  </div>
                </div>

                <!-- Additional Information -->
                <div *ngIf="userDetail.adresse || userDetail.ville || userDetail.pays" class="row mt-3">
                  <div class="col-12">
                    <h5><i class="fas fa-map-marker-alt me-2"></i>Adresse</h5>
                    <hr>
                  </div>
                  <div class="col-md-12 mb-3" *ngIf="userDetail.adresse">
                    <strong>Adresse:</strong>
                    <p class="mb-0">{{ userDetail.adresse }}</p>
                  </div>
                  <div class="col-md-6 mb-3" *ngIf="userDetail.ville">
                    <strong>Ville:</strong>
                    <p class="mb-0">{{ userDetail.ville }}</p>
                  </div>
                  <div class="col-md-6 mb-3" *ngIf="userDetail.code_postal">
                    <strong>Code Postal:</strong>
                    <p class="mb-0">{{ userDetail.code_postal }}</p>
                  </div>
                  <div class="col-md-6 mb-3" *ngIf="userDetail.pays">
                    <strong>Pays:</strong>
                    <p class="mb-0">{{ userDetail.pays }}</p>
                  </div>
                </div>
              </div>
            </app-smart-container>
          </div>

          <!-- No User Data Message -->
          <div *ngIf="!userDetail && !isLoading" class="row mt-4">
            <div class="col-12 text-center">
              <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Aucune information utilisateur disponible.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>