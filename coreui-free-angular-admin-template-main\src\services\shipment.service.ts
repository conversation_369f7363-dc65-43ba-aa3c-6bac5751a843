import { HttpClient, HttpEvent, HttpHeaders, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
const httpOptions = {
    headers: new HttpHeaders({ 'content-type': 'application/json', Authorization: 'basic ' + btoa('med:123456') })
  };
  
  @Injectable({
    providedIn: 'root'
  })

  export class ShipmentService {
    apiURL = environment.apiURL
    constructor(private http: HttpClient) { }

    transferPackages(): Observable<any> {
        return this.http.get<any>(this.apiURL + `transferPackages`, httpOptions);
      }

      receptionPackage(data: any) {
        return this.http.post(`${this.apiURL}receptionPackage`,data, httpOptions);
      }


      findPackages(data: any) {
        return this.http.post(`${this.apiURL}search`,data, httpOptions);
      }

      getStatusByPackage(id:any): Observable<any> {
        return this.http.get<any>(this.apiURL + `getStatusByPackage/${id}`, httpOptions);
      }
      

      generateCode(data: any): Observable<any> {
        return this.http.post(`${this.apiURL}generateCode`,data, httpOptions);
      }

      getBarcodes(data: any): Observable<any> {
        return this.http.post(`${this.apiURL}getBarcodes`,data, httpOptions);
      }
     

      getLigneWithPackageById(id:any): Observable<any> {
        return this.http.get<any>(this.apiURL + `getLigneWithPackageById/${id}`, httpOptions);
      }
      getHistoricalStatusStats(): Observable<any> {
        return this.http.get<any>(this.apiURL + `getHistoricalStatusStats`, httpOptions);
      }
      
      getPackageByStatusAndDate(data: any): Observable<any> {
        return this.http.post(`${this.apiURL}getPackageByStatusAndDate`,data, httpOptions);
      }
      getStatisticPackage(): Observable<any> {
        return this.http.get<any>(this.apiURL + `getStatisticPackage`, httpOptions);
      }
      findPackageDepartByUser(id : any): Observable<any> {
        return this.http.get<any>(this.apiURL + `findPackageDepartByUser/${id}`, httpOptions);
      }

      findPackageArrivalByUser(id : any): Observable<any> {
        return this.http.get<any>(this.apiURL + `findPackageArrivalByUser/${id}`, httpOptions);
      }

      receptionPackageMagasin(data: any) {
        return this.http.post(`${this.apiURL}receptionPackageMagasin`,data, httpOptions);
      }

      
  }