<div class="c-card mt-3">
  <div class="c-card-header">
    <div class="d-flex justify-content-between align-items-center">
      <div class="header-content">
        <h4 class="mb-0">{{ title }}</h4>
        @if (subtitle) {
          <p class="text-muted mb-0 mt-1">{{ subtitle }}</p>
        }
      </div>
      @if (showActions) {
        <div class="header-actions">
          <ng-content select="[slot=actions]"></ng-content>
        </div>
      }
    </div>
  </div>
  <div class="c-card-body">
    <ng-content select="[slot=content]"></ng-content>
  </div>
</div>
