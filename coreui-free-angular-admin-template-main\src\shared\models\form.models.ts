export interface FormField {
  type: 'text' | 'number' | 'email' | 'password' | 'textarea' | 'select' | 'multiselect' | 'image' | 'date' | 'checkbox' | 'radio';
  label?: string;
  name: string;
  value?: any;
  required?: boolean;
  disabled?: boolean | (() => boolean);
  hidden?: boolean | (() => boolean);
  placeholder?: string;
  onChange?: (value: any) => void; 
  validation?: {
    pattern?: string;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
  };
  options?: {
    objectArray?: any[];
    valueAttribute?: string;
    labelAttribute?: string;
    inline?: boolean;
  };
}

export interface FormFieldGroup {
  groupLabel?: string;
  fields: FormField[];
}

export interface FormButton {
  label: string;
  color: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  icon?: string;
  disabled?: boolean | (() => boolean);
  onClick?: (formData: any) => void;
}

export interface FormConfig {
  fields?: FormField[];
  fieldGroups?: FormFieldGroup[];
  buttons?: FormButton[];
  title?: string;
  subtitle?: string;
}