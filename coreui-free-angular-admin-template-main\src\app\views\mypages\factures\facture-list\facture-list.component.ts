import { Component, OnInit, ViewChild } from '@angular/core';
import { FactureService } from '../../../../../services/facture.service';
import { SmartTableComponent } from '../../../../../shared/components/smart-table/smart-table.component';
import { ActionButton, TableColumn, TableConfig } from '../../../../../shared/models/table.models';
import { DynamicExportButtonComponent } from '../../../../../shared/components/dynamic-export-button/dynamic-export-button.component';
import { SmartContainerComponent } from '../../../../../shared/components/smart-container/smart-container.component';
import { DynamicModalComponent } from '../../../../../shared/components/dynamic-modal/dynamic-modal.component';
import { CommonModule } from '@angular/common';
import { ToastrService } from 'ngx-toastr';
import { EntreposageService } from '../../../../../services/entreposage.service';
import { FluxService } from '../../../../../services/flux.service';
import { InvoiceService } from '../../../../../services/invoice.service';
import { FournisseurService } from '../../../../../services/fournisseur.service'
import { FactureImpressionComponent } from '../shared/facture-impression/facture-impression.component';

@Component({
  selector: 'app-facture-list',
  imports: [
    CommonModule, 
    SmartTableComponent, 
    SmartContainerComponent, 
    DynamicExportButtonComponent, 
    FactureImpressionComponent],
  templateUrl: './facture-list.component.html',
  styleUrl: './facture-list.component.scss'
})
export class FactureListComponent implements OnInit {
  @ViewChild('factureImpression', { static: false }) factureImpression!: FactureImpressionComponent;
  DataDetails: any[] = [];
  tableColumnsDetails: TableColumn[] = [];
  detailsFacture: any[] = [];
  factureCourante: any = null;
  afficherDetails: boolean = false;
  afficherFacture: boolean = false;
  loadingDetails: boolean = false;
  colonnesDetails: TableColumn[] = [
    { name: 'id', displayName: 'ID', sortable: true },
    { name: 'description', displayName: 'Description', sortable: false },
    { name: 'quantite', displayName: 'Quantité', sortable: false },
    { name: 'prix', displayName: 'Prix', sortable: false }

  ];

  pdf!: {
    dateFac?: any,
    code?: any,
    adress?: any,
    mat_fisc?: any,
    nom_client?: any,
    tva?: any,
    commandes?: any[],
    timbre?: any,
    pref?: any[],
    verif?: any[],
    HT?: any,
    TTC?: any,
    TVA?: any,
    decEnLettres?: any,
    foot?: any,
    montantEnLettres?: any,
    prix?: any
  };



  listFactures: any[] = [];
  loading: boolean = false;
  pageSize: number = 10;
  totalRows: number = 0;

  configTable: TableConfig = {
    pageSize: this.pageSize,
    pageSizeOptions: [5, 10, 25, 50],
    showFilter: true
  };

  tableColumns: TableColumn[] = [
    { name: 'code', displayName: 'Code', sortable: true },
    { name: 'invoice_date', displayName: 'Invoice Date', sortable: true, dataType: 'date' },
    { name: 'tot_facture', displayName: 'Total Facture', sortable: true },
    { name: 'tot_factureTTC', displayName: 'Total Facture TTC', sortable: true },
    { name: 'nom_fournisseur', displayName: 'Nom Fournisseur', sortable: true },
    { name: 'status', displayName: 'Status', sortable: true },
    { name: 'validation', displayName: 'Validation', sortable: true },
    { name: 'type', displayName: 'Type', sortable: true }
  ]

  tableActions: ActionButton[] = [
    {
      icon: 'cil-list',
      color: 'info',
      callback: (row) => this.showList(row)
    },
    {
      icon: 'cil-file',
      label: '',
      color: 'secondary',
      callback: (row) => this.showFacture(row)
    }
  ]

  element: HTMLElement | null = null;

  constructor(
    private factureService: FactureService,
    private entreposageService: EntreposageService,
    private fluxService: FluxService,
    private invoiceService: InvoiceService,

    private fournisseurService: FournisseurService,
    private toastr: ToastrService) {

  }

  ngOnInit(): void {
    this.loadFactures();
    this.initializePDF();
  }
  initializePDF(){
    this.pdf = {
      dateFac: null,
      code: null,
      adress: null,
      mat_fisc: null,
      nom_client: null,
      tva: null,
      commandes: [],
      timbre: null,
      pref: [],
      verif: [],
      HT: null,
      TTC: null,
      TVA: null,
      decEnLettres: null,
      foot: null,
      montantEnLettres: null,
      prix: null
    };
  }



  private loadFactures() {
    this.loading = true;
    this.factureService.getAllFactureAllPages().subscribe({
      next: (response: any) => {
        this.listFactures = response;
        this.listFactures.forEach((facture: any) => {
          facture.validation ? facture.validation = 'Validée' : facture.validation = 'En attente de validation';
        });
        //this.totalRows = response.total;

      },
      error: (error) => {
        this.toastr.error('Error loading factures');
      },
      complete: () => {
        this.loading = false;
      }
    });
  }

  scrollToDetails() {
    let element = document.getElementById('smart-container-details');
    element?.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    });
  }

  async showList(row: any): Promise<void> {

    this.loadingDetails = true;
    this.afficherFacture = false;
    this.afficherDetails = true;
    this.factureCourante = row;

    if (row.type == 'transport')
      await this.handleTransportDetails(row);
    else if (row.type == 'Entreposage')
      await this.handleEntreposageDetails(row);
    else if (row.type == 'flux')
      await this.handleFluxDetails(row);
    else
      this.toastr.error('Type de facture non reconnu');

    setTimeout(() => {
      this.scrollToDetails();
    }, 150);
  }
  handleTransportDetails(row: any): Promise<void> {

    this.tableColumnsDetails = [
      { name: 'id', displayName: 'ID', sortable: true },
      { name: 'date_voyage', displayName: 'Date du voyage', sortable: true },
      { name: 'nom_depart', displayName: 'Départ', sortable: true },
      { name: 'nom_arrivee', displayName: 'Arrivée', sortable: true },
      { name: 'emplacement', displayName: 'Emplacement', sortable: true },
      { name: 'date_voyage', displayName: 'Date Voyage', sortable: true },
      { name: 'kilometrage', displayName: 'Kilométrage', sortable: true },
      { name: 'type_ligne', displayName: 'Type de ligne', sortable: true },
      { name: 'quantite', displayName: 'Quantité', sortable: true },
      { name: 'prix_tot', displayName: 'Prix total', sortable: true }
    ];

    this.loadingDetails = true;
    return new Promise((resolve) => {

      this.factureService.getFacture(row.id_facture).subscribe({
        next: (response: any) => {
          response.forEach((element: any) => {
            element.volume = element.volume ? element.volume + 'M³' : element.quantite + 'pcs';
          });
          this.DataDetails = response;
          console.log('DataDetails', this.DataDetails)
          resolve();
        },
        error: (error) => {
          console.error('Error loading details:', error);
          resolve();
        },
        complete: () => {
          this.loadingDetails = false;
        }
      });
    });
  }

  handleEntreposageDetails(row: any): Promise<void> {
    this.tableColumnsDetails = [
      { name: 'id', displayName: 'ID', sortable: true },
      { name: 'date', displayName: 'Date', sortable: true },
      { name: 'nom_depot', displayName: 'Nom Dépot', sortable: false },
      { name: 'qte', displayName: 'Quantité', sortable: false },
      { name: 'prix_tot', displayName: 'Prix Totale', sortable: false }
    ]
      ;
    this.loadingDetails = true;
    return new Promise((resolve) => {
      this.entreposageService.getEntreposageByIdfacture(row.id_facture).subscribe({
        next: (response: any) => {
          this.DataDetails = response;
          resolve();
        },
        error: (error) => {
          console.error('Error loading details:', error);
          resolve();
        },
        complete: () => {
          this.loadingDetails = false;
        }
      });
    });
  }

  handleFluxDetails(row: any): Promise<void> {
    this.tableColumnsDetails = [
      { name: 'id', displayName: 'ID', sortable: true },
      { name: 'date', displayName: 'Date', sortable: true },
      { name: 'nom_depot', displayName: 'Nom Dépot', sortable: false },
      { name: 'qte', displayName: 'Quantité', sortable: false },
      { name: 'prix_tot', displayName: 'Prix Totale', sortable: false }
    ]
      ;
    this.loadingDetails = true;
    return new Promise((resolve) => {
      this.fluxService.getFluxByidFacture(row.id_facture).subscribe({
        next: (response: any) => {
          this.DataDetails = response;
          resolve();
        },
        error: (error) => {
          console.error('Error loading details:', error);
          resolve();
        },
        complete: () => {
          this.loadingDetails = false;
        }
      });
    });
  }
  showFacture(row: any) {
    if (this.factureImpression) {
      this.factureImpression.source = row;
      console.log('source Facture ',this.factureImpression.source);
      this.afficherDetails = false;
      this.afficherFacture = true;
      setTimeout(() => this.scrollToDetails(), 150);
    } else {
      setTimeout(() => this.showFacture(row), 100);
    }
  }

  

}
