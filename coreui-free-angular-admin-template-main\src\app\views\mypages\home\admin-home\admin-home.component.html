<div class="container-fluid">
  <!-- Loading Indicator -->
  <div *ngIf="isLoading" class="d-flex justify-content-center align-items-center" style="height: 200px;">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Welcome Section -->
  <div *ngIf="!isLoading && userDetail" class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-body text-center">
          <h2 class="card-title">Bienvenue {{ userDetail.nom_utilisateur || userDetail.prenom + ' ' + userDetail.nom }} chez ZEN LOGISTIC</h2>
          <p class="card-text text-muted">Espace Administrateur</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Dashboard Statistics -->
  <div *ngIf="!isLoading" class="row mb-4">
    <div class="col-md-3 col-sm-6 mb-3">
      <div class="card border-dark cursor-pointer" (click)="redirectToPage('listcommande', notReserved)">
        <div class="card-body text-center">
          <h5 class="card-title">Lignes Pas encore Réservées</h5>
          <div class="display-6 text-dark">
            {{ notReserved }} <i class="fas fa-bookmark"></i>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-3 col-sm-6 mb-3">
      <div class="card border-warning cursor-pointer" (click)="redirectToPage('expedition', notExpedied)">
        <div class="card-body text-center">
          <h5 class="card-title">Lignes Pas encore Expédiées</h5>
          <div class="display-6 text-warning">
            {{ notExpedied }} <i class="fas fa-sign-out-alt"></i>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-3 col-sm-6 mb-3">
      <div class="card border-primary cursor-pointer" (click)="redirectToPage('comm-a-livre', notDelivred)">
        <div class="card-body text-center">
          <h5 class="card-title">Lignes Pas encore Livrées</h5>
          <div class="display-6 text-primary">
            {{ notDelivred }} <i class="fas fa-truck"></i>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-3 col-sm-6 mb-3">
      <div class="card border-success cursor-pointer" (click)="redirectToPage('facture', notInvoiced)">
        <div class="card-body text-center">
          <h5 class="card-title">Lignes Pas encore Facturées</h5>
          <div class="display-6 text-success">
            {{ notInvoiced }} <i class="fas fa-file-invoice"></i>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Livraison Section -->
  <div *ngIf="!isLoading && productivity.length > 0" class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Livraison</h3>
        </div>
        <div class="card-body">
          <div class="row" *ngFor="let item of productivity">
            <div class="col-md-4 text-center mb-3">
              <div class="productivity-circle border-success">
                <div class="productivity-counter" title="Livraison À l'heure">
                  {{
                    item.nombre_total_commandes_Livre !== null && item.nombre_total_commandes_Livre !== 0 ?
                    ((item.nombre_livraison_a_lheure / item.nombre_total_commandes_Livre) * 100).toFixed(2) :
                    0.0
                  }}%
                </div>
              </div>
              <div class="mt-2"><strong>Moyenne Jours:</strong></div>
              <div>{{ item.moyenne_jours_livraison || 0 }}</div>
            </div>
            <div class="col-md-4 text-center mb-3">
              <div class="productivity-circle border-warning">
                <div class="productivity-counter" title="Expédition À l'heure">
                  {{
                    item.nombre_total_commandes_Exp !== null && item.nombre_total_commandes_Exp !== 0 ?
                    ((item.nombre_expedition_a_lheure / item.nombre_total_commandes_Exp) * 100).toFixed(2) :
                    0.0
                  }}%
                </div>
              </div>
              <div class="mt-2"><strong>Moyenne Jours:</strong></div>
              <div>{{ item.moyenne_jours_expedition || 0 }}</div>
            </div>
            <div class="col-md-4 text-center mb-3">
              <div class="productivity-circle border-primary">
                <div class="productivity-counter" title="Réservation À l'heure">
                  {{
                    item.nombre_total_commandes_Res !== null && item.nombre_total_commandes_Res !== 0 ?
                    ((item.nombre_reservation_a_lheure / item.nombre_total_commandes_Res) * 100).toFixed(2) :
                    0.0
                  }}%
                </div>
              </div>
              <div class="mt-2"><strong>Moyenne Jours:</strong></div>
              <div>{{ item.moyenne_jours_reservation || 0 }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Volume Details -->
  <div *ngIf="!isLoading" class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Détails Des Voyages</h3>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <strong>Volume Moyen : {{ moyenneVolume || 0 }} m³</strong>
            </div>
            <div class="col-md-6">
              <strong>Nombre des Voyages : {{ nbrVoyage || 0 }}</strong>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Delays Section -->
  <div *ngIf="!isLoading" class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Délais de Traitement</h3>
        </div>
        <div class="card-body">
          <div *ngIf="retard.length === 0 && !isLoading" class="text-center text-muted py-4">
            <i class="fas fa-info-circle fa-2x mb-3"></i>
            <p>Aucune donnée de délai disponible pour le moment.</p>
          </div>

          <div *ngFor="let item of retard; trackBy: trackByIndex" class="delay-flow-container mb-4 p-3 border rounded">
            <div class="row align-items-center">
              <!-- Reservation Stage -->
              <div class="col-lg-2 col-md-3 col-sm-6 text-center mb-3">
                <div class="delay-stage">
                  <div class="delay-icon">
                    <i class="fas fa-clock"
                       [ngClass]="{
                         'text-success': !item.retard_moyen_reservation.includes('J'),
                         'text-warning': item.retard_moyen_reservation.includes('J') && convertToSeconds(item.retard_moyen_reservation) < (1.5 * 24 * 60 * 60),
                         'text-danger': convertToSeconds(item.retard_moyen_reservation) >= (1.5 * 24 * 60 * 60)
                       }"></i>
                  </div>
                  <h6 class="stage-title"><strong>Cmd Réservée</strong></h6>
                  <div class="delay-time">{{ item.retard_moyen_reservation }}</div>
                </div>
              </div>

              <!-- Arrow 1 -->
              <div class="col-lg-1 col-md-1 col-sm-12 text-center mb-3">
                <i class="fas fa-arrow-right fa-lg text-primary d-none d-sm-inline"></i>
                <i class="fas fa-arrow-down fa-lg text-primary d-sm-none"></i>
              </div>

              <!-- Expedition Stage -->
              <div class="col-lg-2 col-md-3 col-sm-6 text-center mb-3">
                <div class="delay-stage">
                  <div class="delay-icon">
                    <i class="fas fa-clock"
                       [ngClass]="{
                         'text-success': !item.retard_moyen_expedition.includes('J'),
                         'text-warning': item.retard_moyen_expedition.includes('J') && convertToSeconds(item.retard_moyen_expedition) < (1.5 * 24 * 60 * 60),
                         'text-danger': convertToSeconds(item.retard_moyen_expedition) >= (1.5 * 24 * 60 * 60)
                       }"></i>
                  </div>
                  <h6 class="stage-title"><strong>Cmd Expédiée</strong></h6>
                  <div class="delay-time">{{ item.retard_moyen_expedition }}</div>
                </div>
              </div>

              <!-- Arrow 2 -->
              <div class="col-lg-1 col-md-1 col-sm-12 text-center mb-3">
                <i class="fas fa-arrow-right fa-lg text-primary d-none d-sm-inline"></i>
                <i class="fas fa-arrow-down fa-lg text-primary d-sm-none"></i>
              </div>

              <!-- Delivery Stage -->
              <div class="col-lg-2 col-md-3 col-sm-6 text-center mb-3">
                <div class="delay-stage">
                  <div class="delay-icon">
                    <i class="fas fa-clock"
                       [ngClass]="{
                         'text-success': !item.retard_moyen_livraison.includes('J'),
                         'text-warning': item.retard_moyen_livraison.includes('J') && convertToSeconds(item.retard_moyen_livraison) < (1.5 * 24 * 60 * 60),
                         'text-danger': convertToSeconds(item.retard_moyen_livraison) >= (1.5 * 24 * 60 * 60)
                       }"></i>
                  </div>
                  <h6 class="stage-title"><strong>Cmd Livrée</strong></h6>
                  <div class="delay-time">{{ item.retard_moyen_livraison }}</div>
                </div>
              </div>

              <!-- Summary Stats -->
              <div class="col-lg-4 col-md-12 text-center mb-3">
                <div class="delay-summary p-3 bg-light rounded">
                  <h6 class="mb-2"><strong>Résumé des Délais</strong></h6>
                  <div class="row">
                    <div class="col-4">
                      <small class="text-muted">Total</small>
                      <div class="fw-bold">{{ getTotalDelay(item) }}</div>
                    </div>
                    <div class="col-4">
                      <small class="text-muted">Statut</small>
                      <div class="fw-bold" [ngClass]="getOverallDelayStatus(item)">
                        {{ getDelayStatusText(item) }}
                      </div>
                    </div>
                    <div class="col-4">
                      <small class="text-muted">Performance</small>
                      <div class="fw-bold">{{ getPerformanceScore(item) }}%</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters Section -->
  <div *ngIf="!isLoading" class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Filtres et Analyses</h3>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-3 mb-3">
              <label for="dateDebut" class="form-label">Date Début</label>
              <input type="date" id="dateDebut" class="form-control" [(ngModel)]="dateDebut" (change)="applyFilters()">
            </div>
            <div class="col-md-3 mb-3">
              <label for="dateFin" class="form-label">Date Fin</label>
              <input type="date" id="dateFin" class="form-control" [(ngModel)]="dateFin" (change)="applyFilters()">
            </div>
            <div class="col-md-3 mb-3">
              <label for="selectedDestination" class="form-label">Destination</label>
              <select id="selectedDestination" class="form-select" [(ngModel)]="selectedDestination" (change)="loadDestinationMetrics()">
                <option value="">Sélectionner une destination</option>
                <option *ngFor="let destination of destinationList" [value]="destination.id">
                  {{ destination.nom_locale }}
                </option>
              </select>
            </div>
            <div class="col-md-3 mb-3">
              <label for="selectedConducteur" class="form-label">Conducteur</label>
              <select id="selectedConducteur" class="form-select" [(ngModel)]="selectedConducteur" (change)="loadConductorMetrics()">
                <option value="">Sélectionner un conducteur</option>
                <option *ngFor="let conducteur of conducteurList" [value]="conducteur.id">
                  {{ conducteur.prenom }} {{ conducteur.nom }}
                </option>
              </select>
            </div>
          </div>

          <!-- Metrics Display -->
          <div *ngIf="selectedDestination || selectedConducteur" class="row mt-4">
            <div class="col-md-6" *ngIf="selectedDestination">
              <div class="card border-info">
                <div class="card-body">
                  <h5 class="card-title">Métriques Destination: {{ nomLocale }}</h5>
                  <p><strong>Volume Total:</strong> {{ destinationVolume }} m³</p>
                  <p><strong>Nombre de Voyages:</strong> {{ destinationVoyage }}</p>
                </div>
              </div>
            </div>
            <div class="col-md-6" *ngIf="selectedConducteur">
              <div class="card border-warning">
                <div class="card-body">
                  <h5 class="card-title">Métriques Conducteur: {{ conducteurFullName }}</h5>
                  <p><strong>Volume Total:</strong> {{ conducteurVolume }} m³</p>
                  <p><strong>Nombre de Voyages:</strong> {{ conducteurVoyage }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Logout Button -->
  <div class="row">
    <div class="col-12 text-end">
      <button type="button" class="btn btn-danger" (click)="deconnexion()">
        <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
      </button>
    </div>
  </div>
</div>
