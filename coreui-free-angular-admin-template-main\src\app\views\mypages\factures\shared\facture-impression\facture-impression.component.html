<app-smart-container 
        [title]="prefacture ? 'Prefacture PDF' : 'Facture PDF'">
        <div slot="actions">
                <button class="btn btn-primary" (click)="imprimerFacture()">Imprimer la facture</button>
        </div>

        <div slot="content">

            <app-spinner *ngIf="loading"></app-spinner>
            <!-- Container PDF facture -->
            <div *ngIf="!loading" class="pdf-container"
                style="margin: 0; padding: 0; font-family: Arial, sans-serif;">
                <div id="invoice-pdf"
                    style="max-width: 800px; margin: 0 auto; padding: 20px; border: 1px solid #eee; box-shadow: 0 0 10px rgba(0,0,0,0.1); display: flex; flex-direction: column; min-height: 100%;">
                    <!-- Header Section -->
                    <div style="text-align: center; margin-bottom: 20px;">
                        <img src="assets/img/logo.png" alt="Logo" class="logo" style="height: 80px;" />
                    </div>

                    <div
                        style="display: flex; justify-content: space-between; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px;">
                        <!-- Company Info -->
                        <div style="width: 40%;">
                            <h2 style="margin: 0 0 15px 0; color: #333;">STE ZEN LOGISTIC</h2>
                            <p
                                style="margin: 3px 0; font-size: 12px;border: 1px solid #ddd; padding: 10px; background-color: #f9f9f9;">
                                <strong>MF:</strong> 1557718/G/A/M/000<br>
                                <strong>ADRESSE:</strong> Route Gremda km2,5 SFAX<br>
                                <strong>TÉLÉPHONE:</strong> 70 147 680<br>
                                <strong>FAX:</strong> 74 870 248
                            </p>
                        </div>

                        <!-- Invoice Title and Client Info -->
                        <div style="width: 40%;">
                            <h2 style="text-align: center; margin: 0 0 15px 0; color: #333; font-size: 24px;">
                                FACTURE</h2>

                            <div
                                style="margin: 3px 0;border: 1px solid #ddd; padding: 10px; background-color: #f9f9f9;">
                                <p style="margin: 5px 0; font-size: 13px;"><strong>Client:</strong> {{
                                    pdf.nom_client }}</p>
                                <p style="margin: 5px 0; font-size: 13px;"><strong>Adresse:</strong> {{ pdf.adress
                                    }}</p>
                                <p style="margin: 5px 0; font-size: 13px;"><strong>M.F:</strong> {{pdf.mat_fisc}}
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Invoice Details -->
                    <div *ngIf="prefacture" style="text-align: center; margin-bottom: 25px;">
                        <h3 style="margin: 0; font-size: 18px; color: #333;">Prefacture</h3>
                        
                    </div>
                    <div *ngIf="!prefacture" style="text-align: center; margin-bottom: 25px;">
                        <h3 style="margin: 0; font-size: 18px; color: #333;">Facture N°: {{pdf.code}}</h3>
                        <p style="margin: 5px 0; font-size: 14px; color: #555;">
                            <strong>DATE:</strong> {{ pdf.dateFac | date: 'dd/MM/yyyy' }}
                        </p>
                    </div>

                    <!-- Items Table -->
                    <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px; font-size: 13px;">
                        <thead>
                            <tr style="background-color: #f2f2f2;">
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">Désignation
                                </th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">Unité</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">Quantité</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">TVA</th>
                                <th style="border: 1px solid #ddd; padding: 10px; text-align: right;">Total HT</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let item of pdf.pref" style="border-bottom: 1px solid #ddd;">
                                <td *ngIf="item.prix>0" style="border: 1px solid #ddd; padding: 8px; text-align: left;">
                                    {{
                                    item.designation }}</td>
                                <td *ngIf="item.prix>0"
                                    style="border: 1px solid #ddd; padding: 8px; text-align: center;">{{ item.unite
                                    }}</td>
                                <td *ngIf="item.prix>0"
                                    style="border: 1px solid #ddd; padding: 8px; text-align: center;">{{
                                    (item.volume | number:'1.2-2')?.toString()?.replace(',', ' ') }}</td>
                                <td *ngIf="item.prix>0"
                                    style="border: 1px solid #ddd; padding: 8px; text-align: center;">{{ item.tva ||
                                    '19' }}%</td>
                                <td *ngIf="item.prix>0"
                                    style="border: 1px solid #ddd; padding: 8px; text-align: right;">{{ (item.prix |
                                    number:'1.3-3')?.toString()?.replace(',', ' ') }}</td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- Totals -->
                    <div style="display: flex; justify-content: space-between; margin-top: 30px;">
                        <!-- Amount in Words -->
                        <div style="width: 60%; padding-right: 20px;">
                            <p style="font-weight: bold; margin-bottom: 5px;">Arrêtée la présente Facture à la somme
                                De :</p>
                            <p style="border-top: 1px solid #ddd; padding-top: 5px; font-style: italic;">
                                {{pdf.montantEnLettres || ' '}}
                            </p>
                        </div>

                        <!-- Totals -->
                        <div style="width: 35%;">
                            <table style="width: 100%; border-collapse: collapse; font-size: 13px;">
                                <tr>
                                    <th
                                        style="border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f2f2f2;">
                                        Total HT :</th>
                                    <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">{{
                                        (pdf.foot[0]?.HT | number:'1.3-3')?.toString()?.replace(',', ' ') }}</td>
                                </tr>
                                <tr>
                                    <th
                                        style="border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f2f2f2;">
                                        Total TVA :</th>
                                    <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">{{
                                        (pdf.foot[0]?.TVA | number:'1.3-3')?.toString()?.replace(',', ' ') }}</td>
                                </tr>
                                <tr>
                                    <th
                                        style="border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f2f2f2;">
                                        Timbre :</th>
                                    <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">{{
                                        pdf.foot[0]?.timbre?.toString()?.replace('.', ',') }}</td>
                                </tr>
                                <tr>
                                    <th class="ttc"
                                        style="border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #333; color: white;">
                                        Total TTC :</th>
                                    <td
                                        style="border: 1px solid #ddd; padding: 8px; text-align: right; font-weight: bold;">
                                        {{ (pdf.foot[0]?.TTC | number:'1.3-3')?.toString()?.replace(',', ' ') }}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Company Stamp/Signature Area -->
                    <!-- <div style="margin-top: 50px; text-align: right; border-top: 1px solid #ddd; padding-top: 20px;">
                        <p style="font-style: italic; color: #555;">Le responsable</p>
                        <div style="height: 50px; margin-top: 30px;"></div>
                        <p style="border-top: 1px solid #333; display: inline-block; padding-top: 5px; width: 200px; text-align: center;">Signature & Cachet</p>
                    </div> -->
                    <div
                        style="margin-top: 40px; padding-top: 20px; border-top: 2px solid #f0f0f0; text-align: center; color: #777; font-size: 12px;">
                        <p *ngIf="!prefacture">Merci pour votre confiance. Paiement attendu sous 30 jours.</p>
                        <p>STE ZEN LOGISTIC - Route Gremda km2,5 SFAX - Tél: 70 147 680</p>
                    </div>
                </div>
            </div>
        </div>
    </app-smart-container>