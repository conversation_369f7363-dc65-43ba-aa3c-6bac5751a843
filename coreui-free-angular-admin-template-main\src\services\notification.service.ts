import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable, NgZone } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable } from 'rxjs';

// Notification interface
export interface Notification {
  id: number;
  id_user: string;
  message: string;
  url?: string;
  readed?: boolean;
  read?: boolean;
  created_at?: string;
}

const httpOptions = {
  headers: new HttpHeaders({
    'Content-Type': 'application/json',
    'Authorization': 'Basic ' + btoa('med:123456')
  })
};

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private apiURL = environment.apiURL;
  private eventSource: EventSource | undefined;
  public handleNewNotification: (notification: Notification) => void = () => {};

  constructor(private http: HttpClient, private zone: NgZone) { }

  /**
   * Get notifications by user ID (zen-logistic compatible method)
   * @param userId - User ID
   * @returns Promise with notifications list
   */
  getByIdUser(userId: string): Promise<Notification[]> {
    const url = `${this.apiURL}notification/${userId}`;
    return this.http.get<Notification[]>(url, {
      headers: new HttpHeaders({
        'content-type': 'application/json',
        Authorization: 'Basic ' + btoa('med:123456')
      })
    }).toPromise() as Promise<Notification[]>;
  }

  /**
   * Get notifications by user (Observable version)
   * @param userId - User ID
   * @returns Observable with notifications list
   */
  getNotificationsByUser(userId: string): Observable<Notification[]> {
    return this.http.get<Notification[]>(`${this.apiURL}notifications/${userId}`, httpOptions);
  }

  /**
   * Update notification status (zen-logistic compatible method)
   * @param ids - Array of notification IDs
   * @returns Promise with update result
   */
  updateNotificationStatus(ids: number[]): Promise<any> {
    const url = `${this.apiURL}notification/read`;
    return this.http.put(url, { ids: ids }, {
      headers: new HttpHeaders({
        'content-type': 'application/json',
        Authorization: 'Basic ' + btoa('med:123456')
      })
    }).toPromise();
  }

  /**
   * Mark notification as read (single notification)
   * @param notificationId - Notification ID
   * @returns Observable with update result
   */
  markAsRead(notificationId: string): Observable<any> {
    return this.http.put<any>(`${this.apiURL}notifications/read/${notificationId}`, null, httpOptions);
  }

  /**
   * Create notification
   * @param notificationData - Notification data
   * @returns Observable with creation result
   */
  createNotification(notificationData: any): Observable<any> {
    return this.http.post<any>(`${this.apiURL}notifications`, notificationData, httpOptions);
  }

  /**
   * Delete notification
   * @param notificationId - Notification ID
   * @returns Observable with deletion result
   */
  deleteNotification(notificationId: string): Observable<any> {
    return this.http.delete<any>(`${this.apiURL}notifications/${notificationId}`, httpOptions);
  }

  SendSmsNotification(data : any): Observable<any> {
    const endpoint = `${this.apiURL}ptchargement/sendSms`;
    return this.http.post<any>(endpoint, data, httpOptions);
  }

  SendMailNotification(): Observable<any> {
    const endpoint = `${this.apiURL}commande/infoAdmin`;
    return this.http.post<any>(endpoint, null, httpOptions);
  }

  sendReservationMail(data?: any): Observable<any> {
    return this.http.post<any>(`${this.apiURL}mailReservation`, data, httpOptions);
  }

  sendExpeditionMail(data?: any): Observable<any> {
    return this.http.post<any>(`${this.apiURL}mailExpedition`, data, httpOptions);
  }

  
  sendDeliveryMail(data?: any): Observable<any> {
    return this.http.post<any>(`${this.apiURL}mailLivraison`, data, httpOptions);
  }


  
  sendMessage(data?: any): Observable<any> {
    return this.http.post<any>(`${this.apiURL}messages`, data, httpOptions);
  }

  /**
   * Send cancellation mail
   * @param data - Cancellation data
   * @returns Promise with sending result
   */
  sendCancellationMail(data: any): Observable<any> {
    return this.http.post<any>(`${this.apiURL}mailAnnulation`, data, httpOptions);
  }

  /**
   * Send reservation departure update mail
   * @param data - Update data
   * @returns Promise with sending result
   */
  sendReservationDepartureUpdateMail(data?: any): Observable<any> {
    return this.http.post<any>(`${this.apiURL}mailUpdateReservationDepart`, data, httpOptions);
  }

  /**
   * Send reservation arrival update mail
   * @param data - Update data
   * @returns Promise with sending result
   */
  sendReservationArrivalUpdateMail(data?: any): Observable<any> {
    return this.http.post<any>(`${this.apiURL}mailUpdateReservationArrivee`, data, httpOptions);
  }

  /**
   * Send mails from frontend
   * @param data - Mail data
   * @returns Promise with sending result
   */
  sendMailsFromFrontend(data: any): Observable<any> {
    return this.http.post<any>(`${this.apiURL}sendMailsFromFront`, data, httpOptions);
  }

  /**
   * Connect to Server-Sent Events for real-time notifications
   */
  connectToSSE(): void {
    fetch(`${this.apiURL}notifications/events`, {
      method: 'GET',
      headers: {
        'Authorization': 'Basic ' + btoa('med:123456')
      }
    })
    .then(response => {
      if (response.ok) {
        this.eventSource = new EventSource(`${this.apiURL}notifications/events`);

        this.eventSource.onmessage = (event: MessageEvent) => {
          this.zone.run(() => {
            const notification = JSON.parse(event.data);
            this.handleNewNotification(notification);
          });
        };

        this.eventSource.onerror = (error) => {
          console.error('SSE error: ', error);
        };
      } else {
        throw new Error('Authentication failed');
      }
    })
    .catch(error => {
      console.error('Fetch error:', error);
    });
  }

  /**
   * Disconnect from Server-Sent Events
   */
  disconnectSSE(): void {
    if (this.eventSource) {
      this.eventSource.close();
    }
  }

  /**
   * Count unread messages
   * @param id - User ID
   * @returns Observable with count
   */
  countMessageNotReaded(id: string): Observable<any> {
    return this.http.get<any>(`${this.apiURL}countMessageNotReaded/${id}`, httpOptions);
  }

  /**
   * Update messages as read
   * @param id - User ID
   * @returns Observable with update result
   */
  updateMessagesAsRead(id: string): Observable<any> {
    return this.http.get<any>(`${this.apiURL}updateMessagesAsRead/${id}`, httpOptions);
  }
}
