import { Component, OnInit } from '@angular/core';
import { DestinationService } from '../../../../../../services/destination.service';
import { ToastrService } from 'ngx-toastr';
import { ActionButton, TableColumn, TableConfig } from 'src/shared/models/table.models';
import { ConfirmationDialogService } from 'src/services/confirmation-dialog.service';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';
import { SmartButtonComponent } from 'src/shared/components/smart-button/smart-button.component';
import { FormButton, FormConfig } from 'src/shared/models/form.models';
import { FormModalComponent } from 'src/shared/components/form-modal/form-modal.component';
@Component({
  selector: 'app-depot-facturation',
  standalone: true,
  imports: [SmartContainerComponent, SmartTableComponent, SmartButtonComponent,FormModalComponent],
  templateUrl: './depot-facturation.component.html',
  styleUrl: './depot-facturation.component.scss'
})
export class DepotFacturationComponent implements OnInit {
  showModal: boolean = false;

  loading: boolean = false;
  depotList: any[] = [];

  tableConfig: TableConfig = {
    emptyMessage: 'Aucun depot trouvé',
    pageSize: 5,
  }

  tableColumns: TableColumn[] = [
    { name: 'name', displayName: 'Nom', sortable: true, filterable: true },
    { name: 'code', displayName: 'Code', sortable: true, filterable: true },
  ];
  tableActions: ActionButton[] = [
    {
      icon: 'cil-trash',
      tooltip: 'Supprimer',
      color: 'danger',
      callback: (row: any) => this.deleteDepot(row)
    }
  ];
  formConfig: FormConfig = {
    title: 'Ajouter Depot',
    fields: [
      {
        name: 'name',
        label: 'Nom',
        type: 'text',
        required: true
      },
      {
        name: 'code',
        label: 'Code',
        type: 'text',
        required: true
      }
    ],
    buttons: [
      {
        label: 'Annuler',
        color: 'secondary',
        onClick: () => this.closeModal()
      },
      {
        label: 'Ajouter',
        color: 'primary',
        onClick: (formData: any) => this.addDepot(formData)
      }
    ]
  }

  showFormModal() {
    this.showModal = true;
  }

  addDepot(formData: any) {
    let newDepot = {
      name: formData.name,
      code: formData.code,
      depot:true
    }
    this.destinationService.addWarehouse(newDepot).subscribe({
      next: () => {
        this.toastr.success('Depot ajouter avec succès');
        this.closeModal();
        this.loadDepot();
      },
      error: (err) => {
        this.toastr.error("Nom et code sont obligatoires");
      }
    })
  }

  closeModal(): void {
    this.showModal = false;
  }


  constructor(private destinationService: DestinationService,
    private toastr: ToastrService,
    private confirmationDialogService: ConfirmationDialogService) { }

  ngOnInit(): void {
    this.loadDepot();
  }
  loadDepot() {
    this.loading = true;
    this.destinationService.findAllWarehouses().subscribe({
      next: (res) => {
        this.depotList = res;
        console.log(this.depotList);
        this.loading = false;
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        this.loading = false;
      }
    })
  }

  deleteDepot(row: any) {
    this.confirmationDialogService.confirmDelete(`Voulez-vous vraiment supprimer ce depot ${row.name} ?`).then((res) => {
      if (res) {
        this.destinationService.disableWarehouse(row.id).subscribe({
          next: () => {
            this.toastr.success('Depot supprimé avec succès');
            this.loadDepot();
          },
          error: (err) => {
            this.toastr.error(err.error.message);
          }
        })
      }
    })
  }
}
