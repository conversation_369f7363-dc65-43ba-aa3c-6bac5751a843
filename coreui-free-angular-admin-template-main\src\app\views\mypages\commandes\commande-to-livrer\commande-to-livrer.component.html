<app-smart-container>

    <div slot="content">


        <app-smart-container [title]="'Details Ligne'">
            <div slot="actions">
                <app-smart-button
                    [label]="'Panne'"
                    [icon]="'cilBellExclamation'"
                    [disabled]="disableButton"
                    [color]="'danger'"
                    (onClick)="EnPanne()">
                </app-smart-button>
                <app-smart-button 
                    [label]="'Livrer'" 
                    [icon]="'cilPlus'" 
                    [disabled]="disableButton" 
                    [color]="'primary'"
                    (onClick)="Livrer()">
                </app-smart-button>
            </div>
            <div slot="content">
                <app-dynamic-form [config]="formConfig" 
                [initialData]="initialData">
                </app-dynamic-form>
                <div *ngIf="showDetailsTable">
                    <app-smart-table 
                        [data]="sourceDetails" 
                        [columns]="tableDetailsColumn" 
                        [config]="tableDetailsConfig"
                        [isLoading]="loadingDetailsTable"
                        [actionButtons]="tableDetailsAction"
                        (selectionChange)="onSelectDetailsChange($event)">
                    </app-smart-table>
                </div>
            </div>
        </app-smart-container>

        <app-smart-table 
            [data]="source" 
            [columns]="tableColumn" 
            [config]="tableConfig"
            (selectionChange)="onSelectChange($event)">
        </app-smart-table>
    </div>
</app-smart-container>