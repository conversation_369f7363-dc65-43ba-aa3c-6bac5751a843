import { Component, OnInit } from '@angular/core';
import { CommandeService } from '../../../../../../services/commande.service';
import { ToastrService } from 'ngx-toastr';
import { SmartContainerComponent } from '../../../../../../shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from '../../../../../../shared/components/smart-table/smart-table.component';
import { SmartButtonComponent } from '../../../../../../shared/components/smart-button/smart-button.component';
import { CommonModule } from '@angular/common';
import { ActionButton, TableColumn } from '../../../../../../shared/models/table.models';
import { FormModalComponent } from '../../../../../../shared/components/form-modal/form-modal.component';
import { initial } from 'lodash-es';
import { FormButton, FormConfig } from '../../../../../../shared/models/form.models';
@Component({
  selector: 'app-moyenne-qte-saison',
  imports: [SmartContainerComponent, SmartTableComponent,FormModalComponent, CommonModule],
  templateUrl: './moyenne-qte-saison.component.html',
  styleUrl: './moyenne-qte-saison.component.scss'
})
export class MoyenneQteSaisonComponent implements OnInit {
  
  loading: boolean = false;
  showModal: boolean = false;
  initialData: any = {};
  saisonList: any[] = [];
  constructor(private commandeService: CommandeService, private toastr: ToastrService) { }

  ngOnInit(): void {
    this.loadSaison();
  }

  tableColumns: TableColumn[] = [
    { name: 'nom', displayName: 'Nom', sortable: true, filterable: true },
    { name: 'value', displayName: 'Valeur', sortable: true, filterable: true },
  ];
  tableActions: ActionButton[] = [
    {
      icon: 'cil-pencil',
      tooltip: 'Modifier',
      color: 'warning',
      callback: (row: any) => this.openModal(row)
    }
  ];

  formConfig: FormConfig = {
    title: 'Ajouter Saison',
    fields: [
      {
        name: 'nom',
        label: 'Nom',
        type: 'text',
        required: true
      },
      {
        name: 'value',
        label: 'Valeur',
        type: 'number',
        required: true
      }
    ],
    buttons: [
      {
        label: 'Annuler',
        color: 'secondary',
        onClick: () => this.closeModal()
      },
      {
        label: 'Modifier',
        color: 'primary',
        onClick: (formData: any) => this.modifySaison(formData)
      }
    ]
  }

  loadSaison() {
    this.loading = true;
    this.commandeService.getAllSaison().subscribe({
      next: (res) => {
        this.saisonList = res;
        console.log(this.saisonList);
        this.loading = false;
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        this.loading = false;
      },
      complete: () => {
        this.loading = false;
      }
    });
  }

  openModal(row: any): void {
    this.showModal = true;
    this.initialData = row;
  }
  closeModal(): void {
    this.showModal = false;
    this.initialData = {};
  }

  modifySaison(formData: any): void {
    this.commandeService.updateQteSaison(this.initialData.id, formData.value).subscribe({
      next: () => {
        this.toastr.success('Saison modifiée avec succès', 'Succès');
        this.closeModal();
        this.loadSaison();
      },
      error: (err) => {
        this.toastr.error(err.error.message, 'Erreur');
      }
    });
  }
}
