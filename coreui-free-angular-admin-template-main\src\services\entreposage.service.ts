import { HttpClient, HttpEvent, HttpHeaders, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
const httpOptions = {
    headers: new HttpHeaders({ 'content-type': 'application/json', Authorization: 'basic ' + btoa('med:123456') })
};

@Injectable({
    providedIn: 'root'
})

export class EntreposageService {
    apiURL = environment.apiURL
    constructor(private http: HttpClient) { }


    getByClientAndDate(data: any): Observable<any> {
        return this.http.post<any>(this.apiURL + `entreposage/getByClientAndDate`, data, httpOptions);
    }
    getEntreposageByIdfacture(id: any): Observable<any> {
        return this.http.get<any>(this.apiURL + `getEntreposageByIdfacture/${id}`, httpOptions);
    }


}