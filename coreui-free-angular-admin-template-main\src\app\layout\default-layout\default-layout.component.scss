:host::ng-deep app-default-header,
:host::ng-deep c-header {
  margin: 0 !important;
  padding: 0 !important;
}

:host::ng-deep {
  font-size: smaller;
  .ng-scrollbar {
    --scrollbar-padding: 1px;
    --scrollbar-size: 5px;
    --scrollbar-thumb-color: var(--cui-gray-500, #aab3c5);
    --scrollbar-thumb-hover-color: var(--cui-gray-400, #cfd4de);
    --scrollbar-hover-size: calc(var(--scrollbar-size) * 1.5);
    --scrollbar-border-radius: 5px;

    // Ensure scrollbar doesn't interfere with navigation
    width: 100%;
    height: 100%;
    display: block;
  }

  .ng-scroll-content {
    display: block !important; // Changed from flex to block
    min-height: 100%;
    width: 100%;
  }

  .ng-scroll-viewport {
    width: 100% !important;
    height: 100% !important;
  }
  .modal,.modal-backdrop {
    z-index: 1050 !important; // Above both header and sidebar
  }
  .modal-backdrop,.modal.fade {
    z-index: 1040 !important; // Just below modal but above everything else
  }

  // Ensure sidebar navigation is properly displayed
  .sidebar-nav,
  c-sidebar-nav,
  .sidebar-navigation {
    display: block !important;
    width: 100%;
    height: auto;
    overflow: visible;
    position: fixed;
    z-index: 1020;
    // Remove any transform/opacity that creates new stacking context
    transform: none !important;
    opacity: 1 !important;

    .nav {
      display: block !important;
      width: 100%;
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .nav-item {
      display: block !important;
      width: 100%;
      position: relative;
    }

    .nav-link {
      display: flex !important;
      align-items: center;
      padding: 0.75rem 1rem;
      color: inherit;
      text-decoration: none;
      border: 0;
      border-radius: 0;
      width: 100%;
      position: relative;
      z-index: 1;
    }

    .nav-group {
      display: block !important;
      width: 100%;
    }

    .nav-dropdown {
      display: block !important;
      width: 100%;
      position: relative;
      z-index: 1;
    }

    .nav-dropdown-items {
      display: block !important;
      width: 100%;
      padding-left: 1rem;
    }

    .nav-title {
      display: block !important;
      padding: 0.75rem 1rem 0.25rem;
      font-size: 0.75rem;
      font-weight: 600;
      color: var(--cui-nav-title-color, #6c757d);
      text-transform: uppercase;
    }
    c-header, .header {
      z-index: 1030;
      position: relative;
    }
    .body {
      z-index: 1;
      position: relative;
    }
  }

  //.sidebar-nav {
  //  scrollbar-color: var(--cui-gray-500, #444) transparent;
  //}
}

// ng-scrollbar css variables
//.cui-scrollbar {
//  --scrollbar-border-radius: 7px;
//  --scrollbar-padding: 1px;
//  --scrollbar-viewport-margin: 0;
//  --scrollbar-track-color: transparent;
//  --scrollbar-wrapper-color: transparent;
//  --scrollbar-thumb-color: rgba(0, 0, 0, 0.2);
//  --scrollbar-thumb-hover-color: var(--scrollbar-thumb-color);
//  --scrollbar-size: 5px;
//  --scrollbar-hover-size: var(--scrollbar-size);
//  --scrollbar-thumb-transition: height ease-out 150ms, width ease-out 150ms;
//  --scrollbar-track-transition: height ease-out 150ms, width ease-out 150ms;
//}



// Theme layout classes
:host::ng-deep {
  .layout-dark {
    --cui-body-bg: #1e1e2f;
    --cui-body-color: #ffffff;
  }

  .layout-transparent {
    --cui-body-bg: transparent;
  }

  // Sidebar size variations
  .sidebar-sm {
    .sidebar {
      width: 200px;
    }
  }

  .sidebar-lg {
    .sidebar {
      width: 300px;
    }
  }
}

// Fix z-index hierarchy to prevent navigation from appearing above header
:host::ng-deep {
  .sidebar {
    z-index: 1020 !important; // Below header but above main content
    position: fixed;

    .sidebar-nav,
    c-sidebar-nav {
      z-index: 1 !important; // Relative to sidebar
      position: relative;
    }
  }

  .header,
  c-header {
    z-index: 1030 !important; // Above sidebar
    position: relative;
  }

  .wrapper {
    z-index: 1 !important; // Below sidebar and header
    position: relative;
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  // Ensure header has proper positioning
  .header,
  c-header {
    position: sticky !important;
    top: 0;
    z-index: 1030 !important;
    width: 100%;
    // Margin handled by wrapper padding in _fixes.scss
    margin-left: 0;
    margin-right: 0;
  }

  // Fix body content area to respect sidebar spacing
  .body {
    flex: 1;
    width: 100%;
  }
}


