import { Component } from '@angular/core';
import { AffectationClientToChefComponent } from '../affectation-client-to-chef/affectation-client-to-chef.component';
import { AffectationDestToUserComponent } from '../affectation-dest-to-user/affectation-dest-to-user.component';

@Component({
  selector: 'app-wrapper-affectation',
  standalone: true,
  imports: [AffectationClientToChefComponent, AffectationDestToUserComponent],
  templateUrl: './wrapper-affectation.component.html',
  styleUrl: './wrapper-affectation.component.scss'
})
export class WrapperAffectationComponent {

}
