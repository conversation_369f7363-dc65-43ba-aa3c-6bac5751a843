import { Component } from '@angular/core';
import { TableParentOneComponent } from '../table-parent-one/table-parent-one.component';
import { TestDynamicFormComponent } from '../test-dynamic-form/test-dynamic-form.component';
import { TestSpinnerComponent } from '../test-spinner/test-spinner.component';
import { DataExportComponent } from '../data-export/data-export.component';
import { TestSmartButtonComponent } from '../test-smart-button/test-smart-button.component';

@Component({
  standalone: true,
  selector: 'app-component-test-wrapper',
  template: `
    <app-table-parent-one></app-table-parent-one>
    <app-test-dynamic-form></app-test-dynamic-form>
    <app-test-spinner></app-test-spinner>
    <app-data-export></app-data-export>
    <app-test-smart-button></app-test-smart-button>
    
  `,
  imports: [
    TableParentOneComponent,
    TestDynamicFormComponent,
    TestSpinnerComponent,
    DataExportComponent,
    TestSmartButtonComponent
    
  ]
})
export class ComponentTestWrapperComponent {}

