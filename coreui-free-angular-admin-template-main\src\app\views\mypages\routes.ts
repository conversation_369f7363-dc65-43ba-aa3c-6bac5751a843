import { Routes } from '@angular/router';
import { authGuard } from '../../../guards/auth.guard';
import { roleGuard } from '../../../guards/role.guard';

export const routes: Routes = [
  {
    path: '',
    canActivate: [authGuard],
    data: {
      title: 'My Pages'
    },
    children: [
        {
          path: 'ajouter-commande',
          loadComponent: () => import('./ajouter-commande/ajouter-commande.component').then(m => m.AjouterCommandeComponent),
          data: {
            title: 'Ajouter Commande',
            roles: ['Client', 'Chef Departement', 'GS', 'GE', 'Depot', 'SuperAdmin', 'Administrateur']
          }
        },
        {
            path: 'commandes',
            loadChildren: () => import('./commandes/routes').then((m) => m.routes),
            canActivate: [authGuard, roleGuard],
            data: {
              title: 'Commandes',
              roles: ['Client', 'Chef Departement', 'GS', 'GE', 'Depot', 'SuperAdmin', 'Administrateur']
            }
        },
        {
            path: 'factures',
            loadChildren: () => import('./factures/routes').then((m) => m.routes),
            canActivate: [authGuard, roleGuard],
            data: {
              title: 'Factures',
              roles: ['Facturation', 'SuperAdmin', 'Administrateur']
            }
        },
        {
            path: 'inspection',
            loadChildren: () => import('./inspection/routes').then((m) => m.routes),
            canActivate: [authGuard, roleGuard],
            data: {
              title: 'Inspection',
              roles: ['Inspection', 'SuperAdmin', 'Administrateur']
            }
        },
        {
          path: 'utilisateurs',
          loadChildren: () => import('./utilisateurs/routes').then((m) => m.routes),
          canActivate: [authGuard, roleGuard],
          data: {
            title: 'Utilisateurs',
            roles: ['SuperAdmin', 'Administrateur']
          }
        },
        {
          path:'verification',
          loadComponent: () => import('./list-verification/list-verification.component').then((m) => m.ListVerificationComponent),
          canActivate: [authGuard, roleGuard],
          data: {
            title: 'Verification',
            roles: ['SuperAdmin', 'Administrateur','GS','GE','Client','Chef Departement','Depot','Inspection','FACTURATION','MAGASIN','Conducteur']
          }
        },
        {
          path: 'modele',
          loadChildren: () => import('./modele/routes').then((m) => m.routes),
          canActivate: [authGuard, roleGuard],
          data: {
            title: 'Modèle',
            roles: ['SuperAdmin', 'Administrateur','GS','GE','Client','Chef Departement']
          }
        },
        {
          path: 'monitoring',
          loadChildren: () => import('./monitoring/routes').then((m) => m.routes),
          canActivate: [authGuard, roleGuard],
          data: {
            title: 'Monitoring',
            roles: ['GS', 'GE', 'Client','Chef Departement']
          }
        },
        {
          path: 'camions',
          loadChildren: () => import('./camions/routes').then((m) => m.routes),
          canActivate: [authGuard, roleGuard],
          data: {
            title: 'Camions',
            roles: ['Camions', 'SuperAdmin', 'Administrateur']
          }
        },
        {
          path: 'conducteurs',
          loadChildren: () => import('./conducteurs/routes').then((m) => m.routes),
          canActivate: [authGuard, roleGuard],
          data: {
            title: 'Conducteurs',
            roles: ['Conducteurs', 'SuperAdmin', 'Administrateur']
          }
        },
        {
          path:'parametrage',
          loadChildren: () => import('./parametrage/routes').then((m) => m.routes),
          canActivate: [authGuard, roleGuard],
          data: {
            title: 'Parametrage',
            roles: ['SuperAdmin', 'Administrateur']
          }
        },
        {
          path: 'reclamations',
          loadComponent: () => import('./reclamations/reclamation-list/reclamation-list.component').then((m) => m.ReclamationListComponent),
          canActivate: [authGuard, roleGuard],
          data: {
            title: 'Reclamation',
            roles: ['Reclamation', 'SuperAdmin', 'Administrateur']
          }
        },
        {
          path: 'colisage',
          loadChildren: () => import('./colisage/routes').then((m) => m.routes),
          canActivate: [authGuard, roleGuard],
          data: {
            title: 'Colisage',
            roles: ['SuperAdmin', 'Administrateur']
          }
        }

    ]
  }
];
