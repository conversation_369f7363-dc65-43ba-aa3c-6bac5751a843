<section id="filter">
  <div class="row text-left">
    <div class="col-sm-12">
      <div class="card">
        <div class="card-header" style="display: flex; justify-content: space-between;">
          <h4 class="card-title">Liste des Factures</h4>
          <button *ngIf="selectedFactures.length>0" style="background: black; color: white !important;" (click)="sendToSage()" class="btn btn-primary me-2"><i class="fa fa-paper-plane"> Envoyer Vers SAGE X3</i></button>
        </div>

        <div class="row" style="margin: 20px;">
          <!-- Sélection du client -->
          <div class="col-md-4">
            <select id="client" class="form-control" [(ngModel)]="selectedClient">
              <option selected  value="">Choisir un Client</option>
              <option *ngFor="let client of clientsList" [value]="client.id">{{ client.nom_fournisseur }}</option>
            </select>
          </div>
        
          <!-- Sélection du mois et de l'année -->
          <div class="col-md-4">
            <input type="month" id="monthYear" class="form-control" [(ngModel)]="monthYear">
          </div>
        
          <!-- Bouton Filtrer -->
          <div class="col-md-4 d-flex align-items-end">
            <button style="margin-bottom: 2px; color: white !important;" (click)="loadData(1)" class="btn btn-primary w-100">
              <i class="fa fa-search"></i> Filtrer
            </button>
          </div>
        </div>

        
        <div class="card-content">
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-striped">
                <thead>
                  <tr>
                    <th>N° Facture</th>
                    <th>Date Facture</th>
                    <th>ToT HT</th>
                    <th>ToT TTC</th>
                    <th>Client</th>
                    <th>Statut</th>
                    <th>Validation</th>
                    <th>Type</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let facture of source">
                    <td>{{ facture.code }}</td>
                    <td>{{ facture.invoice_date | date: 'dd/MM/yyyy' }}</td>
                    <td>{{( facture.tot_facture | number:'1.3-3').toString().replace(',', ' ') }}</td>
                    <td>{{ (facture.tot_factureTTC | number:'1.3-3').toString().replace(',', ' ')}}</td>
                    <td>{{ facture.nom_fournisseur }}</td>
                    <td>{{ facture.status}}</td>
                    <td>{{ facture.validation ? facture.validation : 'En attente de validation' }}</td>
                    <td>{{ facture.type }}</td>
                    <td>
                      <button (click)="openModal(facture)" class="btn btn-primary me-2">
                        <i class="fa fa-list iconList"></i>
                      </button>
                    
                      <button (click)="onUserRowSelect(facture, content)" class="btn btn-primary eyebutton">
                        <i class="fa fa-eye"></i>
                      </button>
                      <input type="checkbox" [(ngModel)]="facture.isChecked" (change)="updateSelectedFactures()"  *ngIf="!facture.to_sage && isFacturation" [checked]="facture.isChecked" class="form-check-input me-2" style="transform: scale(1.5); margin-left: 10px; margin-top: 10px;" />       
                     </td>
                    
                  </tr>
                </tbody>
              </table>
              <nav aria-label="...">
                <ul class="pagination">
                  <li class="page-item" [class.disabled]="currentPage === 1">
                    <a class="page-link" (click)="changePage(currentPage - 1)">Précédent</a>
                  </li>
                  <li class="page-item" *ngFor="let page of getPages()" [class.active]="currentPage === page">
                    <a class="page-link" (click)="changePage(page)">{{ page }}</a>
                  </li>
                  <li class="page-item" [class.disabled]="currentPage === totalPages">
                    <a class="page-link" (click)="changePage(currentPage + 1)">Suivant</a>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>


 
<ng-template #content let-modal>
  <div class="modal-header">
		<h4 class="modal-title" id="modal-basic-title" style="margin-right: 30px;">
			Facture
		</h4>
    <button type="button" class="btn btn-primary" style="color: white !important;"  (click)="imprimerFacture()">Imprimer</button>
		<button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <!-- <button type="button" class="btn btn-primary" style="color: white !important;" (click)="sendEmail()">Envoyer par Email</button> -->

      <!-- <button type="button" class="btn btn-raised btn-warning mr-1" (click)="telecharger()">
        Télécharger PDF
      </button> -->
			<span aria-hidden="true">
				×
			</span>
		</button>
	</div>
  <div class="card">

    <!-- <div class="card-header"> <button type="button" class="btn btn-raised btn-warning mr-1" (click)="telecharger()">
      Télécharger PDF
    </button>
  
  </div> -->
    
    <div class="card-content">
      <div class="card-body">

        <div id="facture-content">
  
          <div style="display: flex; align-items: center; justify-content: space-between;">
            <div>
              <img src="assets/img/logo.png" alt="Logo" class="logo" />
              <h3><b> STE ZEN LOGISTIC</b></h3><br>
        
              <b>MF: 1557718/G/A/M/000</b><br>
              <b> ADRESSE :</b> Route Gremda km2,5 SFAX<br>
              <b> TELEPHONE :</b> 70 147 680 <br>
              <b> FAX :</b> 74 870 248 <br>
              

            </div>
            <div style="width: 40%;">
              <h3 style="display: flex; justify-content: center; font-weight: bold;">FACTURE</h3>

              <div class="client-section" style="border: solid; padding: 10px;">
                <p><b>Client :</b> {{ nom_client }}</p>
                <p><b>Adresse:</b> {{ adress }}</p>
                <p><b> M.F :</b> {{mat_fisc}}</p>
                <!-- Ajoutez d'autres propriétés client nécessaires ici -->
              </div>
            </div>
          </div>
        
          <div style="display: flex; justify-content: center; margin-top: 20px;font-size: 20px;"><h4><b>Facture N°: {{code}}</b></h4></div>
          <div style="display: flex; justify-content: center;">
            <h5 style="font-size: 16px;">
              <b>DATE :</b> <span >{{ dateFac | date: 'dd/MM/yyyy' }}</span>
            </h5>
            
          </div>
          
      <!-- Ajout du tableau -->
      <!-- <table style="width: 95%; border-collapse: collapse; margin: auto; text-align: center; margin-bottom: 30px; "> -->

        <!-- -----------------------------------------TRASPORT ---------------------------------------------------- -->
      <table style="border-collapse: collapse; width: 100%; margin: auto; text-align: center; " *ngIf="showTransport">
        <thead>
          <tr style="border: 1px solid black;">
            <th style="border: 1px solid black;">Désignation</th>
            <th style="border: 1px solid black;">Unité</th>
            <th style="border: 1px solid black;">Quantité</th>
            <th style="border: 1px solid black;">TVA</th>
            <th style="border: 1px solid black;">Total HT</th>
          </tr>
        </thead>
        <tbody>
          <!-- Utilisation de *ngFor pour parcourir la liste pref -->
          <tr *ngFor="let item of pref" style="border: 1px solid black;" >
            <td *ngIf="item.prix>0" style="border: 1px solid black;">{{ item.designation }}</td>
            <td *ngIf="item.prix>0" style="border: 1px solid black;">{{ item.unite }}</td>
            <td *ngIf="item.prix>0" style="border: 1px solid black;">{{ (item.volume  | number:'1.2-2').toString().replace(',', ' ')}}</td>
            <td *ngIf="item.prix>0" style="border: 1px solid black;">{{tva}}%</td>
            <td *ngIf="item.prix>0" style="border: 1px solid black;">{{ (item.prix  | number:'1.3-3').toString().replace(',', ' ') }}</td>
          </tr>
        </tbody>
      </table>



        <!-- -----------------------------------------ENTREPOSAGE ---------------------------------------------------- -->

      <table style="border-collapse: collapse; width: 100%; margin: auto; text-align: center; " *ngIf="showEntreposage">
        <thead>
          <tr style="border: 1px solid black;">
            <th style="border: 1px solid black;">Désignation</th>
            <th style="border: 1px solid black;">Unité</th>
            <th style="border: 1px solid black;">Quantité</th>
            <th style="border: 1px solid black;">PU .HT</th>

            <th style="border: 1px solid black;">TVA</th>
            <th style="border: 1px solid black;">Total HT</th>
          </tr>
        </thead>
        <tbody>
          <!-- Utilisation de *ngFor pour parcourir la liste pref -->
          <tr *ngFor="let item of pref" style="border: 1px solid black;" >
            <td *ngIf="item.prix>0" style="border: 1px solid black;">{{ item.designation }}</td>
            <td *ngIf="item.prix>0" style="border: 1px solid black;">{{ item.unite }}</td>
            <td *ngIf="item.prix>0" style="border: 1px solid black;">{{ (item.totVolume | number:'1.3-3').toString().replace(',', ' ') }}</td>
            <td *ngIf="item.prix>0" style="border: 1px solid black;">{{ item.pu | number:'1.3-3' }}</td>

            <td *ngIf="item.prix>0" style="border: 1px solid black;">{{tva}}%</td>
            <td *ngIf="item.prix>0" style="border: 1px solid black;">{{ (item.prix  | number:'1.3-3').toString().replace(',', ' ')}}</td>
          </tr>
        </tbody>
      </table>


        <!-- -----------------------------------------FLUX ---------------------------------------------------- -->

      <table style="border-collapse: collapse; width: 100%; margin: auto; text-align: center; " *ngIf="showFlux">
        <thead>
          <tr style="border: 1px solid black;">
            <th style="border: 1px solid black;">Désignation</th>
            <th style="border: 1px solid black;">Unité</th>
            <th style="border: 1px solid black;">Quantité</th>
            <th style="border: 1px solid black;">PU .HT</th>

            <th style="border: 1px solid black;">TVA</th>
            <th style="border: 1px solid black;">Total HT</th>
          </tr>
        </thead>
        <tbody>
          <!-- Utilisation de *ngFor pour parcourir la liste pref -->
          <tr *ngFor="let item of pref" style="border: 1px solid black;" >
            <td *ngIf="item.prix>0" style="border: 1px solid black;">{{ item.designation }}</td>
            <td *ngIf="item.prix>0" style="border: 1px solid black;">{{ item.unite }}</td>
            <td *ngIf="item.prix>0" style="border: 1px solid black;">{{ item.volume.toString().replace('.', ',') }}</td>
            <td *ngIf="item.prix>0" style="border: 1px solid black;">{{ item.pu | number:'1.3-3'}}</td>

            <td *ngIf="item.prix>0" style="border: 1px solid black;">{{tva}}%</td>
            <td *ngIf="item.prix>0" style="border: 1px solid black;">{{ (item.prix  | number:'1.3-3').toString().replace(',', ' ') }}</td>
          </tr>
        </tbody>
      </table>






<div style=" display: flex;flex-direction: row-reverse;justify-content: space-between;align-items: center;">

  <div class="table-container" style="display: flex; margin-top: 20px; margin-bottom: 30px; justify-content: flex-end;">
    <table *ngFor="let item of foot" style="border-collapse: collapse;">
      <tr style="border: 1px solid black;">
        <th style="border: 1px solid black;">Total HT :</th>
        <td style="border: 1px solid black;">{{ (item.HT | number:'1.3-3').toString().replace(',', ' ') }}</td>
      </tr>
      <tr style="border: 1px solid black;">
        <th style="border: 1px solid black;">Total TVA :</th>
        <td style="border: 1px solid black;">{{ (item.TVA | number:'1.3-3').toString().replace(',', ' ')}}</td>
      </tr>
      <tr style="border: 1px solid black;">
        <th style="border: 1px solid black;">Timbre :</th>
        <td style="border: 1px solid black;">{{ item.timbre.toString().replace('.', ',') }}</td>
      </tr>
      <tr style="border: 1px solid black;">
        <th style="border: 1px solid black;">Total TTC :</th>
        <td style="border: 1px solid black;">{{ (item.TTC | number:'1.3-3').toString().replace(',', ' ')}}</td>
      </tr>
    </table>
  </div>


  <div style="display: flex;  margin-left: 30px; margin-bottom: 30px;">
    <h5> <b>Arrêtée la présente Facture à la somme De :</b> <br>
      <p>{{montantEnLettres}} </p>
    </h5>
  </div>

</div>


</div>



      </div>
    </div>


  </div>
</ng-template>



<ng-template #details>

  <button (click)="downloadExcelTransport()" class="btn btn-success" style="margin-bottom: 10px;color: white !important;">
    <i class="fa fa-file-excel-o"></i>
    Exporter en Excel
  </button>
  <div style="max-height: 80vh; overflow-y: auto;">

  <table class="table">
    <thead>
      <tr>
        <th>ID</th>
        <th>Date du voyage</th>
        <th>Départ</th>
        <th>Arrivée</th>
        <th>Emplacement</th>
        <th>Date Voyage</th>
        <th>Kilométrage</th>
        <th>Type de ligne</th>
        <th>Quantité</th>

        <th>Prix total</th>

      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let detail of detailsInvoice">
        <td>{{ detail.id }}</td>
        <td>{{ detail.date_voyage }}</td>
        <td>{{ detail.nom_depart }}</td>
        <td>{{ detail.nom_arrivee }}</td>
        <td>{{ detail.emplacement }}</td>
        <td>{{ detail.date_voyage }}</td>
        <td>{{ detail.kilometrage }}</td>
        <td>{{ detail.type_ligne }}</td>
        <td>{{ detail.volume ? detail.volume + ' m³' : detail.quantite + ' pcs' }}</td>
        <td>{{ detail.prix_tot | number:'1.3-3'}}</td>

      </tr>
    </tbody>
  </table>
  </div>
</ng-template>


<ng-template #detailsFlux>
  <button (click)="downloadExcel()" class="btn btn-success" style="margin-bottom: 10px; color: white !important;">
    <i class="fa fa-file-excel-o"></i>
    Exporter en Excel
  </button>
  
  <div style="max-height: 600px; overflow-y: auto;">
    <table class="table">
      <thead>
        <tr>
          <th style="position: sticky; top: 0; background-color: #fff; z-index: 1;">ID</th>
          <th style="position: sticky; top: 0; background-color: #fff; z-index: 1;">Date</th>
          <th style="position: sticky; top: 0; background-color: #fff; z-index: 1;">Nom Dépot</th>
          <th style="position: sticky; top: 0; background-color: #fff; z-index: 1;">Quantité</th>
          <th style="position: sticky; top: 0; background-color: #fff; z-index: 1;">Prix Totale</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let detail of detailsInvoice">
          <td>{{ detail.id }}</td>
          <td>{{ detail.date | date: 'dd/MM/yyyy' }}</td>
          <td>{{ detail.nom_depot }}</td>
          <td>{{ detail.qte }}</td>
          <td>{{ detail.prix_tot  | number:'1.3-3' }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</ng-template>


