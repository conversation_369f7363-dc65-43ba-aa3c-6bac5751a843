import { Injectable } from '@angular/core';
import { Observable, map } from 'rxjs';
import { AuthService } from './auth.service';
import { User } from '../models/auth.interfaces';
import { SessionStorageService } from './session-storage.service';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../environments/environment';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};
@Injectable({
  providedIn: 'root'
})
export class RoleService {
  private apiURL = environment.apiURL; 

  constructor(
    private http:HttpClient,
    private sessionStorage :SessionStorageService) { }

  /**
   * Get current user's role synchronously
   */
  getRole(): string | null {
    return this.sessionStorage.getSessionValue('userRole');
  }




  /**
   * Check if user has specific role
   */
  hasRole(role: string): boolean {
    const userRole = this.getRole();
    return userRole === role;
  }

  /**
   * Check if user has any of the specified roles
   */
  hasAnyRole(roles: string[]): boolean {
    const userRole = this.getRole();
    return roles.some(role => userRole === role);
  }




  /**
   * Get all available roles (this would typically come from a backend service)
   */
  getAllRoles(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'roles', httpOptions);
  }

  private getAvailableRoles(): string[] {
    return [
      'SuperAdmin',
      'Administrateur',
      'Client',
      'Chef Departement',
      'GS',
      'GE',
      'Depot',
      'Inspection',
      'FACTURATION',
      'MAGASIN',
      'Conducteur'
    ];
  }

  /**
   * Check if a role exists in the system
   */
  isValidRole(role: string): boolean {
    return this.getAvailableRoles().includes(role);
  }
}
