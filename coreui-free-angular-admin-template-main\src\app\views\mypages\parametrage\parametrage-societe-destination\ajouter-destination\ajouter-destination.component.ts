import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { DestinationService, LocationService, MarchandiseService } from '../../../../../../services';
import { FournisseurService } from '../../../../../../services/fournisseur.service';
import { FormConfig } from '../../../../../../shared/models/form.models';
import { DynamicFormComponent } from '../../../../../../shared/components/dynamic-form/dynamic-form.component';
import { SmartContainerComponent } from '../../../../../../shared/components/smart-container/smart-container.component';

@Component({
  selector: 'app-ajouter-destination',
  standalone: true,
  imports: [SmartContainerComponent, DynamicFormComponent],
  templateUrl: './ajouter-destination.component.html',
  styleUrl: './ajouter-destination.component.scss'
})
export class AjouterDestinationComponent implements OnInit {

  fournisseurs: any[] = [];
  regions: any[] = [];
  villes: any[] = [];
  orderTypes: any[] = [];
  formConfig: FormConfig = {
    title: 'Ajouter Destination',
    fields: [],
    buttons: [],
  };
  DataToSend: any = {};

  constructor(
    private locationService: LocationService,
    private fournisseurService: FournisseurService,
    private destinationService: DestinationService,
    private toastr: ToastrService,
    private marchandiseService: MarchandiseService
  ) { }
  ngOnInit(): void {
    this.buildFormConfig();
    this.loadVilles();
    this.loadFournisseurs();
    this.loadMarchandises();
  }

  buildFormConfig(): void {
    this.formConfig = {
      title: 'Ajouter Destination',
      fields: [
        {
          name: 'fournisseurId',
          label: 'Nom Societe',
          placeholder: 'Nom Societe',
          required: true,
          type: 'select',
          options: {
            objectArray: this.fournisseurs,
            valueAttribute: 'id',
            labelAttribute: 'nom_fournisseur',
            inline: true,
          }
        },
        {
          name: 'nom_locale',
          label: 'Nom Locale',
          placeholder: 'Nom Locale',
          required: true,
          type: 'text'

        },
        {
          name: 'adresse',
          label: 'Adresse',
          placeholder: 'Adresse',
          required: true,
          type: 'text'
        },
        {
          name: 'phone',
          label: 'Phone',
          placeholder: 'Phone',
          required: true,
          type: 'text'
        },
        {
          name: 'email',
          label: 'Email',
          placeholder: 'Email',
          required: true,
          type: 'email',
          validation: {
            pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
          }
        },
        {
          name: 'nom_type',
          label: 'Type Ordre',
          placeholder: 'Type Ordre',
          required: true,
          type: 'multiselect',
          options: {
            objectArray: this.orderTypes,
            valueAttribute: 'id',
            labelAttribute: 'nom_type',
            inline: true,
          }
        },
        {
          name: 'ville',
          label: 'Ville',
          placeholder: 'Ville',
          required: true,
          type: 'select',
          onChange: (value: any) => this.loadRegions(value),
          options: {
            objectArray: this.villes,
            valueAttribute: 'id',
            labelAttribute: 'nom_ville',
            inline: true,
          }
        },
        {
          name: 'id_region',
          label: 'Region',
          placeholder: 'Region',
          required: true,
          type: 'select',
          options: {
            objectArray: this.regions,
            valueAttribute: 'id',
            labelAttribute: 'nom_region',
            inline: true,
          }
        }


      ],
      buttons: [
        { label: 'Ajouter', color: 'primary',onClick: (formData: any) => this.onFormSubmit(formData) },
      ],
    };
  }

  loadMarchandises(): void {
    this.marchandiseService.getAllOrderTypes().subscribe({
      next: (res: any) => {
        this.orderTypes = res;
        const orderTypeField = this.formConfig.fields?.find(f => f.name === 'nom_type');
        if (orderTypeField && orderTypeField.options) {
          orderTypeField.options.objectArray = this.orderTypes;
        }
        },
      error: (error: any) => {
        console.log(error);
      }
    })
  }

  loadFournisseurs(): void {
    this.fournisseurService.getAllFournisseur().subscribe({
      next: (res: any) => {
        this.fournisseurs = res;
        const fournisseurField = this.formConfig.fields?.find(f => f.name === 'fournisseurId');
        if (fournisseurField && fournisseurField.options) {
          fournisseurField.options.objectArray = this.fournisseurs;
        }
      },
      error: (error: any) => {
        console.log(error);
      }
    })
  }

  loadVilles(): void {
    this.locationService.getAllCities().subscribe({
      next: (res: any) => {
        this.villes = res;
        const villeField = this.formConfig.fields?.find(f => f.name === 'ville');
        if (villeField && villeField.options) {
          villeField.options.objectArray = this.villes;
        }
      },
      error: (error: any) => {
        console.log(error);
      }
    })
  }
  loadRegions(value: any): void {
    this.locationService.findRegionByCityId(value).subscribe({
      next: (res: any) => {
        this.regions = res;
        const regionField = this.formConfig.fields?.find(f => f.name === 'id_region');
        if (regionField && regionField.options) {
          regionField.options.objectArray = this.regions;
        }
      },
      error: (error: any) => {
        console.log(error);
      }
    })
  }

  onFormChange(formData: any) {
    this.DataToSend = formData;
  }

  onFormSubmit(formData: any) {
    const data = {
      nom_locale: formData.nom_locale,
      adresse: formData.adresse,
      phone: formData.phone,
      email: formData.email,
      id_region: parseInt(formData.id_region),
      id_fournisseur: [parseInt(formData.fournisseurId)],
      id_types: formData.nom_type
    };

    this.destinationService.addDestination(data).subscribe({
      next: (response: any) => {
        // Gérer ici la réponse réussie, si nécessaire
        this.toastr.success("Destination ajoutée avec succès");
        // Réinitialiser les valeurs des champs
        this.DataToSend = {};

      },
      error: (error: any) => {
        // Gérer ici les erreurs lors de l'appel au service
        console.error('Erreur lors de l\'ajout de la destination', error);
        // Afficher un message Toast d'erreur
        this.toastr.error("Erreur lors de l'ajout de la destination");
      }
    });
  }

}
