<app-smart-container
    [title]="showprimaryView ? 'Parametrage des societes/destinations' : 'Gestion des destinations'">
    <div slot="actions">
        <app-smart-button [label]="showprimaryView ? 'Gestion des Destinations' : 'Ajouter Societe/Destination'" [color]="'primary'" (onClick)="toggleView()"></app-smart-button>
    </div>
    <div slot="content">
        <div *ngIf="showprimaryView">
            <app-ajouter-societe></app-ajouter-societe>
            <app-ajouter-destination></app-ajouter-destination>
        </div>
        <div *ngIf="!showprimaryView">
            <app-gestion-destination></app-gestion-destination>
        </div>
    </div>
    
</app-smart-container>
