import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { OrderLineService } from 'src/services';
import { CamionService } from 'src/services/camion.service';
import { ConducteurService } from 'src/services/conducteur.service';
import { LigneCmdService } from 'src/services/ligne-cmd.service';
import { PriceService } from 'src/services/price.service';
import { TableColumn } from 'src/shared/models/table.models';
import { ActionButton, TableConfig } from '../../../../../shared/models/table.models';
import { CommonModule } from '@angular/common';
import { SmartTableComponent } from '../../../../../shared/components/smart-table/smart-table.component';
import { SmartContainerComponent } from '../../../../../shared/components/smart-container/smart-container.component';
import { DynamicFormComponent } from '../../../../../shared/components/dynamic-form/dynamic-form.component';
import { FormConfig } from '../../../../../shared/models/form.models';
import { SmartButtonComponent } from '../../../../../shared/components/smart-button/smart-button.component';
import { initial } from 'lodash-es';
import { CircuitService } from '../../../../../services';

@Component({
  selector: 'app-ajouter-ligne-cmd',
  standalone: true,
  imports: [
    CommonModule,
    SmartTableComponent,
    SmartContainerComponent,
    DynamicFormComponent,
    SmartButtonComponent
  ],
  templateUrl: './ajouter-ligne-cmd.component.html',
  styleUrl: './ajouter-ligne-cmd.component.scss'
})
export class AjouterLigneCmdComponent implements OnInit{

  loadingPrimeTable:boolean = false
  showDetailsTable:boolean = false
  loadingDetailsTable:boolean = false
  initialData: any = {};
  selectedVoyageLigne: any ={};
  selectedDetails:any[] = [];
  conducteurList: any[] = [];
  circuitList: any[] = [];
  camionList: any[] = [];
  VoyageLineToAdjust: any[] = [];

  disableButton :boolean = true;

  sourceDetails:any = [];

  tableColumn: TableColumn[] = [
    { name: 'id', displayName: 'ID', sortable: true ,filterable: true},
    { name:'nom',displayName: 'Nom Transporteur',sortable: true ,filterable: true},
    { name:'prenom',displayName: 'Prenom Transporteur',sortable: true ,filterable: true},
    { name:'immatriculation',displayName: 'Immatriculation',sortable: true ,filterable: true},
    { name:'date_voyage',displayName: 'Date Voyage',sortable: true ,filterable: true},
  ];

  tableConfig: TableConfig = {
    selectable: true,
    multiSelect: false   
  }
  formConfig: FormConfig ={
    fieldGroups : [
      {
        fields: [
          {
            name: 'selectedCamionId',

            type: 'select',
            required: true,
            disabled: true,
            options: {objectArray: 
             this.camionList,
            valueAttribute: 'id',
            labelAttribute: 'immatriculation'}
          },
          {
            name: 'selectedConducteurId',
            type: 'select',
            required: true,
            disabled: true,
            options: {objectArray: 
              this.conducteurList,
            valueAttribute: 'id',
            labelAttribute: 'fullName'}
          },
          {
            name : 'selectedDate',
            type: 'date',
            disabled: true,
            required: true
          },
          {
            name : 'selectedCircuit',
            type: 'select',
            required: true,
            onChange: (event: any) => {
              this.initialData.selectedCircuit = parseInt(event);
              this.UpdateButtonDisableState();
            },
            options: {objectArray: 
              this.circuitList,
            valueAttribute: 'id',
            labelAttribute: 'nom_circuit'}
          }
        ]
      }
    ]
  }

  tableDetailsColumn: TableColumn[] = [
    { name: 'id', displayName: 'ID', sortable: true ,filterable: true},
    { name: 'nom_depart', displayName: 'Nom Depart', sortable: true ,filterable: true},
    { name: 'nom_arrivee', displayName: 'Nom Arrivee', sortable: true ,filterable: true},
    { name: 'type_ligne', displayName: 'Type Ligne', sortable: true ,filterable: true},
    { name: 'kilometrage', displayName: 'Kilometrage', sortable: true ,filterable: true},
    { name: 'nom_client', displayName: 'Nom Client', sortable: true ,filterable: true},
    { name: 'volume', displayName: 'Volume', sortable: true ,filterable: true},
    { name: 'status', displayName: 'Status', sortable: true ,filterable: true},
  ];

  tableDetailsConfig: TableConfig = {
    selectable: true,
    multiSelect: true,
    commentable: true   
  }
  tableDetailsAction: ActionButton[] = [
    {
      icon: 'cil-comment-bubble',
      color: 'info',
      isCommentAction: true
    }
  ]


  constructor(
    private conducteurService: ConducteurService,
    private camionService: CamionService,
    private ligneCmdService: LigneCmdService,
    private orderLigneService :OrderLineService,
    private priceService: PriceService,
    private circuitService: CircuitService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.loadVoyageLineToAdjust();
    this.loadConducteurs();
    this.loadCircuit();
    this.loadCamion();
  }

  loadlineToAdjust(){
    this.loadingDetailsTable = true;
    this.showDetailsTable = true;
    let data ={
      id_camion : this.initialData.selectedCamionId,
      id_conducteur : this.initialData.selectedConducteurId,
      date : this.initialData.selectedDate,
    }
    this.orderLigneService.findLinesToAdjust(data).subscribe({
      next: (data:any) => {
        this.sourceDetails = data;
        this.loadingDetailsTable = false;
      },
      error: (error:any) => {
        this.toastr.error('Erreur lors de la chargement des lignes de voyage');
        this.loadingDetailsTable = false;
      }
    });
  }
  onSelectChange(event: any) {
    this.selectedVoyageLigne = event[0];
    this.initialData = {
      selectedCamionId: this.selectedVoyageLigne.id_camion,
      selectedConducteurId: this.selectedVoyageLigne.id_conducteur,
      selectedDate: this.selectedVoyageLigne.date_voyage,
    };
    
    this.loadlineToAdjust();

  }
  onSelectDetailsChange(event: any) {
    this.UpdateButtonDisableState();
    this.selectedDetails = event;
    console.log(this.selectedDetails);
  }
  UpdateButtonDisableState(){
    if (Object.keys(this.selectedDetails).length > 0 && Object.keys(this.initialData).length > 2) {
      this.disableButton = false;
    } else {
      this.disableButton = true;
    }
  }
  Affecter(){
    if (!this.checkLigneTableConditions()) {
      this.toastr.error('Erreur lors de la vérification des lignes de voyage');
      return;
    }
    let data :{
      id_circuit:any,
      lignes:any[]
    } = {
      id_circuit:this.initialData.selectedCircuit,
      lignes:[]
    }
    this.selectedDetails.forEach((detail:any) => {
      data.lignes.push(detail.id);
    })
    console.log(data);
    
    this.circuitService.adjustVoyage(data).subscribe({
      next: (data:any) => {
        this.sourceDetails = this.sourceDetails.filter((item:any ) => !data.lignes.includes(item.id));
        if (this.sourceDetails.length == 0) {
          this.showDetailsTable = false;
          this.VoyageLineToAdjust = this.VoyageLineToAdjust.filter((item:any) => this.selectedVoyageLigne != item);
        }
        this.toastr.success('Lignes de voyage ajustées avec succès');
      },
      error: (error:any) => {
        this.toastr.error('Erreur lors de l\'ajustement des lignes de voyage');
      }
    })
  }
  loadConducteurs() {
    this.conducteurService.findConducteur().subscribe({
      next: (data:any) => {
        this.conducteurList = data;
        this.conducteurList.forEach((conducteur:any) =>
        {conducteur.fullName = conducteur.nom + ' ' + conducteur.prenom}
        )
        if (this.formConfig.fieldGroups && this.formConfig.fieldGroups[0].fields[1].options) {
          this.formConfig.fieldGroups[0].fields[1].options.objectArray = this.conducteurList;
        }
      },
      error: (error:any) => {
        this.toastr.error('Erreur lors de la chargement des conducteurs');
      }
    });
  }
  loadCircuit() {
    this.circuitService.getAllCircuits().subscribe({
      next: (data:any) => {
        this.circuitList = data;
        if (this.formConfig.fieldGroups && this.formConfig.fieldGroups[0].fields[3].options) {
          this.formConfig.fieldGroups[0].fields[3].options.objectArray = this.circuitList;
        }
      },
      error: (error:any) => {
        this.toastr.error('Erreur lors de la chargement des circuits');
      }
    });
  }
  loadCamion() {
    this.camionService.findAllCamion().subscribe({
      next: (data:any) => {
        this.camionList = data;
        if (this.formConfig.fieldGroups && this.formConfig.fieldGroups[0].fields[0].options) {
          this.formConfig.fieldGroups[0].fields[0].options.objectArray = this.camionList;
        }
      },
      error: (error:any) => {
        this.toastr.error('Erreur lors de la chargement des camions');
      }
    });
  }
  loadVoyageLineToAdjust() {
    this.loadingPrimeTable = true;
    this.orderLigneService.findVoyageListToAdjust().subscribe({
      next: (data:any) => {
        this.VoyageLineToAdjust = data;
        this.loadingPrimeTable = false;
      },
      error: (error:any) => {
        this.toastr.error('Erreur lors de la chargement des lignes de voyage');
        this.loadingPrimeTable = false;
      }
    });
  }

  checkLigneTableConditions() {
    // Filtrer les lignes avec les IDs sélectionnés
   
  
    const typesQuantiteSeule = [
      'Livraison fourniture',
      'Transfert Administratif inter magasin',
      'Transfert administratif technique et matériel informatique',
      'Transfert administratif ZEN Group-Magasin ou dépôt'
    ];
  
    for (const ligne of this.selectedDetails) {
      // Exclure la vérification si nom_arrivee ou nom_depart contient "aramex"
      if (
        ligne.nom_arrivee.toLowerCase().includes('aramex') || 
        ligne.nom_depart.toLowerCase().includes('aramex')
      ) {
        continue; // Le point-virgule est ajouté ici
      }
  
      // Vérification spécifique pour les types de 'typesQuantiteSeule'
      if (typesQuantiteSeule.includes(ligne.type_ligne)) {
        if (ligne.quantite <= 0) {
          this.toastr.error(`La quantité doit être supérieure à 0 pour le type ${ligne.type_ligne}`);
          return false;
        }
      } else {
        // Vérification pour les autres types : quantite et volume > 0
        if (ligne.quantite <= 0 || ligne.volume <= 0) {
          this.toastr.error(`Quantité et volume doivent être supérieurs à 0 pour le type ${ligne.type_ligne}`);
          return false;
        }
      }
    }
  
    return true;
  }

}
