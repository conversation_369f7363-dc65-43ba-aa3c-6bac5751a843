<app-smart-container title="TVA & Prix Prestation">

    <div *ngIf="isPasswordCorrect" slot="actions">
        <app-smart-button [label]="'Modifier'" [color]="'primary'" (onClick)="showFormModal()"></app-smart-button>
    </div>


    <div slot="content">
        <div class="card-body" style="display: flex;" *ngIf="!isPasswordCorrect">
            <input id="password2" type="password" class="form-control" style="width: 300px;" placeholder="Entrez le mot de passe" name="password">
            <app-smart-button [label]="'Valider'" [color]="'primary'" (onClick)="checkPassword()"></app-smart-button>
        </div>

         <div 
            *ngIf="isPasswordCorrect">
            <app-smart-table 
            [data]="impotsKeyValueArray" 
            [columns]="tableColumns"
            [isLoading]="loading">
        </app-smart-table>
        </div> 
    </div>
</app-smart-container>


<app-form-modal 
[config]="formConfig"
[initialData]="impots[0]" 
[(visible)]="showModal" 
size="lg" 
>
</app-form-modal>