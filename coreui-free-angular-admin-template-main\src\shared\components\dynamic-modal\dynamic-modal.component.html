<div *ngIf="show" 
     class="modal fade" 
     [class.show]="show" 
     [class.d-block]="show"
     [class.backdrop]="backdrop"
     (click)="onBackdropClick($event)"
     role="dialog"
     aria-modal="true">
  
  <div class="modal-dialog"
       [class.modal-sm]="size === 'sm'"
       [class.modal-lg]="size === 'lg'"
       [class.modal-xl]="size === 'xl'"
       [class.modal-centered]="centered"
       [class.modal-dialog-scrollable]="scrollable">
    
    <div class="modal-content">
      <!-- Header -->
      <div class="modal-header">
        <ng-container *ngIf="headerContent; else defaultHeader">
          <ng-container *ngTemplateOutlet="headerContent"></ng-container>
        </ng-container>
        
        <ng-template #defaultHeader>
          <h5 class="modal-title">{{ title }}</h5>
          <button *ngIf="showCloseButton" 
                  type="button" 
                  class="btn-close" 
                  aria-label="Close"
                  (click)="close()"></button>
        </ng-template>
      </div>
      
      <!-- Body -->
      <div class="modal-body">
        <ng-content></ng-content>
      </div>
      
      <!-- Footer -->
      <div class="modal-footer">
        <ng-container *ngIf="footerContent; else defaultFooter">
          <ng-container *ngTemplateOutlet="footerContent"></ng-container>
        </ng-container>
        
        <ng-template #defaultFooter>
          <button type="button"
                  class="btn btn-secondary"
                  (click)="close()">
            Fermer
          </button>
          <button *ngIf="submitExist"
                  type="button"
                  class="btn btn-{{buttonVariant}}"
                  [disabled]="disableSubmit"
                  (click)="submit()">
            {{ buttonLabel }}
          </button>
        </ng-template>
      </div>
    </div>
  </div>
</div>