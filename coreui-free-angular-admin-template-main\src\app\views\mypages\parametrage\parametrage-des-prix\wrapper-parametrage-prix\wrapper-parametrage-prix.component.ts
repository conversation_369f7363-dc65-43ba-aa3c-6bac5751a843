import { Component } from '@angular/core';
import { TransportComponent } from '../transport/transport.component';
import { TvaPrestationComponent } from '../tva-prestation/tva-prestation.component';
import { FormuleCalculComponent } from '../formule-calcul/formule-calcul.component';

@Component({
  selector: 'app-wrapper-parametrage-prix',
  standalone: true,
  imports: [TransportComponent, TvaPrestationComponent, FormuleCalculComponent  ],
  templateUrl: './wrapper-parametrage-prix.component.html',
  styleUrl: './wrapper-parametrage-prix.component.scss'
})
export class WrapperParametragePrixComponent {

}
