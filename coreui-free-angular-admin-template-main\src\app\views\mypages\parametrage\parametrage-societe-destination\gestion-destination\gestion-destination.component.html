<app-smart-container [title]="'Societé'"
[subtitle]="'Changer les informations de la societe'">
    <div slot="content">
        <app-dynamic-form 
            [config]="formConfig" 
            [initialData]="initialData" 
            (formChange)="onFormChange($event)">
        </app-dynamic-form>
    </div>


</app-smart-container>

<div *ngIf="showTable">
    <app-smart-container
    [title]="'Destinations de la societe : ' + initialData.nom_fournisseur">
        <div slot="content">
            <app-smart-table 
                [data]="destinations" 
                [isLoading]="loading"
                [columns]="tableColumns"
                [actionButtons]="tableActions"
                [config]="{emptyMessage:'aucune destination affectée au client ' + initialData.nom_fournisseur}">
            </app-smart-table>
        </div>
    </app-smart-container>
</div>

<app-form-modal  
[config]="formModalConfig" 
    [initialData]="initialFormModalData" 
    [(visible)]="showModal" 
    size="lg" >
</app-form-modal>