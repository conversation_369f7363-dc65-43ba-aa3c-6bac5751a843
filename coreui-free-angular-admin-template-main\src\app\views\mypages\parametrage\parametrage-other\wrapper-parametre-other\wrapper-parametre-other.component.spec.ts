import { ComponentFixture, TestBed } from '@angular/core/testing';

import { WrapperParametreOtherComponent } from './wrapper-parametre-other.component';

describe('WrapperParametreOtherComponent', () => {
  let component: WrapperParametreOtherComponent;
  let fixture: ComponentFixture<WrapperParametreOtherComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [WrapperParametreOtherComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(WrapperParametreOtherComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
