import { ComponentFixture, TestBed } from '@angular/core/testing';

import { WrapperParametragePrixComponent } from './wrapper-parametrage-prix.component';

describe('WrapperParametragePrixComponent', () => {
  let component: WrapperParametragePrixComponent;
  let fixture: ComponentFixture<WrapperParametragePrixComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [WrapperParametragePrixComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(WrapperParametragePrixComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
