import { Component, Input, Output, EventE<PERSON>ter, On<PERSON>estroy, SimpleChanges, OnChanges } from '@angular/core';
import { FormGroup, FormBuilder, FormControl, Validators, ValidatorFn } from '@angular/forms';
import { FormField, FormButton, FormConfig } from '../../models/form.models';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-dynamic-form',
  templateUrl: './dynamic-form.component.html',
  styleUrls: ['./dynamic-form.component.scss'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule]
})
export class DynamicFormComponent implements OnDestroy, OnChanges {
  @Input() config!: FormConfig;
  @Input() initialData!: any;
  @Output() formChange = new EventEmitter<any>();

  form!: FormGroup;
  imagePreview: string | ArrayBuffer | null = null;
  dropdownOpen: { [key: string]: boolean } = {};
  private valueChangesSub?: Subscription;

  constructor(private fb: FormBuilder) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['initialData'] && this.form) {
      this.form.patchValue(this.initialData);
    }
    if (changes['config'] && this.form) {
      this.createForm();
    }
  }

  ngOnInit() {
    this.createForm();
    this.setupValueChanges();
  }

  private setupValueChanges() {
    // Unsubscribe from any existing subscription
    this.valueChangesSub?.unsubscribe();
    
    // Create new subscription
    this.valueChangesSub = this.form.valueChanges.subscribe(() => {
      const formValue = this.getConvertedFormValue();
      this.formChange.emit(formValue);
    });
  }

  createForm() {
    const group: any = {};
    
    // Process individual fields
    (this.config.fields || []).forEach(field => {
      this.addFieldToFormGroup(field, group);
    });

    // Process fields in groups
    (this.config.fieldGroups || []).forEach(fieldGroup => {
      fieldGroup.fields.forEach(field => {
        this.addFieldToFormGroup(field, group);
      });
    });

    this.form = this.fb.group(group);

    // After this.form = this.fb.group(group);
    Object.keys(this.form.controls).forEach(fieldName => {
      const field = this.config.fields?.find(f => f.name === fieldName);
      if (field) {
        const shouldDisable = this.isFieldDisabled(field);
        if (shouldDisable && !this.form.get(fieldName)?.disabled) {
          this.form.get(fieldName)?.disable({ emitEvent: false });
        } else if (!shouldDisable && this.form.get(fieldName)?.disabled) {
          this.form.get(fieldName)?.enable({ emitEvent: false });
        }
      }
    });
  }

  private addFieldToFormGroup(field: FormField, group: any) {
    const validators: ValidatorFn[] = this.getValidators(field);
    let initialValue = '';
    if (this.initialData > 0) {
      initialValue = this.getInitialValue(field);
    } 
    const isDisabled = this.isFieldDisabled(field);

    group[field.name] = this.fb.control(
      { value: initialValue, disabled: isDisabled },
      validators
    );
  }

  private getValidators(field: FormField): ValidatorFn[] {
    const validators: ValidatorFn[] = [];
    if (field.required) {
      validators.push(Validators.required);
    }
    if (field.validation) {
      if (field.validation.minLength) {
        validators.push(Validators.minLength(field.validation.minLength));
      }
      if (field.validation.maxLength) {
        validators.push(Validators.maxLength(field.validation.maxLength));
      }
      if (field.validation.pattern) {
        validators.push(Validators.pattern(field.validation.pattern));
      }
      if (field.validation.min) {
        validators.push(Validators.min(field.validation.min));
      }
      if (field.validation.max) {
        validators.push(Validators.max(field.validation.max));
      }
    }
    return validators;
  }

  private getInitialValue(field: FormField): any {
    let value = this.initialData[field.name] ?? field.value;
    
    if (field.type === 'number' && value !== undefined) {
      return Number(value);
    }
    if (field.type === 'checkbox' && value === undefined) {
      return false;
    }
    if (field.type === 'multiselect' && !Array.isArray(value)) {
      return [];
    }
    return value ?? '';
  }

  getConvertedFormValue(): any {
    if (!this.form) {
      console.error('Form is not initialized');
      return {};
    }
    const rawValue = this.form.getRawValue();
    const result: any = {};
    
    const allFields = [
      ...(this.config.fields || []),
      ...(this.config.fieldGroups?.flatMap(g => g.fields) || [])
    ];

    allFields.forEach(field => {
      if (field.name in rawValue) {
        const value = rawValue[field.name];
        if (field.type === 'number' && value !== null && value !== '') {
          result[field.name] = Number(value);
        } else {
          result[field.name] = value;
        }
      }
    });

    return result;
  }

  onFieldChange(field: FormField) {
    if (field.onChange) {
      const value = this.form.get(field.name)?.value;
      field.onChange(value); 
    }
  }
  private markAllAsTouched() {
    Object.values(this.form.controls).forEach(control => {
      control.markAsTouched();
    });
  }

  ngOnDestroy() {
    this.valueChangesSub?.unsubscribe();
  }

  isInputField(field: FormField): boolean {
    return ['text', 'number', 'email', 'password'].includes(field.type);
  }

  shouldSplit(field: FormField): boolean {
    return field.type !== 'textarea';
  }

  isFieldVisible(field: FormField): boolean {
    if (typeof field.hidden === 'function') {
      return !field.hidden();
    }
    return !field.hidden;
  }
  isFieldDisabled(field: FormField): boolean {
    if (typeof field.disabled === 'function') {
      return field.disabled();
    }
    return !!field.disabled;
  }

  toggleDropdown(fieldName: string): void {
    this.dropdownOpen[fieldName] = !this.dropdownOpen[fieldName];
  }

  onFileChange(event: any, fieldName: string) {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        this.imagePreview = reader.result;
        this.form.get(fieldName)?.setValue(file);
      };
      reader.readAsDataURL(file);
    }
  }

  getOptionLabel(option: any, field: FormField): string {
    return field.options?.labelAttribute ? option[field.options.labelAttribute] : option;
  }

  getOptionValue(option: any, field: FormField): any {
    return field.options?.valueAttribute ? option[field.options.valueAttribute] : option;
  }

  isOptionSelected(option: any, field: FormField): boolean {
    const currentValue = this.form.get(field.name)?.value || [];
    const optionValue = this.getOptionValue(option, field);
    return currentValue.includes(optionValue);
  }

  onMultiselectChange(option: any, field: FormField): void {
    const currentValue = this.form.get(field.name)?.value || [];
    const optionValue = this.getOptionValue(option, field);

    if (this.isOptionSelected(option, field)) {
      this.form.get(field.name)?.setValue(currentValue.filter((v: any) => v !== optionValue));
    } else {
      this.form.get(field.name)?.setValue([...currentValue, optionValue]);
    }
  }

  getSelectedLabels(field: FormField): string {
    const selectedValues = this.form.get(field.name)?.value || [];
    if (!selectedValues.length) return '';

    return field.options?.objectArray
      ?.filter(option => selectedValues.includes(this.getOptionValue(option, field)))
      ?.map(option => this.getOptionLabel(option, field))
      ?.join(', ') || '';
  }

  onButtonClick(button: FormButton) {
    if (button.onClick) {
      const formData = this.getConvertedFormValue();
      button.onClick(formData);
    }
    
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}