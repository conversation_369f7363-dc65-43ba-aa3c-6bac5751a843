{"version": 3, "sources": ["../../../../../../node_modules/calendar-utils/date-adapters/esm/date-fns/index.js", "../../../../../../node_modules/angular-calendar/date-adapters/esm/date-fns/index.js"], "sourcesContent": ["import { addDays, addHours, addMinutes, addSeconds, differenceInDays, differenceInMinutes, differenceInSeconds, endOfDay, endOfMonth, endOfWeek, getDay, getMonth, isSameDay, isSameMonth, isSameSecond, max, setHours, setMinutes, startOfDay, startOfMinute, startOfMonth, startOfWeek, getHours, getMinutes, } from 'date-fns';\nfunction getTimezoneOffset(date) {\n    return new Date(date).getTimezoneOffset();\n}\nexport function adapterFactory() {\n    return {\n        addDays: addDays,\n        addHours: addHours,\n        addMinutes: addMinutes,\n        addSeconds: addSeconds,\n        differenceInDays: differenceInDays,\n        differenceInMinutes: differenceInMinutes,\n        differenceInSeconds: differenceInSeconds,\n        endOfDay: endOfDay,\n        endOfMonth: endOfMonth,\n        endOfWeek: endOfWeek,\n        getDay: getDay,\n        getMonth: getMonth,\n        isSameDay: isSameDay,\n        isSameMonth: isSameMonth,\n        isSameSecond: isSameSecond,\n        max: max,\n        setHours: setHours,\n        setMinutes: setMinutes,\n        startOfDay: startOfDay,\n        startOfMinute: startOfMinute,\n        startOfMonth: startOfMonth,\n        startOfWeek: startOfWeek,\n        getHours: getHours,\n        getMinutes: getMinutes,\n        getTimezoneOffset: getTimezoneOffset,\n    };\n}\n", "import { __assign } from \"tslib\";\nimport { adapterFactory as baseAdapterFactory } from 'calendar-utils/date-adapters/date-fns';\nimport { addWeeks, addMonths, subDays, subWeeks, subMonths, getISOWeek, setDate, setMonth, setYear, getDate, getYear, } from 'date-fns';\nexport function adapterFactory() {\n    return __assign(__assign({}, baseAdapterFactory()), { addWeeks: addWeeks, addMonths: addMonths, subDays: subDays, subWeeks: subWeeks, subMonths: subMonths, getISOWeek: getISOWeek, setDate: setDate, setMonth: setMonth, setYear: setYear, getDate: getDate, getYear: getYear });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,SAAS,kBAAkB,MAAM;AAC7B,SAAO,IAAI,KAAK,IAAI,EAAE,kBAAkB;AAC5C;AACO,SAAS,iBAAiB;AAC7B,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;;;AC7BO,SAASA,kBAAiB;AAC7B,SAAO,SAAS,SAAS,CAAC,GAAG,eAAmB,CAAC,GAAG,EAAE,UAAoB,WAAsB,SAAkB,UAAoB,WAAsB,YAAwB,SAAkB,UAAoB,SAAkB,SAAkB,QAAiB,CAAC;AACpR;", "names": ["adapterFactory"]}