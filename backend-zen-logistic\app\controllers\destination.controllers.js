const Destination = require ("../models/destination.model.js") ; 


exports.create = (req, res)=>
{ 

  if(!req.body){
    res.status(400).send({
      message: "Content can not be empty"
    })
  }
 
 
 const destination = new Destination ({
  nom_locale : req.body.nom_locale,
  adresse : req.body.adresse,
  phone : req.body.phone,
  email : req.body.email,
  id_region : req.body.id_region,
  id_fournisseur : req.body.id_fournisseur,
  id_types: req.body.id_types,
 });








// console.log("////////////////////",destination)

 /*console.log('ttt')
console.log(conducteur)  */

 // Save destination in the database
 Destination.create(destination, (err, data) => {
  // console.log('test');
   if (err)
     res.status(500).send({
       message:
         err.message || "Some error occurred while creating the destination ."
     });
   else res.send(data);
 });
}

exports.updateById = (req, res) => {
  const { id } = req.params;
  const { nom_locale, adresse, phone, email, id_region,code_erp } = req.body;

  // Valider la requête
  if (!nom_locale || !adresse || !phone  || !id_region) {
    return res.status(400).json({ message: "Tous les champs sont obligatoires." });
  }

  // Appeler la méthode du modèle pour mettre à jour la destination
  Destination.updateById(id, nom_locale, adresse, phone, email, id_region,code_erp, (err) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: `Destination non trouvée avec l'ID ${id}.` });
      } else {
        return res.status(500).json({ message: `Erreur lors de la mise à jour de la destination avec l'ID ${id}.` });
      }
    }

    res.json({ message: 'Destination mise à jour avec succès' });
  });
};


// Retrieve all destination from the database.
exports.findAll = (req, res) => {
    Destination.findAll((err, data) => {

        
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving destination."
      });
    else res.send(data);
  });
 
};



exports.findByVilleAndFournisseur = (req, res) => {
  const id_ville = req.params.id_ville;
  const id_fournisseur = req.params.id_fournisseur;
  const id_type = req.params.id_type;

  Destination.findByVilleAndFournisseur(id_ville, id_fournisseur,id_type, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving destination."
      });
    else res.send(data);
  });
};


exports.findAllDestinationHaveType = (req, res) => {

  Destination.findAllDestinationHaveType( (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving destination."
      });
    else res.send(data);
  });
};



exports.findByIdClient = (req, res) => {
  const idClient = req.params.idClient;

  Destination.findByIdClient(idClient, (err, data) => {
      if (err) {
          res.status(500).send({
              message:
                  err.message || "Une erreur s'est produite lors de la récupération des destinations par idClient."
          });
      } else {
          res.send(data);
      }
  });
};

exports.deleteDestination = (req, res) => {
  const id = req.params.id;

  // Appel de la méthode du modèle pour vérifier et supprimer la destination
  Destination.deleteDestination(id, (err, data) => {
    if (err) {
      // Gérer les erreurs de manière appropriée
      if (err.kind === "not_found") {
        return res.status(404).json({ message: `Destination non trouvée .` });
      } else if((err.kind === "existe")){

        return res.status(400).json({ message: `La destination est utilisée dans des lignes de commande. Annulation de la suppression
        ` });


      }
      
      
      else  {
        return res.status(500).json({ message: `Erreur lors de la suppression de la destination` });
      }
    }

    // Destination supprimée avec succès
    res.json({ message: 'Destination supprimée avec succès', destination: data });
  });
};




exports.deleteDestinationByUser = (req, res) => {
  const id = req.params.id;

  // Appel de la méthode du modèle pour vérifier et supprimer la destination
  Destination.deleteDestinationByUser(id, (err, data) => {
    if (err) {
      // Gérer les erreurs de manière appropriée
      if (err.kind === "not_found") {
        return res.status(404).json({ message: `Destination non trouvée .` });
      } else if((err.kind === "existe")){

        return res.status(400).json({ message: `La destination est utilisée dans des lignes de commande. Annulation de la suppression
        ` });


      }
      
      
      else  {
        return res.status(500).json({ message: `Erreur lors de la suppression de la destination` });
      }
    }

    // Destination supprimée avec succès
    res.json({ message: 'Destination supprimée avec succès', destination: data });
  });
};


exports.updateTypes = (req, res) => {
  const { id } = req.params;
  const { listeIds } = req.body; // Récupérer la liste des IDs depuis le corps de la requête

  // Valider la requête
  // if (!listeIds || listeIds.length === 0) {
  //   return res.status(400).json({ message: "La liste des IDs est obligatoire." });
  // }

  // Appeler la méthode du modèle pour mettre à jour la destination
  Destination.updateTypes(id, listeIds, (err) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: `Destination non trouvée avec l'ID ${id}.` });
      } else if (err.status === 400) { // Vérifier si c'est une erreur spécifique à "Aucune modification détectée"
        return res.status(400).json({ message: err.message }); // Afficher le message d'erreur retourné par le modèle
      } else {
        return res.status(500).json({ message: `Erreur lors de la mise à jour de la destination avec l'ID ${id}.` });
      }
    }

    res.json({ message: 'Destination mise à jour avec succès' });
  });
};


exports.findDestinationById = (req, res) => {
  const { id } = req.params;

  // Valider la requête
  if (!id) {
    return res.status(400).json({ message: "Tous les champs sont obligatoires." });
  }

  // Appeler la méthode du modèle pour rechercher la destination par ID
  Destination.findDestinationById(id, (err, destination) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: `Destination non trouvée avec l'ID ${id}.` });
      } else {
        return res.status(500).json({ message: `Erreur lors de la recherche de la destination avec l'ID ${id}.` });
      }
    }

    res.json({ destination });
  });
};

exports.findDestinationByCompany = (req, res) => {
  const { id } = req.params;

  // Valider la requête
  if (!id) {
    return res.status(400).json({ message: "Tous les champs sont obligatoires." });
  }

  // Appeler la méthode du modèle pour rechercher la destination par ID
  Destination.findDestinationByCompany(id, (err, destination) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: `Destination non trouvée avec l'ID ${id}.` });
      } else {
        return res.status(500).json({ message: `Erreur lors de la recherche de la destination avec l'ID ${id}.` });
      }
    }

    res.json({ destination });
  });
};


exports.findDestinationByUser = (req, res) => {
  const { id } = req.params;

  // Valider la requête
  if (!id) {
    return res.status(400).json({ message: "Tous les champs sont obligatoires." });
  }

  // Appeler la méthode du modèle pour rechercher la destination par ID
  Destination.findDestinationByUser(id, (err, destination) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: `Destination non trouvée avec l'ID ${id}.` });
      } else {
        return res.status(500).json({ message: `Erreur lors de la recherche de la destination avec l'ID ${id}.` });
      }
    }

    res.json({ destination });
  });
};


exports.assignDestinationToUser = (req, res) => {
  const { id } = req.params;

  // Valider la requête
  if (!id) {
    return res.status(400).json({ message: "Tous les champs sont obligatoires." });
  }

  // Appeler la méthode du modèle pour rechercher la destination par ID
  Destination.assignDestinationToUser(id,req.body, (err, destination) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: `Destination non trouvée avec l'ID ${id}.` });
      } else {
        return res.status(500).json({ message: `Erreur lors de la recherche de la destination avec l'ID ${id}.` });
      }
    }

    res.json({ destination });
  });
};


exports.findAllWarhouses = (req, res) => {
  Destination.findAllWarhouses((err, data) => {
  if (err)
    res.status(500).send({
      message:
        err.message || "Some error occurred while retrieving destination."
    });
  else res.send(data);
});
};


exports.disableWharehouse = (req, res) => {
  const wharehouseId = req.params.id; // Récupération de l'ID depuis l'URL

  if (!wharehouseId) {
    return res.status(400).send({ message: "Wharehouse ID is required" });
  }

  Destination.disableWharehouse(wharehouseId, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).send({ message: "Wharehouse not found" });
      }
      return res.status(500).send({ message: "Error disabling wharehouse" });
    }

    res.send({ message: "Wharehouse disabled successfully", data });
  });
};



// Contrôleur : Récupérer les entrepôts par marque
exports.findWarhousesByBrand = (req, res) => {
  const { id_brand,type } = req.params; // Récupération de l'id_brand depuis l'URL

  if (!id_brand || !type) {
    return res.status(400).send({ message: "L'ID de la marque est requis." });
  }

  Destination.findWarhousesByBrand(id_brand,type, (err, data) => {
    if (err) {
      res.status(500).send({
        message: err.message || "Une erreur est survenue lors de la récupération des entrepôts.",
      });
    } else {
      res.send(data);
    }
  });
};


exports.addWarehouse = (req, res) => {
  // Get data from the request body
  const { name, code, depot } = req.body;

  if (!name || !code) {
    res.status(400).send({
      message: "Name and Code are required!",
    });
    return;
  }

  Destination.addWarehouse({ name, code, depot }, (err, data) => {
    if (err)
      res.status(500).send({
        message: err.message || "Some error occurred while adding the warehouse.",
      });
    else res.send(data);
  });
};
