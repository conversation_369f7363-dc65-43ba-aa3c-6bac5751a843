import { HttpClient, HttpEvent, HttpHeaders, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';

import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
const httpOptions = {
    headers: new HttpHeaders({ 'content-type': 'application/json', Authorization: 'basic ' + btoa('med:123456') })
  };
  
  @Injectable({
    providedIn: 'root'
  })

  export class FournisseurService {
    apiURL = environment.apiURL
    constructor(private http: HttpClient) { }

      addFournisseur(data:any): Observable<any> {
        
        return this.http.post<any>(this.apiURL + 'fournisseur', data, httpOptions);
      }

      getAllFournisseur() : Observable<any> {
        return this.http.get<any>(this.apiURL + 'fournisseur', httpOptions)

      }

      findFournisseurByMatricule(matricule: string): Observable<any> {
        const encodedMatricule = encodeURIComponent(matricule);
        const url = `${this.apiURL}fournisseur/${encodedMatricule}`;
        return this.http.get(url, httpOptions);
      }

      findFournisseurByType(type: any): Observable<any> {
        const url = `${this.apiURL}getFou/${type}`;
        return this.http.get(url, httpOptions);
      }

      updateFournisseur(id: number, data: any): Observable<any> {
        const url = `${this.apiURL}fournisseur/${id}`;
        return this.http.put<any>(url, data, httpOptions);
      }
      
      findFournisseurWhithEntreposage() : Observable<any> {
        return this.http.get<any>(this.apiURL + 'findFournisseurWhithEntreposage', httpOptions)

      }

      findFournisseurWhithFLux() : Observable<any> {
        return this.http.get<any>(this.apiURL + 'findFournisseurWhithFLux', httpOptions)

      }
  
      findFournisseurById(id:any) : Observable<any> {
        return this.http.get<any>(this.apiURL + `fournisseurById/${id}`, httpOptions)

      }

      findClientFromSageByMat(mat:any) : Observable<any> {
        return this.http.get<any>(this.apiURL + `findClientFromSageByMat/${mat}`, httpOptions)

      }

      findClientToInvoiceTransport() : Observable<any> {
        return this.http.get<any>(this.apiURL + `findClientToInvoice`, httpOptions)

      }

      findClientInvoiced() : Observable<any> {
        return this.http.get<any>(this.apiURL + `findClientInvoiced`, httpOptions)

      }
      
      
  }