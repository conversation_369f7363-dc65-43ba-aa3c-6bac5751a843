import { Component, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { ShipmentService } from 'src/services/shipment.service';
import { FormConfig } from 'src/shared/models/form.models';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { DynamicExportButtonComponent } from 'src/shared/components/dynamic-export-button/dynamic-export-button.component';
import { DynamicFormComponent } from 'src/shared/components/dynamic-form/dynamic-form.component';
import { CommonModule } from '@angular/common';
import { ActionButton, TableColumn } from 'src/shared/models/table.models';
import { InspectionColisDetailsModalComponent } from './inspection-colis-details-modal/inspection-colis-details-modal.component';

@Component({
  selector: 'app-inspection-colis',
  standalone: true,
  imports: [SmartTableComponent,SmartContainerComponent,DynamicExportButtonComponent,DynamicFormComponent,CommonModule,InspectionColisDetailsModalComponent],
  templateUrl: './inspection-colis.component.html',
  styleUrl: './inspection-colis.component.scss'
})
export class InspectionColisComponent {
  @ViewChild('colisDetailsModal') colisDetailsModal!: InspectionColisDetailsModalComponent;
  showTable: boolean = false;
  shipments: any[] = [];
  loading: boolean = false;
  DataExportLabel : any = {
    date_debut : '',
    date_fin : ''
  }
  formConfig: FormConfig = {
    title: 'Inspection des colis',
    fieldGroups:[
      {
        fields: [
          {
            name: 'startDate',
            type: 'date',
            label: 'Date de début',
          },
          {
            name: 'endDate',
            type: 'date',
            label: 'Date de fin',
          },
          {
            name: 'status',
            type: 'select',
            label: 'Statut',
            options: {
              objectArray: [
                { value: '', label: 'Tout' },
                { value: 'en attente', label: 'En attente' },
                { value: 'en transit', label: 'En transit' },
                { value: 'livré', label: 'Livré' },
                { value: 'partiellement livré', label: 'Partiellement livré' }
              ],
              valueAttribute: 'value',
              labelAttribute: 'label'
            }
          }
        ]
      }
    ],
    buttons: [
      {
        label: 'Rechercher',
        color: 'primary',
        icon: 'cil-search',
        onClick: (formData:any) => this.chercher(formData)
      }
    ]
  }
  formConfig2 : FormConfig = {
    fields :[{
      name: 'barcode',
      type: 'text',
      label: 'Barcode',
      required: true,
      placeholder: 'pattern : 13 numéros',
      validation : {
        pattern:'^[0-9]{13}$'
      }
    }],
    buttons: [
      {
        label: 'Rechercher',
        color: 'primary',
        icon: 'cil-search',
        onClick: (formData:any) => this.chercher(formData)
      }
    ]
  }

  tableColumn : TableColumn[] = [
    {
      name: 'barcode',
      displayName: 'Barcode',
      sortable: true
    },
    {
      name: 'departure',
      displayName: 'Départ',
      sortable: true
    },
    {
      name: 'arrival',
      displayName: 'Arrivée',
      sortable: true
    },
    {
      name: 'last_reception',
      displayName: 'Dernière réception',
      sortable: true
    },
    {
      name: 'name_receptor',
      displayName: 'Nom du récepteur',
      sortable: true
    },
    {
      name: 'status',
      displayName: 'Statut',
      sortable: true,
      dataType: 'status',
      statusConfig: [
        {
          value: 'en attente',
          badgeColor: 'warning',
          displayText: 'En attente',
          icon: 'cil-clock'
        },
        {
          value: 'en transit',
          badgeColor: 'info',
          displayText: 'En transit',
          icon: 'cil-clock'
        },
        {
          value: 'livré',
          badgeColor: 'success',
          displayText: 'Livré',
          icon: 'cil-check'
        },
        {
          value: 'partiellement livré',
          badgeColor: 'danger',
          displayText: 'Partiellement livré',
          icon: 'cil-clock'
        }
      ]
    }
  ]

  tableAction : ActionButton[] = [
    {
      color: 'primary',
      icon: 'cilZoom',
      condition : (row: any) => { return row.last_reception!=null && row.name_receptor!=null},
      callback: (row: any) => {
        this.ViewDetails(row)
      }
    }
  ]

  ViewDetails(row: any) {
    this.colisDetailsModal.ColisId = row.id;
    this.colisDetailsModal.openModal();
  }

  constructor(
    private shipmentService: ShipmentService,
    private toastr: ToastrService
  ) {}

  chercher(formValue: any) {
    
    
    let args : any = {
      barcode: formValue.barcode? formValue.barcode : "",
      startDate: formValue.startDate? formValue.startDate : "",
      endDate: formValue.endDate? formValue.endDate : "",
      status: formValue.status? formValue.status : ""
    }
    console.log("args",args);
    this.DataExportLabel.date_debut = args.startDate;
    this.DataExportLabel.date_fin = args.endDate;
    if (this.validerForm(args)) {
      this.loadPackages(args);
    }
  }
  loadPackages(args: any) {
    console.log("args",args);
    this.loading = true;
    this.showTable = true;
    this.shipmentService.findPackages(args).subscribe({
      next: (data: any) => {
        this.shipments = data;
        console.log(this.shipments);
      },
      error: (error: any) => {
        console.error(error);
      },
      complete: () => {
        this.loading = false;
      }
    })
  }

  validerForm(args: any) : boolean {
    let errorMessage = "";
  
    switch (true) {
      case args.barcode && args.barcode.length !== 13:
        errorMessage = "Merci de vérifier le Code à barre";
        break;
  
      case (args.startDate && !args.endDate) || (!args.startDate && args.endDate):
        errorMessage = "Veuillez renseigner à la fois la date de début et la date de fin";
        break;
  
      case args.startDate && args.endDate && args.startDate > args.endDate:
        errorMessage = "La date de début ne peut pas être postérieure à la date de fin";
        break;
  
      case args.status && (!args.startDate || !args.endDate):
        errorMessage = "Le statut nécessite des dates de début et de fin.";
        break;
  
      case !args.barcode && !args.status && (!args.startDate || !args.endDate):
        errorMessage = "Veuillez renseigner au moins un filtre (Code-barres, Statut ou Dates)";
        break;
    }
  
    if (errorMessage) {
      this.toastr.error(errorMessage);
      return false;
    }
  
    return true;
  }

}
