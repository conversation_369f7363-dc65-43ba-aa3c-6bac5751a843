import { Component, OnInit } from '@angular/core';
import { LigneCmdService } from 'app/services/ligneCmd.service';
import { RegisterServiceService } from 'app/services/register-service.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-verification-list',
  templateUrl: './verification-list.component.html',
  styleUrls: ['./verification-list.component.scss']
})
export class VerificationListComponent implements OnInit {
  selectedOption: string = '';
  searchQuery: string = '';
  typeList : any =[]
  ligneList : any =[]
  selectedInterval=""
  toExport: any =[]
  searchById=""
  constructor(private ligneCmdService : LigneCmdService, 
    private registerService : RegisterServiceService,
    private toastr : ToastrService) { }

 async ngOnInit() {
    this.typeList = await this.registerService.getAlltypeCmd().toPromise();

  }

  async search() {
    console.log(this.selectedOption)
    if (this.selectedOption == '' ) {
      this.toastr.error("Merci de remplir les filtres");
  return;
}

  
    if (this.searchQuery !='' && this.searchQuery.length < 3 ) {
      this.toastr.error("La longueur minimale de la référence est de 3 caractères");
      return;
    }
  
  
      // Vérifier la requête et appeler le service
      const data = {
        type: this.selectedOption,
        sku: this.searchQuery
      };

    
  
      await this.ligneCmdService.findBySku(data).then(res => {
        this.ligneList = res;
        if (this.ligneList.length === 0) {
          this.toastr.error("Aucune donnée trouvée");
        }
      });
    
  }
  


  settings = {
    columns: {
      id: {
        title: 'ID'
      },
      nom_depart: {
        title: 'Départ'
      },
      nom_arrivee: {
        title: 'Arrivée'
      },
      sku: {
        title: 'Référence'
      },
      status: {
        title: 'Statut'
      },
      date_creation: {
        title: 'Date de création'
      },
      quantite: {
        title: 'Quantité'
      },
      volume: {
        title: 'Volume'
      },
      estimation: {
        title: 'Volume Estimé'
      },
      ajoutee_par: {
        title: 'Demandeur'
      },
      date_voyage: {
        title: 'Date Voyage'
      },
      conducteur_nom_complet: {
        title: 'Conducteur '
      },
    },
    actions: {
      add: false,
      edit: false,
      delete: false
    }
  };
  
  
  exportToExcel() {
    if (this.selectedInterval === '') {
      this.toastr.error("Merci de donner l'historique de recherche");
    } else {
      this.ligneCmdService.findLignesByweek(this.selectedInterval).subscribe((res: any) => {
        const excelData = this.generateExcel(res.data);
        this.downloadExcel(excelData);
      });
    }
  }
  
  generateExcel(data: { [key: string]: any }[]) {
    let excelContent = '<?xml version="1.0"?>';
    excelContent += '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">';
    excelContent += '<Worksheet ss:Name="Sheet1"><Table>';
  
    // Mappage des anciens noms aux nouveaux noms des en-têtes
    const headerMapping: { [key: string]: string } = {
      'id': 'ID',
      'nom_depart': 'Depart',
      'nom_arrivee': 'Arrivee',
      'sku': 'Ref',
      'status': 'Status',
      'date_creation': 'Date Creation',
      'estimation': 'Volume estime',
      'volume': 'Volume',
      'quantite': 'Quantite',
      'ajoutee_par': 'Demandeur',
      'type_ligne': 'Type',
      'date_voyage': 'Date Voyage',
      'nom_fournisseur': 'Client',
      'prix_tot': 'Prix Totale',

    };
  
    // Ajoutez les en-têtes
    excelContent += '<Row>';
    const keys = Object.keys(data[0]);
    keys.forEach((key) => {
      const sanitizedKey = headerMapping[key] || key;
      excelContent += `<Cell><Data ss:Type="String">${sanitizedKey}</Data></Cell>`;
    });
    excelContent += '</Row>';
  
    // Ajoutez les données
    data.forEach((row) => {
      excelContent += '<Row>';
      keys.forEach((key) => {
        const value = row[key];
        const sanitizedValue = (typeof value === 'string') ? value.replace(/[éè]/g, 'e') : value;
        excelContent += `<Cell><Data ss:Type="String">${sanitizedValue || ''}</Data></Cell>`;
      });
      excelContent += '</Row>';
    });
  
    excelContent += '</Table></Worksheet></Workbook>';
    return excelContent;
  }
  
  
  
  
  
  downloadExcel(excelData: string) {
    const blob = new Blob([excelData], { type: 'application/vnd.ms-excel' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'extrait.xls';
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }

  findById(){
    this.ligneCmdService.findLignesByIdToVerification(this.searchById).subscribe(res=>{
      this.ligneList = res;
        if (this.ligneList.length === 0) {
          this.toastr.error("Aucune donnée trouvée");
        }
    })
  }
  

}
