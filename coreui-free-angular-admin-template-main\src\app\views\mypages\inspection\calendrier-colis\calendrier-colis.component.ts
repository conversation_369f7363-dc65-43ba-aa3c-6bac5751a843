import { 
  Component, 
  ChangeDetectionStrategy, 
  ViewChild, 
  TemplateRef,
  OnInit
} from '@angular/core';
import { 
  isSameDay, 
  isSameMonth,
  startOfDay
} from 'date-fns';
import { Subject } from 'rxjs';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { 
  CalendarEvent,
  CalendarView
} from 'angular-calendar';
import { Router } from '@angular/router';
import { ShipmentService } from '../../../../../services/shipment.service';
import { DatePipe } from '@angular/common';
import { CommonModule } from '@angular/common';
import { CalendarModule } from 'angular-calendar';
import { DateAdapter } from 'angular-calendar';
import { NgbModalModule } from '@ng-bootstrap/ng-bootstrap';
import { NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';
import { NgbTimepickerModule } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule } from '@angular/forms';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';
import { TableColumn, TableConfig } from 'src/shared/models/table.models';
import { DynamicExportButtonComponent } from '../../../../../shared/components/dynamic-export-button/dynamic-export-button.component';
import { SmartContainerComponent } from '../../../../../shared/components/smart-container/smart-container.component';
import { SmartButtonComponent } from '../../../../../shared/components/smart-button/smart-button.component';
import { IconModule } from '@coreui/icons-angular';

@Component({
  selector: 'app-calendar',
  standalone: true,
  imports: [
    CommonModule,
    CalendarModule,
    NgbModalModule,
    NgbDatepickerModule,
    NgbTimepickerModule,
    FormsModule,
    DynamicExportButtonComponent,
    SmartTableComponent,
    SmartContainerComponent,
    SmartButtonComponent,
    IconModule
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  templateUrl: './calendrier-colis.component.html',
  styleUrls: ['./calendrier-colis.component.scss']
})
export class CalendrierColisComponent implements OnInit {
  @ViewChild('modalContent') modalContent!: TemplateRef<any>;
  
  view: CalendarView = CalendarView.Month;
  CalendarView = CalendarView;
  viewDate: Date = new Date();
  refresh = new Subject<void>();
  activeDayIsOpen = true;
  events: CalendarEvent[] = [];
  isLoading = false;
  statData: any[] = [];
  packageList: any[] = [];
  title = '';
  date: Date = new Date();
  statsParJour: any[] = [];

  TableColumns: TableColumn[] = [
    {
      name: 'id',
      displayName: 'Identifiant'
    },
    {
      name: 'departure',
      displayName: 'Ville de départ'
    },
    {
      name: 'arrival',
      displayName: "Ville d'arrivée"
    },
    {
      name: 'last_reception',
      displayName: 'Dernière mise à jour'
    },
    {
      name: 'name_receptor',
      displayName: 'Dernier réceptionnaire'
    }
  ];
  TableConfig: TableConfig = {
    pageSize: 50,
    pageSizeOptions: [50, 100, 200],   
  }

  constructor(
    private modal: NgbModal,
    private router: Router,
    private shipmentService: ShipmentService,
    private datePipe: DatePipe
  ) { }

  async ngOnInit() {
    await this.getStatisticPackage();
    await this.loadStats();
  }

  async getStatisticPackage() {
    this.isLoading = true;
    this.shipmentService.getStatisticPackage().subscribe({
      next: (res) => {
        this.statData = res;
        this.isLoading = false;
      },
      error: () => this.isLoading = false
    });
  }

  async loadStats() {
    this.isLoading = true;
    this.shipmentService.getHistoricalStatusStats().subscribe({
      next: (res) => {
        this.statsParJour = res;
        this.generateEvents();
        this.viewDate = new Date();
        this.refresh.next();
      },
      error: (err) => console.error("Erreur:", err),
      complete: () => this.isLoading = false
    });
  }

  private generateEvents(): void {
    const statusColors = this.getStatusColors();
    const now = new Date();
    const past30Days = new Date();
    past30Days.setDate(now.getDate() - 30);
  
    this.events = this.statsParJour
      .filter(entry => this.isWithinDateRange(entry.jour, past30Days, now))
      .reduce((acc: CalendarEvent[], entry) => {
        const dailyEvents = this.mapStatutsToEvents(entry, statusColors);
        return [...acc, ...dailyEvents];
      }, []);
  }
  
  private getStatusColors(): { [key: string]: { primary: string; secondary: string } } {
    return {
      "livré": { primary: '#28a745', secondary: '#c3e6cb' },
      "en attente": { primary: '#ffc107', secondary: '#fff3cd' },
      "en transit": { primary: '#17a2b8', secondary: '#d1ecf1' },
      "partiellement livré": { primary: '#fd7e14', secondary: '#ffe5b4' }
    };
  }
  
  private isWithinDateRange(dateStr: string, start: Date, end: Date): boolean {
    const date = new Date(dateStr);
    return date >= start && date <= end;
  }
  
  private mapStatutsToEvents(entry: any, statusColors: any): CalendarEvent[] {
    return Object.entries(entry.statuts).map(([statut, count]) => ({
      title: `${count} colis ${statut}`,
      start: new Date(entry.jour),
      allDay: true,
      color: statusColors[statut] || { primary: '#6c757d', secondary: '#e2e3e5' },
      meta: { 
        statut, 
        count: count as number 
      }
    }));
  }

  dayClicked({ date, events }: { date: Date; events: CalendarEvent[] }): void {
    if (isSameMonth(date, this.viewDate)) {
      if (
        (isSameDay(this.viewDate, date) && this.activeDayIsOpen === true) ||
        events.length === 0
      ) {
        this.activeDayIsOpen = false;
      } else {
        this.activeDayIsOpen = true;
        this.viewDate = date;
      }
    }
  }

  async handleEvent(action: string, event: CalendarEvent) {
    this.date = event.start;
    const formattedDate = this.datePipe.transform(this.date, 'dd/MM/yyyy', 'fr-FR');
    this.title = `${event.title} le ${formattedDate}`;

    const data = {
      date: formattedDate,
      status: event.meta.statut
    };

    this.isLoading = true;

    try {
      this.shipmentService.getPackageByStatusAndDate(data).subscribe(res => {
        this.packageList = res;
        this.isLoading = false;
        this.modal.open(this.modalContent,{size: 'lg',
          windowClass: 'modal-lg'});
      });  
    } catch (error) {
      console.error('Erreur:', error);
      this.isLoading = false;
    }
  }

  downloadExcel() {
    try {
      let excelContent = 'Code-barres\tDepart\tCode Depart\tArrivee\tCode Arrivee\tDerniere reception\tRecepteur\tStatut\tCree le\n';

      this.packageList.forEach(pkg => {
        excelContent += `${pkg.barcode || ''}\t${this.replaceSpecialChars(pkg.departure || '')}\t${pkg.code_departure || ''}\t${this.replaceSpecialChars(pkg.arrival || '')}\t${pkg.code_arrival || ''}\t${pkg.last_reception || ''}\t${this.replaceSpecialChars(pkg.name_receptor || '')}\t${this.replaceSpecialChars(pkg.status || '')}\t${pkg.created_at || ''}\n`;
      });

      const blob = new Blob([excelContent], { type: 'application/vnd.ms-excel;charset=utf-8' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${this.title.replace(/[^a-zA-Z0-9]/g, '_')}.xls`);
      document.body.appendChild(link);
      link.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Erreur Excel:', error);
    }
  }

  replaceSpecialChars(str: string | null | undefined): string {
    if (!str) return '';
    return str.replace(/[éèô/]/g, char => {
      switch (char) {
        case 'é':
        case 'è': return 'e';
        case 'ô': return 'o';
        case '/': return '-';
        default: return char;
      }
    });
  }

  redirectToInspection(status: string, date_min: string, date_max: string): void {
    const dateMinOnly = date_min.substring(0, 10);
    const dateMaxOnly = date_max.substring(0, 10);

    const confirmation = window.confirm(
      'Vous allez quitter cette page pour consulter les détails.\nVoulez-vous continuer ?'
    );

    if (confirmation) {
      this.router.navigate(['/pages/inspectColis'], {
        queryParams: {
          status: status,
          date_min: dateMinOnly,
          date_max: dateMaxOnly
        }
      });
    }
  }
}