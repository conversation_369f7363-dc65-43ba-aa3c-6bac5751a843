import { Component, OnInit } from '@angular/core';
import { ModalDismissReasons, NgbModal } from '@ng-bootstrap/ng-bootstrap';
//import { NGXToastrService } from 'app/components/extra/toastr/toastr.service';
import { RegisterServiceService } from 'app/services/register-service.service';
import { ToastrService } from 'ngx-toastr';


@Component({
  selector: 'app-list-conducteur',
  templateUrl: './list-conducteur.component.html',
  providers: []

})
export class ListConducteurComponent implements OnInit {
  source = [];
  closeResult = '';
  detail = 0;



  constructor(private toastr: ToastrService, private listconducteurService: RegisterServiceService, private modalService: NgbModal) { }

  ngOnInit() {
    this.listconducteurService.findConducteur().subscribe(data => {
      this.source = data;
    })

  }

  settings = {
    actions: {
      add: false,
      edit: false,
    },
    columns: {
      transporteur:{
        title : 'transporteur'
      },
      nom: {
        title: 'Nom',
      },
      prenom: {
        title: 'Prénom',
      },
      cin: {
        title: 'Cin ',
      },
      mobile: {
        title: 'Mobile',
      },
      // image_cin: {
      //   title: 'Image CIN',
      //   type: 'html',
      //   valuePrepareFunction: (picture: string) => {

      //     return `<img width="50px" width:"50px" src="https://zen-logistic.com//uploads//` + picture + `" />`;
      //   },
      // },
    },
    // attr : les lignes dans le tableau 
    attr: {
      class: "table table-responsive"
    },



    delete: {
      confirmDelete: true,
      deleteButtonContent: '<i class="ft-x danger font-medium-1 mr-2"></i>',
    }
  };

  //  For confirm action On Delete

  onDeleteConfirm(event) {
    if (window.confirm('Etes-vous sûr que vous voulez supprimer ce conducteur ?')) {
      this.listconducteurService.deleteConducteur(event.data.id).subscribe(data => {

        event.confirm.resolve();
        this.delete(event);
        this.toastr.success('Conducteur supprimé avec succés', 'Success!');
        this.listconducteurService.findAllConducteur(sessionStorage.getItem('iduser')).then(data => {
          this.source = data;
        })

      }), error => {  }

    } else {
      event.confirm.reject();
    }
  }
  delete(event) {
    //console.log(event.data.id) ; 

  }


  onUserRowSelect(event, content): void {
    this.open(content);
    this.findConducteur(event);

  }
  // par défaut m3a el popo uppa 
  open(content) {
    this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      this.closeResult = `Closed with: ${result}`;
    }, (reason) => {
      this.closeResult =
        `Dismissed ${this.getDismissReason(reason)}`;
    });
  }

  // aparament par défaut m3a el pop appa 
  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }

  }

  findConducteur(event) {
    this.detail = event.data
  };



}
