import { HttpClient, HttpEvent, HttpHeaders, HttpRequest, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';
import { promise } from 'protractor';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
const httpOptions = {
    headers: new HttpHeaders({ 'content-type': 'application/json', Authorization: 'basic ' + btoa('med:123456') })
  };
  
  @Injectable({
    providedIn: 'root'
  })

  export class LigneCmdService {
    apiURL = environment.apiURL
    constructor(private http: HttpClient) { }

    addLigne(data): Promise<any> {
        const endpoint = `${this.apiURL}ptchargement`;
        return this.http.post<any>(endpoint, data, httpOptions)
          .toPromise();
      }
      

      updateVolume(id: number, newVolume: number): Promise<any> {
        const url = `${this.apiURL}ptchargement/${id}`;
        const body = { volume: newVolume };
    
        return this.http.put(url, body, httpOptions)
          .toPromise()
          .then(response => response)
          .catch(this.handleError);
      }

      updateStatusValid(id: number): Promise<any> {
        const url = `${this.apiURL}ptchargement/valid/${id}`;
    
        return this.http.put(url,null, httpOptions)
          .toPromise()
          .then(response => response)
          .catch(this.handleError);
      }


      updateStatusNoPickup(id: number): Promise<any> {
        const url = `${this.apiURL}ptchargement/updateStatusNoPickup/${id}`;
    
        return this.http.put(url,null, httpOptions)
          .toPromise()
          .then(response => response)
          .catch(this.handleError);
      }


      updateStatusReserved(data: { updates: any[] }): Promise<any> {
        const url = `${this.apiURL}ptchargement/reserved`;
    
        return this.http.post(url,data, httpOptions)
          .toPromise()
          .then(response => response)
          .catch(this.handleError);
      }




    
      private handleError(error: any): Promise<any> {
        console.error('An error occurred', error);
        return Promise.reject(error.message || error);
      }
     

      findAllValid(): Observable<any> {
        
        return this.http.get<any>(this.apiURL + `ptchargement/valid/`, httpOptions);
      }


      findReservedByDateAndCamion(data: any): Observable<any> {
        // Créez un objet HttpParams pour stocker les paramètres de requête
        let params = new HttpParams();
    
        // Ajoutez les paramètres à l'objet HttpParams
        if (data.id_camion) {
          params = params.set('id_camion', data.id_camion);
        }
        if (data.date) {
          params = params.set('date', data.date);
        }
        if (data.id_conducteur) {
          params = params.set('id_conducteur', data.id_conducteur);
        }
    
        // Ajoutez les paramètres de requête à l'URL
        const url = this.apiURL + 'pt/reserved';
        
        // Utilisez l'objet HttpParams dans la requête
        return this.http.get<any>(url, { headers: httpOptions.headers, params });
      }


      findExpediedByDateAndCamion(data: any): Observable<any> {
        // Créez un objet HttpParams pour stocker les paramètres de requête
        let params = new HttpParams();
    
        // Ajoutez les paramètres à l'objet HttpParams
        if (data.id_camion) {
          params = params.set('id_camion', data.id_camion);
        }
        if (data.date) {
          params = params.set('date', data.date);
        }
        if (data.id_conducteur) {
          params = params.set('id_conducteur', data.id_conducteur);
        }
    
        // Ajoutez les paramètres de requête à l'URL
        const url = this.apiURL + 'pt/expedied';
        
        // Utilisez l'objet HttpParams dans la requête
        return this.http.get<any>(url, { headers: httpOptions.headers, params });
      }


      findLineToAdjust(data: any): Observable<any> {
        // Créez un objet HttpParams pour stocker les paramètres de requête
        let params = new HttpParams();
    
        // Ajoutez les paramètres à l'objet HttpParams
        if (data.id_camion) {
          params = params.set('id_camion', data.id_camion);
        }
        if (data.date) {
          params = params.set('date', data.date);
        }
        if (data.id_conducteur) {
          params = params.set('id_conducteur', data.id_conducteur);
        }
    
        // Ajoutez les paramètres de requête à l'URL
        const url = this.apiURL + 'pt/findLineToAdjust';
        
        // Utilisez l'objet HttpParams dans la requête
        return this.http.get<any>(url, { headers: httpOptions.headers, params });
      }


      


      updateStatusReseration(id : any,data:any): Promise<any> {
        const url = `${this.apiURL}ptchargement/reservation/${id}`;
    
        return this.http.put(url,data, httpOptions)
          .toPromise()
          .then(response => response)
          .catch(this.handleError);
      }


      updateStatusExpedition(data:any): Promise<any> {

        console.log(data)
        const url = `${this.apiURL}ptchargement/expedition/${data.id}`;
    
        return this.http.put(url,data, httpOptions)
          .toPromise()
          .then(response => response)
          .catch(this.handleError);
      }


      updateStatusLivred(id:any): Promise<any> {

        const url = `${this.apiURL}ptchargement/livred/${id}`;
    
        return this.http.put(url,null,httpOptions)
          .toPromise()
          .then(response => response)
          .catch(this.handleError);
      }

      

      findLivredByDateAndClient(data: any): Observable<any> {
        // Créez un objet HttpParams pour stocker les paramètres de requête
        let params = new HttpParams();
    
        // Ajoutez les paramètres à l'objet HttpParams
        if (data.date_debut) {
          params = params.set('date_debut', data.date_debut);
        }
        if (data.date_fin) {
          params = params.set('date_fin', data.date_fin);
        }
        if (data.id_client) {
          params = params.set('id_client', data.id_client);
        }
    
        // Ajoutez les paramètres de requête à l'URL
        const url = this.apiURL + 'ligneCmd/toFac';
        
        // Utilisez l'objet HttpParams dans la requête
        return this.http.get<any>(url, { headers: httpOptions.headers, params });
      }


      findLivredByDateAndType(data: any): Observable<any> {
        // Créez un objet HttpParams pour stocker les paramètres de requête
        let params = new HttpParams();
    
        // Ajoutez les paramètres à l'objet HttpParams
        if (data.date_debut) {
          params = params.set('date_debut', data.date_debut);
        }
        if (data.date_fin) {
          params = params.set('date_fin', data.date_fin);
        }
        if (data.type_ligne) {
          params = params.set('type_ligne', data.type_ligne);
        }
    
        // Ajoutez les paramètres de requête à l'URL
        const url = this.apiURL + 'ligneCmd/type';
        
        // Utilisez l'objet HttpParams dans la requête
        return this.http.get<any>(url, { headers: httpOptions.headers, params });
      }



      updateClient(data:any): Promise<any> {

        console.log(data)
        const url = `${this.apiURL}ptchargement/client/${data.id}`;
    
        return this.http.put(url,data, httpOptions)
          .toPromise()
          .then(response => response)
          .catch(this.handleError);
      }



      findReservedByIdUser(id): Observable<any> {
        
        return this.http.get<any>(this.apiURL + `ligneCmdByDemand/${id}`, httpOptions);
      }
      findExpediedByIdUser(id): Observable<any> {
        
        return this.http.get<any>(this.apiURL + `ligneCmdExpByDemand/${id}`, httpOptions);
      }
      ligneCmdLivredByDemand(id): Observable<any> {
        
        return this.http.get<any>(this.apiURL + `ligneCmdLivredByDemand/${id}`, httpOptions);
      }

      countValid(): Observable<any> {
        
        return this.http.get<any>(this.apiURL + `countValid`, httpOptions);
      }
      countReserved(): Observable<any> {
        
        return this.http.get<any>(this.apiURL + `countReserved`, httpOptions);
      }

      countExpedied(): Observable<any> {
        
        return this.http.get<any>(this.apiURL + `countExpedied`, httpOptions);
      }
      
      countDelivred(): Observable<any> {
        
        return this.http.get<any>(this.apiURL + `countDelivred`, httpOptions);
      }

      findVoyageListRes(): Observable<any> {
        
        return this.http.get<any>(this.apiURL + `findVoyageList`, httpOptions);
      }

      findVoyageListExp(): Observable<any> {
        
        return this.http.get<any>(this.apiURL + `findVoyageListExp`, httpOptions);
      }

      findVoyageListToAdjust(): Observable<any> {
        
        return this.http.get<any>(this.apiURL + `findVoyageListToAdjust`, httpOptions);
      }


      CountVolumeParVoyage(id_camion: any, id_conducteur: any, date_voyage: any): Promise<any> {
        return this.http.get<any>(this.apiURL + `volumeBytrip/${id_camion}/${id_conducteur}/${date_voyage}`, httpOptions)
          .toPromise();
      }
      
      findBySku(data): Promise<any> {
        const endpoint = `${this.apiURL}searchBysku`;
        return this.http.post<any>(endpoint, data, httpOptions)
          .toPromise();
      }


      findAllLivred(): Observable<any> {
        
        return this.http.get<any>(this.apiURL + `findAllLivred`, httpOptions);
      }

      findAllAdjusted(): Observable<any> {
        
        return this.http.get<any>(this.apiURL + `findAllAdjusted`, httpOptions);
      }

      updateStatusAfacture(id : any): Promise<any> {
        const url = `${this.apiURL}updateStatusToBeInvoiced/${id}`;
    
        return this.http.put(url,null, httpOptions)
          .toPromise()
          .then(response => response)
          .catch(this.handleError);
      }

      SendSms(data): Promise<any> {
        const endpoint = `${this.apiURL}ptchargement/sendSms`;
        return this.http.post<any>(endpoint, data, httpOptions)
          .toPromise();
      }

      SendMailNotification(): Promise<any> {
        const endpoint = `${this.apiURL}commande/infoAdmin`;
        return this.http.post<any>(endpoint, null, httpOptions)
          .toPromise();
      }
      
      findToInspection(): Promise<any> {
        return new Promise((resolve, reject) => {
          this.http.get<any>(`${this.apiURL}findInspection`, httpOptions)
            .toPromise()
            .then((res) => {
              resolve(res);
            })
            .catch((error) => {
              reject(error);
            });
        });
      }
      


      findAllToInspection(): Promise<any> {
        return this.http.get<any>(this.apiURL + `findAllToInspection`, httpOptions)
          .toPromise();
      }
      


      updatePieceCegid(id: number, num_piece: number): Promise<any> {
        const url = `${this.apiURL}updatePieceCegid/${id}`;
        const body = { comment: num_piece };
    
        return this.http.put(url, body, httpOptions)
          .toPromise()
          .then(response => response)
          .catch(this.handleError);
      }

      updateComment(id: number, comment: number): Promise<any> {
        const url = `${this.apiURL}updatePieceCegid/${id}`;
        const body = { comment: comment };
    
        return this.http.put(url, body, httpOptions)
          .toPromise()
          .then(response => response)
          .catch(this.handleError);
      }
      
      

      findLigneDelivredByTruck(data): Observable<any> {
        
        return this.http.post<any>(this.apiURL + `findDelivredByTruck`,data,httpOptions);
      }

      findLigneDelivredByDriver(data): Observable<any> {
        
        return this.http.post<any>(this.apiURL + `findLigneDelivredByDriver`,data,httpOptions);
      }

      findLignesByweek(intervale): Observable<any> {
        
        return this.http.get<any>(this.apiURL + `findLignesByweek/${intervale}`,httpOptions);
      }

      findReservedByChef(idChef): Observable<any> {
        
        return this.http.get<any>(this.apiURL + `findReservedByChef/${idChef}`,httpOptions);
      }

      findExpediedByChef(idChef): Observable<any> {
        
        return this.http.get<any>(this.apiURL + `findExpediedByChef/${idChef}`,httpOptions);
      }

      findDeliveredByChef(idChef): Observable<any> {
        return this.http.get<any>(this.apiURL + `findDeliveredByChef/${idChef}`,httpOptions);
      }

      findAllValidWithLocalisation(): Observable<any> {
        return this.http.get<any>(this.apiURL + `findAllValidWithLocalisation`,httpOptions);
      }

      getLigneByDestinationAndUser(id): Observable<any> {
        return this.http.get<any>(this.apiURL + `getLigneByDestinationAndUser/${id}`,httpOptions);
      }

      getLigneByDestinationAndUserLiv(id): Observable<any> {
        return this.http.get<any>(this.apiURL + `getLigneByDestinationAndUserLiv/${id}`,httpOptions);
      }
      
      
      

      findRetard(data?: any): Observable<any> {
        return this.http.post<any>(`${this.apiURL}findRetard`, data, httpOptions);
      }

      getProductivity(data?: any): Observable<any> {
        return this.http.post<any>(`${this.apiURL}getProductivity`, data, httpOptions);
      }

      MoyVolumePerVoyage(data?: any): Observable<any> {
        return this.http.post<any>(`${this.apiURL}MoyVolumePerVoyage`, data, httpOptions);
      }
      
      voyagePerDestination(data?: any): Observable<any> {
        return this.http.post<any>(`${this.apiURL}voyagePerDestination`, data, httpOptions);
      }
      getIndicateurByConducteur(data?: any): Observable<any> {
        return this.http.post<any>(`${this.apiURL}getIndicateurByConducteur`, data, httpOptions);
      }
      updateStatusCancel(id: number): Promise<any> {
        const url = `${this.apiURL}ptchargement/cancel/${id}`;
    
        return this.http.put(url,null, httpOptions)
          .toPromise()
          .then(response => response)
          .catch(this.handleError);
      }


      updatePanne(id: number): Promise<any> {
        const url = `${this.apiURL}ptchargement/updatePanne/${id}`;
    
        return this.http.put(url,null, httpOptions)
          .toPromise()
          .then(response => response)
          .catch(this.handleError);
      }

      findAllRetourAndInter(): Observable<any> {
        return this.http.get<any>(this.apiURL + `findAllRetourAndInter`,httpOptions);
      }

      updateVolumeWithXls(data: any): Promise<any> {
        const url = `${this.apiURL}updateVolumeXls`;
    
        return this.http.post(url,data, httpOptions)
          .toPromise()
          .then(response => response)
          .catch(this.handleError);
      }

      
      // adjustPrice(data: any): Observable<any> {
      //   return this.http.post<any>(`${this.apiURL}adjustPrice`, data, httpOptions);
      // }

      fixPrice(data: any): Observable<any> {
        return this.http.post<any>(`${this.apiURL}fixPrice`, data, httpOptions);
      }
     
      findBydestinationsAndDateVaoyage(data: any): Observable<any> {
        return this.http.post<any>(`${this.apiURL}ptchargement/findByDestinationsAndDateVoyage`, data, httpOptions);
      }


      updateVolumeColisage(data: any): Promise<any> {
        const url = `${this.apiURL}ptchargement/updateVolumeColisage`;
        return this.http.post(url,data, httpOptions)
          .toPromise()
          .then(response => response)
          .catch(this.handleError);
      }

      FindLignepdfNotUpdated (): Observable<any> {
        return this.http.get<any>(this.apiURL + `pdfNotUpdated`,httpOptions);
      }

      findLignesByIdVoyage(id): Observable<any> {
        
        return this.http.get<any>(this.apiURL + `findLignesByIdVoyage/${id}`, httpOptions);
      }

      updateStatusReady(data:any): Promise<any> {
        const url = `${this.apiURL}ptchargement/updateStatusReady`;
        return this.http.post(url,data, httpOptions)
          .toPromise()
          .then(response => response)
          .catch(this.handleError);
      }

      
      findLignesByIdToVerification(id): Observable<any> {
        return this.http.get<any>(this.apiURL + `findLignesByIdToVerification/${id}`,httpOptions);
      }


      

      
      
  }