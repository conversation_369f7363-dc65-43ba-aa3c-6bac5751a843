import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';

const httpOptions = {
  headers: new HttpHeaders({
    'Content-Type': 'application/json',
    'Authorization': 'Basic ' + btoa('med:123456')
  })
};
@Injectable({
  providedIn: 'root'
})
export class FactureService {

  private apiURL = environment.apiURL;

  constructor(private http: HttpClient) { }

  getAllFacture(page: number, pageSize: number, idClient?: any, invoiceDate?: string): Observable<any> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('pageSize', pageSize.toString());
  
    if (idClient) {
      params = params.set('id_client', idClient.toString());
    }
  
    if (invoiceDate) {
      params = params.set('invoice_date', invoiceDate);
    }
  
    return this.http.get<any>(`${this.apiURL}allfacture/`, { ...httpOptions,params });
  }

  getAllFactureAllPages(idClient?: any, invoiceDate?: string): Observable<any[]> {
    const pageSize = 10; 
    let page = 1;
    let allData: any[] = [];
  
    return new Observable(observer => {
      const fetchPage = () => {
        this.getAllFacture(page, pageSize, idClient, invoiceDate).subscribe(result => {
          allData = allData.concat(result.data);
          if (allData.length < result.totalRows) {
            page++;
            fetchPage();
          } else {
            observer.next(allData);
            observer.complete();
          }
        }, error => observer.error(error));
      };
      fetchPage();
    });
  }

  getFacture(id: any): Observable<any> {
    return this.http.get<any>(this.apiURL + 'facture/' + id, httpOptions)
  }
}
