import { Routes } from '@angular/router';
import { authGuard } from '../guards/auth.guard';

export const routes: Routes = [
  {
    path: '',
    redirectTo: 'home',
    pathMatch: 'full'
  },
  {
    path: '',
    loadComponent: () => import('./layout').then(m => m.DefaultLayoutComponent),
    canActivate: [authGuard],
    data: {
      title: 'Home'
    },
    children: [
      {
        path: '',
        loadChildren: () => import('./views/mypages/routes').then((m) => m.routes),
        data: {
          title: ''
        }
      },
      {
        path: 'home',
        loadChildren: () => import('./views/mypages/home/<USER>').then((m) => m.routes),
      },
      {
        path: 'profile',
        loadComponent: () => import('./views/pages/profile/profile.component').then(m => m.ProfileComponent),
        data: {
          title: 'Profile'
        }
      },
      {
        path: 'componenttest',
        loadComponent: () => import('./views/mypages/testSharedComponent/component-test-wrapper-component/component-test-wrapper-component.component')
        .then(m => m.ComponentTestWrapperComponent),
        data: {
          title: 'Component Test'
        }
      },
      {
        path: 'dashboard',
        loadChildren: () => import('./views/dashboard/routes').then((m) => m.routes)
      },
      
      {
        path: 'theme',
        loadChildren: () => import('./views/theme/routes').then((m) => m.routes)
      },
      {
        path: 'base',
        loadChildren: () => import('./views/base/routes').then((m) => m.routes)
      },
      {
        path: 'buttons',
        loadChildren: () => import('./views/buttons/routes').then((m) => m.routes)
      },
      {
        path: 'forms',
        loadChildren: () => import('./views/forms/routes').then((m) => m.routes)
      },
      {
        path: 'icons',
        loadChildren: () => import('./views/icons/routes').then((m) => m.routes)
      },
      {
        path: 'notifications',
        loadChildren: () => import('./views/notifications/routes').then((m) => m.routes)
      },
      {
        path: 'widgets',
        loadChildren: () => import('./views/widgets/routes').then((m) => m.routes)
      },
      {
        path: 'charts',
        loadChildren: () => import('./views/charts/routes').then((m) => m.routes)
      },
      {
        path: 'pages',
        loadChildren: () => import('./views/pages/routes').then((m) => m.routes)
      },
      {
        path: 'commandes',
        loadChildren: () => import('./views/mypages/commandes/routes').then((m) => m.routes),
        data: {
          title: 'Commandes'
        }
      },
      {
        path: 'inspection',
        loadChildren: () => import('./views/mypages/inspection/routes').then((m) => m.routes),
        data: {
          title: 'Inspection'
        }
      }
    ]
  },
  {
    path: 'unauthorized',
    loadComponent: () => import('./views/pages/unauthorized/unauthorized.component').then(m => m.AnuthorizedComponent),
    data: {
      title: 'Unauthorized'
    }
  },
  {
    path: '404',
    loadComponent: () => import('./views/pages/page404/page404.component').then(m => m.Page404Component),
    data: {
      title: 'Page 404'
    }
  },
  {
    path: '500',
    loadComponent: () => import('./views/pages/page500/page500.component').then(m => m.Page500Component),
    data: {
      title: 'Page 500'
    }
  },
  {
    path: 'login',
    loadComponent: () => import('./views/pages/login/login.component').then(m => m.LoginComponent),
    data: {
      title: 'Login Page'
    }
  },
  {
    path: 'register',
    loadComponent: () => import('./views/pages/register/register.component').then(m => m.RegisterComponent),
    data: {
      title: 'Register Page'
    }
  },
  { path: '**', redirectTo: 'dashboard' }
];
