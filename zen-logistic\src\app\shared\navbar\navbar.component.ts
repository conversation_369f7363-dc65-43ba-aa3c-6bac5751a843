import { Component, Output, EventEmitter, OnDestroy, OnInit, AfterViewInit, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { LayoutService } from '../services/layout.service';
import { Subscription } from 'rxjs';
import { ConfigService } from '../services/config.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ActivatedRoute, Router } from '@angular/router';
import { NotificationService } from 'app/services/notification.service';
import { formatDate } from '@angular/common';

@Component({
  selector: "app-navbar",
  templateUrl: "./navbar.component.html",
  styleUrls: ["./navbar.component.scss"]
})
export class NavbarComponent implements OnInit, AfterViewInit, OnDestroy {
  currentLang = "en";
  toggleClass = "ft-maximize";
  placement = "bottom-right";
  public isCollapsed = true;
  layoutSub: Subscription;
  @Output() toggleHideSidebar = new EventEmitter<Object>();

  public config: any = {};
  public notifications: any[] = []; // Array to hold notifications
  public unreadCount: number = 0; // To track unread notifications
  private notificationFetchInterval: any; // To hold the interval ID

  constructor(
    
    public translate: TranslateService,
    private layoutService: LayoutService,
    private configService: ConfigService,
    private modal: NgbModal,
    private router: Router,
    private route: ActivatedRoute,
    private notificationService: NotificationService
  ) {
    const browserLang: string = translate.getBrowserLang();
    translate.use(browserLang.match(/en|es|pt|de/) ? browserLang : "en");

    this.layoutSub = layoutService.changeEmitted$.subscribe(direction => {
      const dir = direction.direction;
      this.placement = dir === "rtl" ? "bottom-left" : "bottom-right";
    });
  }

  ngOnInit() {
    this.config = this.configService.templateConf;
    const userId = sessionStorage.getItem("iduser"); // Get userId from session storage
    this.fetchNotifications(userId); // Fetch notifications on init
    this.startFetchingNotifications(userId); // Start fetching notifications every 30 minutes
    this.startSSEConnection(); // Start real-time notifications via SSE
  }

  ngAfterViewInit() {
    if (this.config.layout.dir) {
      setTimeout(() => {
        const dir = this.config.layout.dir;
        this.placement = dir === "rtl" ? "bottom-left" : "bottom-right";
      }, 0);
    }
  }

  ngOnDestroy() {
    if (this.layoutSub) {
      this.layoutSub.unsubscribe();
    }
    if (this.notificationFetchInterval) {
      clearInterval(this.notificationFetchInterval); // Clear interval on component destroy
    }
    this.notificationService.disconnectSSE(); // Disconnect SSE on component destroy
  }

  ChangeLanguage(language: string) {
    this.translate.use(language);
  }

  ToggleClass() {
    this.toggleClass = this.toggleClass === "ft-maximize" ? "ft-minimize" : "ft-maximize";
  }

  toggleNotificationSidebar() {
    this.layoutService.emitNotiSidebarChange(true);
  }

  toggleSidebar() {
    const appSidebar = document.getElementsByClassName("app-sidebar")[0];
    this.toggleHideSidebar.emit(appSidebar.classList.contains("hide-sidebar"));
  }

  deconnexion() {
    sessionStorage.setItem("iduser", '');
    sessionStorage.clear();
    this.router.navigate(['pages/login']);
  }

  fetchNotifications(userId: string) {
    this.notificationService.getByIdUser(userId).then(
        data => {
            this.notifications = data; // Assuming data is the array of notifications
            console.log('Notifications fetched:', this.notifications);
            this.unreadCount = data.filter(notification => !notification.readed).length;
            console.log('Unread notifications count:', this.unreadCount);
        },
        error => {
            console.error('Error fetching notifications:', error);
        }
    );
}

  startFetchingNotifications(userId: string) {
    this.notificationFetchInterval = setInterval(() => {
      this.fetchNotifications(userId); 
    }, 3 * 60 * 1000); 
  }

  startSSEConnection() {
    this.notificationService.connectToSSE();

    // Handle new notifications from SSE
    this.notificationService.handleNewNotification = (notification: any) => {
      this.notifications.push(notification); // Add the new notification to the array
      this.unreadCount++; // Increment unread notifications count
    };
  }

  resetUnreadCount() {
    // Collect the IDs of unread notifications
    const unreadNotificationIds = this.notifications
      .filter(notification => !notification.read) // Assuming you have a `read` property
      .map(notification => notification.id); // Replace `id` with the actual ID property

    if (unreadNotificationIds.length > 0) {
        // Call the service to update the read status
        this.notificationService.updateNotificationStatus(unreadNotificationIds)
            .then(response => {
                console.log('Notifications marked as read:', response);
                // Now reset the unread count
                this.unreadCount = 0;
                // Optionally update the notification list if needed
                this.notifications.forEach(notification => {
                    if (unreadNotificationIds.includes(notification.id)) {
                        notification.read = true; // Update the local state
                    }
                });
            })
            .catch(error => {
                console.error('Error marking notifications as read:', error);
            });
    } else {
        // If there are no unread notifications, just reset the count
        this.unreadCount = 0;
    }
}

getFormattedDate(date: Date): string {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);

  const notificationDate = new Date(date);
  const notificationDay = new Date(notificationDate.getFullYear(), notificationDate.getMonth(), notificationDate.getDate());

  const time = formatDate(notificationDate, 'HH:mm', 'en-US');

  if (notificationDay.getTime() === today.getTime()) {
    return `Aujourd'hui à ${time}`;
  } else if (notificationDay.getTime() === yesterday.getTime()) {
    return `Hier à ${time}`;
  } else {
    return formatDate(notificationDate, 'dd-MM-yyyy HH:mm', 'en-US');
  }
}

handleNotificationClick(notification: any) {
  if (notification.url) {
    this.router.navigate([notification.url]);
  } else {
    console.log("No URL defined for this notification");
  }
}

toggleCustomizer() {
  this.layoutService.emitCustomizerChange('toggle');
}
}


