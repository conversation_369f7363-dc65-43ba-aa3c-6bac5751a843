import { ComponentFixture, TestBed } from '@angular/core/testing';

import { WrapperSocieteDestinationComponent } from './wrapper-societe-destination.component';

describe('WrapperSocieteDestinationComponent', () => {
  let component: WrapperSocieteDestinationComponent;
  let fixture: ComponentFixture<WrapperSocieteDestinationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [WrapperSocieteDestinationComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(WrapperSocieteDestinationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
