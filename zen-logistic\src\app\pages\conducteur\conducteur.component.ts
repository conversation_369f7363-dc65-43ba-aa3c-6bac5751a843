import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { RegisterServiceService } from 'app/services/register-service.service';
import { Md5 } from 'md5-typescript';
//import { NGXToastrService } from 'app/components/extra/toastr/toastr.service';
import  { ToastrService  }  from 'ngx-toastr' ;
import { FileUploader } from 'ng2-file-upload';
const URL = 'https://zen-logistic.com/api/upload';

@Component({
  selector: 'app-conducteur',
  templateUrl: './conducteur.component.html',
  styleUrls: ['./conducteur.component.scss'] ,
  providers: [
    //NGXToastrService
  ]

})
export class ConducteurComponent implements OnInit {
  registerForm: FormGroup;
  submitted = false; 
  err=false;
  loading=false;

  filePath: string;
  constructor( private toastr: ToastrService, private router: Router,
    private route: ActivatedRoute , private formBuilder: FormBuilder , private registerService : RegisterServiceService) { }
    public uploader: FileUploader = new FileUploader({
      url: URL,
      headers: [{ name: 'Authorization', value: 'basic '+btoa('med:123456') }],
      itemAlias: 'image'
    });
    imagePreview(e) {
     if( this.uploader.queue.length ==2)
      {this.uploader.queue[0].remove()}
      //console.log(this.uploader.queue)
          const file = (e.target as HTMLInputElement).files[0];
  
      this.registerForm.patchValue({
        img: file
      });
  
      this.registerForm.get('img').updateValueAndValidity()
  
      const reader = new FileReader();
      reader.onload = () => {
        this.filePath = reader.result as string;
      }
      reader.readAsDataURL(file)
    }
    fileName=""
  ngOnInit() {

    this.uploader.onAfterAddingFile = (file) => {
      let originalname = file.file.name;
      //console.log(originalname);
          let ext = originalname.split('.').pop();
          let filename = (originalname.split('.').slice(0, -1).join('.')).replace(/\s/g, "");
      
           


     file.file.name=filename + '-' + Date.now()+'.'+ext
     this.fileName=file.file.name
      file.withCredentials = false;
    };
    this.uploader.onCompleteItem = (item: any, status: any) => {
      //console.log('Uploaded File Details:', item);
      this.toastr.success('File successfully uploaded!');
    };
    this.registerForm = this.formBuilder.group({
      img: [null],
      nom: ['', Validators.required],
      prenom: ['', Validators.required],
      cin: ['', Validators.required],
      imagecin: ['', Validators.required],
      mobile: ['', Validators.required],
      nomutilisateur: ['', Validators.required],
      motdepasse: ['', Validators.required],
    });

    
  }

  //f : fonction qui contient les informations du formulaires 
  get f() { return this.registerForm.controls; 

  }
  
  saveConducteur () { 
    this.submitted=true;
    this.loading = true;
    this.err=false;


    ////console.log(this.f.nomutilisateur.value) ;
    ////console.log(this.f.motdepasse.value) ;
    for (var n in this.f) {
      if ( this.f[n].status==='INVALID' ) 
      {
        this.err=true ;
       }  }

       if(this.err){
        this.toastr.error('veuillez remplir tous les champs', 'Oups!', { closeButton: true });

    }   



  
    if(!this.err)
   {
    this.registerService.addConducteur({
    nom : this.f.nom.value, 
    prenom : this.f.prenom.value , 
    nom_utilisateur : this.f.nomutilisateur.value,
    mot_de_passe :this.generateMdp(this.f.motdepasse.value),
    mobile : this.f.mobile.value, 
    cin : this.f.cin.value,
    image_cin : this.fileName,
    ajoutee_par: sessionStorage.getItem("iduser")
   }).subscribe((data=>{
    this.loading = false;
    this.uploader.uploadAll()
    this.toastr.success('Le conducteur est ajouté avec succès ', 'Félécitation!' , { closeButton: true })

    this.router.navigate(['listconducteur'] ,{ relativeTo: this.route.parent });
   
  }) ,error=>{
  ////console.log(error)
  this.loading = false;

})


}


else{

  this.loading = false;
}  


} 


generateMdp(mdp) {
  return Md5.init(mdp) ; 
}





}


