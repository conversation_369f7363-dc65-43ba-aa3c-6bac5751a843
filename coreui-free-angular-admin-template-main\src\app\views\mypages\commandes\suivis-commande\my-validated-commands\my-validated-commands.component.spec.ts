import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MyValidatedCommandsComponent } from './my-validated-commands.component';

describe('MyValidatedCommandsComponent', () => {
  let component: MyValidatedCommandsComponent;
  let fixture: ComponentFixture<MyValidatedCommandsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MyValidatedCommandsComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(MyValidatedCommandsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
