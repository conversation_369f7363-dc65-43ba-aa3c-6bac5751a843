import { Component } from '@angular/core';
import { FormConfig } from '../../../../../../shared/models/form.models';
import { DynamicFormComponent } from '../../../../../../shared/components/dynamic-form/dynamic-form.component';
import { SmartContainerComponent } from '../../../../../../shared/components/smart-container/smart-container.component';
import { ToastrService } from 'ngx-toastr';
import { FournisseurService } from 'src/services/fournisseur.service';

@Component({
  selector: 'app-ajouter-societe',
  standalone: true,
  imports: [DynamicFormComponent,SmartContainerComponent],
  templateUrl: './ajouter-societe.component.html',
  styleUrl: './ajouter-societe.component.scss'
})
export class AjouterSocieteComponent {
  
  showMatricule: boolean = true;
  showCegid : boolean = false;

  formConfig: FormConfig = {
    title: 'Ajouter Societe',
    fields: [
      {
        name: 'nom_fournisseur',
        label: 'Nom Fournisseur',
        placeholder: 'Nom Societe',
        type: 'text',
        required: true,
      },
      {
        name: 'adress_client',
        label: 'Adresse Societe',
        placeholder: 'Adresse Societe',
        type: 'text',
        required: true,
        
      },
      {
        name: 'mat_fisc',
        label: 'Matricule Fisc',
        placeholder: 'MF : *******/X/X/X/***',
        type: 'text',
        
        hidden: () => !this.showMatricule,
        validation: {
          maxLength: 17,
          pattern: '^([0-9]{7})\/([A-Z])\/([A-Z])\/([A-Z])\/([0-9]{3})$'
        }
      },
      {
        name: 'email_client',
        label: 'Email Client',
        placeholder: 'Mail Société facturation',
        type: 'email',
        required: true,
        validation: {
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        }
      },
      {
        name: 'code_cegid',
        label: 'Code Cegid',
        placeholder: 'Code Cegid',
        type: 'text',
        hidden: () => !this.showCegid
      },
      {
        name: 'entreposage',
        label: 'Entreposage',
        type: 'checkbox',

      },
      {
        name: 'flux',
        label: 'Flux',
        type: 'checkbox',
       
      },
      {
        name: 'sous_traitant',
        label: 'Sous Traitant',
        type: 'checkbox',

      },
      {
        name: 'showFisc',
        label: 'Pas-facturé',
        type: 'checkbox',
      }

    ],
    buttons: [
      {
        label: 'Ajouter',
        color: 'primary',
        onClick: (event: any) => this.onFormSubmit(event)
      }
    ]
  }
  constructor(private toastr: ToastrService ,
    private fournisseurService: FournisseurService) { }

  onFormSubmit(event: any) {
    if ((event.entreposage || event.flux) && event.code_cegid == "") {
      this.toastr.error('Code Cegid est obligatoire');
      return;
    }
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(event.email_client)) {
      this.toastr.error('Adresse e-mail invalide');
      return;
    }

    const fournisseurData = {
        nom_fournisseur: event.nom_fournisseur,
        mat_fisc: event.mat_fisc,
        sous_traitant: event.sous_traitant,
        adresse: event.adress_client,
        mail: event.email_client,
        flux: event.flux,
        entreposage: event.entreposage,
        code_cegid: event.code_cegid,
        code_sageX3: event.numSage
      };

      if (event.mat_fisc == "0000") {
        this.addFournisseurToDB(fournisseurData);
        
      } else {
        this.checkAndAddFournisseur(fournisseurData);
      }
  }

  addFournisseurToDB(data:any) {
    try {
      this.fournisseurService.addFournisseur(data).subscribe({
        next: (data:any) => {
          this.toastr.success("Fournisseur ajouté avec succès");
        },
        error: (error:any) => {
          this.toastr.error("Erreur lors de l'ajout du Fournisseur");
        }
      });
      
    } catch (error) {
      this.toastr.error("Erreur lors de l'ajout du Fournisseur");
    }
  }
  

  checkAndAddFournisseur(data:any) {
    try {
      this.fournisseurService.findFournisseurByMatricule(data.mat_fisc).subscribe({
        next: (response:any) => {

          if (response && response.fournisseur) {
            this.toastr.error(`Fournisseur ${response.fournisseur[0].nom_fournisseur} déjà existant`);
          } else {
            this.addFournisseurToDB(data);
          }
        },
        error: (error:any) => {
          this.toastr.error("Erreur lors de la recherche du Fournisseur");
        }
      });
      
    } catch (error) {
      this.toastr.error("Erreur lors de la recherche du Fournisseur");
    }
  }

  onFormChange(event: any) {
    this.showMatricule = !event.showFisc;
    this.showCegid = event.entreposage || event.flux;
    
  }



}
