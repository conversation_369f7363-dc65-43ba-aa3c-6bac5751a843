import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ModalDismissReasons, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { LigneCmdService } from 'app/services/ligneCmd.service';
import { RegisterServiceService } from 'app/services/register-service.service';
import { VoyageService } from 'app/services/voyage.service';
import {  ToastrService } from 'ngx-toastr';


@Component({
  selector: 'app-inspection',
  templateUrl: './inspection.component.html',
  styleUrls: ['./inspection.component.scss']
})
export class InspectionComponent implements OnInit {
  currentPage: number = 1;
  @ViewChild('popupTemplate', { static: true }) popupTemplate: TemplateRef<any>;
  @ViewChild('detailsPop', { static: true }) detailsPop: TemplateRef<any>;

// Définissez une variable pour stocker le nombre total de pages
totalPages: number;
lignesXls=[]
  lignes=[]
  trucks=[]
  drivers=[]
  totale = 0
  showTable: boolean = true;
  showDriverInspect: boolean = false;
  showTruckInspect: boolean = false;
  showVoyageInspect: boolean = false;
  startDate: string = '';
  endDate: string = '';
  selectedDriver =""
  selectedTruck=""
  startDateTruck: string = '';
  endDateTruck: string = '';
  TruckList :any=[]
  driverList:any=[]
  voyageList:any=[]
  id_voyage=""
  closeResult = '';
  modalRef : any
  ignoredLines =[]
  dataArr=[]
  Role =""
  details =[]
  totalToPay =0
  isAdmin: boolean;
  isFacturation: boolean;
  constructor(private ligneCmdService : LigneCmdService,
    private registerService: RegisterServiceService,
    private modalService: NgbModal,
    private voyageService: VoyageService,
    private toastr : ToastrService) { }




 async ngOnInit() {
  this.loadData();
    this.isAdmin = sessionStorage.getItem('userRole') == "SuperAdmin"
    this.isFacturation = sessionStorage.getItem('userRole') == "FACTURATION"

    await this.registerService.findAllCamion().subscribe(data => {
      this.trucks = data;
    })

   await this.registerService.findConducteur().subscribe(data => {
      this.drivers = data;

    })

    this.Role = sessionStorage.getItem('userRole')

  }

  settings = {
    pager: {
      
      perPage: 50
    },
    actions: {
      add: false, // Désactiver l'ajout
      edit: false, // Désactiver la modification
      delete: false // Désactiver la suppression
    },
    columns: {
      id: {
        title: 'ID'
      },
      id_voyage: {
        title: 'Id Voyage'
      },
      nom_depart: {
        title: 'Nom de départ'
      },
      nom_arrivee: {
        title: 'Nom d\'arrivée'
      },
      date_livraison: {
        title: 'Date de Livraison'
      },
      date_voyage: {
        title: 'Date du voyage'
      },
      kilometrage: {
        title: 'Kilométrage'
      },
      status: {
        title: 'Status'
      },
      type_ligne: {
        title: 'Type de ligne'
      },
      camion_immatriculation: {
        title: 'Immatriculation'
      }
    }
  };
  

  tableDetails = {
    pager: {
      perPage: 50
    },
    actions: {
      add: false,  // Désactiver l'ajout
      edit: false, // Désactiver la modification
      delete: false,  // Activer la suppression
      custom: [     // Ajouter un bouton personnalisé
        {
          name: 'cancel',  // Nom du bouton
          title: 'Annuler', // Texte du bouton
          type: 'html',     // Type de bouton HTML
          valuePrepareFunction: (value, row) => {
            return `<button class="btn btn-danger" >Annuler</button>`;
          }
        }
      ]
    },
   

    columns: {
      id: {
        title: 'ID'
      },
      nom_voyage: {
        title: 'Nom Voyage'
      },
      sell_price: {
        title: 'Montant de vente'
      },
      purchase_price: {
        title: 'Montant à payer'
      },
      first_date_voyage: {
        title: 'Date du voyage'
      }
    }
  };


  tableVoyage = {
    pager: {
      perPage: 50
    },
    actions: {
      add: false,
      edit: false,
      delete: false,

    },
    columns: {
      id: {
        title: 'ID'
      },

      nom_voyage: {
        title: 'Nom Voyage'
      },
      
      purchase_price: {
        title: 'Montant à payer'
      },
      date_voyage: {
        title: 'Date du voyage'
      }
    }
  };


  onCustomAction(event: any): void {
    // Vérifiez que l'action est bien 'cancel' (ou le nom que vous avez donné)
    if (event.action === 'cancel') {
      const voyageId = event.data.id;  // Récupérer l'ID du voyage
      this.voyageService.cancelVoyage(voyageId).subscribe(
        (response) => {
          this.toastr.success('Le voyage a été annulé avec succès.');
          // Vous pouvez mettre à jour l'affichage ou effectuer d'autres actions ici.
        },
        (error) => {
          this.toastr.error('Erreur lors de l\'annulation du voyage.');
        }
      );
    }
  }

  
  

  onDeleteConfirm(event: any): void {
    if (sessionStorage.getItem('userRole') != "SuperAdmin" && sessionStorage.getItem('userRole') != "Administrateur") {
      this.toastr.error('Vous n\'êtes pas autorisé à faire ça.', 'Accès refusé');
      return;
    }
    if (window.confirm('Êtes-vous sûr de vouloir annuler ce voyage ?')) {
      const voyageId = event.data.id; 
      this.voyageService.cancelVoyage(voyageId).subscribe(
        (response) => {
          this.toastr.success('Le voyage a été annulé avec succès.');
          
          this.driverList = this.driverList.filter((driver) => driver.id !== voyageId);
  
          event.confirm.resolve(); 
        },
        (error) => {
          this.toastr.error('Erreur lors de l\'annulation du voyage.');
          event.confirm.reject();
        }
      );
    } else {
      event.confirm.reject(); 
    }
  }
  
  


  async downloadExcel() {
    try {
        
        let excelContent = 'id\tid Voyage\tdepart\tarrivee\tDate pevu de depart\tDate prevu d\'arrivee\tDate de validation\tDate du voyage\tKilometrage\tStatus\tType de ligne\tConducteur Nom\tConducteur Prenom\tCamion Immatriculation\tQuantite\tDate d\'expedition\tDate de reservation\tDate de livraison\tVolume\tVolume Estime\tVolume Unitaire\tPrix Totale\tClient Finale\tSfax/Hors\n';

        this.lignes.forEach(row => {
          excelContent += `${row.id || ''}\t${row.id_voyage}\t${this.replaceSpecialChars(row.nom_depart || '')}\t${this.replaceSpecialChars(row.nom_arrivee || '')}\t${row.date_depart || ''}\t${row.date_arrivee || ''}\t${row.date_validation || ''}\t${row.date_voyage || ''}\t${row.kilometrage || ''}\t${this.replaceSpecialChars(row.status || '')}\t${this.replaceSpecialChars(row.type_ligne || '')}\t${row.conducteur_nom || ''}\t${row.conducteur_prenom || ''}\t${row.camion_immatriculation || ''}\t${row.quantite || ''}\t${row.date_expedition || ''}\t${row.date_reservation || ''}\t${row.date_livraison || ''}\t${row.volume || ''}\t${row.estimation || ''}\t${row.prix_unit || ''}\t${row.prix_tot || ''}\t${this.replaceSpecialChars(row.nom_fournisseur) || ''}\t${row.emplacement || ''}\n`;
      });
      

        const blob = new Blob([excelContent], { type: 'application/vnd.ms-excel;charset=utf-8' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'Inspection.xls');
        document.body.appendChild(link);
        link.click();
        window.URL.revokeObjectURL(url);
    } catch (error) {
        console.error('Erreur lors du téléchargement du fichier Excel :', error);
        // Gérer l'erreur si nécessaire
    }
}


async downloadVoyageDriver() {
  try {
    // Définir les en-têtes des colonnes en fonction de `tableDetails.columns`
    const excelHeaders = 'Nom Voyage\tMontant à Payer\tDate du Voyage\n';

    // Construire le contenu Excel à partir des données du tableau
    let excelContent = excelHeaders;

    this.driverList.forEach((row) => {
      excelContent += `${this.replaceSpecialChars(row.nom_voyage || '')}\t${row.purchase_price || ''}\t${row.first_date_voyage || ''}\n`;
    });

    // Générer un fichier Blob
    const blob = new Blob([excelContent], { type: 'application/vnd.ms-excel;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);

    // Créer un lien de téléchargement
    const link = document.createElement('a');
    link.href = url;

    const selectedDriverObject = this.drivers.find(driver => driver.id == this.selectedDriver);
    const driverName = selectedDriverObject ? `${selectedDriverObject.nom}_${selectedDriverObject.prenom}` : 'Conducteur_Inconnu';
    
    link.setAttribute('download', `Tableau_Voyages_${driverName}.xls`);
    
    document.body.appendChild(link);
    link.click();
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Erreur lors du téléchargement du fichier Excel :', error);
  }
}


replaceSpecialChars(str: string | null | undefined): string {
  if (!str) return ''; // Retourne une chaîne vide si str est null ou undefined

  return str.replace(/[éèô/]/g, (char) => {
      switch (char) {
          case 'é':
          case 'è':
              return 'e';
          case 'ô':
              return 'o';
          case '/':
              return '-';
          default:
              return char;
      }
  });
}




 

showDriverInspectForm() {
  this.showDriverInspect = true;
  this.showTruckInspect = false;
  this.showTable = false;
  this.showVoyageInspect = false;
  this.driverList=[]
}

showVoyageInspection() {
  this.showDriverInspect = false;
  this.showTruckInspect = false;
  this.showVoyageInspect = true;
  this.showTable = false;
  this.driverList=[]
}


showTruckInspectForm() {
  this.showTruckInspect = true;
  this.showDriverInspect = false;
  this.showTable = false;
  this.showVoyageInspect = false;
  this.TruckList=[]
}

cancelDriver() {
  this.showDriverInspect = false;
  this.showVoyageInspect = false;

  this.showTable = true;

}

cancelTruck() {
  this.showTruckInspect = false;
  this.showVoyageInspect = false;

  this.showTable = true;
}

searchTruck() {

  if (this.selectedTruck == "" || this.endDateTruck == "" || this.startDateTruck == "") {
    this.toastr.error("Merci de remplir tous les filtres");
  } else if (new Date(this.startDateTruck) > new Date(this.endDateTruck)) {
    this.toastr.error("La date de fin doit être postérieure à la date de début");
  } else {
    const data = {
      id_camion: this.selectedTruck,
      date_debut: this.startDateTruck,
      date_fin: this.endDateTruck
    };
    this.ligneCmdService.findLigneDelivredByTruck(data).subscribe(res => {
      this.TruckList = res.data;
    
      if (this.TruckList.length === 0) {
        this.toastr.error("Pas de données récupérées");
      } else {
        this.totalToPay = this.TruckList.reduce((sum, truck) => {
          console.log(truck.purchase_price)
          return sum + (truck.purchase_price || 0);
        }, 0).toLocaleString();
        
     

      }
    });
    
  }
}


searchDriver() {
  if (this.selectedDriver == "" || this.endDate == "" || this.startDate == "") {
    this.toastr.error("Merci de remplir tous les filtres");
  } else if (new Date(this.startDate) > new Date(this.endDate)) {
    this.toastr.error("La date de fin doit être postérieure à la date de début");
  } else {
    const data = {
      id_conducteur: this.selectedDriver,
      date_debut: this.startDate,
      date_fin: this.endDate
    };
    this.ligneCmdService.findLigneDelivredByDriver(data).subscribe(res => {
      this.driverList = res.data;
      if(this.driverList.length==0){
        this.toastr.error("Pas de données récupérées");
      }else {
        this.totalToPay = this.driverList.reduce((sum, driver) => {
          console.log(driver.purchase_price)
          return sum + (driver.purchase_price || 0);
        }, 0).toLocaleString();
    }});
  }
}

async loadData() {
  try {
    const res = await this.ligneCmdService.findAllToInspection();
    this.lignes = res;
  } catch (error) {
    console.error("Une erreur s'est produite lors de la récupération des données:", error);
    // Gérer l'erreur ici, par exemple afficher un message à l'utilisateur ou effectuer une autre action nécessaire.
  }
}


searchVoyageByDate(){
  if(!this.startDate || !this.endDate){
    this.toastr.error("Les dates de début et de fin sont requises")
    return
  }
  const data ={
    date_debut :this.startDate,
    date_fin:this.endDate
  }
  this.voyageService.searchVoyageByDate(data).subscribe(res=>{
    this.voyageList =res
    })

}


onFileChange(event) {
  const file = event.target.files[0];
  if (file) {
    this.readFile(file);
  }
}

readFile(file: File) {
  const fileReader = new FileReader();
  fileReader.onload = (e) => {
    const binaryString = fileReader.result as string;
    this.processExcelData(binaryString);
  };
  fileReader.readAsBinaryString(file);
}

processExcelData(binaryString: string) {
  const lines = binaryString.split('\n'); // Split by newline to get each row
  
  // First line should contain headers, split by ';' or relevant separator
  const firstLine = lines[0].split(';'); 

  console.log('First line:', firstLine); // Log first line to debug

  // Check if the first line contains the correct headers
  if (firstLine.length < 2 || 
      !firstLine[0].toLowerCase().trim().includes('id') || 
      !firstLine[1].toLowerCase().trim().includes('volume')) {
    this.toastr.error('La première cellule doit contenir "ID" et la deuxième "Volume"');
    return;
  }

  const dataArr = [];  // Store valid data
  const ignoredLines = []; // Track ignored lines
  const integerRegex = /^\d+$/;  // Regex for validating ID as integer
  let lastNonEmptyLineIndex = -1;  // Track the last valid data line

  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();  // Remove leading/trailing spaces

    // Split the line by semicolon (change if needed, e.g., use ',' or '\t')
    const cells = line.split(';'); 

    // If the line is empty or doesn't have at least 2 columns, skip it
    if (line === '' || cells.length < 2) {
      ignoredLines.push(i);
      continue; 
    }

    lastNonEmptyLineIndex = i; // Track valid data lines

    const id = cells[0]; // First column is the ID

    // If ID is not a valid integer, skip the line
    if (!integerRegex.test(id)) {
      ignoredLines.push(i);
      continue; 
    }

    // Replace comma with dot for decimal handling in volume
    const volumeString = cells[1].replace(',', '.');
    const volume = parseFloat(volumeString); // Convert to float

    // Skip the line if volume is not a valid number
    if (isNaN(volume) || !Number.isFinite(volume)) {
      ignoredLines.push(i);
      continue; 
    }

    // Push the valid data as an object { id, volume }
    dataArr.push({ id: parseInt(id, 10), volume });
  }

  // Remove any ignored lines that appear after the last valid data line
  while (ignoredLines.length > 0 && ignoredLines[ignoredLines.length - 1] >= lastNonEmptyLineIndex) {
    ignoredLines.pop();
  }

  // Log the results for debugging
  console.log('Données:', dataArr);
  console.log('Lignes ignorées:', ignoredLines);

  // Store the data in the component for further use
  this.dataArr = dataArr;
  this.ignoredLines = ignoredLines;

  // Open the modal to display the results
  this.modalRef = this.modalService.open(this.popupTemplate, {
    ariaLabelledBy: 'modal-basic-title',
    windowClass: 'custom-modal-style'
  });
}





open(content) {
  this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
    this.closeResult = `Closed with: ${result}`;
  }, (reason) => {
    this.closeResult =
      `Dismissed ${this.getDismissReason(reason)}`;
  });
}

private getDismissReason(reason: any): string {
  if (reason === ModalDismissReasons.ESC) {
    return 'by pressing ESC';
  } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
    return 'by clicking on a backdrop';
  } else {
    return `with: ${reason}`;
  }

}



updateVolumeXls() {
  this.ligneCmdService.updateVolumeWithXls(this.dataArr)
    .then(response => {
      // Traitement réussi
      console.log('Mise à jour réussie :', response);
      this.toastr.success('Mise à jour réussie');
      this.modalRef.close();
      const idList = this.dataArr.map(item => item.id);
      console.log(idList)
      this.ligneCmdService.fixPrice(idList).subscribe(res =>{
        console.log(res)
      })
    })
    .catch(error => {
      console.error('Erreur lors de la mise à jour du volume :', error);
      this.toastr.error('Erreur lors de la mise à jour des volumes');
      this.modalRef.close();

    });
}





async downloadExcelConducteur() {

  let selectedDriverName = '';

  this.drivers.forEach(driver => {
    if (driver.id == this.selectedDriver) {
      selectedDriverName = driver.nom;
    }
  });
  
  console.log(selectedDriverName);

  try {
      
      let excelContent = 'id\tdepart\tarrivee\tDate pevu de depart\tDate prevu d\'arrivee\tDate de validation\tDate du voyage\tKilometrage\tStatus\tType de ligne\tQuantite\tSKU\tDate d\'expedition\tDate de reservation\tDate de livraison\tVolume\tVolume Estime\n';

      this.driverList.forEach(row => {
          excelContent += `${row.id}\t${this.replaceSpecialChars(row.nom_depart)}\t${this.replaceSpecialChars(row.nom_arrivee)}\t${row.date_depart}\t${row.date_arrivee}\t${row.date_validation}\t${row.date_voyage}\t${row.kilometrage}\t${this.replaceSpecialChars(row.status)}\t${this.replaceSpecialChars(row.type_ligne)}\t${row.quantite}\t${row.sku}\t${row.date_expedition}\t${row.date_reservation}\t${row.date_livraison}\t${row.volume}\t${row.estimation}\n`;
      });

      const blob = new Blob([excelContent], { type: 'application/vnd.ms-excel;charset=utf-8' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `Inspection_${selectedDriverName}.xls`);
      document.body.appendChild(link);
      link.click();
      window.URL.revokeObjectURL(url);
  } catch (error) {
      console.error('Erreur lors du téléchargement du fichier Excel :', error);
  }
}


async downloadExcelCamion() {

  let selectedTruckMat = '';

  // Parcourir this.driverset
  this.trucks.forEach(truck => {
    // Vérifier si l'ID du driver correspond à this.selectedDriver
    if (truck.id == this.selectedTruck) {
      // Si c'est le cas, assigner le nom du driver à selectedDriverName
      selectedTruckMat = truck.immatriculation;
    }
  });
  
  // Utilisez selectedDriverName comme vous le souhaitez
  console.log(selectedTruckMat);

  try {
      
      let excelContent = `id\t
      depart\t
      arrivee\t
      Date pevu de depart\t
      Date prevu d\'arrivee\t
      Date de validation\t
      Date du voyage\t
      Kilometrage\t
      Status\t
      Type de ligne\t
      Quantite\t
      SKU\t
      Date d\'expedition\t
      Date de reservation\t
      Date de livraison\t
      Volume\t
      Volume Estime\n`;

      this.TruckList.forEach(row => {
          excelContent += `${row.id}\t
          ${this.replaceSpecialChars(row.nom_depart)}\t
          ${this.replaceSpecialChars(row.nom_arrivee)}\t
          ${row.date_depart}\t
          ${row.date_arrivee}\t
          ${row.date_validation}\t
          ${row.date_voyage}\t
          ${row.kilometrage}\t
          ${this.replaceSpecialChars(row.status)}\t
          ${this.replaceSpecialChars(row.type_ligne)}\t
          ${row.quantite}\t
          ${this.replaceSpecialChars(row.sku)}\t
          ${row.date_expedition}\t
          ${row.date_reservation}\t
          ${row.date_livraison}\t
          ${row.volume}\t
          ${row.estimation}\n`;
      });

      const blob = new Blob([excelContent], { type: 'application/vnd.ms-excel;charset=utf-8' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `Inspection_${selectedTruckMat}.xls`);
      document.body.appendChild(link);
      link.click();
      window.URL.revokeObjectURL(url);
  } catch (error) {
      console.error('Erreur lors du téléchargement du fichier Excel :', error);
      // Gérer l'erreur si nécessaire
  }
}

downloadFile() {
  const fileUrl = 'assets/img/elements/exmple_insert_volume.csv'; 
  const a = document.createElement('a'); 
  a.href = fileUrl;
  a.download = 'exmple_insert_volume.csv'; 
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

async openPopup(data: any) {
  console.log(data.data.id)
  this.id_voyage= data.data.id
  this.ligneCmdService.findLignesByIdVoyage(data.data.id).subscribe(
    (res) => {
      this.details = res.data; // Assurez-vous que `res` est un tableau de lignes

      // Ouvrir le modal après avoir reçu les données
      this.modalRef = this.modalService.open(this.detailsPop, {
        ariaLabelledBy: 'modal-basic-title',
        windowClass: 'custom-modal-style',
      });
    },
    (err) => {
      console.error("Erreur lors de la récupération des détails :", err);
    }
  );
}



returnToResponsable() {
  const confirmation = window.confirm('Êtes-vous sûr de vouloir retourner ce voyage au responsable ?');

  if (!confirmation) {
    return; // L'utilisateur a annulé
  }

  this.voyageService.updateVoyageToAdjust(this.id_voyage).subscribe({
    next: (response) => {
      const message = response.message ? response.message : 'Voyage retourné au responsable avec succès.';
      this.toastr.success(message, 'Succès');
    },
    error: (err) => {
      let errorMsg = 'Une erreur est survenue.';
      if (err.error) {
        errorMsg = err.error.message ? err.error.message : (err.error.error ? err.error.error : errorMsg);
      }
      this.toastr.error(errorMsg, 'Erreur');
    }
  });
}




}
