import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class PdfUploadService {
  private apiUrl = environment.apiPdf+'pdf/upload';
  private apiUrlAi = environment.apiURL+'pdf/upload';
 private apiURL=environment.apiURL
  constructor(private http: HttpClient) {}
  private httpOptions = {
    headers: new HttpHeaders({
      'Authorization': 'Basic ' + btoa('med:123456') // Assure-toi que l'utilisateur et le mot de passe correspondent
    })
  };
  uploadPdf(file: File): Observable<any> {
    const formData = new FormData();
    formData.append('pdf', file);

    // Ne pas spécifier 'Content-Type'; le navigateur le fait automatiquement
    return this.http.post(this.apiUrl, formData);
  }
  uploadPdfAi(file: File): Observable<any> {
    const formData = new FormData();
    formData.append('file', file); // Change 'pdf' to 'file'

    // Ne pas spécifier 'Content-Type'; le navigateur le fait automatiquement
    return this.http.post(this.apiUrlAi, formData, this.httpOptions);
}

addColisage(data: any): Observable<any> {
  console.log(data)
  return this.http.post<any>(this.apiURL + 'addColisage', data, this.httpOptions);
}
getColisageByIdUser(id :any): Observable<any> {
  return this.http.get<any>(this.apiURL + `findByUser/${id}`, this.httpOptions);
}

updateColisage(id:any,data:any): Observable<any> {
  return this.http.put<any>(this.apiURL + `colisages/${id}`,data, this.httpOptions);
}
updateColisageStatus(id:any,data:any): Observable<any> {
  return this.http.put<any>(this.apiURL + `updateColisageStatus/${id}`,data, this.httpOptions);
}

getColisages(page: number = 1, limit: number = 50): Observable<any> {
  let params = new HttpParams().set('page', page.toString()).set('limit', limit.toString());
  return this.http.get<any>(`${this.apiURL}colisages`, { params, ...this.httpOptions });
}

updateVisibleById(id:any): Observable<any> {
  return this.http.put<any>(this.apiURL + `updateVisibleById/${id}`,null, this.httpOptions);
}

}
