import { Component, OnInit, ViewChild } from '@angular/core';
import { OrderLineService } from 'src/services';
import { FournisseurService } from 'src/services/fournisseur.service';
import { FormConfig } from 'src/shared/models/form.models';
import { ActionButton, TableColumn, TableConfig } from 'src/shared/models/table.models';
import { FactureImpressionComponent } from '../shared/facture-impression/facture-impression.component';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { DynamicExportButtonComponent } from 'src/shared/components/dynamic-export-button/dynamic-export-button.component';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';
import { DynamicFormComponent } from 'src/shared/components/dynamic-form/dynamic-form.component';
import { CommonModule } from '@angular/common';
import { SmartButtonComponent } from 'src/shared/components/smart-button/smart-button.component';

@Component({
  selector: 'app-facturation-transport',
  standalone :true,
  imports: [
    FactureImpressionComponent,
    SmartContainerComponent,
    SmartTableComponent,
    DynamicFormComponent,
    DynamicExportButtonComponent,
    CommonModule,
    SmartButtonComponent
  ],
  templateUrl: './facturation-transport.component.html',
  styleUrl: './facturation-transport.component.scss'
})
export class FacturationTransportComponent implements OnInit{
  @ViewChild('factureImpression', { static: false }) factureImpression!: FactureImpressionComponent;
  ClientList : any[] =[]
  FactureList : any[] = []
  showTable:boolean =false
  loading:boolean = false;
  selectedRows: any[] = []
  afficherFacture :boolean = false;
  formConfig : FormConfig = {
    title: 'Facturation Transport',
    fieldGroups: [
      {
        fields: [
          {
            name: 'fournisseurId',
            placeholder: 'Selectionner un fournisseur',
            type: 'select',
            options: {
              objectArray: this.ClientList,
              valueAttribute: 'id',
              labelAttribute: 'nom_fournisseur'
            }
          }
        ]
      },
      {
        fields: [
          {
            label: 'Date debut',
            name: 'dateDebut',
            placeholder: 'Selectionner une date',
            type: 'date'
          },
          {
            label: 'Date fin',
            name: 'dateFin',
            placeholder: 'Selectionner une date',
            type: 'date'
          }
        ]
      }
    ],
    buttons :[
      {
        label: 'Rechercher',
        color: 'primary',
        onClick: (formValues: any) => {this.showTable;console.log('here');this.loadFactures(formValues)}
      }
    ]
  }

  tableColumns : TableColumn[] = [
    { name: 'id', displayName: 'ID', sortable: true },
    { name: 'nom_depart', displayName: 'Depart', sortable: true },
    { name: 'nom_arrivee', displayName: 'Arrivee', sortable: true },
    { name: 'type_ligne', displayName: 'Type de ligne', sortable: true },
    { name: 'kilometrage', displayName: 'Kilometrage', sortable: true },
    { name: 'nom_client', displayName: 'Nom Client', sortable: true },
    { name: 'volume', displayName: 'Volume', sortable: true },
    { name: 'date_voyage', displayName: 'Date Voyage', sortable: true },
    { name: 'prix_tot', displayName: 'Prix Total', sortable: true }
  ]

  TableConfig: TableConfig  = {
    selectable :true,
    multiSelect : true
  }

  constructor(
    private fournisseurService: FournisseurService,
    private orderLineService: OrderLineService
  ){}

  ngOnInit(): void {
    this.loadClients()
  }
  scrollToDetails() {
    let element = document.getElementById('smart-container-details');
    element?.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    });
  }
  onSelectChange(event: any) {
    this.selectedRows = event;
    console.log('selectedRows',this.selectedRows);
  }

  showFacture() {
    this.factureImpression.prefacture = true
    this.factureImpression.source = this.selectedRows;
    console.log('source Facture ',this.factureImpression.source);
    this.afficherFacture = true;
    setTimeout(() => this.scrollToDetails(), 150);
  }

  loadClients(){
    this.fournisseurService.findClientToInvoiceTransport().subscribe({
      next: (response: any) => {
        this.ClientList = response;
        
        const ClientField = this.formConfig.fieldGroups?.[0].fields.find((field: any) => field.name === 'fournisseurId')
        if(ClientField?.options){
          ClientField.options.objectArray = this.ClientList;
        }
      },
      error: (error) => {
        console.error('Error loading clients:', error);
      }
    })
  }

  loadFactures(formData: any){
    console.log(formData);
    this.loading = true;
    this.showTable = true
    let data:any = {
      date_debut: formData.dateDebut,
      date_fin: formData.dateFin,
       id_client: formData.fournisseurId
    } 
    this.orderLineService.findDeliveredByDateAndClient(data).subscribe({
      next: (response: any) => {
        this.FactureList = response;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading factures:', error);
        this.loading = false;
      }
    })
  }

}
