import { INavData } from '@coreui/angular';

export interface INavDataWithRoles extends INavData {
  roles?: string[]; // Add roles property
  children?: INavDataWithRoles[];
}
export const navItems: INavDataWithRoles[] = [
  {
    name: 'Dashboard',
    url: '/dashboard',
    iconComponent: { name: 'cil-speedometer' },
    badge: {
      color: 'info',
      text: 'NEW'
    }
  },
  {
    title: true,
    name: 'My Pages'
  },
  {
    name: 'ComponentTest',
    url: '/componenttest',
    iconComponent: { name: 'cil-layers' }
    
  },
  {
    name :'Ajouter Commande',
    url: '/ajouter-commande',
    iconComponent: { name: 'cil-layers' },
    roles: ['Client', 'Chef Departement', 'GS', 'GE', 'Depot', 'SuperAdmin', 'Administrateur']
  },
  {
    name: 'Commandes',
    url: '/commandes',
    roles: ['SuperAdmin','Admin','Client','GS','GE'],
    children: [
      {
        name: 'Commandes à réserver',
        url: '/commandes/commandes-areserver',
        icon: 'nav-icon-bullet',
        roles: ['SuperAdmin','Admin',]
      },
      {
        name: 'Commandes à expédier',
        url: '/commandes/commandes-aexpedier',
        icon: 'nav-icon-bullet',
        roles: ['SuperAdmin','Admin']
      },
      {
        name: 'Commandes à livrer',
        url: '/commandes/commandes-a-livrer',
        icon: 'nav-icon-bullet',
        roles: ['SuperAdmin','Admin']
      },
      {
        name: 'Commandes à facturer',
        url: '/commandes/ligne-afacturer',
        icon: 'nav-icon-bullet',
        roles: ['SuperAdmin','Admin']
      },
      {
        name: 'Ajouter ligne de commande',
        url: '/commandes/assign-ligne-cmd',
        icon: 'nav-icon-bullet',
        roles: ['SuperAdmin','Admin']
      },
      {
        name:'Suivis Commandes',
        url: '/commandes/suivis-commandes',
        icon: 'nav-icon-bullet',
        roles: ['Client','GS']
      },
      {
        name:'Suivis Lignes',
        url: '/commandes/suivis-ligne',
        icon: 'nav-icon-bullet',
        roles: ['Client','GS']
      }
    ]
  },
  {
    name: 'Factures',
    url: '/factures',
    iconComponent: { name: 'cilClipboard' },
    roles: ['SuperAdmin','Admin','FACTURATION'],
    children: [
      {
        name: 'Liste des factures',
        url: '/factures/factures-list',
        icon: 'nav-icon-bullet',
        roles: ['SuperAdmin','Admin','FACTURATION']
      },
      {
        name:'Facturation Transport',
        url: '/factures/transport-facturation',
        icon: 'nav-icon-bullet',
        roles: ['SuperAdmin','Admin','FACTURATION']
      }
    ]
  },
  {
    name:'Modèle Enregistré',
    url: '/modele',
    iconComponent: { name: 'cilClipboard' },
    roles: ['GS','GE','Client','Chef Departement'],
  },
  {
    name: 'Monitoring',
    url: '/monitoring',
    iconComponent: { name: 'cil-check-circle' },
    roles: ['GS', 'GE', 'Client','Chef Departement']
  },
  {
    name: 'Inspection',
    url: '/inspection',
    iconComponent: { name: 'cil-check-circle' },
    roles: ['SuperAdmin','Admin'],
    children: [
      {
        name: 'Inspection des colis',
        url: '/inspection/inspection-colis',
        icon: 'nav-icon-bullet',
        roles: ['SuperAdmin','Admin']
      },
      {
        name: 'Inspection des livraisons',
        url: '/inspection/inspection-livraison',
        icon: 'nav-icon-bullet',
        roles: ['SuperAdmin','Admin']
      },
      {
        name: 'Calendrier des colis',
        url: '/inspection/calendrier-colis',
        icon: 'nav-icon-bullet',
        roles: ['SuperAdmin','Admin']
      }
    ]
  },
  {
    name: 'Conducteurs',
    url: '/conducteurs',
    iconComponent: { name: 'cil-user' },
    roles: ['SuperAdmin','Admin'],
    children: [
      {
        name: 'Liste des conducteurs',
        url: '/conducteurs/liste-conducteurs',
        icon: '',
        roles: ['SuperAdmin','Admin']
      }
    ]
  },
  {
    name: 'Camios',
    url: '/camions',
    iconComponent: { name: 'cil-truck' },
    roles: ['SuperAdmin','Admin'],
    children: [
      {
        name: 'Liste des camions',
        url: '/camions/liste-camions',
        icon: '',
        roles: ['SuperAdmin','Admin']
      },]
  },
  {
    name: 'Liste de vérification',
    url: '/verification',
    iconComponent: { name: 'cil-check-circle' },
    roles: ['SuperAdmin','Admin','GS','GE','Client','Chef Departement','Depot','Inspection','FACTURATION','MAGASIN','Conducteur']
  },
  {
    name: 'Utilisateurs',
    url: '/utilisateurs',
    iconComponent: { name: 'cil-people' },
    roles: ['SuperAdmin','Admin'],
    children: [
      {
        name: 'Liste des utilisateurs',
        url: '/utilisateurs/liste-utilisateurs',
        icon: '',
        roles: ['SuperAdmin','Admin']
      }
    ]
  },
  {
    name: 'Parametrage',
    url: '/parametrage',
    iconComponent: { name: 'cil-settings' },
    roles: ['SuperAdmin','Admin'],
    children: [
      {
        name: 'Types',
        url: '/parametrage/parametrage-types',
        icon: '',
        roles: ['SuperAdmin','Admin']
      },
      {
        name: 'Prix',
        url: '/parametrage/parametrage-prix',
        icon: '',
        roles: ['SuperAdmin','Admin']
      },
      {
        name: 'societé et destination',
        url: '/parametrage/parametrage-societe-destination',
        icon: '',
        roles: ['SuperAdmin','Admin']
      },
      {
        name: 'Autres',
        url: '/parametrage/parametrage-other',
        icon: '',
        roles: ['SuperAdmin','Admin']
      },
      {
        name: 'Affectation',
        url: '/parametrage/parametrage-affectation',
        icon: '',
        roles: ['SuperAdmin','Admin'],
        children: [
          {
            name: 'Chef Client',
            url: '/parametrage/parametrage-affectation/chef-client',
            icon: '',
            roles: ['SuperAdmin','Admin']
          },
          {
            name: 'Destination Client',
            url: '/parametrage/parametrage-affectation/dest-client',
            icon: '',
            roles: ['SuperAdmin','Admin']
          }
        ]
      }
    ]
  },
  {
    name: 'Reclamations',
    url: '/reclamations',
    iconComponent: { name: 'cil-bell' },
    roles: ['SuperAdmin','Admin']

  },
  {
    name: 'Colisage',
    url: '/colisage',
    iconComponent: { name: 'cibDropbox' },
    roles: ['SuperAdmin','Admin'],
    children: [
      {
        name: 'Liste des colis',
        url: '/colisage/list-colisage',
        icon: '',
        roles: ['SuperAdmin','Admin']
      },
      {
        name: 'Insertion des colis',
        url: '/colisage/insertion-colisage',
        icon: '',
        roles: ['SuperAdmin','Admin']
      },
    ]
  },
  {
    title: true,
    name: 'Theme'
  },
  {
    name: 'Colors',
    url: '/theme/colors',
    iconComponent: { name: 'cil-drop' }
  },
  {
    name: 'Typography',
    url: '/theme/typography',
    linkProps: { fragment: 'headings' },
    iconComponent: { name: 'cil-pencil' }
  },
  {
    name: 'Components',
    title: true
  },
  {
    name: 'Base',
    url: '/base',
    iconComponent: { name: 'cil-puzzle' },
    children: [
      {
        name: 'Accordion',
        url: '/base/accordion',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Breadcrumbs',
        url: '/base/breadcrumbs',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Cards',
        url: '/base/cards',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Carousel',
        url: '/base/carousel',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Collapse',
        url: '/base/collapse',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'List Group',
        url: '/base/list-group',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Navs & Tabs',
        url: '/base/navs',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Pagination',
        url: '/base/pagination',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Placeholder',
        url: '/base/placeholder',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Popovers',
        url: '/base/popovers',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Progress',
        url: '/base/progress',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Spinners',
        url: '/base/spinners',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Tables',
        url: '/base/tables',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Tabs',
        url: '/base/tabs',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Tooltips',
        url: '/base/tooltips',
        icon: 'nav-icon-bullet'
      }
    ]
  },
  {
    name: 'Buttons',
    url: '/buttons',
    iconComponent: { name: 'cil-cursor' },
    children: [
      {
        name: 'Buttons',
        url: '/buttons/buttons',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Button groups',
        url: '/buttons/button-groups',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Dropdowns',
        url: '/buttons/dropdowns',
        icon: 'nav-icon-bullet'
      }
    ]
  },
  {
    name: 'Forms',
    url: '/forms',
    iconComponent: { name: 'cil-notes' },
    children: [
      {
        name: 'Form Control',
        url: '/forms/form-control',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Select',
        url: '/forms/select',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Checks & Radios',
        url: '/forms/checks-radios',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Range',
        url: '/forms/range',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Input Group',
        url: '/forms/input-group',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Floating Labels',
        url: '/forms/floating-labels',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Layout',
        url: '/forms/layout',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Validation',
        url: '/forms/validation',
        icon: 'nav-icon-bullet'
      }
    ]
  },
  {
    name: 'Charts',
    iconComponent: { name: 'cil-chart-pie' },
    url: '/charts'
  },
  {
    name: 'Icons',
    iconComponent: { name: 'cil-star' },
    url: '/icons',
    children: [
      {
        name: 'CoreUI Free',
        url: '/icons/coreui-icons',
        icon: 'nav-icon-bullet',
        badge: {
          color: 'success',
          text: 'FREE'
        }
      },
      {
        name: 'CoreUI Flags',
        url: '/icons/flags',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'CoreUI Brands',
        url: '/icons/brands',
        icon: 'nav-icon-bullet'
      }
    ]
  },
  {
    name: 'Notifications',
    url: '/notifications',
    iconComponent: { name: 'cil-bell' },
    children: [
      {
        name: 'Alerts',
        url: '/notifications/alerts',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Badges',
        url: '/notifications/badges',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Modal',
        url: '/notifications/modal',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Toast',
        url: '/notifications/toasts',
        icon: 'nav-icon-bullet'
      }
    ]
  },
  {
    name: 'Widgets',
    url: '/widgets',
    iconComponent: { name: 'cil-calculator' },
    badge: {
      color: 'info',
      text: 'NEW'
    }
  },
  {
    title: true,
    name: 'Extras'
  },
  {
    name: 'Pages',
    url: '/login',
    iconComponent: { name: 'cil-star' },
    children: [
      {
        name: 'Login',
        url: '/login',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Register',
        url: '/register',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Error 404',
        url: '/404',
        icon: 'nav-icon-bullet'
      },
      {
        name: 'Error 500',
        url: '/500',
        icon: 'nav-icon-bullet'
      }
    ]
  },
  {
    title: true,
    name: 'Links',
    class: 'mt-auto'
  },
  {
    name: 'Docs',
    url: 'https://coreui.io/angular/docs/',
    iconComponent: { name: 'cil-description' },
    attributes: { target: '_blank' }
  }
  
];

export function getNavItemsByRole(userRole: string): INavData[] {
  return navItems
    .filter(item => !item.roles || item.roles.includes(userRole))
    .map(item => {
      if (item.children) {
        return {
          ...item,
          children: item.children.filter(child => !child.roles || child.roles.includes(userRole))
        };
      }
      return item;
    })
    .filter(item => !item.children || item.children.length > 0); // Remove parent items with no visible children
}
