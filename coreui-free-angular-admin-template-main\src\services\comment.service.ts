import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable, throwError, of } from 'rxjs';
import { expand, takeWhile, reduce, map } from 'rxjs/operators';

import { catchError } from 'rxjs/operators';

const httpOptions = {
  headers: new HttpHeaders({
    'Content-Type': 'application/json',
    'Authorization': 'Basic ' + btoa('med:123456')
  })
};

@Injectable({
  providedIn: 'root'
})
export class CommentService {
  private apiURL = environment.apiURL;

  constructor(private http: HttpClient) { }

  /**
   * Add new comment
   * @param commentData - Comment data
   * @returns Observable with creation result
   * @originalName addComment
   */
  addComment(commentData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'commentaire', commentData, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Find comments by line ID
   * @param id - Line ID
   * @returns Observable with comments list
   * @originalName findCommentsByLigne
   */
  findCommentsByLine(id: string): Observable<any[]> {
    return this.http.get<any[]>(this.apiURL + `commentaire/${id}`, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Find all comments with pagination
   * @param page - Page number
   * @param limit - Items per page
   * @returns Observable with comments list
   * @originalName findAllComments
   */
  fetchPage(page: number, limit: number): Observable<{
    commentaire: any[]; page: number;
    limit: number;
    totalItems: number;
  }> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<{
      commentaire: any[];
      page: number;
      limit: number;
      totalItems: number;
    }>(this.apiURL + 'findAllComments', { params, ...httpOptions });
  }

  findAllComments(): Observable<any[]> {
    const pageSize = 50;
    let currentPage = 1;
    let totalItems = 0;

    return this.fetchPage(currentPage, pageSize).pipe(
      expand((response: any) => {
        const { commentaire, totalItems: total } = response;
        totalItems = total;

        const loadedItems = currentPage * pageSize;
        if (loadedItems >= totalItems) return of(null); // no more pages

        currentPage++;
        return this.fetchPage(currentPage, pageSize);
      }),
      takeWhile(response => response !== null),
      map(response => response.commentaire), // extract the comment list
      reduce((all, comments) => all.concat(comments), [] as any[])
    );
  }

  /**
   * Handle HTTP errors
   * @param error - Error object
   * @returns Observable error
   */
  private handleError(error: any): Observable<never> {
    console.error('An error occurred:', error);
    return throwError(() => new Error(error.message || 'Server error'));
  }
}
