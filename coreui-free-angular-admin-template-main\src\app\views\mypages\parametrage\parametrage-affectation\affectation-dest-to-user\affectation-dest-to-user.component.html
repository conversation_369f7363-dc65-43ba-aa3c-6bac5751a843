<app-smart-container title="Affectation Destination Client">
    <div slot="content">
        <app-smart-container
        [title]="'Affecter une Destination au Client'"
        >
            <div slot="content">
                <app-dynamic-form 
                [config]="formConfig"
                >

                </app-dynamic-form>
            </div>
        </app-smart-container>
        <div *ngIf="showTable">
            <app-smart-container [title]="'Destination(s) affecté au client ' + selectedUser.nom_utilisateur">
                <div slot="content">
                    <app-smart-table [data]="selectedUserDestinations" [columns]="tableColumns"
                        [actionButtons]="tableActions"
                        [config]="{emptyMessage:'aucune destination affectée au client ' + selectedUser.nom_utilisateur}"
                        [isLoading]="loading">
                    </app-smart-table>
                </div>
            </app-smart-container>
        </div>
    </div>
</app-smart-container>