import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { UtilisateurService } from 'src/services';
import { ConfirmationDialogService } from 'src/services/confirmation-dialog.service';
import { DynamicFormComponent } from 'src/shared/components/dynamic-form/dynamic-form.component';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';
import { FormConfig } from 'src/shared/models/form.models';
import { TableColumn,ActionButton } from 'src/shared/models/table.models';
import { FormButton } from '../../../../../../shared/models/form.models';

@Component({
  selector: 'app-affectation-client-to-chef',
  standalone: true,
  imports: [SmartContainerComponent,SmartTableComponent,DynamicFormComponent,CommonModule],
  templateUrl: './affectation-client-to-chef.component.html',
  styleUrl: './affectation-client-to-chef.component.scss'
})
export class AffectationClientToChefComponent implements OnInit{
  

  loading: boolean = true;
  showTable: boolean = false;
  chefs: any[] = []
  clientsUnderChef: any[] = []
  clientsUnderChefIds: number[] = []

  selectedChef: any = {};

  allClients: any[] = []


  clientThatCanBeAffected: any[] = []

  tableColumns: TableColumn[] = [
    {
      name:'nom_utilisateur',
      displayName:'Nom',
      sortable:true,
    }
  ]
  tableActions: ActionButton[] = [
    {
      icon:'cil-trash',
      tooltip:'Supprimer',
      color:'danger',
      callback: (row: any) => this.removeUserFromChef(row)
    },   
  ]


  formConfig: FormConfig = {
    title: "Affecter Utilisateur à un Chef",
    fieldGroups: [],
    buttons: []
  }
  constructor(private utilisateurService: UtilisateurService,
    private confirmationDialogService: ConfirmationDialogService,
    private toastr: ToastrService) { }

  ngOnInit(): void {
    this.buildFormConfig()
    this.loadAllusers()
    this.loadChefs()
  }

  removeUserFromChef(row: any) {
    this.confirmationDialogService.confirmDelete('Voulez-vous vraiment supprimer cette affectation ?').then((result: boolean) => {
      if (result) {
        this.clientsUnderChefIds = this.clientsUnderChefIds.filter((id: number) => id !== row.id)
        let data = {
          id_chef: this.selectedChef.id,
          id_users: this.clientsUnderChefIds
        }
        this.utilisateurService.createChefUser(data).subscribe({
          next: (data: any) => {
            this.toastr.success("clients affectés avec succès")
            this.loadUsersByChef(this.selectedChef.id)
          },
          error: (error: any) => {
            this.toastr.error("erreur affectation des clients")
          }
        })
      }
    })
  }

  loadAllusers() {
    this.utilisateurService.getAllClients().subscribe({
      next: (data: any) => {
        this.allClients = Object.values(data)
        console.log(this.allClients)
      },
      error: (error: any) => {
        this.toastr.error("erreur récupération des utilisateurs")
      }
    })
  }

  buildFormConfig() {
    this.formConfig = {
      fieldGroups: [
        {
          fields :[
            {
              name:'selectedChef',
              label: 'Chef',
              type:'select',
              onChange: (value: any) => {
                this.onChefChange(value)
              },
              options: {
                objectArray: this.chefs,
                valueAttribute:'id',
                labelAttribute: 'nom_utilisateur'
              }
            }
          ]
        },
        {
          fields :[
            {
              name:'client',
              label: 'Client',
              type:'select',
              options: {
                objectArray: this.clientsUnderChef,
                valueAttribute:'id',
                labelAttribute: 'nom_utilisateur'
              }
            }
          ]
        }
      ],
      buttons: [
        {
          label: 'Affecter',
          color: 'primary',
          onClick: (formData: any) => {
            this.affecterClients(formData)
          }
        }
      ]
    }

  }
  
  affecterClients(formData: any) {
    this.loading = true;
    console.log(formData)
    this.clientsUnderChefIds.push(parseInt(formData.client))
    const data = {
      id_chef: parseInt(formData.selectedChef),
      id_users: this.clientsUnderChefIds
    }
    this.utilisateurService.createChefUser(data).subscribe({
      next: (data: any) => {
        this.toastr.success("clients affectés avec succès")
        this.loadUsersByChef(this.selectedChef.id)
        
      },
      error: (error: any) => {
        this.toastr.error("erreur affectation des clients")
      },
      complete: () => {
        this.loading = false;
      }
    })
  }
  onChefChange(value:any) {
    this.selectedChef = this.chefs.find((chef: any) => chef.id == value);
    this.selectedChef = {
      id: value,
      nom:this.selectedChef.nom,
      prenom:this.selectedChef.prenom,
      nom_utilisateur: this.selectedChef.nom_utilisateur
    }
    
    this.loadUsersByChef(value)
    this.loading = true;
    this.showTable = true;
  }

  loadUsersByChef(value:number) {
    this.utilisateurService.findUsersByChefId(value).subscribe({
      next: (data: any) => {
        this.clientsUnderChef = data
        this.clientsUnderChefIds = this.clientsUnderChef.map((client: any) => client.id)
        this.clientThatCanBeAffected = this.allClients.filter((client: any) => !this.clientsUnderChefIds.includes(client.id))
        this.clientThatCanBeAffected.forEach((client: any) => {
          client.nom_utilisateur = client.nom + ' ' + client.prenom
        })
        console.log('clientThatCanBeAffected',this.clientThatCanBeAffected)
        const clientsField = this.formConfig.fieldGroups?.[1].fields?.find((f: any) => f.name === 'client');
        if (clientsField && clientsField.options) {
          clientsField.options.objectArray = this.clientThatCanBeAffected;
        }
      },
      error: (error: any) => {
        this.toastr.error("erreur récupération des clients")
      },
      complete: () => {
        this.loading = false;
      }
    })
  }

  loadChefs() {  
    this.utilisateurService.findAllChef().subscribe({
      next: (data: any) => {
        this.chefs = data
        const chefField = this.formConfig.fieldGroups?.[0].fields?.find((f: any) => f.name === 'selectedChef');
        if (chefField && chefField.options) {
          chefField.options.objectArray = this.chefs;
        }
      },
      error: (error: any) => {
        this.toastr.error("erreur récupération des chefs")
      },

    })

  }

}
