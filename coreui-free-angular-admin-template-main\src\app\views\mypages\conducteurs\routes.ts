import { Routes } from "@angular/router";
import { authGuard } from "../../../../guards/auth.guard";
import { roleGuard } from "../../../../guards/role.guard";

export const routes: Routes = [
    {
        path: '',
        data: {
            title: 'Conducteurs'
        },
        children: [
            {
                path: 'liste-conducteurs',
                loadComponent: () => import('./list-conducteurs/list-conducteurs.component').then((c) => c.ListConducteursComponent),
                canActivate: [authGuard, roleGuard],
                data: {
                    title: 'Liste Conducteurs',
                    roles: ['Conducteurs', 'SuperAdmin', 'Administrateur']
                }
            }
        ]
    }
];
