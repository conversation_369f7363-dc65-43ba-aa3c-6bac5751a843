import { Component, OnInit } from '@angular/core';
import { SessionStorageService } from '../../../../../services';
import { CommonModule } from '@angular/common';
import { AdminHomeComponent } from '../admin-home/admin-home.component';
import { ExpediteurHomeComponent } from '../expediteur-home/expediteur-home.component';

@Component({
  selector: 'app-home',
  imports: [CommonModule, AdminHomeComponent, ExpediteurHomeComponent],
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss'
})
export class HomeComponent implements OnInit{

  userRole!: string | null;

  constructor(
    private sessionStorageService: SessionStorageService
  ) 
  {}

  ngOnInit(): void {
    this.userRole = this.sessionStorageService.getSessionValue('userRole')
  }

}
