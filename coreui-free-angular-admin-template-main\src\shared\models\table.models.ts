export interface StatusConfig {
  value: any;
  displayText: string;
  badgeColor: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  icon?: string;
  customClasses?: string;
}

export interface TableColumn {
  name: string;
  displayName: string;
  sortable?: boolean;
  filterable?: boolean;
  nested?: boolean;
  width?: string;
  dataType?: 'text' | 'number' | 'date' | 'boolean' | 'status' ;
  statusConfig?: StatusConfig[];
}


export interface ActionButton {
  label?: string;
  icon?: string;
  color?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  cssClasses?: string;
  isCommentAction?: boolean;
  callback?: (row: any) => void;
  tooltip?: string;
  condition?: (row: any) => boolean;
  disabled?: (row: any) => boolean;

}
export function isCommentAction(button: ActionButton): button is ActionButton & { isCommentAction: true } {
  return button.isCommentAction === true;
}

export interface TableConfig {
  pageSizeOptions?: number[];
  pageSize?: number;
  currentPage?: number;
  selectable?: boolean;
  multiSelect?: boolean;
  emptyMessage?: string;
  striped?: boolean;
  hover?: boolean;
  bordered?: boolean;
  small?: boolean;
  showFilter?: boolean;
  commentable?: boolean;
  commentService?: any;
}