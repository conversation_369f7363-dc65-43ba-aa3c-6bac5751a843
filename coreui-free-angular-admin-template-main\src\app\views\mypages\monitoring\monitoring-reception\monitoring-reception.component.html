<app-smart-container [title]="title">
    <div slot="actions">
        <form [formGroup]="formRadio">
            <c-button-group aria-label="Basic radio toggle button group" role="group">
                <input class="btn-check" formControlName="radio" type="radio" value="Liste des livraisons" />
                <label (click)="setRadioValue('Liste des livraisons')" cButton cFormCheckLabel variant="outline">Liste des livraisons </label>
                
                <input class="btn-check" formControlName="radio" type="radio" value="Liste des colis" />
                <label (click)="setRadioValue('Liste des colis')" cButton cFormCheckLabel variant="outline">Liste des colis</label>
            </c-button-group>
        </form>
    </div>
    <div slot="content">

        <div [hidden]="formRadio.value['radio'] === 'Liste des colis'">
            <app-reception-colis></app-reception-colis>
        </div>
        <div [hidden]="formRadio.value['radio'] === 'Liste des livraisons'">
            <app-reception-commande></app-reception-commande>
        </div>
    </div>
</app-smart-container>