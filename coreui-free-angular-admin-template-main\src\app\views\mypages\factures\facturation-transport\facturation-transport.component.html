<app-smart-container [title]="'Facturation Transport'">
<div slot="actions">
    <app-export-button *ngIf="showTable"
        [buttonText]="'Export Facturation Transport'" 
        [fileName]="'facturation-transport'"
        [data]="FactureList">
    </app-export-button>
</div>
<div slot="content">

    <app-dynamic-form
    [config]="formConfig"
    >
    </app-dynamic-form>
<div [hidden]="!showTable">
    <app-smart-container >
        <div slot="actions">
            
            <app-smart-button
            label="Visualiser Perfacture"
            icon="cilFile"
            color="primary"
            (onClick)="showFacture()">
            </app-smart-button>
            
        </div>
        <div slot="content">
            <app-smart-table
            [isLoading]="loading"
            [columns]="tableColumns"
            [data]="FactureList"
            [config]="TableConfig"
            (selectionChange)="onSelectChange($event)">

            </app-smart-table>
        </div>
    </app-smart-container>
    </div>
</div>

</app-smart-container>
<div [hidden]="!afficherFacture" id="smart-container-details">
<app-facture-impression  #factureImpression></app-facture-impression>
</div>