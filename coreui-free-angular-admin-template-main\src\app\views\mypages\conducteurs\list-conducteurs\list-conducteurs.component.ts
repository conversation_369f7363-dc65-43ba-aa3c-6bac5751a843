import { Component, OnInit } from '@angular/core';
import { ConducteurService } from 'src/services/conducteur.service';
import { DynamicModalComponent } from 'src/shared/components/dynamic-modal/dynamic-modal.component';
import { FormModalComponent } from 'src/shared/components/form-modal/form-modal.component';
import { SmartButtonComponent } from 'src/shared/components/smart-button/smart-button.component';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';
import { FormButton, FormConfig } from 'src/shared/models/form.models';
import { ActionButton, TableColumn, } from 'src/shared/models/table.models';
import { Md5 } from 'md5-typescript';
import { SessionStorageService } from 'src/services';
import { ConfirmationDialogService } from 'src/services/confirmation-dialog.service';
import { ToastrService } from 'ngx-toastr';


@Component({
  selector: 'app-list-conducteurs',
  standalone: true,
  imports: [SmartContainerComponent, SmartButtonComponent, SmartTableComponent, FormModalComponent],
  templateUrl: './list-conducteurs.component.html',
  styleUrl: './list-conducteurs.component.scss'
})
export class ListConducteursComponent implements OnInit{
  listConducteurs: any[] = [];
  showModal: boolean = false;

  constructor(private conducteurService: ConducteurService , 
    private sessionStorage: SessionStorageService,
    private confirmationDialog: ConfirmationDialogService,
    private Toastr: ToastrService) {
    
  }

  TableColumns: TableColumn[] = [
    { name: 'nom_utilisateur', displayName: 'Nom Utilisateur', sortable: true , filterable: true },
    //{name: 'image_cin', displayName: 'Image CIN', sortable: false, dataType: 'image'},
    { name: 'nom', displayName: 'Nom', sortable: true , filterable: true },
    { name: 'prenom', displayName: 'Prénom', sortable: true , filterable: true },
    { name: 'mobile', displayName: 'Mobile', sortable: true , filterable: true },
    { name: 'cin', displayName: 'CIN', sortable: true , filterable: true },
    { name: 'transporteur', displayName: 'Transporteur', sortable: true },
  ];

  TableActions: ActionButton[] = [
    {
      tooltip: 'Blocker',
      icon:'cilXCircle',
      color: 'danger',
      callback: (conducteur: any) => {
        this.blocker(conducteur);
      }
    }
  ]

  formConfig: FormConfig = {
    title: 'Ajouter Conducteur',
    fields: [
      { name: 'nom', label: 'Nom', type: 'text', required: true , placeholder: 'Entrez le nom' },
      { name: 'prenom', label: 'Prénom', type: 'text', required: true , placeholder: 'Entrez le prénom' },
      { name: 'nom_utilisateur', label: 'Nom d\'utilisateur', type: 'text', required: true , placeholder: 'Entrez le nom d\'utilisateur' },
      { name: 'mot_de_passe', label: 'Mot de passe', type: 'password', required: true , placeholder: 'Entrez le mot de passe' },
      { name: 'mobile', label: 'Mobile', type: 'text', required: true , placeholder: 'Entrez le mobile' },
      { name: 'cin', label: 'CIN', type: 'text', required: true , placeholder: 'Entrez le CIN' },
      { name: 'image_cin', label: 'Image CIN', type: 'image' , placeholder: 'Télécharger l\'image du CIN' },
    ],
    buttons: [
      { label: 'Annuler', color: 'secondary', onClick: () => this.closeModal() },
      { label: 'Ajouter',  color: 'primary' , onClick: (formData: any) => this.onFormSubmit(formData) },
    ]
  }

  ngOnInit(): void {
    this.loadConducteurs();
  }


  loadConducteurs() {
    this.conducteurService.findConducteurAdmin().subscribe({
      next: (data:any) => {
        this.listConducteurs = data;
      },
      error: (error:any) => {
        console.error('Error loading conducteurs:', error);
      }
    });
  }

  openModal() {
    this.showModal = true;
    console.log('Opening modal to add conducteur', this.showModal);
  }

  closeModal() {
    this.showModal = false;
    console.log('Modal closed');
  }

  onFormSubmit(conducteur: any) {
    
    const newConducteur = {
      nom: conducteur.nom,
      prenom: conducteur.prenom,
      nom_utilisateur: conducteur.nom_utilisateur,
      mot_de_passe: this.generateMdp(conducteur.mot_de_passe),
      mobile: conducteur.mobile,
      cin: conducteur.cin,
      image_cin: conducteur.image_cin ? conducteur.image_cin : null,
      ajoutee_par: this.sessionStorage.getSessionValue("iduser")
    }
    console.log('Form submitted with data:', newConducteur);
    this.conducteurService.addConducteur(newConducteur).subscribe({
      next: (response :any) => {
        this.Toastr.success('Conducteur ajouté avec succès');
        this.loadConducteurs();
        this.closeModal();
      },
      error: (error :any) => {
        console.error('Error adding conducteur:', error);
        this.closeModal();
      },
      
    });
    this.closeModal();
  }
  generateMdp(mdp:any) {
  return Md5.init(mdp) ; 
}
  blocker(conducteur: any) {
    this.confirmationDialog.confirmDelete(`Êtes-vous sûr de vouloir bloquer ce conducteur ${conducteur.nom} ?`).then(
      (result :any) => { 
      if (result) {
        console.log('Blocking conducteur with ID:', conducteur.id);
        this.conducteurService.blockConducteur(conducteur.id).subscribe({
          next: (response :any) => {
            this.Toastr.success(`Conducteur ${conducteur.nom} bloqué avec succès.`);
            this.loadConducteurs();
          },
          error: (error :any) => {
            console.error('Error blocking conducteur:', error);
          }
        });
      }
    });
  }



}
