import { ComponentFixture, TestBed } from '@angular/core/testing';

import { WrapperMonitoringComponent } from './wrapper-monitoring.component';

describe('WrapperMonitoringComponent', () => {
  let component: WrapperMonitoringComponent;
  let fixture: ComponentFixture<WrapperMonitoringComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [WrapperMonitoringComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(WrapperMonitoringComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
