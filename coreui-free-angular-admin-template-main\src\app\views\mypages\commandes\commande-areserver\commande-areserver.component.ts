import { Component, OnInit } from '@angular/core';
import { DynamicExportButtonComponent } from '../../../../../shared/components/dynamic-export-button/dynamic-export-button.component';
import { DynamicFormComponent } from '../../../../../shared/components/dynamic-form/dynamic-form.component';
import { SmartTableComponent } from '../../../../../shared/components/smart-table/smart-table.component';
import { CamionService } from '../../../../../services/camion.service';
import { CommandeService } from '../../../../../services/commande.service';
import { ConducteurService } from '../../../../../services/conducteur.service'
import { TableColumn, TableConfig } from '../../../../../shared/models/table.models';
import { DynamicModalComponent } from '../../../../../shared/components/dynamic-modal/dynamic-modal.component';
import { CommonModule } from '@angular/common';
import { FormConfig } from '../../../../../shared/models/form.models';
import { LigneCmdService } from '../../../../../services/ligne-cmd.service';
import { FormsModule } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { NotificationService } from '../../../../../services';
import { SmartContainerComponent } from "src/shared/components/smart-container/smart-container.component";
import { SmartButtonComponent } from "src/shared/components/smart-button/smart-button.component";

@Component({
  selector: 'app-commande-areserver',
  standalone: true,
  imports: [
    CommonModule,
    DynamicFormComponent,
    SmartTableComponent,
    DynamicExportButtonComponent,
    DynamicModalComponent,
    FormsModule,
    SmartContainerComponent,
],
  templateUrl: './commande-areserver.component.html',
  styleUrl: './commande-areserver.component.scss'
})
export class CommandeAReserverComponent implements OnInit {
  camionList = [];
  conducteurList: any[] = []
  SourceData: any = [];

  minDate: String;
  selectedRows: any[] = [];
  isSelected: boolean = false;
  totalVolume: number = 0;
  showFirstModal: boolean = false;
  showHoraireModal: boolean = false;
  formConfig: FormConfig= {
    title: 'Réserver Commande',
    fields: [
      {
        name: 'camionId',
        label: 'Camion',
        type: 'select',
        placeholder: 'Sélectionner un camion',
        options: { objectArray: this.camionList, valueAttribute: 'id', labelAttribute: 'immatriculation' },
        required: true
      },
      {
        name: 'conducteurId',
        label: 'Conducteur',
        type: 'select',
        placeholder: 'Sélectionner un conducteur',
        options: { objectArray: this.conducteurList, valueAttribute: 'id', labelAttribute: 'fullName' },
        required: true
      },
      {
        name: 'date',
        label: 'Date',
        type: 'date',
        required: true
      }
    ],
    buttons: [
      { label: 'Réserver', color: 'primary', onClick: (formData) => this.onFormSubmit(formData)},

    ]
  }

  TableColumn: TableColumn[] = [
    { name: 'id', displayName: 'ID', sortable: true, dataType: 'number' },
    { name: 'date_creation', displayName: 'Creation Date', sortable: true, dataType: 'date' },
    { name: 'ajoutee_par', displayName: 'Ajoutée Par', sortable: true, dataType: 'text' },
    { name: 'date_depart', displayName: 'Date de Départ', sortable: true, dataType: 'date' },
    { name: 'date_arrivee', displayName: 'Date d\'Arrivée', sortable: true, dataType: 'date' },
    { name: 'nom_depart', displayName: 'Destination Départ', sortable: true, dataType: 'text' },
    { name: 'nom_arrivee', displayName: 'Destination Arrivée', sortable: true, dataType: 'text' },
    { name: 'type_ligne', displayName: 'Type', sortable: true, dataType: 'text' },
    { name: 'volume', displayName: 'Volume (m³)', sortable: true, dataType: 'number' },
    { name: 'quantite', displayName: 'Quantité', sortable: true, dataType: 'number' },
    { name: 'estimation', displayName: 'Volume Estimé', sortable: true, dataType: 'number' }

  ]
  tableConfig: TableConfig = {
    pageSizeOptions: [5, 10, 25],
    pageSize: 10,
    selectable: true,
    multiSelect: true,
    emptyMessage: 'Aucun Commande A réserver',
    striped: true,
    hover: true,
    bordered: false,
    small: false,
    showFilter: true
  }
  historique: number = 0
  today = new Date().toISOString().slice(0, 10);
  hours: any[] = [];

  selectedCamion: any = {};
  selectedConducteur: any = {};
  selectedDate: string = '';
  tot: number = 0;

  resultatFinal: any = '';
  filteredRows: any[] = [];

  constructor(
    private commandeService: CommandeService,
    private camionService: CamionService,
    private conducteurService: ConducteurService,
    private ligneCmdService: LigneCmdService,

    private notificationService: NotificationService,
    private toastr: ToastrService
  ) {
    const today = new Date();
    this.minDate = today.toISOString().split('T')[0]
  }

  ngOnInit(): void {
    this.toastr.success('Bienvenue dans la page de réservation des commandes !', 'Réservation Commandes');
    this.loadSourceData();
    this.loadCamionList();
    this.loadConducteurList();
    
    for (let i = 0; i < 24; i++) {
      const hourString = i.toString().padStart(2, '0'); // Formatage de l'heure à deux chiffres
      this.hours.push(hourString);
    }
  }
  loadSourceData() {
    this.commandeService.findAllCommandesAReserver().subscribe({
      next: (data) => {
        this.SourceData = data;
        console.log("Commande A Reserve", this.SourceData)
      },
      error: (error) => {
        console.error('Error loading commandes:', error);
      }
    });
  }
  loadCamionList() {
    this.camionService.findAllCamion().subscribe({
      next: (data) => {
        this.camionList = data;
        console.log("Camion", this.camionList)
        
        const CamionField = this.formConfig.fields?.find((field) => field.name === 'camionId');
        if (CamionField && CamionField.options) {
          CamionField.options.objectArray = this.camionList;
        }
        
      },
      error: (error) => {
        console.error('Error loading camions:', error);
      }
    });
  }
  loadConducteurList() {
    this.conducteurService.findConducteur().subscribe({
      next: (data) => {
        this.conducteurList = data;
        this.conducteurList = this.conducteurList.map((conducteur) => ({
        ...conducteur,
        fullName: `${conducteur.nom.trim()} ${conducteur.prenom.trim()}`
      }));
      const ConducteurField = this.formConfig.fields?.find((field) => field.name === 'conducteurId');
      if (ConducteurField && ConducteurField.options) {
        ConducteurField.options.objectArray = this.conducteurList;
      }
      },
    error: (error) => {
      console.error('Error loading conducteurs:', error);
    }
  });
}

  handleSelectionChange(selectedRows: any[]) {
    this.selectedRows = selectedRows;
    if (this.selectedRows.length > 0) {
      this.isSelected = true;
    }
    else {
      this.isSelected = false;
    }
    console.log("Selected Rows:", this.selectedRows);
    console.log("Is Selected:", this.isSelected);
  }




  async onFormSubmit(formData: any) {
    if (!this.isSelected) {
      this.toastr.error('Merci de sélectionner des lignes de Commandes!');
      return;
    }
    if (!formData.camionId || !formData.conducteurId || !formData.date) {
      this.toastr.error('Merci de remplir tous les champs obligatoires!');
      return;
    }
    console.log("Form Data:", formData);
    this.selectedCamion = this.camionList.find((camion:any) => camion.id === parseInt(formData.camionId))
    this.selectedConducteur = this.conducteurList.find((conducteur:any) => conducteur.id === parseInt(formData.conducteurId))
    this.selectedDate = formData.date;
    this.totalVolume = 0
    console.log("Selected Camion:", this.selectedCamion);
    console.log("Selected Conducteur:", this.selectedConducteur);
    if (this.selectedRows.length === 0) {
      this.toastr.error('Merci de sélectionner des lignes de Commandes!');
      return;
    }

    if (formData.camion === "" || formData.conducteur === "") {
      this.toastr.error('Merci de remplir les champs obligatoires!');
      return;
    }
    if (formData.date == "") {
      this.toastr.error('Merci de remplir le date prévu du voyage!');
      return;
    }

    this.ligneCmdService.CountVolumeParVoyage(this.selectedCamion.id, this.selectedConducteur.id, formData.date)
      .subscribe({
        next: (res) => {
          console.log(res);
          if (res && res.result) {
            this.historique = res.result.estimation + res.result.volume;
          }
          this.totalVolume = this.selectedRows.reduce((total, row) => {
            return total + row.volume + row.estimation;
          }, 0);
      
          this.totalVolume = parseFloat((this.totalVolume + this.historique).toFixed(2));
          console.log(this.totalVolume)
      
          this.showFirstModal = true;
          console.log("Total Volume:", this.totalVolume);
        },
        error: (error) => {
          console.error("Error: ", error);
          this.toastr.error("erreur")
        }
      });
    
  }


  res() {
    console.log("Lignes initiales :", this.filteredRows);

    let hasError = false;

    // Parcourir `filteredRows` pour vérifier les erreurs
    for (const destination of this.filteredRows) {
      console.log("Destination :", destination);
      if (
        typeof destination.tolerance === 'undefined' ||
        typeof destination.plage_horaire_heure_arr === 'undefined' ||
        typeof destination.plage_horaire_heure_dep === 'undefined'
      ) {
        this.toastr.error("Merci de remplir tous les champs.");
        hasError = true;
        break;
      }
    }

    if (!hasError) {
      // Filtrer les lignes sélectionnées à partir de `this.source`
      //const selectedLignes = this.source.filter(ligne => this.selectedRows.includes(ligne.id));

      // Regrouper les lignes par `nom_depart` et `nom_arrivee`
      const groupedRoutes: { [key: string]: any[] } = {};

      this.selectedRows.forEach(ligne => {
        const key = `${ligne.nom_depart}-${ligne.nom_arrivee}`; // Clé de regroupement
        if (!groupedRoutes[key]) {
          groupedRoutes[key] = [];
        }
        groupedRoutes[key].push(ligne);
      });

      // Appliquer les ajustements pour chaque groupe
      for (const key in groupedRoutes) {
        const group = groupedRoutes[key];
        const firstDestination = group[0];

        const tolerance = parseFloat(firstDestination.tolerance);
        const heureArr = parseInt(firstDestination.plage_horaire_heure_arr, 10);

        // const newHeureArr = (heureArr + tolerance) * 60;
        let newHeureDep = (heureArr - tolerance) * 60;

        if (newHeureDep < 0) {
          newHeureDep = 1440 + newHeureDep;
        }

        group.forEach(ligne => {
          ligne.plage_horaire_heure_arr
          ligne.plage_horaire_heure_dep
          ligne.tolerance

          console.log("Horaires ajustés :", {
            nom_depart: ligne.nom_depart,
            nom_arrivee: ligne.nom_arrivee,
            plage_horaire_heure_arr: ligne.plage_horaire_heure_arr,
            plage_horaire_heure_dep: ligne.plage_horaire_heure_dep,
          });
        });
      }

      // Rassembler toutes les lignes dans une seule liste
      const allLignes: any[] = [];

      // Parcourir chaque groupe et ajouter les éléments à `allLignes`
      for (const key in groupedRoutes) {
        if (groupedRoutes.hasOwnProperty(key)) { // Vérifier que la propriété existe
          const group = groupedRoutes[key];
          group.forEach(ligne => {
            allLignes.push(ligne); // Ajouter chaque ligne à la liste unique
          });
        }
      }

      // Afficher le résultat
      // Afficher toutes les lignes mises à jour sans hiérarchie
      console.log("Toutes les lignes mises à jour :", allLignes);
      this.reserverSelectedRows()
    }
  }
  async reserverSelectedRows() {
    let reservations:any;
    reservations = this.buildReservations();

    console.log("Reservations :", reservations);
    // Mettre à jour les réservations
    this.updateReservations(reservations)

    // Envoyer les notifications SMS
    this.sendSmsNotification()
    this.loadCamionList();
    this.resetSelections();
  }
  buildReservations(): any[] {
    const reservations: any[] = [];

    for (const row of this.selectedRows) {
      const reservation = {
        id: row.id,
        id_camion: this.selectedCamion.id,
        id_conducteur: this.selectedConducteur.id,
        date_voyage: this.selectedDate,
        plage_horaire_heure_arr: null as string | null,
        plage_horaire_heure_dep: null as string | null,
        tolerance: null as number | null,
      };

      const initialSource = this.SourceData.find((sourceRow: any) => sourceRow.id == row.id);
      if (initialSource) {
        const { nom_arrivee, nom_depart } = initialSource;

        const correspondingSource = this.SourceData.find(
          (sourceRow: any) =>
            sourceRow.nom_arrivee == nom_arrivee && sourceRow.nom_depart == nom_depart
        );
        
        if (correspondingSource) {
          reservation.plage_horaire_heure_arr = correspondingSource.plage_horaire_heure_arr + ':00';
          reservation.plage_horaire_heure_dep = correspondingSource.plage_horaire_heure_dep + ':00';
          reservation.tolerance = correspondingSource.tolerance;
        }
      }

      reservations.push(reservation);

    }
    console.log("////////*************", reservations)
    return reservations;
  }

  /**
   * Met à jour les réservations côté serveur.
   */
  updateReservations(reservations: any[]): void {
    console.log("Réservations à mettre à jour :", reservations);
    const data = { updates: reservations }
    try {
      console.log('Mise à jour des réservations :', data);
      let updatedPts :any ;
      this.commandeService.updateStatusReserved(data).subscribe({
        next: (data) => {
          updatedPts = data;
          console.log('Mise à jour réussie :', updatedPts);
        },
        error: (error) => {
          console.error('Erreur lors de la mise à jour des réservations :', error);
          throw error;
        }
      });
      // Met à jour la source pour ne plus afficher les lignes mises à jour
    } catch (error) {
      console.error('Erreur lors de la mise à jour des réservations :', error);
      throw error;
    }
  }

  sendSmsNotification(): void {
    const selectedDate = new Date(this.selectedDate);
    const jour = selectedDate.getDate().toString().padStart(2, '0');
    const mois = (selectedDate.getMonth() + 1).toString().padStart(2, '0');
    const annee = selectedDate.getFullYear();
    const dateFormatee = `${jour}/${mois}/${annee}`;
    this.recupererNomsDepartArrivee()
    const contentSms: any = {
      phone: this.selectedConducteur.mobile,
      message: `Vous Avez un voyage le ${dateFormatee} : ${this.resultatFinal} avec estimation de ${this.tot}m³`,
    };

    try {
      this.notificationService.SendSmsNotification(contentSms).subscribe({
        next: () => {
          this.toastr.success('SMS envoyé avec succès !');
        },
        error: (error) => {
          console.error('Erreur lors de l’envoi du SMS :', error);
          this.toastr.error('Erreur lors de l’envoi du SMS.');
        }
      });
      this.notificationService.sendReservationMail(this.selectedRows).subscribe({
        next: () => {
          this.toastr.success('Email envoyé avec succès !');
        },
        error: (error) => {
          console.error('Erreur lors de l’envoi de l’email :', error);
          this.toastr.error('Erreur lors de l’envoi de l’email.');
        }
      });
      this.SourceData = this.SourceData.filter((sourceRow : any) => !this.selectedRows.includes(sourceRow.id));

      this.resetSelections()
      this.toastr.success('SMS envoyé avec succès !');
    } catch (error) {
      console.error("Erreur lors de l'envoi du SMS :", error);
      this.toastr.error('Erreur lors de l’envoi du SMS.');
    }

  }
  resetSelections(): void {
    this.selectedCamion = {};
    this.selectedConducteur = {};
    this.selectedRows = [];
    this.selectedDate = '';
    this.loadSourceData();
    this.isSelected = false;
    this.showFirstModal = false;
    this.showHoraireModal = false;
  }

  recupererNomsDepartArrivee() {
    const nomsDepartArriveeMap: Map<string, Set<string>> = new Map<string, Set<string>>(); // Map pour stocker les paires nom_depart - ensemble de nom_arrivee
    let totalVolume = 0; // Initialiser le total du volume à 0
    let totalEstimation = 0; // Initialiser le total de l'estimation à 0
  
    // Parcourir les lignes sélectionnées
    for (const selectedRow of this.selectedRows) {
      // Rechercher la ligne correspondante dans this.SourceData
      const correspondingRow = this.SourceData.find((sourceRow: any) => sourceRow.id == selectedRow.id);

      console.log(correspondingRow)
      // Vérifier si une ligne correspondante a été trouvée
      if (correspondingRow) {
        // Récupérer les valeurs de nom_depart et nom_arrivee de la ligne correspondante
        let nomArrivee: string;
        const nomDepart: string = correspondingRow.nom_depart;
        if (correspondingRow.type_ligne == "livraison PF (dépôt)" ||
          correspondingRow.type_ligne == "Livraison PSF" ||
          correspondingRow.type_ligne == "livraison MP (tissu)" ||
          correspondingRow.type_ligne == "Agencement et Matériels") {
          nomArrivee = correspondingRow.nom_arrivee + ' à collecté à ' + correspondingRow.plage_horaire_heure_arr;

        } else {
          nomArrivee = correspondingRow.nom_arrivee + ' à Livré à ' + correspondingRow.plage_horaire_heure_arr;

        }

        // Vérifier si nom_depart existe déjà dans la Map
        if (nomsDepartArriveeMap.has(nomDepart)) {
          // Ajouter nom_arrivee à l'ensemble correspondant à nom_depart dans la Map
          const arriveeSet = nomsDepartArriveeMap.get(nomDepart);
          if (arriveeSet) {
            arriveeSet.add(nomArrivee);
          }
        } else {
          // Créer un nouvel ensemble avec nom_arrivee pour nom_depart et l'ajouter à la Map
          nomsDepartArriveeMap.set(nomDepart, new Set([nomArrivee]));
        }

        // Ajouter le volume de la ligne au total du volume
        totalVolume += correspondingRow.volume;
        // Ajouter l'estimation de la ligne au total de l'estimation
        totalEstimation += correspondingRow.estimation;
      }
    }

    // Afficher les résultats dans la console
    console.log('Noms de départ et d\'arrivée par départ :', nomsDepartArriveeMap);

    // Afficher le total du volume et le total de l'estimation dans la console
    console.log('Total du volume :', totalVolume);
    console.log('Total de l\'estimation :', totalEstimation);
    // Afficher le total du volume et le total de l'estimation dans la console
    this.tot = parseFloat((totalVolume + totalEstimation).toFixed(2));
    console.log('ffffffffffffffffffffffff', this.tot);
    // Simplifier les noms de départ
    const nomsDepartSimples = Array.from(nomsDepartArriveeMap.keys());

    // Assembler les noms de départ et d'arrivée dans la forme spécifiée
    const departArriveeStrings = Array.from(nomsDepartArriveeMap.entries()).map(([nomDepart, nomsArrivee]) => {
      const nomsArriveeString = Array.from(nomsArrivee).join('/');
      return `${nomDepart} : ${nomsArriveeString}`;
    });

    // Assembler toutes les chaînes dans une seule chaîne
    this.resultatFinal = departArriveeStrings.join('; ');

    // Afficher le résultat final dans la console
    console.log('Résultat final :', this.resultatFinal);

    // Afficher les noms de départ simplifiés dans la console
    console.log('Noms de départ simplifiés :', nomsDepartSimples);

  }
  next() {
    const uniqueCombinations: { [key: string]: boolean } = {};
    this.filteredRows = []
    for (const selectedRow of this.selectedRows) {
      // Trouver la ligne correspondante dans `this.SourceData`
      const correspondingRow = this.SourceData.find((sourceRow: any) => sourceRow.id == selectedRow.id);
      console.log(correspondingRow)
      // S'assurer qu'une ligne correspondante a été trouvée
      if (correspondingRow) {
        const { nom_depart, nom_arrivee } = correspondingRow;

        // Créer une clé unique pour cette combinaison de `nom_depart` et `nom_arrivee`
        const uniqueKey = `${nom_depart}-${nom_arrivee}`;

        // Vérifier si cette combinaison a déjà été ajoutée
        if (!uniqueCombinations[uniqueKey]) {
          uniqueCombinations[uniqueKey] = true; // Marquer cette combinaison comme ajoutée
          this.filteredRows.push(correspondingRow); // Ajouter la ligne sans duplication
        }
      }
    }
    console.log("Lignes sans duplication :", this.filteredRows); // Afficher les lignes sans duplication
    this.showHoraireModal = true;
    this.showFirstModal = false;
  }

}
