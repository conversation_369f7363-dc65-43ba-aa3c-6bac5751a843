import { Component, ViewChild } from '@angular/core';
import { ContextService } from '../../../../../../services/strategy/access-context.service';
import { ToastrService } from 'ngx-toastr';
import { DetailContainerComponent } from '../shared/detail-container/detail-container.component';
import { ActionButton, TableColumn } from '../../../../../../shared/models/table.models';
import { TableConfig } from '../../../../../../shared/models/table.models';
import { SmartContainerComponent } from '../../../../../../shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from '../../../../../../shared/components/smart-table/smart-table.component';
@Component({
  selector: 'app-my-validated-commands',
  standalone: true,
  imports: [SmartContainerComponent, SmartTableComponent, DetailContainerComponent],
  templateUrl: './my-validated-commands.component.html',
  styleUrl: './my-validated-commands.component.scss'
})
export class MyValidatedCommandsComponent {
  @ViewChild('DetailContainer') DetailContainer !: DetailContainerComponent;

  loading : boolean = false;

  source : any[] = [];

  constructor(
    private contextService : ContextService,
    private toastr : ToastrService
  ) { }

  TableColumns: TableColumn[] = [
    {
      name: 'id',
      displayName: 'ID',
      sortable: true,
      filterable: true,
      
    },
    {
      name: 'date_depart',
      displayName: 'Date de depart',
      sortable: true,
      filterable: true,
    },
    {
      name: 'date_arrivee',
      displayName: 'Date d\'arrivée',
      sortable: true,
      filterable: true,
    },
    {
      name: 'statut',
      displayName: 'Statut',
      sortable: true,
      filterable: true,
      dataType: 'status',
      statusConfig: [
        {
          value: 'en attente de confirmation',
          displayText: 'En attente de confirmation',
          badgeColor: 'warning',
          icon : 'cil-clock'
        },
        {
          value: 'Valide',
          displayText: 'Validé',
          badgeColor: 'success',
          icon : 'cil-check'
        }
      ]
    },
    {
      name: 'type_conditionnement',
      displayName: 'Type de conditionnement',
      sortable: true,
      filterable: true,
    },
    {
      name: 'type_marchandise',
      displayName: 'Type de marchandise',
      sortable: true,
      filterable: true,
    }, 
    {
      name: 'nom_utilisateur',
      displayName: 'Ajouté Par',
      sortable: true,
      filterable: true,
    },
    {
      name:'mention',
      displayName: 'Mention',
      sortable: true,
      filterable: true,
    }
  ]
  TableActions: ActionButton[] = [
    {
      tooltip: 'detail',
      color: 'info',
      icon: 'cilZoom',
      callback: (row: any) => {
        this.showDetail(row);
      }
    }
  ] 

  tableConfig: TableConfig = {
    emptyMessage: 'Aucune commande trouvée',
    
  }

  ngOnInit(): void {
    this.loadMyCommands();
  }

  ScrollToTop() {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }
  showDetail(row: any) {
    this.DetailContainer.source = row;
    this.DetailContainer.open();
    setTimeout(() => {
      this.ScrollToTop();
    }, 100);
  }

  loadMyCommands() {
    this.loading = true;
    this.contextService.findValidCommandsByUserType().subscribe({
      next: (data:any) => {
        this.source = data;
        console.log('data', this.source);
        this.loading = false;
      },
      error: (error:any) => {
        this.toastr.error('Error loading commands');
        console.error(error);
        this.loading = false;
      }
    })
  }
}
