import { Component, OnInit, OnDestroy, ViewChild, ElementRef, Renderer2 } from '@angular/core';
import { RouterLink, RouterOutlet } from '@angular/router';
import { NgScrollbar } from 'ngx-scrollbar';
import { Subscription } from 'rxjs';

import { IconDirective } from '@coreui/icons-angular';
import {
  ContainerComponent,
  ShadowOnScrollDirective,
  SidebarBrandComponent,
  SidebarComponent,
  SidebarFooterComponent,
  SidebarHeaderComponent,
  SidebarNavComponent,
  SidebarToggleDirective,
  SidebarTogglerDirective
} from '@coreui/angular';

import { DefaultFooterComponent, DefaultHeaderComponent } from './';
import { INavDataWithRoles, navItems } from './_nav';
import { CommonModule } from '@angular/common';
import { CustomizerThemeComponent } from './customizer-theme/customizer-theme.component';
import { LayoutService, SessionStorageService, CustomerService } from '../../../services';
import { CustomizerOptions, CustomizerChangeType, SIDEBAR_COLORS, User } from '../../../models';
import { ToastrComponentlessModule, ToastrModule } from 'ngx-toastr';
import { getNavItemsByRole } from "./_nav";
function isOverflown(element: HTMLElement) {
  return (
    element.scrollHeight > element.clientHeight ||
    element.scrollWidth > element.clientWidth
  );
}

@Component({
  selector: 'app-dashboard',
  standalone: true,
  templateUrl: './default-layout.component.html',
  styleUrls: ['./default-layout.component.scss'],
  imports: [
    SidebarComponent,
    SidebarHeaderComponent,
    SidebarBrandComponent,
    SidebarNavComponent,
    SidebarFooterComponent,
    SidebarToggleDirective,
    SidebarTogglerDirective,
    ContainerComponent,
    DefaultFooterComponent,
    DefaultHeaderComponent,
    IconDirective,
    NgScrollbar,
    RouterOutlet,
    RouterLink,
    ShadowOnScrollDirective,
    CommonModule,
    CustomizerThemeComponent,
    ToastrComponentlessModule
  ]
})
export class DefaultLayoutComponent implements OnInit, OnDestroy {
  @ViewChild('sidebarBgImage', { static: false }) sidebarBgImage!: ElementRef;

  private layoutSubscription: Subscription = new Subscription();

  constructor(
    private layoutService: LayoutService,
    private renderer: Renderer2,
    private sessionStorageService: SessionStorageService,
    private customerService: CustomerService
  ) {}

  public navItems!: INavDataWithRoles[] ;


  ngOnInit(): void {
    // Subscribe to customizer changes to apply theme modifications
    this.layoutSubscription.add(
      this.layoutService.customizerChangeEmitted$.subscribe((change: CustomizerOptions | CustomizerChangeType) => {
        if (typeof change === 'object' && change) {
          this.applyThemeChanges(change);
        }
      })
    );
    console.log('role: ',this.sessionStorageService.getSessionValue('userRole'));
    this.navItems = getNavItemsByRole(this.sessionStorageService.getSessionValue('userRole') || '');
    

    // Load user's saved color preference on initialization
    this.loadUserColorPreference();
  }

  ngOnDestroy(): void {
    this.layoutSubscription.unsubscribe();
  }

  private applyThemeChanges(options: CustomizerOptions): void {
    const body = document.body;
    const sidebar = document.getElementById('sidebar1');

    if (sidebar) {
      // Apply sidebar background color using data-background-color attribute (zen-logistic approach)
      if (options.bgColor) {
        // Remove any existing data-background-color attribute
        sidebar.removeAttribute('data-background-color');

        // Set the new background color
        sidebar.setAttribute('data-background-color', options.bgColor);

        // Ensure sidebar has proper positioning for background overlay
        if (!sidebar.classList.contains('sidebar')) {
          sidebar.classList.add('sidebar');
        }

        // Remove CoreUI specific attributes that might interfere
        sidebar.removeAttribute('data-coreui-theme');
        sidebar.style.removeProperty('--cui-sidebar-bg');
      }

      // Apply sidebar background image
      if (options.bgImage && options.bgImageDisplay) {
        this.applySidebarBackgroundImage(sidebar, options.bgImage);
      } else {
        this.removeSidebarBackgroundImage(sidebar);
      }

      // Apply sidebar size classes to body
      if (options.sidebarSize) {
        body.classList.remove('sidebar-sm', 'sidebar-md', 'sidebar-lg');
        body.classList.add(options.sidebarSize);
      }

      // Apply compact menu (narrow sidebar)
      if (options.compactMenu !== undefined) {
        if (options.compactMenu) {
          sidebar.classList.add('sidebar-narrow');
        } else {
          sidebar.classList.remove('sidebar-narrow');
        }
      }


    }
  }

  private applySidebarBackgroundImage(sidebar: HTMLElement, imageUrl: string): void {
    // Remove existing background image element
    const existingBgImage = sidebar.querySelector('.sidebar-background');
    if (existingBgImage) {
      existingBgImage.remove();
    }

    // Create new background image element
    const bgImageElement = this.renderer.createElement('div');
    this.renderer.addClass(bgImageElement, 'sidebar-background');
    this.renderer.setStyle(bgImageElement, 'background-image', `url("${imageUrl}")`);
    this.renderer.setStyle(bgImageElement, 'position', 'absolute');
    this.renderer.setStyle(bgImageElement, 'top', '0');
    this.renderer.setStyle(bgImageElement, 'left', '0');
    this.renderer.setStyle(bgImageElement, 'width', '100%');
    this.renderer.setStyle(bgImageElement, 'height', '100%');
    this.renderer.setStyle(bgImageElement, 'background-size', 'cover');
    this.renderer.setStyle(bgImageElement, 'background-position', 'center');
    this.renderer.setStyle(bgImageElement, 'background-repeat', 'no-repeat');
    this.renderer.setStyle(bgImageElement, 'z-index', '-1');
    this.renderer.setStyle(bgImageElement, 'opacity', '0.1');

    // Append to sidebar
    this.renderer.appendChild(sidebar, bgImageElement);
  }

  private removeSidebarBackgroundImage(sidebar: HTMLElement): void {
    const existingBgImage = sidebar.querySelector('.sidebar-background');
    if (existingBgImage) {
      existingBgImage.remove();
    }
  }

  /**
   * Load user's saved color preference from session or backend
   */
  private loadUserColorPreference(): void {
    // First check session data for color preference
    const sessionData = this.sessionStorageService.getSession();
    let colorToApply = sessionData?.color || this.sessionStorageService.getSessionColor() || SIDEBAR_COLORS.BLACK;

    // Apply the color immediately
    this.applyThemeChanges({
      direction: 'ltr',
      bgColor: colorToApply,
      bgImage: 'assets/img/sidebar-bg/01.jpg',
      bgImageDisplay: false,
      compactMenu: false,
      sidebarSize: 'sidebar-md'
    });

    // If no color in session and user is logged in, fetch from backend
    if (!sessionData?.color && sessionData?.iduser) {
      this.customerService.findCustomerById(sessionData.iduser.toString()).subscribe({
        next: (user: User) => {
          if (user && user.color && user.color !== colorToApply) {
            // Update session storage with backend color
            this.sessionStorageService.updateSessionColor(user.color);

            // Apply the backend color
            this.applyThemeChanges({
              direction: 'ltr',
              bgColor: user.color,
              bgImage: 'assets/img/sidebar-bg/01.jpg',
              bgImageDisplay: false,
              compactMenu: false,
              sidebarSize: 'sidebar-md'
            });

            // Notify customizer component about the color change
            this.layoutService.emitCustomizerChange({
              direction: 'ltr',
              bgColor: user.color,
              bgImage: 'assets/img/sidebar-bg/01.jpg',
              bgImageDisplay: false,
              compactMenu: false,
              sidebarSize: 'sidebar-md'
            });
          }
        },
        error: (error: any) => {
          console.warn('Could not load user color preference from backend:', error);
          // Continue with session color or default
        }
      });
    }
  }
}
