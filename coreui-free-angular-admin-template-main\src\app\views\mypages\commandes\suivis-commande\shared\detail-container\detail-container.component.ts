import { Component, Input, OnInit } from '@angular/core';
import { SmartContainerComponent } from '../../../../../../../shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from '../../../../../../../shared/components/smart-table/smart-table.component';
import { CommandeService } from '../../../../../../../services/commande.service';
import { ToastrService } from 'ngx-toastr';
import { ActionButton, TableColumn, TableConfig } from '../../../../../../../shared/models/table.models';
import { CommonModule } from '@angular/common';
import { IconModule } from '@coreui/icons-angular';
import { ConfirmationDialogService } from '../../../../../../../services/confirmation-dialog.service';
import { LigneCmdService } from '../../../../../../../services/ligne-cmd.service';
@Component({
  selector: 'app-detail-container',
  imports: [SmartContainerComponent,SmartTableComponent,CommonModule, IconModule],
  templateUrl: './detail-container.component.html',
  styleUrls: ['./detail-container.component.scss']
})
export class DetailContainerComponent implements OnInit {
  @Input() 
  set source(value: any) {
    this._source = value;
    if (value) { // Only load if value exists
      this.loading = true;
      this.setValideOrNotValide()
      this.buildTable()
      this.loadSourceDetails();
    }
  }
  get source(): any {
    return this._source;
  }
  private _source: any = {};

  showContainer:boolean = false;
  loading : boolean = true;
  valide !: boolean ;
  volTot !: number;

  sourceDetails: any = {};

  TableColumns !: TableColumn[]

  TableActions !: ActionButton[]
  infoItems = [
    { label: 'Date départ', key: 'date_depart', icon: 'cilCalendar' },
    { label: 'Date arrivée', key: 'date_arrivee', icon: 'cilCalendar' }, 
    { label: 'Type marchandise', key: 'type_marchandise', icon: 'cilInfo' },
    { label: 'Fragile', key: 'fragile', icon: 'cilWarning' },
    { label: 'Type conditionnement', key: 'type_conditionnement', icon: 'cilInfo' }
  ];
  TableConfig !: TableConfig ;


  constructor(
    private commandeService : CommandeService,
    private ligneCmdService : LigneCmdService,
    private confirmationDialogService : ConfirmationDialogService,
    private toastr : ToastrService
  ) { }

  ngOnInit(): void {
  }
  setValideOrNotValide() {
    this.valide = this.source.statut === "Valide";
    console.log(this.valide)
  }
  buildTable() {
    if (this.valide) {
      this.buildValideTable()
    } else {
      this.buildNotValideTable()
    }
  }

  buildNotValideTable() {
    this.TableColumns = [
      { name:'id', displayName: 'ID', sortable: true, filterable: true },
      { name:'from_destination.nom_locale', nested: true, displayName: 'From Destination', sortable: true, filterable: true },
      { name:'to_destination.nom_locale', nested: true, displayName: 'To Destination', sortable: true, filterable: true },
      { name:'type_ligne', displayName: 'Type Ligne', sortable: true, filterable: true },
      { name:'volume', displayName: 'Volume', sortable: true, filterable: true },
      { name:'quantite', displayName: 'Quantite', sortable: true, filterable: true },
      { name:'estimation', displayName: 'Volume Estimé', sortable: true, filterable: true },
    ]
    this.TableActions = []
    this.TableConfig = {
      emptyMessage: 'Aucune ligne trouvée',
    }
    
  }
  buildValideTable() {
    this.TableColumns = [
      { name:'id', displayName: 'ID', sortable: true, filterable: true },
      { name:'from_destination.nom_locale', nested: true, displayName: 'From Destination', sortable: true, filterable: true },
      { name:'to_destination.nom_locale', nested: true, displayName: 'To Destination', sortable: true, filterable: true },
      { name:'type_ligne', displayName: 'Type Ligne', sortable: true, filterable: true },
      { name:'quantite', displayName: 'Quantite', sortable: true, filterable: true }, 
      { name:'date_voyage', displayName: 'Date Voyage', sortable: true, filterable: true },
      { name:'status', displayName: 'Statut', sortable: true, filterable: true,
        dataType: 'status',
        statusConfig: [
          {
            value: 'Valide',
            displayText: 'Validé',
            badgeColor: 'success',
            icon : 'cil-check'
          },
          {
            value: 'Annulé',
            displayText: 'Annulé',
            badgeColor: 'danger',
            icon : 'cil-trash'
          },
          {
            value: 'Réservé',
            displayText: 'Réservé',
            badgeColor: 'info',
            icon : 'cil-clock'
          }
        ]
      },
      { name:'conducteur.fullName', nested: true, displayName: 'Conducteur', sortable: true, filterable: true },
      { name:'conducteur.mobile', nested: true, displayName: 'Mobile', sortable: true, filterable: true },
    ]
    this.TableActions = [
      {
        icon: 'cil-trash',
        tooltip: 'Annuler',
        condition : (row: any) => row.status == 'Valide',
        color: 'danger',
        callback: (row: any) => this.cancelCommand(row)
      },
      {
        icon: 'cil-comment-bubble',
        tooltip: 'Commenter',
        color: 'info',
        isCommentAction: true
      },
    ]
    this.TableConfig = {
      commentable : true,
      emptyMessage: 'Aucune ligne trouvée',
    }

  }
  cancelCommand(row: any) {
    this.confirmationDialogService.confirmDelete('Voulez-vous vraiment annuler cette commande ?').then((result) => {
      if (result) {
        this.ligneCmdService.updateStatusCancel(row.id).subscribe({
          next: (data:any) => {
            this.toastr.success('Commande annulée');
            this.sourceDetails = this.sourceDetails.filter((item: any) => item.id !== row.id);
          },
          error: (error:any) => {
            this.toastr.error('Error canceling command');
            console.error(error);
          }
        })
      }
    })
  }
  loadSourceDetails() {
    this.loading = true;
    this.commandeService.addCharDecharByIdCmd(this.source.id).subscribe({
      next: (data:any) => {
        console.log('data details', data);
        this.sourceDetails = data;
        this.sourceDetails.forEach((item: any) => {
          item.conducteur.fullName = item.conducteur.nom ? item.conducteur.nom + ' ' + item.conducteur.prenom : 'Pas encore programmé';
          item.date_voyage = item.date_voyage ? item.date_voyage : 'Pas encore programmé';
          item.conducteur.mobile = item.conducteur.mobile ? item.conducteur.mobile : 'Pas encore programmé';
        })
        this.loading = false;
        if (!this.valide) {
          this.volTot = this.sourceDetails.reduce((acc: number, item: any) => acc + item.estimation, 0);
        }
        
      },
      error: (error:any) => {
        this.toastr.error('Error loading commands');
        console.error(error);
        this.loading = false;
      }
    })
  }

  open() {
    this.showContainer = true;
  }
}
