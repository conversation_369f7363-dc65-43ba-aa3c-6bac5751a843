import { Component, OnInit } from '@angular/core';
import { SessionStorageService } from '../../../../services';

@Component({
  selector: 'app-profile',
  imports: [],
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.scss'
})
export class ProfileComponent implements OnInit{
  user !: any;
  constructor(
    private sessionStorageService : SessionStorageService
  ) { }

  ngOnInit(): void {
    this.loaduser()
  }

  loaduser(){
    this.user = this.sessionStorageService.getSession()
    this.user.nom_utilisateur = this.user.prenom + " " + this.user.nom 
    console.log(this.user)
  }
}
