import { Observable } from "rxjs";
import { landingPage } from "./enum/landing-page.enum";

export interface ServiceStrategy {
    
    getLandingPage() : landingPage;

    findReservedLineByUserId(userId: string): Observable<any>;
    findExpediedLineByUserId(userId: string): Observable<any>;
    findDeliveredLineByUserId(userId: string): Observable<any>;
    findEnrgCommandsByUserId(userId: string): Observable<any>;
    getNonReservedCommands(userId: string): Observable<any>;
    findCommandeToValidate(userId: string): Observable<any>;
    findValidCommands(userId: string): Observable<any>;
}