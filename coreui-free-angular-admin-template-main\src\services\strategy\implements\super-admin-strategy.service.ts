import { Injectable } from '@angular/core';
import { ServiceStrategy } from '../service-strategy';
import { landingPage } from '../enum/landing-page.enum';
import { Observable } from 'rxjs';
import { CommandeService } from '../../commande.service';

@Injectable({
  providedIn: 'root'
})
export class SuperAdminStrategyService implements ServiceStrategy{

  constructor(
    private commandeService: CommandeService
  ) { }
  getLandingPage(): landingPage {
    return landingPage.SuperAdmin;
  }

  findReservedLineByUserId(userId: string): Observable<any> {
    return new Observable((observer) => {
      observer.next([this.getLandingPage()]);
      observer.complete();
    })
  }

  findExpediedLineByUserId(userId: string): Observable<any> {
    return new Observable((observer) => {
      observer.next([this.getLandingPage()]);
      observer.complete();
    })
  }

  findDeliveredLineByUserId(userId: string): Observable<any> {
    return new Observable((observer) => {
      observer.next([this.getLandingPage()]);
      observer.complete();
    })
  }
  findEnrgCommandsByUserId(userId: string): Observable<any> {
    return new Observable((observer) => {
      observer.next([this.getLandingPage()]);
      observer.complete();
    })  
  }
  getNonReservedCommands(userId: string): Observable<any> {
    return this.commandeService.getAllCommands();
  }
  findCommandeToValidate(userId: string): Observable<any>{
    return new Observable()
  }
  findValidCommands(userId: string): Observable<any>{
    return new Observable()
  }

}
