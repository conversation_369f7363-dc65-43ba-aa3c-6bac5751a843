// confirmation-dialog.service.ts
import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmationDialogComponent } from '../shared/components/confirmation-dialog/confirmation-dialog.component';
import { ConfirmationDialogData } from '../shared/components/confirmation-dialog/confirmation-dialog.component';

@Injectable({
  providedIn: 'root'
})
export class ConfirmationDialogService {
  constructor(private modalService: NgbModal) {}

  private openModal(config: ConfirmationDialogData): Promise<boolean> {
    const modalRef = this.modalService.open(ConfirmationDialogComponent, { 
      size: config.dialogSize || 'md', 
      centered: true 
    });
    
    // Assign data directly to component instance
    modalRef.componentInstance.data = config;
    
    return modalRef.result;
  }

  public confirm(config:
    {title?: string,
    message?: string,
    btnOkText?: string ,
    btnCancelText?: string ,
    dialogSize?: 'sm' | 'lg' | 'xl' | 'md',
    color?: string }
  ): Promise<boolean> {
    return this.openModal({
      title:config.title? config.title : 'Confirmation',
      message: config.message? config.message : 'Êtes-vous sûr de vouloir continuer ?',
      btnOkText: config.btnOkText? config.btnOkText : 'Confirmer',
      btnCancelText: config.btnCancelText? config.btnCancelText : 'Annuler',
      dialogSize: config.dialogSize? config.dialogSize : 'md',
      color: config.color? config.color : 'primary'
    });
  }

  public confirmDelete(message: string): Promise<boolean> {
    return this.confirm(
      {title: 'Confirmer la suppression',
      message: message,
      btnOkText: 'Supprimer',
      btnCancelText: 'Annuler',
      dialogSize: 'md',
      color: 'danger',
    });
  }

  public confirmSave(message: string): Promise<boolean> {
    return this.confirm(
      {title:'Confirmer l\'enregistrement',
      message : message,
      btnOkText: 'Enregistrer',
      btnCancelText: 'Annuler',
      dialogSize: 'md',
      color: 'primary'
    });
  }

  public confirmEdit(message: string): Promise<boolean> {
    return this.confirm(
      {title:'Confirmer la modification',
      message : message,
      btnOkText: 'Modifier',
      btnCancelText: 'Annuler',
      dialogSize: 'md',
      color: 'warning'
    });
  }
}