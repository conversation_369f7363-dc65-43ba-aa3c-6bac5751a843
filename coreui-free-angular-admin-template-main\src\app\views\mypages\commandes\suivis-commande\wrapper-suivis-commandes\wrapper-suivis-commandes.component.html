<app-smart-container [title]="title">
    <div slot="actions">
        <form [formGroup]="formRadio">
            <c-button-group aria-label="Basic radio toggle button group" role="group">
                <input class="btn-check" formControlName="radio" type="radio" value="Mes Commandes" />
                <label (click)="setRadioValue('Mes Commandes')" cButton cFormCheckLabel variant="outline">Mes Commandes</label>

                <input class="btn-check" formControlName="radio" type="radio" value="Mes Commandes à valider" />
                <label (click)="setRadioValue('Mes Commandes à valider')" cButton cFormCheckLabel variant="outline">Mes Commandes à valider</label>

                <input class="btn-check" formControlName="radio" type="radio" value="Mes Commandes Validées" />
                <label (click)="setRadioValue('Mes Commandes Validées')" cButton cFormCheckLabel variant="outline">Mes Commandes Validées</label>

            </c-button-group>
        </form>
    </div>
    <div slot="content">

        <div *ngIf="formRadio.value['radio'] === 'Mes Commandes'">
            <app-my-commands></app-my-commands>
        </div>

        <div *ngIf="formRadio.value['radio'] === 'Mes Commandes à valider'">
            <app-my-commands-to-validate></app-my-commands-to-validate>
        </div>
        <div *ngIf="formRadio.value['radio'] === 'Mes Commandes Validées'">
            <app-my-validated-commands></app-my-validated-commands>
        </div>
    </div>
</app-smart-container>