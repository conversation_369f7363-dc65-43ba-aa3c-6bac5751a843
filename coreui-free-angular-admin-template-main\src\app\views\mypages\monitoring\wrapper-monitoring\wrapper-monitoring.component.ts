import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { ButtonDirective, ButtonGroupComponent, FormCheckLabelDirective } from '@coreui/angular';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { MonitoringLivraisonComponent } from '../monitoring-livraison/monitoring-livraison.component';
import { MonitoringReceptionComponent } from '../monitoring-reception/monitoring-reception.component';


@Component({
  selector: 'app-wrapper-monitoring',
  standalone:true,
  imports: [
    SmartContainerComponent,
    CommonModule,
    MonitoringReceptionComponent,
    MonitoringLivraisonComponent,
    ButtonGroupComponent,
    FormCheckLabelDirective,
    ButtonDirective,
    ReactiveFormsModule
],
  templateUrl: './wrapper-monitoring.component.html',
  styleUrl: './wrapper-monitoring.component.scss'
})
export class WrapperMonitoringComponent {
  title: string = 'Mes Livraisons';
  formRadio = new UntypedFormGroup({
    radio: new UntypedFormControl('Mes livraisons')
  });

  constructor(
    private formBuilder: UntypedFormBuilder
  ) { }

  setRadioValue(value: string): void {
    this.formRadio.setValue({ radio: value });
    console.log(this.formRadio.value)
    switch(this.formRadio.value['radio']){
      case 'Mes livraisons':
        this.title = 'Mes Livraisons';
        break;
      case 'Mes Réceptions':
        this.title = 'Mes Réceptions';
        break;
    }
  }
}

