import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { RegisterServiceService } from 'app/services/register-service.service';
import { ToastrService } from 'ngx-toastr';
import { Md5 } from 'md5-typescript';
import { mailService } from 'app/services/mail.service';

@Component({
  selector: 'app-add-user',
  templateUrl: './add-user.component.html',
  styleUrls: ['./add-user.component.scss']
})
export class AddUserComponent implements OnInit {
  userForm: FormGroup;
  updateUser: boolean = false
  roles: any = []
  users: any = []
  selectedUserToUpdate: any = {};
  matchingRole: any
  showPassword = false
  email: string = '';
  password: string = '';
  selectedUserType: any
  constructor(
    private formBuilder: FormBuilder,
    private toastr: ToastrService,
    private registerService: RegisterServiceService,
    private mailService: mailService
  ) {
    this.userForm = this.formBuilder.group({
      nom: ['', Validators.required],
      prenom: ['', Validators.required],
      typeUtilisateur: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      motDePasse: ['', Validators.required]
    });
  }

  // Getter pratique pour un accès facile aux champs du formulaire
  get f() { return this.userForm.controls; }

  async submitForm() {
    // Vérifie si le formulaire est invalide
    if (this.userForm.invalid) {
      // Marquer tous les champs comme touchés pour afficher les messages d'erreur
      Object.values(this.userForm.controls).forEach(control => {
        control.markAsTouched();
      });


      this.toastr.error("Merci de vérifier tout les champs obligatoire !")
      return;
    }
    const email = this.userForm.value.email;

    await this.registerService.findCustomerByMail(email).subscribe(
      async (res) => {
        if (res) {
          this.toastr.error("Utilisateur déjà existant");
          return
        } else {

          const data = {
            nom: this.userForm.value.nom,
            prenom: this.userForm.value.prenom,
            type_utilisateur: this.userForm.value.typeUtilisateur,
            email: this.userForm.value.email,
            nom_utilisateur: this.userForm.value.nom + " " + this.userForm.value.prenom,
            mot_de_passe: this.generateMdp(this.userForm.value.motDePasse),
            statut: "activé",
            cle_activation: "Created By Super Admin " + sessionStorage.getItem('iduser'),

          }

          await this.registerService.addCustomers(data).subscribe(
            async (res) => {
              // Gérer la réponse réussie ici
              this.toastr.success('Utilisateur ajouté avec succès');
              await this.SendEmailNewUser(this.userForm.value.nom + " " + this.userForm.value.prenom, this.userForm.value.email, this.userForm.value.motDePasse)
              this.initForm();

            },
            (error) => {
              // Gérer l'erreur ici
              console.error('Erreur lors de l\'ajout de l\'utilisateur:', error);
              this.toastr.error('Erreur lors de l\'ajout de l\'utilisateur');
            }
          );



        }
      },
      (error) => {
        // Gérer les erreurs ici
      }
    );



  }


  generateMdp(mdp) {
    return Md5.init(mdp);
  }


  ngOnInit(): void {
    this.initForm();
    this.registerService.getAllRole().subscribe(res => {
      this.roles = res
    })
    this.registerService.findAllExp().subscribe(data => {
      this.users = data;

    })

  }

  initForm() {
    this.userForm = this.formBuilder.group({
      nom: ['', Validators.required],
      prenom: ['', Validators.required],
      typeUtilisateur: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      motDePasse: ['', Validators.required]
    });
  }

  showUpdateUser() {
    this.updateUser = !this.updateUser
    //console.log(this.updateUser)
  }


  onUserChange(event: Event): void {
    const selectedUserId = (event.target as HTMLSelectElement).value;
    const selectedUser = this.users.find(user => user.id == selectedUserId);

    if (selectedUser) {
      //console.log("Utilisateur sélectionné:", selectedUser);
      const userType = selectedUser.type_utilisateur;

      // Find the matching role
      this.matchingRole = this.roles.find(role => role.value == userType);
      if (this.matchingRole) {
        //console.log("Rôle correspondant:", this.matchingRole);
        this.selectedUserType = this.matchingRole.value;
      }
      this.selectedUserToUpdate = selectedUser;
    }
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  async updateUserData(): Promise<void> {
    if (!this.selectedUserToUpdate) {
      //console.log('Aucun utilisateur sélectionné.');
      this.toastr.error('Aucun utilisateur sélectionné')
      return;
    }

    // Check if all required fields are filled
    if (!this.selectedUserToUpdate.nom || !this.selectedUserToUpdate.prenom || !this.selectedUserToUpdate.email || !this.selectedUserType) {
      console.error('Merci de remplir tous les champs.');
      this.toastr.error('Merci de remplir tous les champs.');
      return;
    }

    // Validate email format
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailPattern.test(this.selectedUserToUpdate.email)) {
      console.error('L\'email fourni n\'est pas valide.');
      this.toastr.error('L\'email fourni n\'est pas valide.');
      return;
    }

    // Validate password length if provided
    if (this.selectedUserToUpdate.password && this.selectedUserToUpdate.password.length < 8) {
      console.error('Le mot de passe doit comporter au moins 8 caractères.');
      this.toastr.error('Le mot de passe doit comporter au moins 8 caractères.');
      return;
    }

    // Prepare the user object for update
    const userToUpdate = {
      ...this.selectedUserToUpdate,
      id: this.selectedUserToUpdate.id,
      nom: this.selectedUserToUpdate.nom,
      prenom: this.selectedUserToUpdate.prenom,
      email: this.selectedUserToUpdate.email,
      type_utilisateur: this.selectedUserType,
      nom_utilisateur: this.selectedUserToUpdate.nom + " " + this.selectedUserToUpdate.prenom,

      ...(this.selectedUserToUpdate.password ? { mot_de_passe: this.generateMdp(this.selectedUserToUpdate.password) } : {})
    };

    

    try {
      const response = await this.registerService.updateUserInfo(userToUpdate).toPromise();
      //console.log('Réponse du serveur:', response);
      this.toastr.success('Mise à jour avec succès');
      this.SendEmailModifyUser(userToUpdate.nom+" "+userToUpdate.prenom,userToUpdate.email,this.selectedUserToUpdate.password)
      this.selectedUserToUpdate = {}

    } catch (error) {
      console.error('Erreur lors de la mise à jour de l\'utilisateur:', error);
      this.toastr.error('Erreur lors de la mise à jour de l\'utilisateur.');
    }
  }


  onUserTypeChange(event: any): void {
    const selectedValue = event.target.value;
    this.selectedUserType = selectedValue;
    //console.log('Selected User Type:', this.selectedUserType);
  }

  async SendEmailNewUser(recipientName, email, password) {
    const emailContent = `
        <p><b>Bonjour ${recipientName},</b></p>
        
        <p>J'espère que vous allez bien. Je vous écris pour vous fournir les informations nécessaires pour accéder à notre application Zen Logistique, afin que vous puissiez commencer dès maintenant.</p>
        
        <p>Voici le lien de l'application : <a href="http://zenlogistic.tn">Zen Logistique</a></p>
        
        <p>Et voici vos identifiants de connexion :</p>
        
        <p><strong>Nom d'utilisateur :</strong> ${email}</p>
        <p><strong>Mot de passe :</strong> ${password}</p>
        
        <p>N'hésitez pas à nous contacter si vous rencontrez le moindre problème ou si vous avez besoin d'assistance pour démarrer. Je suis à votre disposition pour toute question ou clarification supplémentaire.</p>
        
        <p>Je vous souhaite une excellente utilisation de notre application Zen Logistique.</p>
        
        <p>Cordialement,</p>
    `;

    const data = {
      to: email,
      subject: "Accès Zen Logistique",
      htmlContent: emailContent,
    };

    await this.mailService.sendMailsFromFront(data).then(res => {
      //console.log(res)
      this.toastr.success('Email envoyé avec succée')
    }
    );
  }

  async SendEmailModifyUser(recipientName, email, newPassword) {
    const passwordMessage = newPassword 
      ? `<p><strong>Nouveau mot de passe :</strong> ${newPassword}</p>` 
      : `<p><strong>Mot de passe :</strong> Vos identifiants de connexion restent inchangés.</p>`;
  
    const emailContent = `
      <p><b>Bonjour ${recipientName},</b></p>
      
      <p>J'espère que vous allez bien. Je vous écris pour vous informer que vos informations d'accès à notre application Zen Logistique ont été modifiées.</p>
      
      <p>Voici le lien de l'application : <a href="http://zenlogistic.tn">Zen Logistique</a></p>
      
      <p>Et voici vos identifiants de connexion :</p>
      
      <p><strong>Nom d'utilisateur :</strong> ${email}</p>
      ${passwordMessage}
      
      <p>N'hésitez pas à nous contacter si vous rencontrez le moindre problème ou si vous avez besoin d'assistance pour vous connecter avec vos identifiants. Nous sommes à votre disposition pour toute question ou clarification supplémentaire.</p>
      
      <p>Nous vous souhaitons une excellente utilisation de notre application Zen Logistique.</p>
      
      <p>Cordialement,</p>
    `;
  
    const data = {
      to: email,
      subject: "Modification de vos accès Zen Logistique",
      htmlContent: emailContent,
    };
  
    await this.mailService.sendMailsFromFront(data).then(res => {
      //console.log(res);
      this.toastr.success('Email envoyé avec succès');
    }).catch(err => {
      console.error(err);
      this.toastr.error("Échec de l'envoi de l'email");
    });
  }
  
  

}
