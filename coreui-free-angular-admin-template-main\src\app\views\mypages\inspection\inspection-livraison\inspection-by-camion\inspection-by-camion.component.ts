import { Component, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { SessionStorageService, VoyageService } from 'src/services';
import { CamionService } from 'src/services/camion.service';
import { ConfirmationDialogService } from 'src/services/confirmation-dialog.service';
import { LigneCmdService } from 'src/services/ligne-cmd.service';
import { FormConfig } from 'src/shared/models/form.models';
import { ActionButton, TableColumn, TableConfig } from 'src/shared/models/table.models';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';
import { CommonModule } from '@angular/common';
import { DynamicFormComponent } from 'src/shared/components/dynamic-form/dynamic-form.component';
import { DynamicExportButtonComponent } from 'src/shared/components/dynamic-export-button/dynamic-export-button.component';
import { InspectionSharedModalComponent } from '../shared/inspection-shared-modal/inspection-shared-modal.component';

@Component({
  selector: 'app-inspection-by-camion',
  standalone: true,
  imports: [SmartContainerComponent,SmartTableComponent,DynamicExportButtonComponent,CommonModule,DynamicFormComponent,InspectionSharedModalComponent],
  templateUrl: './inspection-by-camion.component.html',
  styleUrl: './inspection-by-camion.component.scss'
})
export class InspectionByCamionComponent {
  @ViewChild('detailsModal') detailsModal !: InspectionSharedModalComponent;
  loading = false;
    showTable = false;
    DataExportLabel!: {
      immatriculation: string,
      date_debut: string,
      date_fin: string
    } 
    tableColumn : TableColumn[] =[
      {
        name: 'id',
        displayName: 'ID',
        sortable: true,
        filterable: true,
      },
      {
        name: 'nom_voyage',
        displayName: 'Nom Voyage',
        sortable: true,
        filterable: true,
      },
      {
        name: 'sell_price',
        displayName: 'Montant de vente',
        sortable: true,
        filterable: true,
      },
      {
        name: 'purchase_price',
        displayName: 'Montant à payer',
        sortable: true,
        filterable: true,
      },
      {
        name: 'date_voyage',
        displayName: 'Date du voyage',
        dataType: 'date',
        sortable: true,
        filterable: true,
      }
    ];
    tableConfig: TableConfig = {
      emptyMessage: 'Aucune ligne trouvée', 
    }
    tableActions: ActionButton[] = [
      {
        icon: 'cil-trash',
        color: 'danger',
        callback: (row: any) => {this.handleVoyageCancellation(row)}
      },
      {
        icon:'cil-zoom',
        color: 'info',
        callback: (row:any) => this.ViewDetails(row)
      }
    ]
    
    camions: any[] = [];
    lignes: any[] = [];
    totalToPay: any;
    formConfig!: FormConfig
    constructor(
      private camionService: CamionService,
      private voyageService: VoyageService,
      private LigneCmdService: LigneCmdService,
      private confirmDialogService: ConfirmationDialogService,
      private toastr: ToastrService,
      private sessionService: SessionStorageService
    ){}

    ngOnInit(): void {
      this.loadCamions();
      this.buildFormConfig();
    }

    loadCamions() {
      this.camionService.findAllCamion().subscribe({
        next: (data: any) => {
          this.camions = data;
          console.log(this.camions);
          const DriverFiels = this.formConfig?.fieldGroups?.find((fieldGroup: any) => fieldGroup.fields.find((field: any) => field.name === 'id_camion'));
          if (DriverFiels && DriverFiels.fields) {
            DriverFiels.fields[0].options = {
              objectArray: this.camions,
              valueAttribute: 'id',
              labelAttribute: 'immatriculation'
            };
          }
        },
        error: (error: any) => {
          console.error(error);
        }
      })
    }
    
    buildFormConfig() {
      this.formConfig = {
        fieldGroups: [
          {
            fields: [
              {
                name: 'id_camion',
                type: 'select',
                label: 'Camion',
                required: true,
                options: 
                  {
                    objectArray: this.camions,
                    valueAttribute: 'id',
                    labelAttribute: 'immatriculation'
                  }
              },
            ]
          }
        ],
        fields: [
          
          {
            name: 'date_debut',
            type: 'date',
            label: 'Date de début',
            required: true
          },
          {
            name: 'date_fin',
            type: 'date',
            label: 'Date de fin',
            required: true
          }
        ],
        buttons: [
          {
            label: 'Rechercher',
            color: 'primary',
            icon: 'cil-search',
            onClick: (formData: any) => this.onSubmit(formData)
          }
        ]
      }
    }
  
    onSubmit(formData: any) {
      this.showTable = true;
      this.loading = true;
      console.log(formData);
      const data = {
        id_camion: formData.id_camion,
        date_debut: formData.date_debut,
        date_fin: formData.date_fin
      }
      this.DataExportLabel = {
        immatriculation: this.camions.find((camion: any) => camion.id === parseInt(formData.id_camion))?.matricule,
        date_debut: formData.date_debut,
        date_fin: formData.date_fin
      };
      this.LigneCmdService.findLigneDelivredByTruck(data).subscribe({
        next: (data: any) => {
          this.lignes = data.data;
          if (this.lignes.length > 0) {
            this.totalToPay = this.lignes.reduce((total: any, ligne: any) => total + ligne.purchase_price, 0);
          }
          console.log(this.lignes);
        },
        error: (error: any) => {
          console.error(error);
        },
        complete: () => {
          this.loading = false;
        }
      })
      this.loading = false;
  
    }
    handleVoyageCancellation(row: any): void {
      const voyageId = row.id;
      this.confirmDialogService.confirmDelete("Voulez-vous vraiment annuler ce voyage ?").then(
        (data: any) => {
          this.voyageService.cancelVoyage(voyageId).subscribe({
              next: (data: any) => {
                this.toastr.success('Voyage annulé avec succès');
                this.lignes = this.lignes.filter((ligne: any) => ligne.id !== voyageId);
              },
              error: (error: any) => {
                console.error(error);
              },
              complete: () => {
                this.loading = false;
              }
            })
          }
      )  
    }
    ViewDetails(row: any): void {
     if (this.detailsModal) {
      console.log("row",row);
      this.detailsModal.VoyageId = row.id;
      this.detailsModal.openModal();
     }
     else {
      this.toastr.error('Modal non trouvé');
     }
    }
}
