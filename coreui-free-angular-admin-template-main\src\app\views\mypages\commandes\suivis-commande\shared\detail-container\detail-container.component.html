<div *ngIf="showContainer">
    <app-smart-container 
    [title]="valide ? 'Details Commande Validée' : 'Details Commande non Validée'"
    [subtitle]="'Commande ID : ' + source.id">
        <div slot="content">
            <div slot="content">
                <div class="row" style="justify-content: center;">
                    <div class="col-12 col-md-2 mb-3" *ngFor="let item of infoItems">
                        <div class="card">
                            <div class="card-body text-center">
                                <div class="text-uppercase text-medium-emphasis small">
                                    <c-icon [name]="item.icon"></c-icon> {{item.label}}
                                </div>
                                <div class="fs-5 fw-semibold">
                                    {{source[item.key]}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <app-smart-container 
            [title]="!valide ? 'Volume Estimé Total : ' + volTot : ''">
                <div slot="content">
                <app-smart-table 
                    [isLoading]="loading" 
                    [data]="sourceDetails" 
                    [columns]="TableColumns"
                    [actionButtons]="TableActions"
                >
                </app-smart-table>
            </div>
            </app-smart-container>
        </div>
    </app-smart-container>
</div>