import { Injectable } from '@angular/core';
import { ServiceStrategy } from '../service-strategy';
import { landingPage } from '../enum/landing-page.enum';
import { Observable } from 'rxjs';
import { CommandeService } from '../../commande.service';

@Injectable({
  providedIn: 'root'
})
export class AdministrateurStrategyService implements ServiceStrategy{

  constructor(
    private commandeService: CommandeService
  ) { }
  getLandingPage(): landingPage {
    return landingPage.Administrateur
  }

  findReservedLineByUserId(userId: string): Observable<any> {
    return new Observable()
  }

  findExpediedLineByUserId(userId: string): Observable<any> {
    return new Observable()
  }

  findDeliveredLineByUserId(userId: string): Observable<any> {
    return new Observable()
  }
  findEnrgCommandsByUserId(userId: string): Observable<any> {
    return new Observable()  
  }

  getNonReservedCommands(userId: string): Observable<any> {
    return this.commandeService.getAllCommands();
  }

  findCommandeToValidate(userId: string): Observable<any>{
    return new Observable()
  }
  findValidCommands(userId: string): Observable<any>{
    return new Observable()
  }


}
