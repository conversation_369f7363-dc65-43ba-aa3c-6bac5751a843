import { Injectable } from '@angular/core';
import { ServiceStrategy } from './service-strategy';
import { UserRole } from './enum/user-role.enum';
import { RoleService } from '../../services/role.service';
import { AdministrateurStrategyService } from './implements/administrateur-strategy.service';
import { SuperAdminStrategyService } from './implements/super-admin-strategy.service';
import { ClientService } from './implements/client.service';
import { ChefDepartementStrategyService } from './implements/chef-departement-strategy.service';
import { GSStrategyService } from './implements/gsstrategy.service';
import { GEStrategyService } from './implements/gestrategy.service';
import { DepotStrategyService } from './implements/depot-strategy.service';
import { InspectionStrategyService } from './implements/inspection-strategy.service';
import { FacturationStrategyService } from './implements/facturation-strategy.service';
import { MagasinStrategyService } from './implements/magasin-strategy.service';
import { Observable } from 'rxjs';
import { ConducteurStrategieService } from './implements/conducteur-strategie.service';
import { SessionStorageService } from '../session-storage.service';

@Injectable({
  providedIn: 'root'
})
export class ContextService {
  private currentStrategy!: ServiceStrategy;

  private role!:any;
  private userId!:any
  constructor(
    private roleService: RoleService,
    private adminStrategy: AdministrateurStrategyService,
    private superAdminStrategy: SuperAdminStrategyService,
    private clientStrategy: ClientService,
    private chefDepartementStrategy: ChefDepartementStrategyService,
    private gsStrategy: GSStrategyService,
    private geStrategy: GEStrategyService,
    private depotStrategy: DepotStrategyService,
    private inspectionStrategy: InspectionStrategyService,
    private facturationStrategy: FacturationStrategyService,
    private magasinStrategy: MagasinStrategyService,
    private conducteurStrategy: ConducteurStrategieService,
    private sessionStorage: SessionStorageService 
  ) {
    this.role = this.roleService.getRole();
    this.userId = this.sessionStorage.getSessionValue('iduser')
    console.log(this.userId)
  }

  setStrategy() {
    const userRole: string | null = this.roleService.getRole();
    switch(userRole) {
      case UserRole.SUPERADMIN:
        this.currentStrategy = this.superAdminStrategy;
        break;
      case UserRole.ADMINISTRATEUR:
        this.currentStrategy = this.adminStrategy;
        break;
      case UserRole.CLIENT:
        this.currentStrategy = this.clientStrategy;
        break;
      case UserRole.CHEF_DEPARTEMENT:
        this.currentStrategy = this.chefDepartementStrategy;
        break;
      case UserRole.GS:
        this.currentStrategy = this.gsStrategy;
        break;
      case UserRole.GE:
        this.currentStrategy = this.geStrategy;
        break;
      case UserRole.DEPOT:
        this.currentStrategy = this.depotStrategy;
        break;
      case UserRole.INSPECTION:
        this.currentStrategy = this.inspectionStrategy;
        break;
      case UserRole.FACTURATION:
        this.currentStrategy = this.facturationStrategy;
        break;
      case UserRole.MAGASIN:
        this.currentStrategy = this.magasinStrategy;
        break;
      case UserRole.CONDUCTEUR:
        this.currentStrategy = this.conducteurStrategy;
        break;
      default:
        throw new Error('Unknown user role');
    }
  }

  getLandingPage() {
    if (!this.currentStrategy) {
      this.setStrategy();
    }
    return this.currentStrategy.getLandingPage();
  }

  findReservedLineWithByUserId(): Observable<any> {
    if (!this.currentStrategy) {
      this.setStrategy();
    }
    return this.currentStrategy.findReservedLineByUserId(this.userId);
  }
  findExpediedLineWithByUserId(): Observable<any> {
    if (!this.currentStrategy) {
      this.setStrategy();
    }
    return this.currentStrategy.findExpediedLineByUserId(this.userId);
  }
  findDeliveredLineWithByUserId(): Observable<any> {
    if (!this.currentStrategy) {
      this.setStrategy();
    }
    return this.currentStrategy.findDeliveredLineByUserId(this.userId);
  }

  findEnrgCommandsByUserId(): Observable<any> {
    if (!this.currentStrategy) {
      this.setStrategy();
    }
    return this.currentStrategy.findEnrgCommandsByUserId(this.userId);
  }
  findNonReservedCommandsByUserType(): Observable<any> {
    if (!this.currentStrategy) {
      this.setStrategy();
    }
    return this.currentStrategy.getNonReservedCommands(this.userId);
  }
  findValidCommandsByUserType(): Observable<any> {
    if (!this.currentStrategy) {
      this.setStrategy();
    }
    return this.currentStrategy.findValidCommands(this.userId);
  }
  findCommandsToValidate() {
    if (!this.currentStrategy) {
      this.setStrategy();
    }
    return this.currentStrategy.findCommandeToValidate(this.userId);
  }
  

}
