{"version": 3, "sources": ["../../../../../../node_modules/angular-resizable-element/fesm2020/angular-resizable-element.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Directive, Inject, Input, Output, Optional, NgModule } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\nimport { Subject, merge, Observable, fromEvent } from 'rxjs';\nimport { tap, share, mergeMap, take, map, pairwise, filter, takeUntil } from 'rxjs/operators';\n\n/**\n * @hidden\n */\nconst IS_TOUCH_DEVICE = (() => {\n  // In case we're in Node.js environment.\n  if (typeof window === 'undefined') {\n    return false;\n  } else {\n    return 'ontouchstart' in window || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0;\n  }\n})();\n\n/** Creates a deep clone of an element. */\nfunction deepCloneNode(node) {\n  const clone = node.cloneNode(true);\n  const descendantsWithId = clone.querySelectorAll('[id]');\n  const nodeName = node.nodeName.toLowerCase();\n  // Remove the `id` to avoid having multiple elements with the same id on the page.\n  clone.removeAttribute('id');\n  descendantsWithId.forEach(descendant => {\n    descendant.removeAttribute('id');\n  });\n  if (nodeName === 'canvas') {\n    transferCanvasData(node, clone);\n  } else if (nodeName === 'input' || nodeName === 'select' || nodeName === 'textarea') {\n    transferInputData(node, clone);\n  }\n  transferData('canvas', node, clone, transferCanvasData);\n  transferData('input, textarea, select', node, clone, transferInputData);\n  return clone;\n}\n/** Matches elements between an element and its clone and allows for their data to be cloned. */\nfunction transferData(selector, node, clone, callback) {\n  const descendantElements = node.querySelectorAll(selector);\n  if (descendantElements.length) {\n    const cloneElements = clone.querySelectorAll(selector);\n    for (let i = 0; i < descendantElements.length; i++) {\n      callback(descendantElements[i], cloneElements[i]);\n    }\n  }\n}\n// Counter for unique cloned radio button names.\nlet cloneUniqueId = 0;\n/** Transfers the data of one input element to another. */\nfunction transferInputData(source, clone) {\n  // Browsers throw an error when assigning the value of a file input programmatically.\n  if (clone.type !== 'file') {\n    clone.value = source.value;\n  }\n  // Radio button `name` attributes must be unique for radio button groups\n  // otherwise original radio buttons can lose their checked state\n  // once the clone is inserted in the DOM.\n  if (clone.type === 'radio' && clone.name) {\n    clone.name = `mat-clone-${clone.name}-${cloneUniqueId++}`;\n  }\n}\n/** Transfers the data of one canvas element to another. */\nfunction transferCanvasData(source, clone) {\n  const context = clone.getContext('2d');\n  if (context) {\n    // In some cases `drawImage` can throw (e.g. if the canvas size is 0x0).\n    // We can't do much about it so just ignore the error.\n    try {\n      context.drawImage(source, 0, 0);\n    } catch {}\n  }\n}\nfunction getNewBoundingRectangle(startingRect, edges, clientX, clientY) {\n  const newBoundingRect = {\n    top: startingRect.top,\n    bottom: startingRect.bottom,\n    left: startingRect.left,\n    right: startingRect.right\n  };\n  if (edges.top) {\n    newBoundingRect.top += clientY;\n  }\n  if (edges.bottom) {\n    newBoundingRect.bottom += clientY;\n  }\n  if (edges.left) {\n    newBoundingRect.left += clientX;\n  }\n  if (edges.right) {\n    newBoundingRect.right += clientX;\n  }\n  newBoundingRect.height = newBoundingRect.bottom - newBoundingRect.top;\n  newBoundingRect.width = newBoundingRect.right - newBoundingRect.left;\n  return newBoundingRect;\n}\nfunction getElementRect(element, ghostElementPositioning) {\n  let translateX = 0;\n  let translateY = 0;\n  const style = element.nativeElement.style;\n  const transformProperties = ['transform', '-ms-transform', '-moz-transform', '-o-transform'];\n  const transform = transformProperties.map(property => style[property]).find(value => !!value);\n  if (transform && transform.includes('translate')) {\n    translateX = transform.replace(/.*translate3?d?\\((-?[0-9]*)px, (-?[0-9]*)px.*/, '$1');\n    translateY = transform.replace(/.*translate3?d?\\((-?[0-9]*)px, (-?[0-9]*)px.*/, '$2');\n  }\n  if (ghostElementPositioning === 'absolute') {\n    return {\n      height: element.nativeElement.offsetHeight,\n      width: element.nativeElement.offsetWidth,\n      top: element.nativeElement.offsetTop - translateY,\n      bottom: element.nativeElement.offsetHeight + element.nativeElement.offsetTop - translateY,\n      left: element.nativeElement.offsetLeft - translateX,\n      right: element.nativeElement.offsetWidth + element.nativeElement.offsetLeft - translateX\n    };\n  } else {\n    const boundingRect = element.nativeElement.getBoundingClientRect();\n    return {\n      height: boundingRect.height,\n      width: boundingRect.width,\n      top: boundingRect.top - translateY,\n      bottom: boundingRect.bottom - translateY,\n      left: boundingRect.left - translateX,\n      right: boundingRect.right - translateX,\n      scrollTop: element.nativeElement.scrollTop,\n      scrollLeft: element.nativeElement.scrollLeft\n    };\n  }\n}\nconst DEFAULT_RESIZE_CURSORS = Object.freeze({\n  topLeft: 'nw-resize',\n  topRight: 'ne-resize',\n  bottomLeft: 'sw-resize',\n  bottomRight: 'se-resize',\n  leftOrRight: 'col-resize',\n  topOrBottom: 'row-resize'\n});\nfunction getResizeCursor(edges, cursors) {\n  if (edges.left && edges.top) {\n    return cursors.topLeft;\n  } else if (edges.right && edges.top) {\n    return cursors.topRight;\n  } else if (edges.left && edges.bottom) {\n    return cursors.bottomLeft;\n  } else if (edges.right && edges.bottom) {\n    return cursors.bottomRight;\n  } else if (edges.left || edges.right) {\n    return cursors.leftOrRight;\n  } else if (edges.top || edges.bottom) {\n    return cursors.topOrBottom;\n  } else {\n    return '';\n  }\n}\nfunction getEdgesDiff({\n  edges,\n  initialRectangle,\n  newRectangle\n}) {\n  const edgesDiff = {};\n  Object.keys(edges).forEach(edge => {\n    edgesDiff[edge] = (newRectangle[edge] || 0) - (initialRectangle[edge] || 0);\n  });\n  return edgesDiff;\n}\nconst RESIZE_ACTIVE_CLASS = 'resize-active';\nconst RESIZE_GHOST_ELEMENT_CLASS = 'resize-ghost-element';\nconst MOUSE_MOVE_THROTTLE_MS = 50;\n/**\n * Place this on an element to make it resizable. For example:\n *\n * ```html\n * <div\n *   mwlResizable\n *   [resizeEdges]=\"{bottom: true, right: true, top: true, left: true}\"\n *   [enableGhostResize]=\"true\">\n * </div>\n * ```\n * Or in case they are sibling elements:\n * ```html\n * <div mwlResizable #resizableElement=\"mwlResizable\"></div>\n * <div mwlResizeHandle [resizableContainer]=\"resizableElement\" [resizeEdges]=\"{bottom: true, right: true}\"></div>\n * ```\n */\nclass ResizableDirective {\n  /**\n   * @hidden\n   */\n  constructor(platformId, renderer, elm, zone) {\n    this.platformId = platformId;\n    this.renderer = renderer;\n    this.elm = elm;\n    this.zone = zone;\n    /**\n     * Set to `true` to enable a temporary resizing effect of the element in between the `resizeStart` and `resizeEnd` events.\n     */\n    this.enableGhostResize = false;\n    /**\n     * A snap grid that resize events will be locked to.\n     *\n     * e.g. to only allow the element to be resized every 10px set it to `{left: 10, right: 10}`\n     */\n    this.resizeSnapGrid = {};\n    /**\n     * The mouse cursors that will be set on the resize edges\n     */\n    this.resizeCursors = DEFAULT_RESIZE_CURSORS;\n    /**\n     * Define the positioning of the ghost element (can be fixed or absolute)\n     */\n    this.ghostElementPositioning = 'fixed';\n    /**\n     * Allow elements to be resized to negative dimensions\n     */\n    this.allowNegativeResizes = false;\n    /**\n     * The mouse move throttle in milliseconds, default: 50 ms\n     */\n    this.mouseMoveThrottleMS = MOUSE_MOVE_THROTTLE_MS;\n    /**\n     * Called when the mouse is pressed and a resize event is about to begin. `$event` is a `ResizeEvent` object.\n     */\n    this.resizeStart = new EventEmitter();\n    /**\n     * Called as the mouse is dragged after a resize event has begun. `$event` is a `ResizeEvent` object.\n     */\n    this.resizing = new EventEmitter();\n    /**\n     * Called after the mouse is released after a resize event. `$event` is a `ResizeEvent` object.\n     */\n    this.resizeEnd = new EventEmitter();\n    /**\n     * @hidden\n     */\n    this.mouseup = new Subject();\n    /**\n     * @hidden\n     */\n    this.mousedown = new Subject();\n    /**\n     * @hidden\n     */\n    this.mousemove = new Subject();\n    this.destroy$ = new Subject();\n    this.pointerEventListeners = PointerEventListeners.getInstance(renderer, zone);\n  }\n  /**\n   * @hidden\n   */\n  ngOnInit() {\n    const mousedown$ = merge(this.pointerEventListeners.pointerDown, this.mousedown);\n    const mousemove$ = merge(this.pointerEventListeners.pointerMove, this.mousemove).pipe(tap(({\n      event\n    }) => {\n      if (currentResize && event.cancelable) {\n        event.preventDefault();\n      }\n    }), share());\n    const mouseup$ = merge(this.pointerEventListeners.pointerUp, this.mouseup);\n    let currentResize;\n    const removeGhostElement = () => {\n      if (currentResize && currentResize.clonedNode) {\n        this.elm.nativeElement.parentElement.removeChild(currentResize.clonedNode);\n        this.renderer.setStyle(this.elm.nativeElement, 'visibility', 'inherit');\n      }\n    };\n    const getResizeCursors = () => {\n      return {\n        ...DEFAULT_RESIZE_CURSORS,\n        ...this.resizeCursors\n      };\n    };\n    const mousedrag = mousedown$.pipe(mergeMap(startCoords => {\n      function getDiff(moveCoords) {\n        return {\n          clientX: moveCoords.clientX - startCoords.clientX,\n          clientY: moveCoords.clientY - startCoords.clientY\n        };\n      }\n      const getSnapGrid = () => {\n        const snapGrid = {\n          x: 1,\n          y: 1\n        };\n        if (currentResize) {\n          if (this.resizeSnapGrid.left && currentResize.edges.left) {\n            snapGrid.x = +this.resizeSnapGrid.left;\n          } else if (this.resizeSnapGrid.right && currentResize.edges.right) {\n            snapGrid.x = +this.resizeSnapGrid.right;\n          }\n          if (this.resizeSnapGrid.top && currentResize.edges.top) {\n            snapGrid.y = +this.resizeSnapGrid.top;\n          } else if (this.resizeSnapGrid.bottom && currentResize.edges.bottom) {\n            snapGrid.y = +this.resizeSnapGrid.bottom;\n          }\n        }\n        return snapGrid;\n      };\n      function getGrid(coords, snapGrid) {\n        return {\n          x: Math.ceil(coords.clientX / snapGrid.x),\n          y: Math.ceil(coords.clientY / snapGrid.y)\n        };\n      }\n      return merge(mousemove$.pipe(take(1)).pipe(map(coords => [, coords])), mousemove$.pipe(pairwise())).pipe(map(([previousCoords, newCoords]) => {\n        return [previousCoords ? getDiff(previousCoords) : previousCoords, getDiff(newCoords)];\n      })).pipe(filter(([previousCoords, newCoords]) => {\n        if (!previousCoords) {\n          return true;\n        }\n        const snapGrid = getSnapGrid();\n        const previousGrid = getGrid(previousCoords, snapGrid);\n        const newGrid = getGrid(newCoords, snapGrid);\n        return previousGrid.x !== newGrid.x || previousGrid.y !== newGrid.y;\n      })).pipe(map(([, newCoords]) => {\n        const snapGrid = getSnapGrid();\n        return {\n          clientX: Math.round(newCoords.clientX / snapGrid.x) * snapGrid.x,\n          clientY: Math.round(newCoords.clientY / snapGrid.y) * snapGrid.y\n        };\n      })).pipe(takeUntil(merge(mouseup$, mousedown$)));\n    })).pipe(filter(() => !!currentResize));\n    mousedrag.pipe(map(({\n      clientX,\n      clientY\n    }) => {\n      return getNewBoundingRectangle(currentResize.startingRect, currentResize.edges, clientX, clientY);\n    })).pipe(filter(newBoundingRect => {\n      return this.allowNegativeResizes || !!(newBoundingRect.height && newBoundingRect.width && newBoundingRect.height > 0 && newBoundingRect.width > 0);\n    })).pipe(filter(newBoundingRect => {\n      return this.validateResize ? this.validateResize({\n        rectangle: newBoundingRect,\n        edges: getEdgesDiff({\n          edges: currentResize.edges,\n          initialRectangle: currentResize.startingRect,\n          newRectangle: newBoundingRect\n        })\n      }) : true;\n    }), takeUntil(this.destroy$)).subscribe(newBoundingRect => {\n      if (currentResize && currentResize.clonedNode) {\n        this.renderer.setStyle(currentResize.clonedNode, 'height', `${newBoundingRect.height}px`);\n        this.renderer.setStyle(currentResize.clonedNode, 'width', `${newBoundingRect.width}px`);\n        this.renderer.setStyle(currentResize.clonedNode, 'top', `${newBoundingRect.top}px`);\n        this.renderer.setStyle(currentResize.clonedNode, 'left', `${newBoundingRect.left}px`);\n      }\n      if (this.resizing.observers.length > 0) {\n        this.zone.run(() => {\n          this.resizing.emit({\n            edges: getEdgesDiff({\n              edges: currentResize.edges,\n              initialRectangle: currentResize.startingRect,\n              newRectangle: newBoundingRect\n            }),\n            rectangle: newBoundingRect\n          });\n        });\n      }\n      currentResize.currentRect = newBoundingRect;\n    });\n    mousedown$.pipe(map(({\n      edges\n    }) => {\n      return edges || {};\n    }), filter(edges => {\n      return Object.keys(edges).length > 0;\n    }), takeUntil(this.destroy$)).subscribe(edges => {\n      if (currentResize) {\n        removeGhostElement();\n      }\n      const startingRect = getElementRect(this.elm, this.ghostElementPositioning);\n      currentResize = {\n        edges,\n        startingRect,\n        currentRect: startingRect\n      };\n      const resizeCursors = getResizeCursors();\n      const cursor = getResizeCursor(currentResize.edges, resizeCursors);\n      this.renderer.setStyle(document.body, 'cursor', cursor);\n      this.setElementClass(this.elm, RESIZE_ACTIVE_CLASS, true);\n      if (this.enableGhostResize) {\n        currentResize.clonedNode = deepCloneNode(this.elm.nativeElement);\n        this.elm.nativeElement.parentElement.appendChild(currentResize.clonedNode);\n        this.renderer.setStyle(this.elm.nativeElement, 'visibility', 'hidden');\n        this.renderer.setStyle(currentResize.clonedNode, 'position', this.ghostElementPositioning);\n        this.renderer.setStyle(currentResize.clonedNode, 'left', `${currentResize.startingRect.left}px`);\n        this.renderer.setStyle(currentResize.clonedNode, 'top', `${currentResize.startingRect.top}px`);\n        this.renderer.setStyle(currentResize.clonedNode, 'height', `${currentResize.startingRect.height}px`);\n        this.renderer.setStyle(currentResize.clonedNode, 'width', `${currentResize.startingRect.width}px`);\n        this.renderer.setStyle(currentResize.clonedNode, 'cursor', getResizeCursor(currentResize.edges, resizeCursors));\n        this.renderer.addClass(currentResize.clonedNode, RESIZE_GHOST_ELEMENT_CLASS);\n        currentResize.clonedNode.scrollTop = currentResize.startingRect.scrollTop;\n        currentResize.clonedNode.scrollLeft = currentResize.startingRect.scrollLeft;\n      }\n      if (this.resizeStart.observers.length > 0) {\n        this.zone.run(() => {\n          this.resizeStart.emit({\n            edges: getEdgesDiff({\n              edges,\n              initialRectangle: startingRect,\n              newRectangle: startingRect\n            }),\n            rectangle: getNewBoundingRectangle(startingRect, {}, 0, 0)\n          });\n        });\n      }\n    });\n    mouseup$.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      if (currentResize) {\n        this.renderer.removeClass(this.elm.nativeElement, RESIZE_ACTIVE_CLASS);\n        this.renderer.setStyle(document.body, 'cursor', '');\n        this.renderer.setStyle(this.elm.nativeElement, 'cursor', '');\n        if (this.resizeEnd.observers.length > 0) {\n          this.zone.run(() => {\n            this.resizeEnd.emit({\n              edges: getEdgesDiff({\n                edges: currentResize.edges,\n                initialRectangle: currentResize.startingRect,\n                newRectangle: currentResize.currentRect\n              }),\n              rectangle: currentResize.currentRect\n            });\n          });\n        }\n        removeGhostElement();\n        currentResize = null;\n      }\n    });\n  }\n  /**\n   * @hidden\n   */\n  ngOnDestroy() {\n    // browser check for angular universal, because it doesn't know what document is\n    if (isPlatformBrowser(this.platformId)) {\n      this.renderer.setStyle(document.body, 'cursor', '');\n    }\n    this.mousedown.complete();\n    this.mouseup.complete();\n    this.mousemove.complete();\n    this.destroy$.next();\n  }\n  setElementClass(elm, name, add) {\n    if (add) {\n      this.renderer.addClass(elm.nativeElement, name);\n    } else {\n      this.renderer.removeClass(elm.nativeElement, name);\n    }\n  }\n}\nResizableDirective.ɵfac = function ResizableDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ResizableDirective)(i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n};\nResizableDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: ResizableDirective,\n  selectors: [[\"\", \"mwlResizable\", \"\"]],\n  inputs: {\n    validateResize: \"validateResize\",\n    enableGhostResize: \"enableGhostResize\",\n    resizeSnapGrid: \"resizeSnapGrid\",\n    resizeCursors: \"resizeCursors\",\n    ghostElementPositioning: \"ghostElementPositioning\",\n    allowNegativeResizes: \"allowNegativeResizes\",\n    mouseMoveThrottleMS: \"mouseMoveThrottleMS\"\n  },\n  outputs: {\n    resizeStart: \"resizeStart\",\n    resizing: \"resizing\",\n    resizeEnd: \"resizeEnd\"\n  },\n  exportAs: [\"mwlResizable\"],\n  standalone: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ResizableDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mwlResizable]',\n      exportAs: 'mwlResizable'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    validateResize: [{\n      type: Input\n    }],\n    enableGhostResize: [{\n      type: Input\n    }],\n    resizeSnapGrid: [{\n      type: Input\n    }],\n    resizeCursors: [{\n      type: Input\n    }],\n    ghostElementPositioning: [{\n      type: Input\n    }],\n    allowNegativeResizes: [{\n      type: Input\n    }],\n    mouseMoveThrottleMS: [{\n      type: Input\n    }],\n    resizeStart: [{\n      type: Output\n    }],\n    resizing: [{\n      type: Output\n    }],\n    resizeEnd: [{\n      type: Output\n    }]\n  });\n})();\nclass PointerEventListeners {\n  constructor(renderer, zone) {\n    this.pointerDown = new Observable(observer => {\n      let unsubscribeMouseDown;\n      let unsubscribeTouchStart;\n      zone.runOutsideAngular(() => {\n        unsubscribeMouseDown = renderer.listen('document', 'mousedown', event => {\n          observer.next({\n            clientX: event.clientX,\n            clientY: event.clientY,\n            event\n          });\n        });\n        if (IS_TOUCH_DEVICE) {\n          unsubscribeTouchStart = renderer.listen('document', 'touchstart', event => {\n            observer.next({\n              clientX: event.touches[0].clientX,\n              clientY: event.touches[0].clientY,\n              event\n            });\n          });\n        }\n      });\n      return () => {\n        unsubscribeMouseDown();\n        if (IS_TOUCH_DEVICE) {\n          unsubscribeTouchStart();\n        }\n      };\n    }).pipe(share());\n    this.pointerMove = new Observable(observer => {\n      let unsubscribeMouseMove;\n      let unsubscribeTouchMove;\n      zone.runOutsideAngular(() => {\n        unsubscribeMouseMove = renderer.listen('document', 'mousemove', event => {\n          observer.next({\n            clientX: event.clientX,\n            clientY: event.clientY,\n            event\n          });\n        });\n        if (IS_TOUCH_DEVICE) {\n          unsubscribeTouchMove = renderer.listen('document', 'touchmove', event => {\n            observer.next({\n              clientX: event.targetTouches[0].clientX,\n              clientY: event.targetTouches[0].clientY,\n              event\n            });\n          });\n        }\n      });\n      return () => {\n        unsubscribeMouseMove();\n        if (IS_TOUCH_DEVICE) {\n          unsubscribeTouchMove();\n        }\n      };\n    }).pipe(share());\n    this.pointerUp = new Observable(observer => {\n      let unsubscribeMouseUp;\n      let unsubscribeTouchEnd;\n      let unsubscribeTouchCancel;\n      zone.runOutsideAngular(() => {\n        unsubscribeMouseUp = renderer.listen('document', 'mouseup', event => {\n          observer.next({\n            clientX: event.clientX,\n            clientY: event.clientY,\n            event\n          });\n        });\n        if (IS_TOUCH_DEVICE) {\n          unsubscribeTouchEnd = renderer.listen('document', 'touchend', event => {\n            observer.next({\n              clientX: event.changedTouches[0].clientX,\n              clientY: event.changedTouches[0].clientY,\n              event\n            });\n          });\n          unsubscribeTouchCancel = renderer.listen('document', 'touchcancel', event => {\n            observer.next({\n              clientX: event.changedTouches[0].clientX,\n              clientY: event.changedTouches[0].clientY,\n              event\n            });\n          });\n        }\n      });\n      return () => {\n        unsubscribeMouseUp();\n        if (IS_TOUCH_DEVICE) {\n          unsubscribeTouchEnd();\n          unsubscribeTouchCancel();\n        }\n      };\n    }).pipe(share());\n  }\n  static getInstance(renderer, zone) {\n    if (!PointerEventListeners.instance) {\n      PointerEventListeners.instance = new PointerEventListeners(renderer, zone);\n    }\n    return PointerEventListeners.instance;\n  }\n}\n\n/**\n * An element placed inside a `mwlResizable` directive to be used as a drag and resize handle\n *\n * For example\n *\n * ```html\n * <div mwlResizable>\n *   <div mwlResizeHandle [resizeEdges]=\"{bottom: true, right: true}\"></div>\n * </div>\n * ```\n * Or in case they are sibling elements:\n * ```html\n * <div mwlResizable #resizableElement=\"mwlResizable\"></div>\n * <div mwlResizeHandle [resizableContainer]=\"resizableElement\" [resizeEdges]=\"{bottom: true, right: true}\"></div>\n * ```\n */\nclass ResizeHandleDirective {\n  constructor(renderer, element, zone, resizableDirective) {\n    this.renderer = renderer;\n    this.element = element;\n    this.zone = zone;\n    this.resizableDirective = resizableDirective;\n    /**\n     * The `Edges` object that contains the edges of the parent element that dragging the handle will trigger a resize on\n     */\n    this.resizeEdges = {};\n    this.eventListeners = {};\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.zone.runOutsideAngular(() => {\n      this.listenOnTheHost('mousedown').subscribe(event => {\n        this.onMousedown(event, event.clientX, event.clientY);\n      });\n      this.listenOnTheHost('mouseup').subscribe(event => {\n        this.onMouseup(event.clientX, event.clientY);\n      });\n      if (IS_TOUCH_DEVICE) {\n        this.listenOnTheHost('touchstart').subscribe(event => {\n          this.onMousedown(event, event.touches[0].clientX, event.touches[0].clientY);\n        });\n        merge(this.listenOnTheHost('touchend'), this.listenOnTheHost('touchcancel')).subscribe(event => {\n          this.onMouseup(event.changedTouches[0].clientX, event.changedTouches[0].clientY);\n        });\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.unsubscribeEventListeners();\n  }\n  /**\n   * @hidden\n   */\n  onMousedown(event, clientX, clientY) {\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n    if (!this.eventListeners.touchmove) {\n      this.eventListeners.touchmove = this.renderer.listen(this.element.nativeElement, 'touchmove', touchMoveEvent => {\n        this.onMousemove(touchMoveEvent, touchMoveEvent.targetTouches[0].clientX, touchMoveEvent.targetTouches[0].clientY);\n      });\n    }\n    if (!this.eventListeners.mousemove) {\n      this.eventListeners.mousemove = this.renderer.listen(this.element.nativeElement, 'mousemove', mouseMoveEvent => {\n        this.onMousemove(mouseMoveEvent, mouseMoveEvent.clientX, mouseMoveEvent.clientY);\n      });\n    }\n    this.resizable.mousedown.next({\n      clientX,\n      clientY,\n      edges: this.resizeEdges\n    });\n  }\n  /**\n   * @hidden\n   */\n  onMouseup(clientX, clientY) {\n    this.unsubscribeEventListeners();\n    this.resizable.mouseup.next({\n      clientX,\n      clientY,\n      edges: this.resizeEdges\n    });\n  }\n  // directive might be passed from DI or as an input\n  get resizable() {\n    return this.resizableDirective || this.resizableContainer;\n  }\n  onMousemove(event, clientX, clientY) {\n    this.resizable.mousemove.next({\n      clientX,\n      clientY,\n      edges: this.resizeEdges,\n      event\n    });\n  }\n  unsubscribeEventListeners() {\n    Object.keys(this.eventListeners).forEach(type => {\n      this.eventListeners[type]();\n      delete this.eventListeners[type];\n    });\n  }\n  listenOnTheHost(eventName) {\n    return fromEvent(this.element.nativeElement, eventName).pipe(takeUntil(this.destroy$));\n  }\n}\nResizeHandleDirective.ɵfac = function ResizeHandleDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ResizeHandleDirective)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(ResizableDirective, 8));\n};\nResizeHandleDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: ResizeHandleDirective,\n  selectors: [[\"\", \"mwlResizeHandle\", \"\"]],\n  inputs: {\n    resizeEdges: \"resizeEdges\",\n    resizableContainer: \"resizableContainer\"\n  },\n  standalone: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ResizeHandleDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mwlResizeHandle]'\n    }]\n  }], function () {\n    return [{\n      type: i0.Renderer2\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: ResizableDirective,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, {\n    resizeEdges: [{\n      type: Input\n    }],\n    resizableContainer: [{\n      type: Input\n    }]\n  });\n})();\nclass ResizableModule {}\nResizableModule.ɵfac = function ResizableModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ResizableModule)();\n};\nResizableModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ResizableModule,\n  declarations: [ResizableDirective, ResizeHandleDirective],\n  exports: [ResizableDirective, ResizeHandleDirective]\n});\nResizableModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ResizableModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [ResizableDirective, ResizeHandleDirective],\n      exports: [ResizableDirective, ResizeHandleDirective]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of angular-resizable-element\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ResizableDirective, ResizableModule, ResizeHandleDirective };\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,mBAAmB,MAAM;AAE7B,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;AAAA,EACT,OAAO;AACL,WAAO,kBAAkB,UAAU,UAAU,iBAAiB,KAAK,UAAU,mBAAmB;AAAA,EAClG;AACF,GAAG;AAGH,SAAS,cAAc,MAAM;AAC3B,QAAM,QAAQ,KAAK,UAAU,IAAI;AACjC,QAAM,oBAAoB,MAAM,iBAAiB,MAAM;AACvD,QAAM,WAAW,KAAK,SAAS,YAAY;AAE3C,QAAM,gBAAgB,IAAI;AAC1B,oBAAkB,QAAQ,gBAAc;AACtC,eAAW,gBAAgB,IAAI;AAAA,EACjC,CAAC;AACD,MAAI,aAAa,UAAU;AACzB,uBAAmB,MAAM,KAAK;AAAA,EAChC,WAAW,aAAa,WAAW,aAAa,YAAY,aAAa,YAAY;AACnF,sBAAkB,MAAM,KAAK;AAAA,EAC/B;AACA,eAAa,UAAU,MAAM,OAAO,kBAAkB;AACtD,eAAa,2BAA2B,MAAM,OAAO,iBAAiB;AACtE,SAAO;AACT;AAEA,SAAS,aAAa,UAAU,MAAM,OAAO,UAAU;AACrD,QAAM,qBAAqB,KAAK,iBAAiB,QAAQ;AACzD,MAAI,mBAAmB,QAAQ;AAC7B,UAAM,gBAAgB,MAAM,iBAAiB,QAAQ;AACrD,aAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,KAAK;AAClD,eAAS,mBAAmB,CAAC,GAAG,cAAc,CAAC,CAAC;AAAA,IAClD;AAAA,EACF;AACF;AAEA,IAAI,gBAAgB;AAEpB,SAAS,kBAAkB,QAAQ,OAAO;AAExC,MAAI,MAAM,SAAS,QAAQ;AACzB,UAAM,QAAQ,OAAO;AAAA,EACvB;AAIA,MAAI,MAAM,SAAS,WAAW,MAAM,MAAM;AACxC,UAAM,OAAO,aAAa,MAAM,IAAI,IAAI,eAAe;AAAA,EACzD;AACF;AAEA,SAAS,mBAAmB,QAAQ,OAAO;AACzC,QAAM,UAAU,MAAM,WAAW,IAAI;AACrC,MAAI,SAAS;AAGX,QAAI;AACF,cAAQ,UAAU,QAAQ,GAAG,CAAC;AAAA,IAChC,QAAQ;AAAA,IAAC;AAAA,EACX;AACF;AACA,SAAS,wBAAwB,cAAc,OAAO,SAAS,SAAS;AACtE,QAAM,kBAAkB;AAAA,IACtB,KAAK,aAAa;AAAA,IAClB,QAAQ,aAAa;AAAA,IACrB,MAAM,aAAa;AAAA,IACnB,OAAO,aAAa;AAAA,EACtB;AACA,MAAI,MAAM,KAAK;AACb,oBAAgB,OAAO;AAAA,EACzB;AACA,MAAI,MAAM,QAAQ;AAChB,oBAAgB,UAAU;AAAA,EAC5B;AACA,MAAI,MAAM,MAAM;AACd,oBAAgB,QAAQ;AAAA,EAC1B;AACA,MAAI,MAAM,OAAO;AACf,oBAAgB,SAAS;AAAA,EAC3B;AACA,kBAAgB,SAAS,gBAAgB,SAAS,gBAAgB;AAClE,kBAAgB,QAAQ,gBAAgB,QAAQ,gBAAgB;AAChE,SAAO;AACT;AACA,SAAS,eAAe,SAAS,yBAAyB;AACxD,MAAI,aAAa;AACjB,MAAI,aAAa;AACjB,QAAM,QAAQ,QAAQ,cAAc;AACpC,QAAM,sBAAsB,CAAC,aAAa,iBAAiB,kBAAkB,cAAc;AAC3F,QAAM,YAAY,oBAAoB,IAAI,cAAY,MAAM,QAAQ,CAAC,EAAE,KAAK,WAAS,CAAC,CAAC,KAAK;AAC5F,MAAI,aAAa,UAAU,SAAS,WAAW,GAAG;AAChD,iBAAa,UAAU,QAAQ,iDAAiD,IAAI;AACpF,iBAAa,UAAU,QAAQ,iDAAiD,IAAI;AAAA,EACtF;AACA,MAAI,4BAA4B,YAAY;AAC1C,WAAO;AAAA,MACL,QAAQ,QAAQ,cAAc;AAAA,MAC9B,OAAO,QAAQ,cAAc;AAAA,MAC7B,KAAK,QAAQ,cAAc,YAAY;AAAA,MACvC,QAAQ,QAAQ,cAAc,eAAe,QAAQ,cAAc,YAAY;AAAA,MAC/E,MAAM,QAAQ,cAAc,aAAa;AAAA,MACzC,OAAO,QAAQ,cAAc,cAAc,QAAQ,cAAc,aAAa;AAAA,IAChF;AAAA,EACF,OAAO;AACL,UAAM,eAAe,QAAQ,cAAc,sBAAsB;AACjE,WAAO;AAAA,MACL,QAAQ,aAAa;AAAA,MACrB,OAAO,aAAa;AAAA,MACpB,KAAK,aAAa,MAAM;AAAA,MACxB,QAAQ,aAAa,SAAS;AAAA,MAC9B,MAAM,aAAa,OAAO;AAAA,MAC1B,OAAO,aAAa,QAAQ;AAAA,MAC5B,WAAW,QAAQ,cAAc;AAAA,MACjC,YAAY,QAAQ,cAAc;AAAA,IACpC;AAAA,EACF;AACF;AACA,IAAM,yBAAyB,OAAO,OAAO;AAAA,EAC3C,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AACf,CAAC;AACD,SAAS,gBAAgB,OAAO,SAAS;AACvC,MAAI,MAAM,QAAQ,MAAM,KAAK;AAC3B,WAAO,QAAQ;AAAA,EACjB,WAAW,MAAM,SAAS,MAAM,KAAK;AACnC,WAAO,QAAQ;AAAA,EACjB,WAAW,MAAM,QAAQ,MAAM,QAAQ;AACrC,WAAO,QAAQ;AAAA,EACjB,WAAW,MAAM,SAAS,MAAM,QAAQ;AACtC,WAAO,QAAQ;AAAA,EACjB,WAAW,MAAM,QAAQ,MAAM,OAAO;AACpC,WAAO,QAAQ;AAAA,EACjB,WAAW,MAAM,OAAO,MAAM,QAAQ;AACpC,WAAO,QAAQ;AAAA,EACjB,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,SAAS,aAAa;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,YAAY,CAAC;AACnB,SAAO,KAAK,KAAK,EAAE,QAAQ,UAAQ;AACjC,cAAU,IAAI,KAAK,aAAa,IAAI,KAAK,MAAM,iBAAiB,IAAI,KAAK;AAAA,EAC3E,CAAC;AACD,SAAO;AACT;AACA,IAAM,sBAAsB;AAC5B,IAAM,6BAA6B;AACnC,IAAM,yBAAyB;AAiB/B,IAAM,qBAAN,MAAyB;AAAA;AAAA;AAAA;AAAA,EAIvB,YAAY,YAAY,UAAU,KAAK,MAAM;AAC3C,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,OAAO;AAIZ,SAAK,oBAAoB;AAMzB,SAAK,iBAAiB,CAAC;AAIvB,SAAK,gBAAgB;AAIrB,SAAK,0BAA0B;AAI/B,SAAK,uBAAuB;AAI5B,SAAK,sBAAsB;AAI3B,SAAK,cAAc,IAAI,aAAa;AAIpC,SAAK,WAAW,IAAI,aAAa;AAIjC,SAAK,YAAY,IAAI,aAAa;AAIlC,SAAK,UAAU,IAAI,QAAQ;AAI3B,SAAK,YAAY,IAAI,QAAQ;AAI7B,SAAK,YAAY,IAAI,QAAQ;AAC7B,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,wBAAwB,sBAAsB,YAAY,UAAU,IAAI;AAAA,EAC/E;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACT,UAAM,aAAa,MAAM,KAAK,sBAAsB,aAAa,KAAK,SAAS;AAC/E,UAAM,aAAa,MAAM,KAAK,sBAAsB,aAAa,KAAK,SAAS,EAAE,KAAK,IAAI,CAAC;AAAA,MACzF;AAAA,IACF,MAAM;AACJ,UAAI,iBAAiB,MAAM,YAAY;AACrC,cAAM,eAAe;AAAA,MACvB;AAAA,IACF,CAAC,GAAG,MAAM,CAAC;AACX,UAAM,WAAW,MAAM,KAAK,sBAAsB,WAAW,KAAK,OAAO;AACzE,QAAI;AACJ,UAAM,qBAAqB,MAAM;AAC/B,UAAI,iBAAiB,cAAc,YAAY;AAC7C,aAAK,IAAI,cAAc,cAAc,YAAY,cAAc,UAAU;AACzE,aAAK,SAAS,SAAS,KAAK,IAAI,eAAe,cAAc,SAAS;AAAA,MACxE;AAAA,IACF;AACA,UAAM,mBAAmB,MAAM;AAC7B,aAAO,kCACF,yBACA,KAAK;AAAA,IAEZ;AACA,UAAM,YAAY,WAAW,KAAK,SAAS,iBAAe;AACxD,eAAS,QAAQ,YAAY;AAC3B,eAAO;AAAA,UACL,SAAS,WAAW,UAAU,YAAY;AAAA,UAC1C,SAAS,WAAW,UAAU,YAAY;AAAA,QAC5C;AAAA,MACF;AACA,YAAM,cAAc,MAAM;AACxB,cAAM,WAAW;AAAA,UACf,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AACA,YAAI,eAAe;AACjB,cAAI,KAAK,eAAe,QAAQ,cAAc,MAAM,MAAM;AACxD,qBAAS,IAAI,CAAC,KAAK,eAAe;AAAA,UACpC,WAAW,KAAK,eAAe,SAAS,cAAc,MAAM,OAAO;AACjE,qBAAS,IAAI,CAAC,KAAK,eAAe;AAAA,UACpC;AACA,cAAI,KAAK,eAAe,OAAO,cAAc,MAAM,KAAK;AACtD,qBAAS,IAAI,CAAC,KAAK,eAAe;AAAA,UACpC,WAAW,KAAK,eAAe,UAAU,cAAc,MAAM,QAAQ;AACnE,qBAAS,IAAI,CAAC,KAAK,eAAe;AAAA,UACpC;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,eAAS,QAAQ,QAAQ,UAAU;AACjC,eAAO;AAAA,UACL,GAAG,KAAK,KAAK,OAAO,UAAU,SAAS,CAAC;AAAA,UACxC,GAAG,KAAK,KAAK,OAAO,UAAU,SAAS,CAAC;AAAA,QAC1C;AAAA,MACF;AACA,aAAO,MAAM,WAAW,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,IAAI,YAAU,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,WAAW,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,gBAAgB,SAAS,MAAM;AAC5I,eAAO,CAAC,iBAAiB,QAAQ,cAAc,IAAI,gBAAgB,QAAQ,SAAS,CAAC;AAAA,MACvF,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC,gBAAgB,SAAS,MAAM;AAC/C,YAAI,CAAC,gBAAgB;AACnB,iBAAO;AAAA,QACT;AACA,cAAM,WAAW,YAAY;AAC7B,cAAM,eAAe,QAAQ,gBAAgB,QAAQ;AACrD,cAAM,UAAU,QAAQ,WAAW,QAAQ;AAC3C,eAAO,aAAa,MAAM,QAAQ,KAAK,aAAa,MAAM,QAAQ;AAAA,MACpE,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,SAAS,MAAM;AAC9B,cAAM,WAAW,YAAY;AAC7B,eAAO;AAAA,UACL,SAAS,KAAK,MAAM,UAAU,UAAU,SAAS,CAAC,IAAI,SAAS;AAAA,UAC/D,SAAS,KAAK,MAAM,UAAU,UAAU,SAAS,CAAC,IAAI,SAAS;AAAA,QACjE;AAAA,MACF,CAAC,CAAC,EAAE,KAAK,UAAU,MAAM,UAAU,UAAU,CAAC,CAAC;AAAA,IACjD,CAAC,CAAC,EAAE,KAAK,OAAO,MAAM,CAAC,CAAC,aAAa,CAAC;AACtC,cAAU,KAAK,IAAI,CAAC;AAAA,MAClB;AAAA,MACA;AAAA,IACF,MAAM;AACJ,aAAO,wBAAwB,cAAc,cAAc,cAAc,OAAO,SAAS,OAAO;AAAA,IAClG,CAAC,CAAC,EAAE,KAAK,OAAO,qBAAmB;AACjC,aAAO,KAAK,wBAAwB,CAAC,EAAE,gBAAgB,UAAU,gBAAgB,SAAS,gBAAgB,SAAS,KAAK,gBAAgB,QAAQ;AAAA,IAClJ,CAAC,CAAC,EAAE,KAAK,OAAO,qBAAmB;AACjC,aAAO,KAAK,iBAAiB,KAAK,eAAe;AAAA,QAC/C,WAAW;AAAA,QACX,OAAO,aAAa;AAAA,UAClB,OAAO,cAAc;AAAA,UACrB,kBAAkB,cAAc;AAAA,UAChC,cAAc;AAAA,QAChB,CAAC;AAAA,MACH,CAAC,IAAI;AAAA,IACP,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,qBAAmB;AACzD,UAAI,iBAAiB,cAAc,YAAY;AAC7C,aAAK,SAAS,SAAS,cAAc,YAAY,UAAU,GAAG,gBAAgB,MAAM,IAAI;AACxF,aAAK,SAAS,SAAS,cAAc,YAAY,SAAS,GAAG,gBAAgB,KAAK,IAAI;AACtF,aAAK,SAAS,SAAS,cAAc,YAAY,OAAO,GAAG,gBAAgB,GAAG,IAAI;AAClF,aAAK,SAAS,SAAS,cAAc,YAAY,QAAQ,GAAG,gBAAgB,IAAI,IAAI;AAAA,MACtF;AACA,UAAI,KAAK,SAAS,UAAU,SAAS,GAAG;AACtC,aAAK,KAAK,IAAI,MAAM;AAClB,eAAK,SAAS,KAAK;AAAA,YACjB,OAAO,aAAa;AAAA,cAClB,OAAO,cAAc;AAAA,cACrB,kBAAkB,cAAc;AAAA,cAChC,cAAc;AAAA,YAChB,CAAC;AAAA,YACD,WAAW;AAAA,UACb,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,oBAAc,cAAc;AAAA,IAC9B,CAAC;AACD,eAAW,KAAK,IAAI,CAAC;AAAA,MACnB;AAAA,IACF,MAAM;AACJ,aAAO,SAAS,CAAC;AAAA,IACnB,CAAC,GAAG,OAAO,WAAS;AAClB,aAAO,OAAO,KAAK,KAAK,EAAE,SAAS;AAAA,IACrC,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAC/C,UAAI,eAAe;AACjB,2BAAmB;AAAA,MACrB;AACA,YAAM,eAAe,eAAe,KAAK,KAAK,KAAK,uBAAuB;AAC1E,sBAAgB;AAAA,QACd;AAAA,QACA;AAAA,QACA,aAAa;AAAA,MACf;AACA,YAAM,gBAAgB,iBAAiB;AACvC,YAAM,SAAS,gBAAgB,cAAc,OAAO,aAAa;AACjE,WAAK,SAAS,SAAS,SAAS,MAAM,UAAU,MAAM;AACtD,WAAK,gBAAgB,KAAK,KAAK,qBAAqB,IAAI;AACxD,UAAI,KAAK,mBAAmB;AAC1B,sBAAc,aAAa,cAAc,KAAK,IAAI,aAAa;AAC/D,aAAK,IAAI,cAAc,cAAc,YAAY,cAAc,UAAU;AACzE,aAAK,SAAS,SAAS,KAAK,IAAI,eAAe,cAAc,QAAQ;AACrE,aAAK,SAAS,SAAS,cAAc,YAAY,YAAY,KAAK,uBAAuB;AACzF,aAAK,SAAS,SAAS,cAAc,YAAY,QAAQ,GAAG,cAAc,aAAa,IAAI,IAAI;AAC/F,aAAK,SAAS,SAAS,cAAc,YAAY,OAAO,GAAG,cAAc,aAAa,GAAG,IAAI;AAC7F,aAAK,SAAS,SAAS,cAAc,YAAY,UAAU,GAAG,cAAc,aAAa,MAAM,IAAI;AACnG,aAAK,SAAS,SAAS,cAAc,YAAY,SAAS,GAAG,cAAc,aAAa,KAAK,IAAI;AACjG,aAAK,SAAS,SAAS,cAAc,YAAY,UAAU,gBAAgB,cAAc,OAAO,aAAa,CAAC;AAC9G,aAAK,SAAS,SAAS,cAAc,YAAY,0BAA0B;AAC3E,sBAAc,WAAW,YAAY,cAAc,aAAa;AAChE,sBAAc,WAAW,aAAa,cAAc,aAAa;AAAA,MACnE;AACA,UAAI,KAAK,YAAY,UAAU,SAAS,GAAG;AACzC,aAAK,KAAK,IAAI,MAAM;AAClB,eAAK,YAAY,KAAK;AAAA,YACpB,OAAO,aAAa;AAAA,cAClB;AAAA,cACA,kBAAkB;AAAA,cAClB,cAAc;AAAA,YAChB,CAAC;AAAA,YACD,WAAW,wBAAwB,cAAc,CAAC,GAAG,GAAG,CAAC;AAAA,UAC3D,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,aAAS,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACtD,UAAI,eAAe;AACjB,aAAK,SAAS,YAAY,KAAK,IAAI,eAAe,mBAAmB;AACrE,aAAK,SAAS,SAAS,SAAS,MAAM,UAAU,EAAE;AAClD,aAAK,SAAS,SAAS,KAAK,IAAI,eAAe,UAAU,EAAE;AAC3D,YAAI,KAAK,UAAU,UAAU,SAAS,GAAG;AACvC,eAAK,KAAK,IAAI,MAAM;AAClB,iBAAK,UAAU,KAAK;AAAA,cAClB,OAAO,aAAa;AAAA,gBAClB,OAAO,cAAc;AAAA,gBACrB,kBAAkB,cAAc;AAAA,gBAChC,cAAc,cAAc;AAAA,cAC9B,CAAC;AAAA,cACD,WAAW,cAAc;AAAA,YAC3B,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,2BAAmB;AACnB,wBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AAEZ,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,WAAK,SAAS,SAAS,SAAS,MAAM,UAAU,EAAE;AAAA,IACpD;AACA,SAAK,UAAU,SAAS;AACxB,SAAK,QAAQ,SAAS;AACtB,SAAK,UAAU,SAAS;AACxB,SAAK,SAAS,KAAK;AAAA,EACrB;AAAA,EACA,gBAAgB,KAAK,MAAM,KAAK;AAC9B,QAAI,KAAK;AACP,WAAK,SAAS,SAAS,IAAI,eAAe,IAAI;AAAA,IAChD,OAAO;AACL,WAAK,SAAS,YAAY,IAAI,eAAe,IAAI;AAAA,IACnD;AAAA,EACF;AACF;AACA,mBAAmB,OAAO,SAAS,2BAA2B,mBAAmB;AAC/E,SAAO,KAAK,qBAAqB,oBAAuB,kBAAkB,WAAW,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,CAAC;AAClM;AACA,mBAAmB,OAAyB,kBAAkB;AAAA,EAC5D,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,EACpC,QAAQ;AAAA,IACN,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,yBAAyB;AAAA,IACzB,sBAAsB;AAAA,IACtB,qBAAqB;AAAA,EACvB;AAAA,EACA,SAAS;AAAA,IACP,aAAa;AAAA,IACb,UAAU;AAAA,IACV,WAAW;AAAA,EACb;AAAA,EACA,UAAU,CAAC,cAAc;AAAA,EACzB,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,WAAW;AAAA,MACpB,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,YAAY,UAAU,MAAM;AAC1B,SAAK,cAAc,IAAI,WAAW,cAAY;AAC5C,UAAI;AACJ,UAAI;AACJ,WAAK,kBAAkB,MAAM;AAC3B,+BAAuB,SAAS,OAAO,YAAY,aAAa,WAAS;AACvE,mBAAS,KAAK;AAAA,YACZ,SAAS,MAAM;AAAA,YACf,SAAS,MAAM;AAAA,YACf;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AACD,YAAI,iBAAiB;AACnB,kCAAwB,SAAS,OAAO,YAAY,cAAc,WAAS;AACzE,qBAAS,KAAK;AAAA,cACZ,SAAS,MAAM,QAAQ,CAAC,EAAE;AAAA,cAC1B,SAAS,MAAM,QAAQ,CAAC,EAAE;AAAA,cAC1B;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,aAAO,MAAM;AACX,6BAAqB;AACrB,YAAI,iBAAiB;AACnB,gCAAsB;AAAA,QACxB;AAAA,MACF;AAAA,IACF,CAAC,EAAE,KAAK,MAAM,CAAC;AACf,SAAK,cAAc,IAAI,WAAW,cAAY;AAC5C,UAAI;AACJ,UAAI;AACJ,WAAK,kBAAkB,MAAM;AAC3B,+BAAuB,SAAS,OAAO,YAAY,aAAa,WAAS;AACvE,mBAAS,KAAK;AAAA,YACZ,SAAS,MAAM;AAAA,YACf,SAAS,MAAM;AAAA,YACf;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AACD,YAAI,iBAAiB;AACnB,iCAAuB,SAAS,OAAO,YAAY,aAAa,WAAS;AACvE,qBAAS,KAAK;AAAA,cACZ,SAAS,MAAM,cAAc,CAAC,EAAE;AAAA,cAChC,SAAS,MAAM,cAAc,CAAC,EAAE;AAAA,cAChC;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,aAAO,MAAM;AACX,6BAAqB;AACrB,YAAI,iBAAiB;AACnB,+BAAqB;AAAA,QACvB;AAAA,MACF;AAAA,IACF,CAAC,EAAE,KAAK,MAAM,CAAC;AACf,SAAK,YAAY,IAAI,WAAW,cAAY;AAC1C,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,WAAK,kBAAkB,MAAM;AAC3B,6BAAqB,SAAS,OAAO,YAAY,WAAW,WAAS;AACnE,mBAAS,KAAK;AAAA,YACZ,SAAS,MAAM;AAAA,YACf,SAAS,MAAM;AAAA,YACf;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AACD,YAAI,iBAAiB;AACnB,gCAAsB,SAAS,OAAO,YAAY,YAAY,WAAS;AACrE,qBAAS,KAAK;AAAA,cACZ,SAAS,MAAM,eAAe,CAAC,EAAE;AAAA,cACjC,SAAS,MAAM,eAAe,CAAC,EAAE;AAAA,cACjC;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AACD,mCAAyB,SAAS,OAAO,YAAY,eAAe,WAAS;AAC3E,qBAAS,KAAK;AAAA,cACZ,SAAS,MAAM,eAAe,CAAC,EAAE;AAAA,cACjC,SAAS,MAAM,eAAe,CAAC,EAAE;AAAA,cACjC;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,aAAO,MAAM;AACX,2BAAmB;AACnB,YAAI,iBAAiB;AACnB,8BAAoB;AACpB,iCAAuB;AAAA,QACzB;AAAA,MACF;AAAA,IACF,CAAC,EAAE,KAAK,MAAM,CAAC;AAAA,EACjB;AAAA,EACA,OAAO,YAAY,UAAU,MAAM;AACjC,QAAI,CAAC,uBAAsB,UAAU;AACnC,6BAAsB,WAAW,IAAI,uBAAsB,UAAU,IAAI;AAAA,IAC3E;AACA,WAAO,uBAAsB;AAAA,EAC/B;AACF;AAkBA,IAAM,wBAAN,MAA4B;AAAA,EAC1B,YAAY,UAAU,SAAS,MAAM,oBAAoB;AACvD,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,qBAAqB;AAI1B,SAAK,cAAc,CAAC;AACpB,SAAK,iBAAiB,CAAC;AACvB,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,SAAK,KAAK,kBAAkB,MAAM;AAChC,WAAK,gBAAgB,WAAW,EAAE,UAAU,WAAS;AACnD,aAAK,YAAY,OAAO,MAAM,SAAS,MAAM,OAAO;AAAA,MACtD,CAAC;AACD,WAAK,gBAAgB,SAAS,EAAE,UAAU,WAAS;AACjD,aAAK,UAAU,MAAM,SAAS,MAAM,OAAO;AAAA,MAC7C,CAAC;AACD,UAAI,iBAAiB;AACnB,aAAK,gBAAgB,YAAY,EAAE,UAAU,WAAS;AACpD,eAAK,YAAY,OAAO,MAAM,QAAQ,CAAC,EAAE,SAAS,MAAM,QAAQ,CAAC,EAAE,OAAO;AAAA,QAC5E,CAAC;AACD,cAAM,KAAK,gBAAgB,UAAU,GAAG,KAAK,gBAAgB,aAAa,CAAC,EAAE,UAAU,WAAS;AAC9F,eAAK,UAAU,MAAM,eAAe,CAAC,EAAE,SAAS,MAAM,eAAe,CAAC,EAAE,OAAO;AAAA,QACjF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,0BAA0B;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,OAAO,SAAS,SAAS;AACnC,QAAI,MAAM,YAAY;AACpB,YAAM,eAAe;AAAA,IACvB;AACA,QAAI,CAAC,KAAK,eAAe,WAAW;AAClC,WAAK,eAAe,YAAY,KAAK,SAAS,OAAO,KAAK,QAAQ,eAAe,aAAa,oBAAkB;AAC9G,aAAK,YAAY,gBAAgB,eAAe,cAAc,CAAC,EAAE,SAAS,eAAe,cAAc,CAAC,EAAE,OAAO;AAAA,MACnH,CAAC;AAAA,IACH;AACA,QAAI,CAAC,KAAK,eAAe,WAAW;AAClC,WAAK,eAAe,YAAY,KAAK,SAAS,OAAO,KAAK,QAAQ,eAAe,aAAa,oBAAkB;AAC9G,aAAK,YAAY,gBAAgB,eAAe,SAAS,eAAe,OAAO;AAAA,MACjF,CAAC;AAAA,IACH;AACA,SAAK,UAAU,UAAU,KAAK;AAAA,MAC5B;AAAA,MACA;AAAA,MACA,OAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,SAAS,SAAS;AAC1B,SAAK,0BAA0B;AAC/B,SAAK,UAAU,QAAQ,KAAK;AAAA,MAC1B;AAAA,MACA;AAAA,MACA,OAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,KAAK,sBAAsB,KAAK;AAAA,EACzC;AAAA,EACA,YAAY,OAAO,SAAS,SAAS;AACnC,SAAK,UAAU,UAAU,KAAK;AAAA,MAC5B;AAAA,MACA;AAAA,MACA,OAAO,KAAK;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,4BAA4B;AAC1B,WAAO,KAAK,KAAK,cAAc,EAAE,QAAQ,UAAQ;AAC/C,WAAK,eAAe,IAAI,EAAE;AAC1B,aAAO,KAAK,eAAe,IAAI;AAAA,IACjC,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB,WAAW;AACzB,WAAO,UAAU,KAAK,QAAQ,eAAe,SAAS,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC;AAAA,EACvF;AACF;AACA,sBAAsB,OAAO,SAAS,8BAA8B,mBAAmB;AACrF,SAAO,KAAK,qBAAqB,uBAA0B,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,GAAM,kBAAkB,oBAAoB,CAAC,CAAC;AAC/M;AACA,sBAAsB,OAAyB,kBAAkB;AAAA,EAC/D,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,EACvC,QAAQ;AAAA,IACN,aAAa;AAAA,IACb,oBAAoB;AAAA,EACtB;AAAA,EACA,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAsB;AAAC;AACvB,gBAAgB,OAAO,SAAS,wBAAwB,mBAAmB;AACzE,SAAO,KAAK,qBAAqB,iBAAiB;AACpD;AACA,gBAAgB,OAAyB,iBAAiB;AAAA,EACxD,MAAM;AAAA,EACN,cAAc,CAAC,oBAAoB,qBAAqB;AAAA,EACxD,SAAS,CAAC,oBAAoB,qBAAqB;AACrD,CAAC;AACD,gBAAgB,OAAyB,iBAAiB,CAAC,CAAC;AAAA,CAC3D,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,oBAAoB,qBAAqB;AAAA,MACxD,SAAS,CAAC,oBAAoB,qBAAqB;AAAA,IACrD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}