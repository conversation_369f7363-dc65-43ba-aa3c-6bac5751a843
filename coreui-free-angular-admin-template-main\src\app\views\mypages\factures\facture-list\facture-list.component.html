<app-smart-container title="Liste des Factures">

    <div slot="content">
        <app-smart-table [data]="listFactures" [isLoading]="loading" [config]="configTable" [columns]="tableColumns"
            [actionButtons]="tableActions">
        </app-smart-table>

        <!-- Container détails facture -->

    </div>
</app-smart-container>

<div id="smart-container-details">

    <div [hidden]="!afficherDetails">
    <app-smart-container  [title]="'Détails de la facture'">
        <div slot="actions">
            <ng-container>
                <app-export-button [buttonText]="'Export Détails Facture'" [fileName]="'facture-details'"
                    [data]="DataDetails"></app-export-button>
            </ng-container>
        </div>

        <div slot="content">
            <div *ngIf="afficherDetails" class="details-container" style="margin-top: 2rem;">
                <app-smart-table [data]="DataDetails" [isLoading]="loadingDetails" [columns]="tableColumnsDetails"
                    [config]="{ emptyMessage: 'Aucun détail de facture trouvé' }">
                </app-smart-table>
            </div>
        </div>

    </app-smart-container>
    </div>
    <div [hidden]="!afficherFacture" class="pdf-container" style="margin: 0; padding: 0; font-family: Arial, sans-serif;">
        <app-facture-impression #factureImpression></app-facture-impression>
    </div>
</div>