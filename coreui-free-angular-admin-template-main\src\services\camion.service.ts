import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable } from 'rxjs';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};
@Injectable({
  providedIn: 'root'
})
export class CamionService {
  apiURL = environment.apiURL;
  constructor(private http: HttpClient) { }
  addCamion(data: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'camion', data, httpOptions);
  }

  findAllCamion(): Observable<any> {

    return this.http.get<any>(this.apiURL + 'camion', httpOptions);
    }
  deleteCamion(id:any): Observable<any> {
    return this.http.get<any>(this.apiURL + 'camion/delate/' + id, httpOptions);
  }
}
