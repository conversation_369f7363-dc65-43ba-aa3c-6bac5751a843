import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
//import { NGXToastrService } from 'app/components/extra/toastr/toastr.service';
import { RegisterServiceService } from 'app/services/register-service.service';
import { Md5 } from 'md5-typescript';
import { FileItem, FileUploader } from 'ng2-file-upload';
import { ToastrService } from 'ngx-toastr';

const URL = 'https://zen-logistic.com/api/upload';

@Component({
  selector: 'app-camion',
  templateUrl: './camion.component.html',
  styleUrls: ['./camion.component.scss'],
  providers: [//NGXToastrService
  ]

})
export class CamionComponent implements OnInit {
  registerForm: FormGroup;
  submitted = false;
  err = false;
  errmessage: String = ' veuillez remplir tous les champs ';
  loading = false;

  filePath: string;



  constructor(private toastr: ToastrService, private router: Router,
    private route: ActivatedRoute, private formBuilder: FormBuilder, private registerService: RegisterServiceService) { }
  public uploader: FileUploader = new FileUploader({
    url: URL,
    headers: [{ name: 'Authorization', value: 'basic ' + btoa('med:123456') }],
    itemAlias: 'image'
  });
  imagePreview(e) {
    if (this.uploader.queue.length == 2) { this.uploader.queue[0].remove() }
    //console.log(this.uploader.queue)
    const file = (e.target as HTMLInputElement).files[0];

    this.registerForm.patchValue({
      img: file
    });

    this.registerForm.get('img').updateValueAndValidity()

    const reader = new FileReader();
    reader.onload = () => {
      this.filePath = reader.result as string;
    }
    reader.readAsDataURL(file)
  }

  renameFile(name: string): void {
    // //console.log(this.uploader.queue)

    // fetching the item uploaded
    const oldFileItem: FileItem = this.uploader.queue[0];
    // re-instanciating a new file based on the old file by changing the name property
    const newFile: File = new File([this.uploader.queue[0]._file], name, { type: oldFileItem.file.type });
    // uploader.queue is taking FileItem objects, instanciating a new FileItem
    const newFileItem = new FileItem(this.uploader, newFile, {
      url: URL,
      headers: [{ name: 'Authorization', value: 'basic ' + btoa('med:123456') }],
      itemAlias: 'image'
    });

    // replacing the old one by the new file updated

  }
  fileName = ""
  ngOnInit() {
    this.uploader.onAfterAddingFile = (file) => {
      let originalname = file.file.name;
      //console.log(originalname);
      let ext = originalname.split('.').pop();
      let filename = (originalname.split('.').slice(0, -1).join('.')).replace(/\s/g, "");




      file.file.name = filename + '-' + Date.now() + '.' + ext
      this.fileName = file.file.name
      file.withCredentials = false;
    };
    this.uploader.onCompleteItem = (item: any, status: any) => {
      //console.log('Uploaded File Details:', item);
      this.toastr.success('File successfully uploaded!');
    };
    this.registerForm = this.formBuilder.group({
      img: [null],
      type_camion: ['Fourgonnette 3m3', Validators.required],
      immatriculation: ['', Validators.required],
      immat_rs_tn: ['RS', Validators.required],
      date_circulation: ['', Validators.required],
      poids: ['', Validators.required],
      unite_poids: ['KG', Validators.required],
      volume: ['', Validators.required],
      nombre_palettes: ['', Validators.required],
      palette_met_eur: ['MET', Validators.required],
      longeur: ['', Validators.required],
      largeur: ['', Validators.required],
      hauteur: ['', Validators.required],
      image_carte_grise: ['', Validators.required],
      ajoutee_par: sessionStorage.getItem("iduser"),


    });

  }



  get f() {
    return this.registerForm.controls;

  }
  saveCamion() {
    this.submitted = true;
    this.err = false;
    for (var n in this.f) {
      if (this.f[n].status === 'INVALID') {
        this.err = true;
      }
    }

    if (this.err) {
      this.toastr.error('veuillez remplir tous les champs', 'Oups!', { closeButton: true });

    }


    if (!this.err) {
      this.registerService.addCamion({
        "type_camion": this.f.type_camion.value,
        "immatriculation": this.f.immatriculation.value,
        "immat_rs_tn": this.f.immat_rs_tn.value,
        "date_circulation": this.f.date_circulation.value,
        "poids": this.f.poids.value, "unite_poids": this.f.unite_poids.value,
        "volume": this.f.volume.value,
        "nombre_palettes": this.f.nombre_palettes.value,
        "palette_met_eur": this.f.palette_met_eur.value, "longeur": this.f.longeur.value,
        "largeur": this.f.largeur.value, "hauteur": this.f.hauteur.value,
        "image_carte_grise": this.fileName,
        "ajoutee_par": sessionStorage.getItem('iduser')
        ,
      }).subscribe((data => {
        this.uploader.uploadAll()
        this.toastr.success('Le camion est ajouté avec succès', 'Félécitation!', { closeButton: true })
        this.router.navigate(['listcamion'], { relativeTo: this.route.parent });

        this.loading = false;
      }), error => {
        this.loading = false;

      })


    }

    else {

      this.loading = false;
    }

  }



}

