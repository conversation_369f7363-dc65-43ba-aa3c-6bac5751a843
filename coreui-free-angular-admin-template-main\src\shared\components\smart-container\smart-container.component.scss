.c-card {
  border: 1px solid var(--cui-border-color);
  border-radius: var(--cui-border-radius);
  box-shadow: 0 0.125rem 0.25rem rgba(255, 255, 255, 0.075);
  margin: 5rem,5rem,5rem,5rem;
  
  .c-card-header {
    background-color: var(--cui-card-cap-bg);
    border-bottom: 1px solid var(--cui-border-color);
    padding: 1rem 1.25rem;
    
    .header-content {
      h4 {
        color: var(--cui-body-color);
        font-weight: 600;
        font-size: 1.25rem;
      }
      
      p {
        font-size: 0.875rem;
        color: var(--cui-text-muted);
      }
    }
    
    .header-actions {
      display: flex;
      gap: 0.5rem;
      align-items: center;
      
      // Responsive adjustments
      @media (max-width: 576px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
        
        .btn {
          font-size: 0.875rem;
          padding: 0.375rem 0.75rem;
        }
      }
    }
    
    // Mobile responsive header
    @media (max-width: 768px) {
      .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 1rem;
        
        .header-actions {
          align-self: stretch;
          justify-content: flex-end;
        }
      }
    }
  }
  
  .c-card-body {
    padding: 1.25rem;
    background-color: var(--cui-body-bg,#ffffff);
    
    // Remove default margins from first and last children
    > :first-child {
      margin-top: 0;
    }
    
    > :last-child {
      margin-bottom: 0;
    }
  }
}
