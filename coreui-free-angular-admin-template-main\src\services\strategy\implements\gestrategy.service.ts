import { Injectable } from '@angular/core';
import { ServiceStrategy } from '../service-strategy';
import { landingPage } from '../enum/landing-page.enum';
import { Observable } from 'rxjs';
import { LigneCmdService } from '../../ligne-cmd.service';
import { CommandeService } from '../../commande.service';

@Injectable({
  providedIn: 'root'
})
export class GEStrategyService implements ServiceStrategy {

  constructor(
    private ligneCmdService: LigneCmdService,
    private commandeService: CommandeService
  ) { }

  getLandingPage(): landingPage {
    // Example implementation: return a default landing page enum value
    return landingPage.GE;
  }

  findReservedLineByUserId(userId: string): Observable<any> {
    return this.ligneCmdService.findReservedByIdUser(userId);
  }
  findExpediedLineByUserId(userId: string): Observable<any> {
    return this.ligneCmdService.findExpediedByIdUser(userId);
  }
  findDeliveredLineByUserId(userId: string): Observable<any> {
    return this.ligneCmdService.findDeliveredByIdUser(userId);
  }
  findEnrgCommandsByUserId(userId: string): Observable<any> {
    return this.commandeService.findEnrgCommandeModele(userId);
  }
  getNonReservedCommands(userId: string): Observable<any> {
    return this.commandeService.getAllOrdersByUser(userId);
  }
  findCommandeToValidate(userId: string): Observable<any> {
    return this.commandeService.findComToValidateByUser(userId);
  }
  findValidCommands(userId: string): Observable<any> {
    return this.commandeService.findAllComValidByUser(userId);
  }

}
