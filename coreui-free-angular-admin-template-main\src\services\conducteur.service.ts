import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable } from 'rxjs';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};
@Injectable({
  providedIn: 'root'
})
export class ConducteurService {
  apiURL = environment.apiURL;
  constructor(private http:HttpClient) { }

  addConducteur(data: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'conducteur', data, httpOptions);
  }

  findAllConducteur(id: any): Observable<any> {
    return this.http.get<any>(this.apiURL + 'conducteurTr/' + id, httpOptions)
        
  }

  findConducteur(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'conducteur/', httpOptions);
  }

  findConducteurById(id : any): Observable<any> {
    const url = `${this.apiURL}conducteur/${id}`;

    return this.http.get(url, httpOptions);
  }

  findConducteurAdmin(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'conducteur', httpOptions);
  }

  blockConducteur(id: any): Observable<any> {
    return this.http.get<any>(this.apiURL + 'conducteur/conduceurBloqued/' + id, httpOptions);
  }
}