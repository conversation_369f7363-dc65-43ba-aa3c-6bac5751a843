import { Injectable } from '@angular/core';
import { ServiceStrategy } from '../service-strategy';
import { landingPage } from '../enum/landing-page.enum';
import { Observable } from 'rxjs';
import { LigneCmdService } from '../../ligne-cmd.service';
import { CommandeService } from '../../commande.service';

@Injectable({
  providedIn: 'root'
})
export class ChefDepartementStrategyService implements ServiceStrategy{

  constructor(
    private ligneCmdService: LigneCmdService,
    private commandeService: CommandeService
  ) { }
  getLandingPage(): landingPage {
    return landingPage.Chef_Department;
  }

  findReservedLineByUserId(userId: string): Observable<any> {
    return this.ligneCmdService.findReservedByChef(userId);
  }
  findExpediedLineByUserId(userId: string): Observable<any> {
    return this.ligneCmdService.findExpediedByChef(userId);
  }

  findDeliveredLineByUserId(userId: string): Observable<any> {
    return this.ligneCmdService.findDeliveredByChef(userId);
  }
  findEnrgCommandsByUserId(userId: string): Observable<any> {
    return this.commandeService.findEnrgCommandeModeleByChef(userId);
  }

  getNonReservedCommands(userId: string): Observable<any> {
    return this.commandeService.getAllCommandsByChef(userId);
  }
  findCommandeToValidate(userId: string): Observable<any> {
    return this.commandeService.getComToValidateByChef(userId);
  }
  findValidCommands(userId: string): Observable<any> {
    return this.commandeService.getAllComValidByChef(userId);
  }
}
