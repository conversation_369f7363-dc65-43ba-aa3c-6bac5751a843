import { Component } from '@angular/core';
import { FormField, FormButton, FormConfig } from '../../../../../shared/models/form.models';
import { SmartContainerComponent } from '../../../../../shared/components/smart-container/smart-container.component';
import { DynamicFormComponent } from '../../../../../shared/components/dynamic-form/dynamic-form.component';
import { FormModalComponent } from '../../../../../shared/components/form-modal/form-modal.component';


@Component({
  standalone:true,
  selector: 'app-test-dynamic-form',
  templateUrl: './test-dynamic-form.component.html',
  imports:[SmartContainerComponent,DynamicFormComponent,FormModalComponent]
})
export class TestDynamicFormComponent {
  onChange(value: any): void {
      console.log('Field changed:', value);
  }
  showModal = false;
  countries = [
    { id: 1, name: 'United States' },
    { id: 2, name: 'Canada' },
    { id: 3, name: 'United Kingdom' },
    { id: 4, name: 'Australia' }
  ];

  existingData: any = {
    firstName:'aymen',
    lastName: 'hadriche',
    age: 25,
    email: '<EMAIL>',
    country: 1,
    interests: ['sports', 'music'],
    bio: 'I am a software developer',
    birthDate: '2000-01-01',
  };


  formConfig: FormConfig = {
    title: 'User Registration',
    fieldGroups: [ {
      groupLabel: 'Personal Information',
      fields: [
        {
        type: 'text',
        onChange: (value) => this.onChange(value),
        label: 'First Name',
        name: 'firstName',
        required: true,
        placeholder: 'Enter your first name',
        validation: {
          minLength: 2,
          maxLength: 30
        }
      },
      {
        type: 'text',
        hidden: true,
        label: 'Last Name',
        name: 'lastName',
        required: true,
        placeholder: 'Enter your last name'
      },
      {
        type: 'email',
        label: 'Email',
        name: 'email',
        required: true,
        placeholder: 'Enter your email',
        validation: {
          pattern: '^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$'
        }
      }]
    } ],
    fields: [
      {
        type :'number',
        label:'age',
        name:'age',
        required:true,
        placeholder:'Enter your age',
        validation: {
          pattern: '^[0-9]*$'
        }
      },
      {
        type: 'password',
        label: 'Password',
        name: 'password',
        required: true,
        validation: {
          minLength: 8
        }
      },
      {
        type: 'select',
        label: 'Country',
        name: 'country',
        options: {
          objectArray: this.countries,
          valueAttribute: 'id',
          labelAttribute: 'name'
        }
      },
      {
        type: 'multiselect',
        label: 'Interests',
        name: 'interests',
        options: {
          objectArray: [
            { id: 'sports', name: 'Sports' ,any:'ca'},
            { id: 'music', name: 'Music' ,any:'ca'},
            { id: 'reading', name: 'Reading' ,any:'ca'},
            { id: 'travel', name: 'Travel' ,any:'ca'}
          ],
          valueAttribute: 'id',
          labelAttribute: 'name'
        }
      },
      {
        type: 'textarea',
        label: 'Bio',
        name: 'bio',
        placeholder: 'Tell us about yourself'
      },
      {
        type: 'date',
        label: 'Birth Date',
        name: 'birthDate',
        required: true
      },
      {
        type: 'image',
        label: 'Profile Picture',
        name: 'profilePicture'
      }
    ],
    buttons: [
      
      {
        label: 'Cancel',
        color: 'secondary',
        onClick: (formData) => this.onCancel(formData)
      },
      {
        label: 'Submit',
        color: 'primary',
        onClick : (formData) => this.onFormSubmit(formData)
      }
    ]
  };

  async CreateModal() {
    this.showModal=true;
    this.existingData=null;
    console.log("test - Create - Modal",this.existingData)
  }
  async ModifierModal() {
    
    this.existingData= {
    firstName:'aymen',
    lastName: 'hadriche',
    age: 25,
    email: '<EMAIL>',
    country: 1,
    interests: ['sports', 'music'],
    bio: 'I am a software developer',
    birthDate: '2000-01-01'
  }
  this.showModal=true;
  console.log("test - Modify - Modal",this.existingData)
  }

  onFormSubmit(formData: any) {
    console.log('Form submitted:', formData);
    // Handle form submission
  }

  onCancel(formData: any) {
    console.log('Form cancelled. Current data:', formData);
    // Here you might want to reset the form or navigate away
    alert('Form cancelled!');
  }

  onFormChange(formData: any) {
    console.log('Form changed:', formData);
  }
}