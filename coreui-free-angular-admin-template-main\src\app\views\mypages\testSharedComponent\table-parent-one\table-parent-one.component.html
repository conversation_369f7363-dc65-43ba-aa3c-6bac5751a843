
  <app-smart-container
    title="Enhanced Smart Table Demo"
    subtitle="Demonstrating configurable action buttons with callbacks and theme compatibility">

    <div slot="actions">
      <button class="btn btn-primary" (click)="addUser()">
        <c-icon name="cilPlus"></c-icon> Add User
      </button>
      <button class="btn btn-outline-secondary" (click)="refreshData()">
        <c-icon name="cilReload"></c-icon> Refresh
      </button>
    </div>

    <div slot="content">
      <div class="mb-3">
        <div class="alert alert-primary">
          <h6 class="alert-heading">Enhanced Action Buttons</h6>
          <p class="mb-2">This table demonstrates the new action button system with:</p>
          <ul class="mb-0">
            <li><strong>Action Buttons:</strong> Edit, Delete, View, Activate/Deactivate, and Send Email (using direct callbacks)</li>
            <li><strong>Conditional Display:</strong> Activate/Deactivate buttons show based on user status</li>
            <li><strong>Disabled State:</strong> Send Email button is disabled for users without email</li>
            <li><strong>Theme Compatibility:</strong> All buttons adapt to light/dark themes</li>
          </ul>
        </div>
      </div>

      <div class="status-bar">
        @if (lastAction) {
          <div class="alert alert-info">
            Last action: <strong>{{lastAction}}</strong>
          </div>
        }
        @if (selectedRows.length) {
          <div class="alert alert-success">
            Selected {{selectedRows.length}} row(s):
            IDs: {{getSelectedIds()}}
          </div>
        }
      </div>

      <app-smart-table
        [columns]="tableColumns"
        [data]="tableData"
        [actionButtons]="actionButtons"
        [config]="tableConfig"
        [isLoading]="loading"
        (selectionChange)="handleSelectionChange($event)">
      </app-smart-table>
    </div>

  </app-smart-container>
