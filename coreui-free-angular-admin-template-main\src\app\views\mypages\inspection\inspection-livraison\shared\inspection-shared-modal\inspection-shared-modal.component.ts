import { Component, Input } from '@angular/core';
import { VoyageService } from 'src/services';
import { LigneCmdService } from 'src/services/ligne-cmd.service';
import { DynamicModalComponent } from 'src/shared/components/dynamic-modal/dynamic-modal.component';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';
import { TableColumn } from 'src/shared/models/table.models';

@Component({
  selector: 'app-inspection-shared-modal',
  standalone: true,
  imports: [DynamicModalComponent,SmartTableComponent],
  templateUrl: './inspection-shared-modal.component.html',
  styleUrl: './inspection-shared-modal.component.scss'
})
export class InspectionSharedModalComponent {
  @Input() VoyageId!: number;

  loading: boolean = false;
  showModal: boolean = false;

  lignes: any[] = [];
  tableColumn :TableColumn[] = [
    { name:'id', displayName: 'ID', dataType: 'number' },
    { name:'nom_depart', displayName: 'Nom de départ' },
    { name:'nom_arrivee', displayName: 'Nom d\'arrivée' },
    { name:'date_livraison', displayName: 'Date de Livraison', dataType: 'date' },
    { name:'date_voyage', displayName: 'Date du voyage', dataType: 'date' },
    { name:'kilometrage', displayName: 'Kilométrage', dataType: 'number' },
    { name:'type_ligne', displayName: 'Type de ligne' }
  ]

  constructor(
    private voyageService: VoyageService,
    private ligneCmdService: LigneCmdService
  ){}

  ngOnInit(): void {
    this.loadLignes();
  }

  loadLignes() {
    this.loading = true;
    this.ligneCmdService.findLignesByIdVoyage(this.VoyageId).subscribe({
      next: (data: any) => {
        console.log("data",data);
        this.lignes = data.data;
        console.log("lignes Voyage",this.lignes);
      },
      error: (error: any) => {
        console.error(error);
      },
      complete: () => {
        this.loading = false;
      }
    })
  }
  openModal() {
    this.showModal = true;
    console.log("VoyageId",this.VoyageId);
    this.loadLignes();
  }
  closeModal() {
    this.showModal = false;
  }
}
