import { AfterViewInit,ViewChild,TemplateRef , Component, OnInit, ViewEncapsulation } from '@angular/core';
import { ModalDismissReasons, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { RegisterServiceService } from 'app/services/register-service.service';
import { SelectcamionComponent } from '../selectcamion/selectcamion.component';
import { SelectconducteurComponent } from '../selectconducteur/selectconducteur.component';
import { ToastrService } from 'ngx-toastr';
//import { NGXToastrService } from 'app/components/extra/toastr/toastr.service';
import { throwError } from 'rxjs';
import { LigneCmdService } from 'app/services/ligneCmd.service';
import { CommandeService } from 'app/services/commande.service';
import { mailService } from 'app/services/mail.service';
import { DestinationService } from 'app/services/destination.service';


@Component({
  selector: 'app-list-commande',
  templateUrl: './list-commande.component.html',
  styleUrls: ['./list-commande.component.scss'],
  encapsulation: ViewEncapsulation.None,
  providers: []
})
export class ListCommandeComponent implements OnInit {
  @ViewChild('popupTemplate', { static: true }) popupTemplate: TemplateRef<any>;
  @ViewChild('details', { static: true }) details: TemplateRef<any>;
  @ViewChild('horaire', { static: true }) horaire: TemplateRef<any>;

  nomsDepartArriveeMap
  source = [];
  closeResult = '';
  detail = 0;
  source1 = []
  source2 = []
  listptChrgDechrg = '';
  ligneTable=[]
  toVerif=[]
  toastDisplayed = false
  selectedRowIndex: number = -1;
  idCmd=""
  fullname=""
  camionList=[]
  conducteurList=[]
  selectedCamionId: string =""; // ou tout autre type qui convient à votre modèle
  selectedConducteur: any ="";
  selectedDate :any = ""
  minDate: string; // ou utilisez le type Date si nécessaire
  totalVolume = 0
  modalRef : any
  historique = 0
  resultatFinal
  tot=0
  contentSms : any ={}
  detailsLigne=[]
  selectedRows: any[] = []; // tableau pour stocker les lignes sélectionnées
  toDownload : any =[]
  filteredRows = [];
  hours: any[] = [];


  constructor(private toastr: ToastrService,
     private listconducteurService: RegisterServiceService, 
     private modalService: NgbModal,
     private ligneCmdService : LigneCmdService,
     private commandeService : CommandeService,
      private listcamionService: RegisterServiceService,
      private mailService : mailService,
      private destinationService: DestinationService,

      ) {

        const today = new Date();
    this.minDate = today.toISOString().split('T')[0];
       }

 async ngOnInit() {

  for (let i = 0; i < 24; i++) {
    const hourString = i.toString().padStart(2, '0'); // Formatage de l'heure à deux chiffres
    this.hours.push(hourString);
  }


    await this.ligneCmdService.findAllValid().subscribe(data => {
      this.source = data;
      console.log("this.source",data)
    }, error => {
    })

   await this.listcamionService.findAllCamion().subscribe(data => {
      this.camionList = data;
      console.log("camion:",this.camionList)
    })

    this.listconducteurService.findConducteur().subscribe(data => {
      this.conducteurList = data;
      console.log("conducteurList:",this.conducteurList)

    })


  }




  settings = {
    hideSelectAllCheckbox: false,
    selectMode: 'multi',
    actions: {
      add: false,
      edit: false,
      delete: false,
      select: true,
      custom: [
        {
          name: 'voir détails',
          type: "custom",
          class: "btn btn-danger",
          title: '<i title="detail"> <label class="danger">Détails</label></i>'
        },
      ],
    },
    columns: {
      id: { title: 'ID' },
      date_creation: { title: 'Date création' },
      ajoutee_par: { title: 'Ajoutée par' },
      date_depart: { title: 'Date départ' },
      date_arrivee: { title: 'Date arrivée' },
      nom_depart: { title: 'Destination Départ' },
      nom_arrivee: { title: 'Destination Arrivée' },
      type_ligne: { title: 'Type' },
      volume: { title: 'Volume (m³)' },
      quantite: { title: 'Quantité' },
      estimation: { title: 'Volume Estimé' },
    },
    attr: { class: 'table table-responsive' },
    rowClassFunction: (row) => {
      // Vérification des conditions
      if (row.data.driver_confirm == false && row.data.date_reservation != null) {
        console.log('row-red')
        return 'row-red'; // Retourner la classe CSS si la condition est vraie
      }
      return ''; // Sinon, pas de classe personnalisée
    },
  };
  
  
  onUserRowSelect(event: any) {
    this.updateSelectedRowsState();
  
    // Check if the row is already in the list
    if (event == null || event.data == null) {
      if (event.selected.length > 0) {
        // Checkbox is selected, add all source IDs to selectedRows
        this.source.forEach(element => {
          if (!this.selectedRows.includes(element.id)) {
            this.selectedRows.push(element.id);
          }
        });
        console.log('All rows added to selectedRows:', this.selectedRows);
      } else {
        // Checkbox is deselected, remove all items from selectedRows
        this.selectedRows = [];
        console.log('All rows removed from selectedRows:', this.selectedRows);
      }
    } else {
      const isSelected = this.selectedRows.includes(event.data.id);
  
      if (!isSelected) {
        // Row is not selected yet, so add its ID to the list
        this.selectedRows.push(event.data.id);
      } else {
        // Row is already selected, so remove its ID from the list
        this.selectedRows = this.selectedRows.filter(id => id !== event.data.id);
      }
  
      // Update the CSS class for selected rows
      const rowElement = document.getElementById(`row-${event.data.id}`);
      if (rowElement) {
        rowElement.classList.toggle('selected-row', !isSelected);
      }
    }
  
    console.log('Updated selectedRows:', this.selectedRows);
    this.applySelectionAfterSearchAndSort(); // Call method to apply selection after each action
  }
  
  isRowSelected(rowId: number): boolean {
    return this.selectedRows.includes(rowId);
  }
  
  applySelectionAfterSearchAndSort() {
    console.log("Applying selection after search and sort");
    this.source.forEach(row => {
      row.isSelected = this.selectedRows.includes(row.id);
    });
  }
  
  updateSelectedRowsState() {
    console.log("Updating selected rows state");
    this.source.forEach(element => {
      const rowElement = document.getElementById(`row-${element.id}`);
      const isSelected = this.selectedRows.includes(element.id);
      if (rowElement) {
        rowElement.classList.toggle('selected-row', isSelected);
      }
      element.selected = isSelected; // Set the selected property based on isSelected
      console.log(element.id, isSelected);
    });
  }
  



onCamionChange() {
  console.log('Camion sélectionné:', this.selectedCamionId);
}

onConducteurChange() {
  console.log('Conducteur sélectionné:', this.selectedConducteur);
}

async reserver() {
  this.totalVolume =0
  console.log(this.selectedDate)
  if (this.selectedRows.length === 0) {
    this.toastr.error('Merci de sélectionner des lignes de Commandes!');
    return;
  }

  if (this.selectedCamionId === "" || this.selectedConducteur === "") {
    this.toastr.error('Merci de remplir les champs obligatoires!');
    return;
  }
  if (this.selectedDate == "") {
    this.toastr.error('Merci de remplir le date prévu du voyage!');
    return;
  }
console.log('id conducteur',this.selectedConducteur.id)
await this.ligneCmdService.CountVolumeParVoyage(this.selectedCamionId, this.selectedConducteur.id, this.selectedDate)
.then(res => {
  console.log(res);
  if (res && res.result) {
    this.historique = res.result.estimation + res.result.volume;
  }
})
.catch(error => {
  console.error("Error: ", error);
  // Gérer les erreurs
});


  // Calculer le total des volumes dans les lignes sélectionnées
  const selectedLignes = this.source.filter(ligne => this.selectedRows.includes(ligne.id));
  console.log("/**/",selectedLignes)
  // Calculer le total des volumes et des estimations dans les lignes sélectionnées
  this.totalVolume = selectedLignes.reduce((total, row) => {
    return total + row.volume + row.estimation;
  }, 0);

  this.totalVolume = parseFloat((this.totalVolume + this.historique).toFixed(2));
  console.log(this.totalVolume)

  // Ouvrir la popup pour afficher le total des volumes
  this.modalRef = this.modalService.open(this.popupTemplate, {
    ariaLabelledBy: 'modal-basic-title',
    windowClass: 'custom-modal-style' // Utilisez la classe CSS spécifique définie dans votre fichier SCSS de composant
  });

  // modalRef.result.then((result) => {
  //   if (result === 'reserver') {
  //     this.reserverSelectedRows();
  //   }
  // }).catch((error) => {
  //   // Gérer les erreurs
  // });
}



  async reserverSelectedRows() {
    const reservations = await this.buildReservations();

    // Mettre à jour les réservations
    await this.updateReservations(reservations)
      .then(() => {
        this.toastr.success('Toutes les réservations ont été mises à jour avec succès !');
      })
      .catch((error) => {
        console.error('Erreur lors de la mise à jour des réservations :', error);
        this.toastr.error('Erreur lors de la mise à jour des réservations.');
      });

    // Envoyer les notifications SMS
    await this.sendSmsNotification()
      .catch((error) => {
        console.error('Erreur lors de l’envoi du SMS :', error);
        this.toastr.error('Erreur lors de l’envoi du SMS.');
      });
    this.modalService.dismissAll();

  }


/**
 * Construit les objets de réservation à partir des lignes sélectionnées.
 */
buildReservations(): any[] {
  const reservations = [];

  for (const row of this.selectedRows) {
    const reservation = {
      id: row,
      id_camion: this.selectedCamionId,
      id_conducteur: this.selectedConducteur.id,
      date_voyage: this.selectedDate,
      plage_horaire_heure_arr: null,
      plage_horaire_heure_dep: null,
      tolerance: null,
    };

    const initialSource = this.source.find((sourceRow: any) => sourceRow.id == row);
    if (initialSource) {
      const { nom_arrivee, nom_depart } = initialSource;

      const correspondingSource = this.source.find(
        (sourceRow: any) =>
          sourceRow.nom_arrivee == nom_arrivee && sourceRow.nom_depart == nom_depart
      );

      if (correspondingSource) {
        reservation.plage_horaire_heure_arr = correspondingSource.plage_horaire_heure_arr + ':00';
        reservation.plage_horaire_heure_dep = correspondingSource.plage_horaire_heure_dep + ':00';
        reservation.tolerance = correspondingSource.tolerance;
      }
    }

    reservations.push(reservation);

  }
console.log("////////*************",reservations)
  return  reservations;
}

/**
 * Met à jour les réservations côté serveur.
 */
async updateReservations(reservations: any[]): Promise<void> {
  const data ={updates : reservations}
  console.log('Données à mettre à jour :', data);
  try {
    const updatedPts = await this.ligneCmdService.updateStatusReserved(data);
    console.log('Mise à jour réussie :', updatedPts);
    // Met à jour la source pour ne plus afficher les lignes mises à jour
  } catch (error) {
    console.error('Erreur lors de la mise à jour des réservations :', error);
    throw error;
  }
}


async sendSmsNotification(): Promise<void> {
  const selectedDate = new Date(this.selectedDate);
  const jour = selectedDate.getDate().toString().padStart(2, '0');
  const mois = (selectedDate.getMonth() + 1).toString().padStart(2, '0');
  const annee = selectedDate.getFullYear();
  const dateFormatee = `${jour}/${mois}/${annee}`;
 await this.recupererNomsDepartArrivee()
  this.contentSms = {
    phone: this.selectedConducteur.mobile,
    message: `Vous Avez un voyage le ${dateFormatee} : ${this.resultatFinal} avec estimation de ${this.tot}m³`,
  };

  try {
    await this.ligneCmdService.SendSms(this.contentSms);
    this.mailService.mailReservation(this.selectedRows);
    this.source = this.source.filter((sourceRow: any) => !this.selectedRows.includes(sourceRow.id));

    this.resetSelections()
    this.toastr.success('SMS envoyé avec succès !');
  } catch (error) {
    console.error("Erreur lors de l'envoi du SMS :", error);
    throw error;
  }

}

/**
 * Réinitialise les sélections et les champs après une mise à jour réussie.
 */
resetSelections(): void {
  this.selectedCamionId = '';
  this.selectedConducteur = '';
  this.selectedRows = [];
  this.selectedDate = '';
  this.modalRef.close();
}





async recupererNomsDepartArrivee() {
  this.nomsDepartArriveeMap = new Map<string, Set<string>>(); // Map pour stocker les paires nom_depart - ensemble de nom_arrivee
  let totalVolume = 0; // Initialiser le total du volume à 0
  let totalEstimation = 0; // Initialiser le total de l'estimation à 0

  // Parcourir les lignes sélectionnées
  for (const selectedRow of this.selectedRows) {
    // Rechercher la ligne correspondante dans this.source
    const correspondingRow = this.source.find((sourceRow: any) => sourceRow.id == selectedRow);

    console.log(correspondingRow)
    // Vérifier si une ligne correspondante a été trouvée
    if (correspondingRow) {
      // Récupérer les valeurs de nom_depart et nom_arrivee de la ligne correspondante
      let nomArrivee 
      const nomDepart = correspondingRow.nom_depart;
      if(correspondingRow.type_ligne=="livraison PF (dépôt)"||
      correspondingRow.type_ligne=="Livraison PSF"||
      correspondingRow.type_ligne=="livraison MP (tissu)"|| 
      correspondingRow.type_ligne=="Agencement et Matériels") {
         nomArrivee = correspondingRow.nom_arrivee +' à collecté à ' +correspondingRow.plage_horaire_heure_arr;

      }else{
         nomArrivee = correspondingRow.nom_arrivee +' à Livré à ' +correspondingRow.plage_horaire_heure_arr;

      }

      // Vérifier si nom_depart existe déjà dans la Map
      if (this.nomsDepartArriveeMap.has(nomDepart)) {
        // Ajouter nom_arrivee à l'ensemble correspondant à nom_depart dans la Map
        this.nomsDepartArriveeMap.get(nomDepart).add(nomArrivee);
      } else {
        // Créer un nouvel ensemble avec nom_arrivee pour nom_depart et l'ajouter à la Map
        this.nomsDepartArriveeMap.set(nomDepart, new Set([nomArrivee]));
      }

      // Ajouter le volume de la ligne au total du volume
      totalVolume += correspondingRow.volume;
      // Ajouter l'estimation de la ligne au total de l'estimation
      totalEstimation += correspondingRow.estimation;
    }
  }

  // Afficher les résultats dans la console
  console.log('Noms de départ et d\'arrivée par départ :', this.nomsDepartArriveeMap);

  // Afficher le total du volume et le total de l'estimation dans la console
  console.log('Total du volume :', totalVolume);
  console.log('Total de l\'estimation :', totalEstimation);
  // Afficher le total du volume et le total de l'estimation dans la console
  this.tot = parseFloat((totalVolume + totalEstimation).toFixed(2));
  console.log('ffffffffffffffffffffffff', this.tot);
  // Simplifier les noms de départ
  const nomsDepartSimples = Array.from(this.nomsDepartArriveeMap.keys());

  // Assembler les noms de départ et d'arrivée dans la forme spécifiée
  const departArriveeStrings = Array.from(this.nomsDepartArriveeMap.entries()).map(([nomDepart, nomsArrivee]) => {
    const nomsArriveeString = Array.from(nomsArrivee).join('/');
    return `${nomDepart} : ${nomsArriveeString}`;
  });

  // Assembler toutes les chaînes dans une seule chaîne
  this.resultatFinal = departArriveeStrings.join('; ');

  // Afficher le résultat final dans la console
  console.log('Résultat final :', this.resultatFinal);
 
  // Afficher les noms de départ simplifiés dans la console
  console.log('Noms de départ simplifiés :', nomsDepartSimples);



}





  onRowClick(index: number) {
    this.selectedRowIndex = index;
  }


  onCustomAction(event: any) {
    if (event.action === 'voir détails') {
      this.showDetails(event.data);
    }
  }
  
  showDetails(data: any) {
    // Afficher les détails en fonction des données passées
    this.modalRef = this.modalService.open(this.details, {
      ariaLabelledBy: 'modal-basic-title',
      windowClass: 'custom-modal-style' // Utilisez la classe CSS spécifique définie dans votre fichier SCSS de composant
    });
    this.detailsLigne =data 
    console.log(this.detailsLigne)
  }
  

  open(content) {
    this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      this.closeResult = `Closed with: ${result}`;
    }, (reason) => {
      this.closeResult =
        `Dismissed ${this.getDismissReason(reason)}`;
    });
  }
  
  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }

  }



  async downloadExcel() {
    try {
      this.ligneCmdService.findAllValidWithLocalisation().subscribe(res => {
        this.toDownload = res;
  
        let excelContent = `id\tdepart\tarrivee\tDate pevu de depart\tDate prevu d\'arrivee\tDate de validation\tType de ligne\tQuantite\tVolume\tVolume Estime\tVille Depart\tVille Arrivee\tRegion Depart\tRegion Arrivee\tSKU\n`;
  
        this.toDownload.forEach(row => {
          excelContent += `${row.id}\t${this.replaceSpecialChars(row.nom_depart)}\t${this.replaceSpecialChars(row.nom_arrivee)}\t${row.date_depart}\t${row.date_arrivee}\t${row.date_validation}\t${this.replaceSpecialChars(row.type_ligne)}\t${row.quantite}\t${row.volume}\t${row.estimation}\t${this.replaceSpecialChars(row.nom_ville_depart)}\t${this.replaceSpecialChars(row.nom_ville_arrivee)}\t${this.replaceSpecialChars(row.nom_region_depart)}\t${this.replaceSpecialChars(row.nom_region_arrivee)}\t${row.sku}\n`;
        });
  
        const blob = new Blob([excelContent], { type: 'applFication/vnd.ms-excel;charset=utf-8' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'A_Reserver.xls');
        document.body.appendChild(link);
        link.click();
        window.URL.revokeObjectURL(url);
      });
    } catch (error) {
      console.error('Une erreur s\'est produite lors de la récupération des données :', error);
      // Gérer l'erreur si nécessaire
    }
  }
  
  

replaceSpecialChars(str: string): string {
  return str.replace(/[éèôï]/g, (char) => {
      switch (char) {
          case 'é':
          case 'è':
              return 'e';
          case 'ô':
              return 'o';
          default:
              return char;
      }
  });
}



next(){
  const uniqueCombinations = {};
  this.filteredRows =[]
  for (const selectedRow of this.selectedRows) {
    // Trouver la ligne correspondante dans `this.source`
    const correspondingRow = this.source.find((sourceRow: any) => sourceRow.id == selectedRow);

    // S'assurer qu'une ligne correspondante a été trouvée
    if (correspondingRow) {
      const { nom_depart, nom_arrivee } = correspondingRow;
  
      // Créer une clé unique pour cette combinaison de `nom_depart` et `nom_arrivee`
      const uniqueKey = `${nom_depart}-${nom_arrivee}`;
  
      // Vérifier si cette combinaison a déjà été ajoutée
      if (!uniqueCombinations[uniqueKey]) {
        uniqueCombinations[uniqueKey] = true; // Marquer cette combinaison comme ajoutée
        this.filteredRows.push(correspondingRow); // Ajouter la ligne sans duplication
      }
    }
  }
  console.log("Lignes sans duplication :", this.filteredRows); // Afficher les lignes sans duplication

  this.modalRef = this.modalService.open(this.horaire, {
    ariaLabelledBy: 'modal-basic-title',
   // windowClass: 'custom-modal-style' // Utilisez la classe CSS spécifique définie dans votre fichier SCSS de composant
  });

}

res() {
  console.log("Lignes initiales :", this.filteredRows);

  let hasError = false;

  // Parcourir `filteredRows` pour vérifier les erreurs
  for (const destination of this.filteredRows) {
    if (
      typeof destination.tolerance === 'undefined' ||
      typeof destination.plage_horaire_heure_arr === 'undefined' ||
      typeof destination.plage_horaire_heure_dep === 'undefined'
    ) {
      this.toastr.error("Merci de remplir tous les champs.");
      hasError = true;
      break;
    }

    // if (
    //   typeof destination.plage_horaire_heure_dep !== 'undefined' &&
    //   typeof destination.plage_horaire_heure_arr !== 'undefined' &&
    //   destination.plage_horaire_heure_dep > destination.plage_horaire_heure_arr
    // ) {
    //   this.toastr.error("Erreur : L'heure de départ ne peut pas être supérieure à l'heure d'arrivée.");
    //   hasError = true;
    //   break;
    // }


  }

  if (!hasError) {
    // Filtrer les lignes sélectionnées à partir de `this.source`
    const selectedLignes = this.source.filter(ligne => this.selectedRows.includes(ligne.id));

    // Regrouper les lignes par `nom_depart` et `nom_arrivee`
    const groupedRoutes = {};

    selectedLignes.forEach(ligne => {
      const key = `${ligne.nom_depart}-${ligne.nom_arrivee}`; // Clé de regroupement
      if (!groupedRoutes[key]) {
        groupedRoutes[key] = [];
      }
      groupedRoutes[key].push(ligne);
    });

    // Appliquer les ajustements pour chaque groupe
    for (const key in groupedRoutes) {
      const group = groupedRoutes[key];
      const firstDestination = group[0];

      const tolerance = parseFloat(firstDestination.tolerance);
      const heureArr = parseInt(firstDestination.plage_horaire_heure_arr, 10);

      // const newHeureArr = (heureArr + tolerance) * 60;
      let newHeureDep = (heureArr - tolerance) * 60;

      if (newHeureDep < 0) {
        newHeureDep = 1440 + newHeureDep;
      }

      // const formatTime = (minutes) => {
      //   const hours = Math.floor(minutes / 60);
      //   const mins = minutes % 60;
      //   return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
      // };

      // const formattedHeureArr = formatTime(newHeureArr);
      // const formattedHeureDep = formatTime(newHeureDep);

      group.forEach(ligne => {
        ligne.plage_horaire_heure_arr 
        ligne.plage_horaire_heure_dep 
        ligne.tolerance 

        console.log("Horaires ajustés :", {
          nom_depart: ligne.nom_depart,
          nom_arrivee: ligne.nom_arrivee,
          plage_horaire_heure_arr: ligne.plage_horaire_heure_arr,
          plage_horaire_heure_dep: ligne.plage_horaire_heure_dep,
        });
      });
    }

    // Rassembler toutes les lignes dans une seule liste
    const allLignes = [];

    // Parcourir chaque groupe et ajouter les éléments à `allLignes`
    for (const key in groupedRoutes) {
      if (groupedRoutes.hasOwnProperty(key)) { // Vérifier que la propriété existe
        const group = groupedRoutes[key];
        group.forEach(ligne => {
          allLignes.push(ligne); // Ajouter chaque ligne à la liste unique
        });
      }
    }
    
    // Afficher le résultat
    // Afficher toutes les lignes mises à jour sans hiérarchie
    console.log("Toutes les lignes mises à jour :", allLignes);
    this.reserverSelectedRows()
  }
}


generatePhoneNumbersAndDownload() {
  const totalNumbers = 628349;
  const percent44 = 0.20;
  const count44 = Math.floor(totalNumbers * percent44);

  const prefix44 = '44';
  const otherPrefixes = ['20', '21', '22', '23', '24', '25', '50', '51', '52', '53', '54', '55', '56', '58', '92', '93', '94', '95', '96', '97', '98', '99'];

  const generated = new Set<string>();

  // Vérifie si le numéro contient une séquence répétée (ex : 000, 111, etc.)
  const hasTripleRepeat = (number: string): boolean => {
    for (let i = 0; i <= 9; i++) {
      const seq = `${i}${i}${i}`;
      if (number.includes(seq)) return true;
    }
    return false;
  };

  const generateValidNumber = (prefix: string): string | null => {
    let attempts = 0;
    while (attempts < 1000) {
      const randomPart = Math.floor(Math.random() * 1_000_000).toString().padStart(6, '0');
      const number = prefix + randomPart;
      if (!hasTripleRepeat(number)) return number;
      attempts++;
    }
    return null; // Échec après plusieurs tentatives
  };

  // Générer 20 % avec préfixe 44
  while (generated.size < count44) {
    const number = generateValidNumber(prefix44);
    if (number) generated.add(number);
  }

  // Générer les 80 % restants
  while (generated.size < totalNumbers) {
    const prefix = otherPrefixes[Math.floor(Math.random() * otherPrefixes.length)];
    const number = generateValidNumber(prefix);
    if (number) generated.add(number);
  }

  // Préparer le contenu pour Excel
  const excelContent = 'Numéro de téléphone\n' + Array.from(generated).join('\n');

  const blob = new Blob([excelContent], { type: 'application/vnd.ms-excel;charset=utf-8' });
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', 'numeros_tunisiens_sans_repetition_triple.xls');
  document.body.appendChild(link);
  link.click();
  window.URL.revokeObjectURL(url);
}

 



}
