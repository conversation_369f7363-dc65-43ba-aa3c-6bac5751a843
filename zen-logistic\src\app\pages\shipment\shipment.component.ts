import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ShipmentService } from 'app/services/shipment.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-shipment',
  templateUrl: './shipment.component.html',
  styleUrls: ['./shipment.component.scss']
})
export class ShipmentComponent implements OnInit {
    @ViewChild('history', { static: true }) history: TemplateRef<any>;
  
  barcode= ""
  status =""
  startDate =""
  endDate =""
  packageList:any=[]
  statusList:any=[]
  constructor(private shipmentService : ShipmentService,
    private toastr: ToastrService,
    private modalService: NgbModal,
    private route: ActivatedRoute  ) { }

  ngOnInit() {
    this.syncroWithCegid()
    console.log("here")
    this.getParams();

  }

  async search() {
    this.syncroWithCegid()
    if (!this.validateFilters()) return;
  
    const args = this.buildSearchArgs();
    await this.performSearch(args);
  }
  
  private validateFilters(): boolean {
    let errorMessage = "";
  
    switch (true) {
      case this.barcode && this.barcode.length !== 13:
        errorMessage = "Merci de vérifier le Code à barre";
        break;
  
      case (this.startDate && !this.endDate) || (!this.startDate && this.endDate):
        errorMessage = "Veuillez renseigner à la fois la date de début et la date de fin";
        break;
  
      case this.startDate && this.endDate && this.startDate > this.endDate:
        errorMessage = "La date de début ne peut pas être postérieure à la date de fin";
        break;
  
      case this.status && (!this.startDate || !this.endDate):
        errorMessage = "Le statut nécessite des dates de début et de fin.";
        break;
  
      case !this.barcode && !this.status && (!this.startDate || !this.endDate):
        errorMessage = "Veuillez renseigner au moins un filtre (Code-barres, Statut ou Dates)";
        break;
    }
  
    if (errorMessage) {
      this.toastr.error(errorMessage);
      return false;
    }
  
    return true;
  }
  
  
  private buildSearchArgs() {
    return {
      barcode: this.barcode,
      startDate: this.startDate,
      endDate: this.endDate,
      status: this.status
    };
  }
  
  private async performSearch(args: any) {
    try {
      console.log("args",args)
      this.shipmentService.findPackages(args).subscribe(
        res => {
          this.packageList = res;
          if (this.packageList.length === 0) {
            this.toastr.error("Aucun colis trouvé");
          }
        },
        error => {
          this.handleSearchError(error);
        }
      );
    } catch (error) {
      this.handleSearchError(error);
    }
  }
  
  private handleSearchError(error: any) {
    const errorMessage = error.message || "Une erreur est survenue lors de la recherche.";
    this.toastr.error(errorMessage);
  }
  
  async viewHistory(id){

   await this.shipmentService.getStatusByPackage(id).subscribe(res=>{
    this.statusList=res
    this.modalService.open(this.history);
    })

  }

  async downloadExcel() {
    try {
        let excelContent = 'Code-barres\tDepart\tCode Depart\tArrivee\tCode Arrivee\tDerniere reception\tRecepteur\tStatut\tCree le\n';

        this.packageList.forEach(pkg => {
            excelContent += `${pkg.barcode || ''}\t${this.replaceSpecialChars(pkg.departure || '')}\t${pkg.code_departure || ''}\t${this.replaceSpecialChars(pkg.arrival || '')}\t${pkg.code_arrival || ''}\t${pkg.last_reception || ''}\t${this.replaceSpecialChars(pkg.name_receptor || '')}\t${this.replaceSpecialChars(pkg.status || '')}\t${pkg.created_at || ''}\n`;
        });

        const blob = new Blob([excelContent], { type: 'application/vnd.ms-excel;charset=utf-8' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'Packages.xls');
        document.body.appendChild(link);
        link.click();
        window.URL.revokeObjectURL(url);
    } catch (error) {
        console.error('Erreur lors du téléchargement du fichier Excel :', error);
    }
}

replaceSpecialChars(str: string): string {
    return str.replace(/[éèô/]/g, (char) => {
        switch (char) {
            case 'é':
            case 'è':
                return 'e';
            case 'ô':
                return 'o';
            case '/':
                return '-';
            default:
                return char;
        }
    });
}

closeModal(){
  this.modalService.dismissAll()
}

validateBarcode() {
  // Supprimer tout caractère non numérique (lettres, symboles, etc.)
  this.barcode = this.barcode.replace(/\D/g, ''); // \D correspond à tout ce qui n'est pas un chiffre
  
  // Limiter à 13 caractères
  if (this.barcode.length > 13) {
    this.barcode = this.barcode.substring(0, 13);
  }
}

async syncroWithCegid(){
  await this.shipmentService.transferPackages().subscribe()
  }

getParams(){
  this.route.queryParams.subscribe(params => {
    const status = params['status'];
    const dateMin = params['date_min'];
    const dateMax = params['date_max'];
     this.startDate =dateMin,
     this.endDate =dateMax,
    this.status = status
  });
  if( this.status &&  this.endDate &&  this.startDate ){
    this.search()

  }
}



}
