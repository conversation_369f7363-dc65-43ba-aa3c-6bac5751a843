// AdminHome Component Styles - Adapted from zen-logistic with CoreUI compatibility

.cursor-pointer {
  cursor: pointer;
  transition: transform 0.2s ease-in-out;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

// Dashboard Statistics Cards
.card {
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
}

// Productivity Circles
.productivity-circle {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 10px solid;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;

  &.border-success {
    border-color: var(--cui-success);
  }

  &.border-warning {
    border-color: var(--cui-warning);
  }

  &.border-primary {
    border-color: var(--cui-primary);
  }
}

.productivity-counter {
  font-size: 24px;
  font-weight: bold;
  color: var(--cui-body-color);
  text-align: center;
}

// Delay Processing Section
.delay-flow-container {
  background: var(--cui-card-bg);
  border: 1px solid var(--cui-border-color) !important;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: var(--cui-primary) !important;
  }
}

.delay-stage {
  padding: 1rem;
  border-radius: 6px;
  background: rgba(var(--cui-primary-rgb), 0.05);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(var(--cui-primary-rgb), 0.1);
    transform: translateY(-2px);
  }
}

.delay-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 0.5rem;

  i {
    font-size: 2rem;
    margin-bottom: 8px;
    transition: all 0.3s ease;
  }
}

.stage-title {
  margin: 0.5rem 0;
  font-size: 0.85rem;
  color: var(--cui-heading-color);
  font-weight: 600;
}

.delay-time {
  font-size: 1rem;
  font-weight: bold;
  color: var(--cui-body-color);
  padding: 0.25rem 0.5rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  border: 1px solid var(--cui-border-color);
}

.delay-summary {
  background: var(--cui-gray-100) !important;
  border: 1px solid var(--cui-border-color);

  h6 {
    color: var(--cui-heading-color);
    margin-bottom: 1rem;
  }

  .fw-bold {
    font-weight: 600;
    font-size: 0.9rem;
  }

  small {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

// Dashboard Container Styles
.dashboard-container {
  display: flex;
  flex-wrap: wrap;
  margin: 20px 0;
  justify-content: flex-start;
}

.dashboard-item {
  border: 1px solid var(--cui-border-color);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  background: var(--cui-card-bg);

  h3 {
    margin-top: 0;
    font-size: 18px;
    color: var(--cui-heading-color);
  }

  p {
    margin-bottom: 10px;
    font-size: 16px;
    color: var(--cui-body-color);
  }
}

// Center alignment utility
.center-div {
  display: flex;
  flex-direction: column;
  align-items: center;
}

// Loading spinner
.spinner-border {
  width: 3rem;
  height: 3rem;
}

// Responsive Design
@media (max-width: 768px) {
  .productivity-circle {
    width: 100px;
    height: 100px;
    border-width: 8px;
  }

  .productivity-counter {
    font-size: 20px;
  }

  .delay-stage {
    padding: 0.75rem;
    margin-bottom: 1rem;
  }

  .delay-icon {
    i {
      font-size: 1.5rem;
    }
  }

  .stage-title {
    font-size: 0.8rem;
  }

  .delay-time {
    font-size: 0.9rem;
  }

  .delay-summary {
    margin-top: 1rem;

    .fw-bold {
      font-size: 0.8rem;
    }
  }

  .dashboard-item {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 576px) {
  .productivity-circle {
    width: 80px;
    height: 80px;
    border-width: 6px;
  }

  .productivity-counter {
    font-size: 16px;
  }

  .card-body {
    padding: 1rem;
  }
}

// Color variants for statistics cards
.border-dark .card-body {
  border-left: 4px solid var(--cui-dark);
}

.border-warning .card-body {
  border-left: 4px solid var(--cui-warning);
}

.border-primary .card-body {
  border-left: 4px solid var(--cui-primary);
}

.border-success .card-body {
  border-left: 4px solid var(--cui-success);
}

// Text color utilities
.text-dark {
  color: var(--cui-dark) !important;
}

.text-warning {
  color: var(--cui-warning) !important;
}

.text-primary {
  color: var(--cui-primary) !important;
}

.text-success {
  color: var(--cui-success) !important;
}

.text-danger {
  color: var(--cui-danger) !important;
}

// Form controls styling
.form-control, .form-select {
  border-radius: 6px;
  border: 1px solid var(--cui-border-color);

  &:focus {
    border-color: var(--cui-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--cui-primary-rgb), 0.25);
  }
}

// Button styling
.btn {
  border-radius: 6px;
  font-weight: 500;

  &.btn-danger {
    background-color: var(--cui-danger);
    border-color: var(--cui-danger);

    &:hover {
      background-color: var(--cui-danger-hover);
      border-color: var(--cui-danger-hover);
    }
  }
}