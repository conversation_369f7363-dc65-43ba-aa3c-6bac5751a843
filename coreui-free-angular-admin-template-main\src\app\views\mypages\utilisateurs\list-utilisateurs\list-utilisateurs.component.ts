import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { ConfirmationDialogService } from 'src/services/confirmation-dialog.service';
import { UtilisateurService } from 'src/services/utilisateur.service';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';
import { ActionButton, TableColumn } from 'src/shared/models/table.models';
import { FormModalComponent } from '../../../../../shared/components/form-modal/form-modal.component';
import { TableConfig } from '../../../../../shared/models/table.models';
import { SmartContainerComponent } from '../../../../../shared/components/smart-container/smart-container.component';
import { FormButton, FormConfig } from '../../../../../shared/models/form.models';
import { RoleService } from 'src/services/role.service';
import { SmartButtonComponent } from "src/shared/components/smart-button/smart-button.component";
import { Md5 } from 'md5-typescript';
import { MailService, SessionStorageService } from '../../../../../services';

@Component({
  selector: 'app-list-utilisateurs',
  standalone: true,
  imports: [SmartTableComponent, FormModalComponent, SmartButtonComponent, SmartContainerComponent, SmartButtonComponent],
  templateUrl: './list-utilisateurs.component.html',
  styleUrl: './list-utilisateurs.component.scss'
})
export class ListUtilisateursComponent implements OnInit{


  listUtilisateurs: any[] = [];
  roles: any[] = []
  loading: boolean = true;
  showModal: boolean = false;
  existingData: any;

  constructor(
    private UtilisateurService: UtilisateurService,
    private roleService: RoleService,
    private toastr: ToastrService,
    private mailService: MailService,
    private sessionStorageService: SessionStorageService,
    
    private ConfirmationDialogService: ConfirmationDialogService) {
    
  }
  ngOnInit(): void {
    this.loadUtilisateurs();
    this.loadRoles();
  }
  private loadUtilisateurs() {
    this.loading = true;
    this.UtilisateurService.findAllExpediteur().subscribe({
      next: (data:any) => {
        this.listUtilisateurs = data;
        console.log('Utilisateurs loaded successfully:', this.listUtilisateurs);
      },
      error: (error:any) => {
        console.error('Error loading utilisateurs:', error);
      },
      complete: () => {
        this.loading = false;
      }
    });
  }

  private loadRoles() {
    this.roleService.getAllRoles().subscribe({
      next: (data: any) => {
        this.roles = data;
        console.log(this.roles)
        this.updateFormConfigWithRoles();
      },
      error: (error:any) => {
        console.error('Error loading roles:', error);
      }
    }
    )
  }

  TableColumns: TableColumn[] = [
    { name: 'nom', displayName: 'Nom', sortable: true, filterable: true },
    { name: 'prenom', displayName: 'Prénom', sortable: true, filterable: true },
    { name: 'email', displayName: 'Email', sortable: true, filterable: true },
    { name: 'type_utilisateur', displayName: 'Type Utilisateur', sortable: true, filterable: true },
    {
      name: 'statut',
      displayName: 'Statut',
      sortable: true,
      dataType: 'status',
      filterable: true,
      statusConfig: [
        { value: 'activé', displayText: 'Activé', badgeColor: 'success', icon: 'cilCheckCircle' },
        { value: 'invalide', displayText: 'Invalide', badgeColor: 'danger', icon: 'cilXCircle' }
      ]
    },
  ];

  tableConfig: TableConfig = {
    emptyMessage: 'aucun utilisateur',
  }



  actionButtons: ActionButton[] = [
    {
      icon: 'cil-pencil',
      color: 'info',
      tooltip: 'modifier l\'utilisateur',
      callback: (row: any) => this.openEditModal(row)
    },
    {
      icon: 'cilCheckCircle',
      color: 'success',
      tooltip: 'Activer l\'utilisateur',
      condition: (row: any) => row.statut === 'invalide',
      callback: (row: any) => this.toggleUserStatus(row)
    },
    {
      icon: 'cilXCircle',
      color: 'danger',
      tooltip: 'Désactiver l\'utilisateur',
      condition: (row: any) => row.statut === 'activé',
      callback: (row: any) => this.toggleUserStatus(row)
    }
  ];
  formConfig: FormConfig = {
    title: '',
    fieldGroups: [
      {
        fields: [
          {
            name: 'nom',
            label: 'Nom',
            type: 'text',
            required: true,
            placeholder: 'Entrer votre nom'
          },
          {
            name: 'prenom',
            label: 'Prenom',
            type: 'text',
            required: true,
            placeholder: 'Entrer votre prenom'
          },
        ]
      },
      {
        fields: [
          {
            type: 'email',
            label: 'email',
            name: 'email',
            placeholder: 'Email',
            required: true,
            validation: {
              pattern: '^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$'
            }
          },
          {
            type: 'password',
            label: 'Mot de passe',
            name: 'mot_de_passe',
            placeholder: 'Mot de passe',
            required: true,
          }
        ]
      }
    ],
    fields: [
      {
        type: 'select',
        label: 'Type Utilisateur',
        name: 'type_utilisateur',
        required: true,
        placeholder: 'Choisir Type Utilisateur',
        options: {
          objectArray: this.roles,
          labelAttribute: 'name',
          valueAttribute: 'value'
        }
      }
    ],
    buttons: [
      {
        label: 'Annuler',
        color: 'secondary',
        onClick: () => this.showModal = false
        
      },
      {
        label: 'Ajouter',
        color: 'primary',
        onClick: (formData: any) => this.addUser(formData)
      }

    ]
  }
    



  openCreateModal() {
    console.log('hello')
    this.formConfig.title = 'Créaton d\'un utilisateur'
    if (this.formConfig.buttons) {
        this.formConfig.buttons[0].label = 'Ajouter'
        this.formConfig.buttons[0].onClick = (formData: any) => this.addUser(formData)
      }
    this.formConfig = this.formConfig
    this.existingData = false,
    this.showModal = true
  }
  openEditModal(data: any) {
    try {
      console.log(data)
      this.formConfig.title = 'Modification d\'un utilisateur'
      if (this.formConfig.buttons) {
        this.formConfig.buttons[1].label = 'Modifier'
        this.formConfig.buttons[1].onClick = (formData: any) => this.editUser(formData)
      }
      this.formConfig = this.formConfig
      this.existingData = data
      console.log(this.existingData)
      this.showModal = true
    } catch (error) {
      console.error(error)
    }
  }
  toggleUserStatus(row: any): void {

    if (row.statut === 'activé') {
      this.ConfirmationDialogService.confirm(
        {
          title: 'Désactiver l\'utilisateur',
          message: `Êtes-vous sûr de vouloir désactiver l'utilisateur ${row.nom} ?`,
          btnOkText: 'Oui, désactiver',
          btnCancelText: 'Annuler',
          color: 'danger'
        }).then((confirmed) => {
          if (confirmed) {
            row = { ...row, statut: 'invalide' }
            console.log(row)
            console.log(row)
            this.UtilisateurService.updateUserStatus(row).subscribe({
              next: () => {
                this.loadUtilisateurs();
                this.toastr.success(`Utilisateur ${row.nom} désactivé avec succès.`);
              },
              error: (error) => {
                this.toastr.error(`Erreur lors de la désactivation de l'utilisateur ${row.nom}: ${error.message}`);
              }
            });
          }
        });
    }
    else if (row.statut === 'invalide') {
      this.ConfirmationDialogService.confirm(
        {
          title: 'Activer l\'utilisateur',
          message: `Êtes-vous sûr de vouloir activer l'utilisateur ${row.nom} ?`,
          btnOkText: 'Oui, activer',
          btnCancelText: 'Annuler',
          dialogSize: 'md',
          color: 'primary'
        }).then((confirmed) => {
          if (confirmed) {
            row = { ...row, statut: 'activé' }
            console.log(row)
            this.UtilisateurService.updateUserStatus(row).subscribe({
              next: () => {
                this.loadUtilisateurs();
                this.toastr.success(`Utilisateur ${row.nom} activé avec succès.`);
              },
              error: (error) => {
                this.toastr.error(`Erreur lors de l'activation de l'utilisateur ${row.nom}: ${error.message}`);
              }
            });
          }
        });
    }
    else {
      this.toastr.error(`Statut de l'utilisateur ${row.nom} inconnu.`);
    }
  }

  

  onCancel() {
    this.showModal = false
  }

  addUser(data: any) {
    const userToAdd = {
      nom: data.nom,
      prenom: data.prenom,
      email: data.email,
      type_utilisateur: data.type_utilisateur,
      nom_utilisateur: data.nom + " " + data.prenom,
      mot_de_passe: this.generateMdp(data.mot_de_passe),
      statut: 'activé',
      cle_activation: "Created By Super Admin " + this.sessionStorageService.getSessionValue('iduser')
    };
    console.log('prepare to add user', userToAdd)
    this.UtilisateurService.findUserByMail(data.email).subscribe({
      next: (user: any) => {
        if (user) {
          this.toastr.error(`Un utilisateur avec l'email ${data.email} existe deja.`);
        } else {
          this.UtilisateurService.addUsers(userToAdd).subscribe({
            next: () => {
              this.loadUtilisateurs();
              this.showModal = false;
              this.toastr.success(`Utilisateur ${data.nom} ajouté avec succès.`);
              this.SendEmailNewUser(data.nom, data.email, data.password);
            },
            error: (error) => {
              this.toastr.error(`Erreur lors de l'ajout de l'utilisateur ${data.nom}: ${error.message}`);
            }
          });
        }
      }
    })
    
  }
  editUser(data: any) {
    const selectedUsertoUpdate = this.listUtilisateurs.find(user => user.email === data.email);
    
    if (!selectedUsertoUpdate) {
      this.toastr.error('Aucun utilisateur sélectionné')
      return;
    }

    const userToUpdate = {
      ...selectedUsertoUpdate,
      id: selectedUsertoUpdate.id,
      nom: data.nom,
      prenom: data.prenom,
      email: data.email,
      type_utilisateur: data.type_utilisateur,
      nom_utilisateur: data.nom + " " + data.prenom,

      ...(data.password ? { mot_de_passe: this.generateMdp(data.password) } : {})
    };
    console.log(userToUpdate)
    this.UtilisateurService.updateUserStatus(userToUpdate).subscribe({
      next: () => {
        this.loadUtilisateurs();
        this.showModal = false;
        this.toastr.success(`Utilisateur ${data.nom} modifié avec succès.`);
        this.SendEmailModifyUser(data.nom, data.email, data.mot_de_passe);
      },
      error: (error) => {
        this.toastr.error(`Erreur lors de la modification de l'utilisateur ${data.nom}: ${error.message}`);
      }
    })
  }
  generateMdp(password: any) {
    return Md5.init(password);
  }

  

private updateFormConfigWithRoles() {
  // Find the type_utilisateur field in the form config and update its options
  const updatedFields = this.formConfig.fields?.map(field => {
    if (field.name === 'type_utilisateur') {
      return {
        ...field,
        options: {
          objectArray: this.roles,
          labelAttribute: 'name',
          valueAttribute: 'value'
        }
      };
    }
    return field;
  });

  // Create a new form config object to trigger change detection
  this.formConfig = {
    ...this.formConfig,
    fields: updatedFields
  };
}

async SendEmailNewUser(recipientName:any, email:any, password:any) {
    const emailContent = `
        <p><b>Bonjour ${recipientName},</b></p>
        
        <p>J'espère que vous allez bien. Je vous écris pour vous fournir les informations nécessaires pour accéder à notre application Zen Logistique, afin que vous puissiez commencer dès maintenant.</p>
        
        <p>Voici le lien de l'application : <a href="http://zenlogistic.tn">Zen Logistique</a></p>
        
        <p>Et voici vos identifiants de connexion :</p>
        
        <p><strong>Nom d'utilisateur :</strong> ${email}</p>
        <p><strong>Mot de passe :</strong> ${password}</p>
        
        <p>N'hésitez pas à nous contacter si vous rencontrez le moindre problème ou si vous avez besoin d'assistance pour démarrer. Je suis à votre disposition pour toute question ou clarification supplémentaire.</p>
        
        <p>Je vous souhaite une excellente utilisation de notre application Zen Logistique.</p>
        
        <p>Cordialement,</p>
    `;

    const data = {
      to: email,
      subject: "Accès Zen Logistique",
      htmlContent: emailContent,
    };

    await this.mailService.sendMailsFromFrontend(data).then(res => {
      //console.log(res)
      this.toastr.success('Email envoyé avec succée')
    }
    );
  }

  async SendEmailModifyUser(recipientName:any, email:any, newPassword:any) {
    const passwordMessage = newPassword 
      ? `<p><strong>Nouveau mot de passe :</strong> ${newPassword}</p>` 
      : `<p><strong>Mot de passe :</strong> Vos identifiants de connexion restent inchangés.</p>`;
  
    const emailContent = `
      <p><b>Bonjour ${recipientName},</b></p>
      
      <p>J'espère que vous allez bien. Je vous écris pour vous informer que vos informations d'accès à notre application Zen Logistique ont été modifiées.</p>
      
      <p>Voici le lien de l'application : <a href="http://zenlogistic.tn">Zen Logistique</a></p>
      
      <p>Et voici vos identifiants de connexion :</p>
      
      <p><strong>Nom d'utilisateur :</strong> ${email}</p>
      ${passwordMessage}
      
      <p>N'hésitez pas à nous contacter si vous rencontrez le moindre problème ou si vous avez besoin d'assistance pour vous connecter avec vos identifiants. Nous sommes à votre disposition pour toute question ou clarification supplémentaire.</p>
      
      <p>Nous vous souhaitons une excellente utilisation de notre application Zen Logistique.</p>
      
      <p>Cordialement,</p>
    `;
  
    const data = {
      to: email,
      subject: "Modification de vos accès Zen Logistique",
      htmlContent: emailContent,
    };
  
    await this.mailService.sendMailsFromFrontend(data).then(res => {
      //console.log(res);
      this.toastr.success('Email envoyé avec succès');
    }).catch(err => {
      console.error(err);
      this.toastr.error("Échec de l'envoi de l'email");
    });
  }


}
