import { HttpClient, HttpEvent, HttpHeaders, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
const httpOptions = {
    headers: new HttpHeaders({ 'content-type': 'application/json', Authorization: 'basic ' + btoa('med:123456') })
};

@Injectable({
    providedIn: 'root'
})

export class ImpotService {
    apiURL = environment.apiURL
    constructor(private http: HttpClient) { }


    getImpot(): Observable<any> {
        return this.http.get<any>(this.apiURL + `impot`, httpOptions);
    }

    updateById(id: number, data: any): Observable<any> {
        const url = `${this.apiURL}impot/${id}`;
        return this.http.put(url, data, httpOptions)
            .pipe(
                catchError(this.handleError)
            );
    }

    private handleError(error: any): Observable<never> {
        console.error('An error occurred:', error);
        return throwError(() => new Error(error.message || 'Server error'));
    }



}