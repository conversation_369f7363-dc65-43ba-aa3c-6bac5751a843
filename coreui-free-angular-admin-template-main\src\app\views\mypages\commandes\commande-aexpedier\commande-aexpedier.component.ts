import { Component } from '@angular/core';
import { DestinationService, NotificationService } from 'src/services';
import { ConducteurService } from 'src/services/conducteur.service';
import { TableColumn, TableConfig } from 'src/shared/models/table.models';
import { FormConfig } from 'src/shared/models/form.models';
import { CamionService } from 'src/services/camion.service';
import { LigneCmdService } from 'src/services/ligne-cmd.service';
import { OrderLineService } from 'src/services';
import { PriceService } from 'src/services/price.service';
import { CircuitService } from 'src/services/circuit.service';
import { ToastrService } from 'ngx-toastr';
import { CommonModule } from '@angular/common';
import { SmartTableComponent } from '../../../../../shared/components/smart-table/smart-table.component';
import { SmartContainerComponent } from '../../../../../shared/components/smart-container/smart-container.component';
import { DynamicFormComponent } from '../../../../../shared/components/dynamic-form/dynamic-form.component';
import { SmartButtonComponent } from '../../../../../shared/components/smart-button/smart-button.component';
import { ActionButton } from '../../../../../shared/models/table.models';
import { ConfirmationDialogService } from '../../../../../services/confirmation-dialog.service';
import { MailService } from '../../../../../services';
import { environment } from 'src/environments/environment';
import { FormsModule } from '@angular/forms';
import { DynamicModalComponent } from 'src/shared/components/dynamic-modal/dynamic-modal.component';

@Component({
  selector: 'app-commande-aexpedier',
  standalone: true,
  imports: [
    CommonModule,
    SmartTableComponent,
    SmartContainerComponent,
    DynamicFormComponent,
    SmartButtonComponent,
    DynamicModalComponent,
    FormsModule
  ],
  templateUrl: './commande-aexpedier.component.html',
  styleUrl: './commande-aexpedier.component.scss'
})
export class CommandeAExpedierComponent {

  showHoraireModal: boolean = false;

  TotalVol: any = 0
  loadingPrimeTable: boolean = false
  showDetailsTable: boolean = false
  loadingDetailsTable: boolean = false
  initialData: any = {};
  selectedSource: any = {};
  selectedDetails: any[] = [];
  conducteurList: any[] = [];
  circuitList: any[] = [];
  camionList: any[] = [];
  source: any[] = [];
  uniqueNomArriveeList: any = []
  disableButton: boolean = true;
  hours: any[] = [];
  newData : any = {};
  newReservation : any = {};
  resultatFinal: any = "";
  tot: any = 0;

  sourceDetails: any = [];

  tableColumn: TableColumn[] = [
    { name: 'id', displayName: 'ID', sortable: true, filterable: true },
    { name: 'nom', displayName: 'Nom Transporteur', sortable: true, filterable: true },
    { name: 'prenom', displayName: 'Prenom Transporteur', sortable: true, filterable: true },
    { name: 'immatriculation', displayName: 'Immatriculation', sortable: true, filterable: true },
    { name: 'date_voyage', displayName: 'Date Voyage', sortable: true, filterable: true },
  ];

  tableConfig: TableConfig = {
    selectable: true,
    multiSelect: false
  }
  formConfig: FormConfig = {
    fieldGroups: [
      {
        fields: [
          {
            name: 'selectedCamionId',

            type: 'select',
            required: true,
            options: {
              objectArray:
                this.camionList,
              valueAttribute: 'id',
              labelAttribute: 'immatriculation'
            }
          },
          {
            name: 'selectedConducteurId',
            type: 'select',
            required: true,
            options: {
              objectArray:
                this.conducteurList,
              valueAttribute: 'id',
              labelAttribute: 'fullName'
            }
          },
          {
            name: 'selectedDate',
            type: 'date',
            required: true
          }
        ]
      }
    ],
    buttons: [
      {
        label: 'modifier',
        color: 'primary',
        onClick: (formData:any) => this.openHoraire(formData)
      }
    ]
  }

  tableDetailsColumn: TableColumn[] = [
    { name: 'id', displayName: 'ID', sortable: true, filterable: true },
    { name: 'nom_depart', displayName: 'Nom Depart', sortable: true, filterable: true },
    { name: 'nom_arrivee', displayName: 'Nom Arrivee', sortable: true, filterable: true },
    { name: 'type_ligne', displayName: 'Type Ligne', sortable: true, filterable: true },
    { name: 'kilometrage', displayName: 'Kilometrage', sortable: true, filterable: true },
    { name: 'nom_client', displayName: 'Nom Client', sortable: true, filterable: true },
    { name: 'volume', displayName: 'Volume', sortable: true, filterable: true },
    { name: 'status', displayName: 'Status', sortable: true, filterable: true },
  ];

  tableDetailsConfig: TableConfig = {
    pageSize:5,
    selectable: true,
    multiSelect: true,
    commentable: true
  }
  tableDetailsAction: ActionButton[] = [
    {
      icon: 'cilTruck',
      color: 'warning',
      callback: (row:any) => this.updateStatusNoPickup(row.id)
    },
    {
      icon: 'cil-comment-bubble',
      color: 'info',
      isCommentAction: true
    }
  ]


  constructor(
    private conducteurService: ConducteurService,
    private destinationService: DestinationService,
    private camionService: CamionService,
    private ligneCmdService: LigneCmdService,
    private orderLigneService: OrderLineService,
    private mailService: MailService,
    private priceService: PriceService,
    private circuitService: CircuitService,
    private notificationService: NotificationService,
    private confirmationDialogService: ConfirmationDialogService,
    private toastr: ToastrService
  ) { }

  ngOnInit(): void {
    this.loadSource();
    this.loadConducteurs();
    this.loadCamion();
    for (let i = 0; i < 24; i++) {
      const hourString = `${i.toString().padStart(2, '0')}:00:00`; // Formatage correct
      this.hours.push(hourString);
    }
  }

  
  onSelectChange(event: any) {
    this.selectedSource = event[0];
    console.log(this.selectedSource)
    this.initialData = {
      selectedCamionId: this.selectedSource.id_camion,
      selectedConducteurId: this.selectedSource.id_conducteur,
      selectedDate: this.selectedSource.date_voyage,
    };

    this.loadDetails();

  }
  onSelectDetailsChange(event: any) {
    this.selectedDetails = event;
    this.UpdateButtonDisableState();


  }
  UpdateButtonDisableState() {
    if (this.selectedDetails.length > 0) {
      this.disableButton = false;
    } else {
      this.disableButton = true;
    }
  }
  updateStatusNoPickup(id:any) {
    try {
      // Affichage de la fenêtre de validation avant la mise à jour
      this.confirmationDialogService.confirmEdit("Êtes-vous sûr de vouloir mettre 'Pas de prélevement' de cette commandes?")
      .then((confirmation) =>{
         // Vérification de la confirmation de l'utilisateur
       if(confirmation) {
        // Mise à jour du statut
        this.orderLigneService.updateStatusNoPickup(id);

        // Affichage de la fenêtre de validation si la mise à jour est réussie
        this.toastr.success("Mise à jour du statut réussie !")

        const index = this.sourceDetails.findIndex((item:any) => item.id === id);
        if (index !== -1) {
          this.sourceDetails.splice(index, 1);
        }
        // Suppression de la ligne de la liste reservationList
        // (code de suppression de la ligne ici)
      }
    }
      )
    } catch (error) {
      // Gestion des erreurs
      this.toastr.error("Une erreur s'est produite lors de la mise à jour du statut")

    }
  }
  async res() {
    console.log("Lignes sélectionnées pour mise à jour:", this.selectedDetails);

    const reservation = {
      id_camion: this.newData.selectedCamionId,
      id_conducteur: this.newData.selectedConducteurId,
      date_voyage: this.newData.selectedDate,

    };
    console.log(reservation);
    this.newReservation = reservation

    console.log('initialData', this.initialData);
    console.log('newData', this.newData);
    console.log('newReservation', this.newReservation);

    

    const idsWithChangedDepart : any[] = [];
    const idsWithChangedArrivee : any[] = [];
    const idsWithChangedBoth : any[] = [];

    const isDateChanged = (row : any) => row.date_voyage !== this.newData.selectedDate;

    for (const uniqueItem of this.uniqueNomArriveeList) {
      const matchingRows : any[] = this.selectedDetails.filter(
        (row : any) =>
          row.nom_depart === uniqueItem.nom_depart &&
          row.nom_arrivee === uniqueItem.nom_arrivee
      );

      matchingRows.forEach((row : any) => {
        const originalHDepart : string = row.H_depart;
        const originalHArrivee : string = row.H_arrivee;

        if (isDateChanged(row)) {
          idsWithChangedBoth.push(row);
          row.H_depart = uniqueItem.H_depart;
          row.H_arrivee = uniqueItem.H_arrivee;
          row.tolerance = uniqueItem.tolerance;
        }
        else {
          
        

        // Comparer les nouvelles valeurs avec les anciennes
        row.H_depart = uniqueItem.H_depart;
        row.H_arrivee = uniqueItem.H_arrivee;

        const departChanged : boolean = originalHDepart !== row.H_depart;
        const arriveeChanged : boolean = originalHArrivee !== row.H_arrivee;

        if (departChanged && arriveeChanged) {
          idsWithChangedBoth.push(row);
        } else {
          if (departChanged) {
            idsWithChangedDepart.push(row);
          }
          if (arriveeChanged) {
            idsWithChangedArrivee.push(row);
          }
        }
      }
      })
    }

    const idsWithChangedBothSet = new Set(idsWithChangedBoth);

    const idsWithCleanedDepart : any[] = idsWithChangedDepart.filter((row : any) => !idsWithChangedBothSet.has(row.id));
    const idsWithCleanedArrivee : any[] = idsWithChangedArrivee.filter((row : any) => !idsWithChangedBothSet.has(row.id));

    console.log("IDs avec H_depart changé:", idsWithCleanedDepart);
    console.log("IDs avec H_arrivee changé:", idsWithCleanedArrivee);
    console.log("IDs avec les deux changé:", idsWithChangedBoth);

    // Effectuer la mise à jour
    try {
      this.updateRes();
      setTimeout(() => {
      }, 500);



    } catch (error:any) {
      console.error("Erreur lors de la recherche : ", error);
      this.toastr.error('Erreur lors de la mis à jour : ' + error.message);
      return
    }

    const promises:any[] = [];

    // // Envoyer des e-mails pour les départs modifiés
    // if (idsWithCleanedDepart.length > 0) {
    //   promises.push(this.mailService.sendReservationDepartureUpdateMail(idsWithCleanedDepart));
    // }

    // // Envoyer des e-mails pour les arrivées modifiées
    // if (idsWithCleanedArrivee.length > 0) {
    //   promises.push(this.mailService.sendReservationArrivalUpdateMail(idsWithCleanedArrivee));
    // }

    // // Envoyer des e-mails pour les deux modifications (départ et arrivée)
    // if (idsWithChangedBoth.length > 0) {
    //   promises.push(this.mailService.sendReservationArrivalUpdateMail(idsWithChangedBoth));
    //   promises.push(this.mailService.sendReservationDepartureUpdateMail(idsWithChangedBoth));
    // }

    this.smsToSend()

    try {
      // Attendre que toutes les promesses soient résolues
      await Promise.all(promises);

      // console.log("Tous les e-mails ont été envoyés avec succès.");
      // this.toastr.success("Les mises à jour ont été communiquées avec succès.");
    } catch (error) {
      // console.error("Erreur lors de l'envoi des e-mails:", error);
      // this.toastr.error("Une erreur est survenue lors de l'envoi des notifications.");
    }

    for (const row of this.selectedDetails) {
      this.sourceDetails = this.sourceDetails.filter((sourceRow:any) => sourceRow.id !== row);

    }


    this.toastr.success('Mise à jour réussie !');

    this.newData = {}
    this.showHoraireModal = false

    // Envoyer des notifications ou des e-mails
  }
  openHoraire(formData:any) {
    console.log('initialData', this.initialData);
    console.log('formData', formData);
    if (this.selectedDetails.length == 0) {
      this.toastr.error("Veuillez selectionner au moins une ligne!");
      return;
    }

    const currentDate = new Date();
    const previous = new Date(this.initialData.date_voyage);


    if (
      this.initialData.selectedCamionId == formData.selectedCamionId &&
      this.initialData.selectedConducteurId == formData.selectedConducteurId &&
      this.initialData.selectedDate == formData.selectedDate
    ) {
      this.toastr.error("Pas de modification détectée!");
      return;
    }

    // if (previous > currentDate) {
    //   this.toastr.error("La date programmée d'expédition n'est pas encore arrivée.");
    //   return;
    // }

    // Utilisation d'un Map pour garantir l'unicité des départs et arrivées
    const uniqueDepartArrivee = new Map();

    for (const selectedRow of this.selectedDetails) {
      const correspondingRow = this.sourceDetails.find(
        (sourceRow:any) => sourceRow.id === selectedRow.id
      );

      if (correspondingRow) {
        // Générer une clé unique
        const uniqueKey = `${correspondingRow.nom_depart.trim()}-${correspondingRow.nom_arrivee.trim()}`;

        if (!uniqueDepartArrivee.has(uniqueKey)) {
          uniqueDepartArrivee.set(uniqueKey, {
            nom_depart: correspondingRow.nom_depart,
            nom_arrivee: correspondingRow.nom_arrivee,
            H_depart: correspondingRow.H_depart,
            H_arrivee: correspondingRow.H_arrivee,
            tolerance: correspondingRow.tolerance,
          });
        }
      }
    }
    this.newData = {
      selectedCamionId: parseInt(formData.selectedCamionId),
      selectedConducteurId: parseInt(formData.selectedConducteurId),
      selectedDate: formData.selectedDate,
    }
    // Récupérer les valeurs du Map
    this.uniqueNomArriveeList = Array.from(uniqueDepartArrivee.values());
    console.log("Noms de départ et d'arrivée uniques:", this.uniqueNomArriveeList);
    this.showHoraireModal = true;
    
  }
  Expedier() {
    const currentDate = new Date();
    const previousDate = new Date(this.initialData.selectedDate);
    if (previousDate > currentDate) {
      this.toastr.error('La date programmée d\'expédition n\'est pas encore arrivée.');
      return;
    }
    const hasNotReadyItems = this.sourceDetails.some((item:any) => item.ready == false);
    if (hasNotReadyItems) {
      this.toastr.error('Certains éléments ne sont pas prêts pour l\'expédition.');
      return;
    }
    const updatePromises = this.sourceDetails.map((item:any) => {
      const data = {
        id: item.id,
        kilometrage: item.kilometrage,
        volume: item.volume,
        type_ligne: item.type_ligne,
        quantite: item.quantite,
      };

      // Attendre la mise à jour de l'expédition
      // this.orderLigneService.updateExpeditionStatus(data);
      console.log(data)
      //await Promise.all(updatePromises);
      this.orderLigneService.updateExpeditionStatus(data);
    });
    try {
      // Autres opérations...

      // Génération du PDF
      this.imprimerPDF();
      const adjustList:any[] = []
      this.sourceDetails.map((item:any) => {
        adjustList.push(item)
      });
     // this.ligneCmdService.adjustPrice(adjustList)

    } catch (error) {
      console.error('Une erreur est survenue lors de la génération du PDF :', error);
    }
    this.toastr.success('Mise à jour de l\'expédition réussie');
      this.source = this.source.filter(
        (row : any) =>
          row.id_camion !== this.initialData.selectedCamionId &&
          row.date !== this.initialData.selectedDate &&
          row.id_conducteur !== this.initialData.selectedConducteurId
      );

      this.getInfoByDestination()
      let idList:any[] = [];

      for (const reservation of this.sourceDetails) {
        idList.push(reservation.id);
      }
      this.ligneCmdService.fixPrice(idList).subscribe({
        next: (res:any) =>{
          this.loadDetails()
          
        },
        error: (err:any)=>{
          this.toastr.error('erreur')
        }
      })
      // Vider la liste des réservations
  }
  getInfoByDestination() {
    let destinationInfoMap :any = new Map<string, { date_voyage: string, type_ligne: string[], volume: number[], qte: number[] }>();

    // Parcourir la liste de réservations
    for (const reservation of this.sourceDetails) {
      const to_destination = reservation.to_destination;
      const date_voyage = reservation.date_voyage;
      const type_ligne = reservation.type_ligne;
      const volume = reservation.volume;
      const qte = reservation.quantite;

      // Vérifier si la destination existe déjà dans la map
      if (!destinationInfoMap.has(to_destination)) {
        // Si non, ajouter une nouvelle entrée dans la map
        destinationInfoMap.set(to_destination, {
          date_voyage: date_voyage,
          type_ligne: [type_ligne],
          volume: [volume],
          qte: [qte]
        });
      } else {
        // Si oui, obtenir les informations existantes
        const destinationInfo = destinationInfoMap.get(to_destination);
        const index = destinationInfo.type_ligne.indexOf(type_ligne);

        // Vérifier si le type de ligne existe déjà
        if (index === -1) {
          // Si non, ajouter le nouveau type de ligne
          destinationInfo.type_ligne.push(type_ligne);
          destinationInfo.volume.push(volume);
          destinationInfo.qte.push(qte);
        } else {
          // Si oui, cumuler le volume et la quantité existants
          destinationInfo.volume[index] += volume;
          destinationInfo.qte[index] += qte;
        }

        // Mettre à jour la map avec les informations mises à jour
        destinationInfoMap.set(to_destination, destinationInfo);

      }

    }
    console.log("--------*************", destinationInfoMap);

    // Appeler la fonction sendMail() pour envoyer un e-mail avec les données préparées
   // await this.sendMail();
  }

  loadConducteurs() {
    this.conducteurService.findConducteur().subscribe({
      next: (data: any) => {
        this.conducteurList = data;
        this.conducteurList.forEach((conducteur: any) => { conducteur.fullName = conducteur.nom + ' ' + conducteur.prenom }
        )
        if (this.formConfig.fieldGroups && this.formConfig.fieldGroups[0].fields[1].options) {
          this.formConfig.fieldGroups[0].fields[1].options.objectArray = this.conducteurList;
        }
      },
      error: (error: any) => {
        this.toastr.error('Erreur lors de la chargement des conducteurs');
      }
    });
  }

  loadCamion() {
    this.camionService.findAllCamion().subscribe({
      next: (data: any) => {
        this.camionList = data;
        if (this.formConfig.fieldGroups && this.formConfig.fieldGroups[0].fields[0].options) {
          this.formConfig.fieldGroups[0].fields[0].options.objectArray = this.camionList;
        }
      },
      error: (error: any) => {
        this.toastr.error('Erreur lors de la chargement des camions');
      }
    });
  }
  loadSource() {
    this.loadingPrimeTable = true;
    this.orderLigneService.findVoyageListReserved().subscribe({
      next: (data: any) => {
        this.source = data;
        this.loadingPrimeTable = false;
      },
      error: (error: any) => {
        this.toastr.error('Erreur lors de la chargement des lignes de voyage');
        this.loadingPrimeTable = false;
      }
    });
  }
  loadDetails() {
    this.loadingDetailsTable = true;
    this.showDetailsTable = true;
    let data = {
      id_camion: this.initialData.selectedCamionId,
      id_conducteur: this.initialData.selectedConducteurId,
      date: this.initialData.selectedDate,
    }
    console.log('data',data)
    this.orderLigneService.findReservedByDateAndTruck(data).subscribe({
      next: (data: any) => {
        this.sourceDetails = data;
        this.sourceDetails.forEach((element:any) => {
          this.TotalVol += element.volume
        });
        console.log('Details', this.sourceDetails)
        console.log(this.TotalVol)
        this.loadingDetailsTable = false;
      },
      error: (error: any) => {
        this.toastr.error('Erreur lors de la chargement des lignes de voyage');
        this.loadingDetailsTable = false;
      }
    });
  }
  updateRes() {
    // Récupérer les lignes sélectionnées pour mise à jour
    

    // Préparer les données à envoyer au backend pour mise à jour
    const updatePayload = this.selectedDetails.map((row:any) => ({
      id: row.id,
      H_depart: row.H_depart,
      H_arrivee: row.H_arrivee,
      tolerance: row.tolerance,
      date_voyage: this.newData.selectedDate,
      id_camion: this.newData.selectedCamionId,
      id_conducteur: this.newData.selectedConducteurId,
    }));

    console.log("Payload pour mise à jour:", updatePayload);

    // Faire une mise à jour pour chaque élément de updatePayload
    updatePayload.forEach(item => {
      this.orderLigneService.updateReservationStatus(item.id, item).subscribe({
        next: (response:any) => {
          console.log(`Mise à jour réussie pour l'ID ${item.id}:`, response);
          this.sourceDetails = this.sourceDetails.filter((row:any) => row.id !== item.id);
          this.toastr.success(`Réservation ID ${item.id} mise à jour avec succès.`);
          this.showHoraireModal = false;
        },
        error: (error:any) => {
          console.error(`Erreur lors de la mise à jour de la réservation ID ${item.id}:`, error);
          this.toastr.error(`Erreur lors de la mise à jour de la réservation ID ${item.id}.`);
          this.showHoraireModal = false;
        }
      }); 
    });
  }
  smsToSend() {
    this.recupererNomsDepartArrivee()
    // Vérification si toVerify et newReservation sont définis
    if (!this.initialData || !this.newReservation) {
      console.error('Les données à vérifier ou les nouvelles réservations ne sont pas définies.');
      return;
    }


    // Récupérer l'id du conducteur de toVerify et newReservation
    const idConducteurToVerify = this.initialData.selectedConducteurId;
    const idConducteurNewReservation = this.newReservation.id_conducteur;

    console.log("idConducteurToVerify", idConducteurToVerify);
    console.log("idConducteurNewReservation", idConducteurNewReservation);

    // Recherche du conducteur correspondant à l'id_conducteur dans la liste conducteurList
    const conducteurToVerify = this.conducteurList.find((conducteur:any) => conducteur.id == idConducteurToVerify);
    const conducteurNewReservation = this.conducteurList.find((conducteur:any) => conducteur.id == idConducteurNewReservation);

    // Vérification si les conducteurs ont été trouvés
    if (!conducteurToVerify || !conducteurNewReservation) {
      console.error('Les conducteurs correspondants n\'ont pas été trouvés dans la liste.');
      return;
    }

    // Création de l'objet contentSms avec le mobile du conducteur de toVerify
    const content = {
      phone: conducteurToVerify.mobile,
      message: `Vous avez Livraison(s) annulé le  ${this.initialData.date} : ${this.resultatFinal}`
    };

    const contentSms = {
      phone: conducteurNewReservation.mobile,
      message: `Vous avez Livraison(s) le  ${this.newData.date_voyage} : ${this.resultatFinal} avec estimation de ${this.tot}m³`
    };

    const contentSmsSameConductor = {
      phone: conducteurNewReservation.mobile,
      message: `Vous avez une modification du voyage programmé le ${this.initialData.selectedDate}.\n` +
        `Le voyage est maintenant prévu pour le ${this.newData.selectedDate}.\n` +
        `Estimation de ${this.tot}m³.\n` +
        `Itinéraire modifié : ${this.resultatFinal}`
    };

    if (idConducteurToVerify == idConducteurNewReservation) {
      this.notificationService.SendSmsNotification(contentSmsSameConductor).subscribe({
        next: () => {
          this.toastr.success("Sms envoyé avec succès!")
        },
        error: (error:any) => {
          console.error("Erreur lors de l'envoi du SMS :", error);
          this.toastr.error("Erreur lors de l'envoi du SMS.");
        }
      });

    } else {
      this.notificationService.SendSmsNotification(content).subscribe({
        next: () => {
          this.toastr.success("Sms envoyé avec succès!")
        },
        error: (error:any) => {
          console.error("Erreur lors de l'envoi du SMS :", error);
          this.toastr.error("Erreur lors de l'envoi du SMS.");
        }
      });
      this.notificationService.SendSmsNotification(contentSms).subscribe({
        next: () => {
          this.toastr.success("Sms envoyé avec succès!")
        },
        error: (error:any) => {
          console.error("Erreur lors de l'envoi du SMS :", error);
          this.toastr.error("Erreur lors de l'envoi du SMS.");
        }
      });
    }



  }
  async recupererNomsDepartArrivee() {
    let nomsDepartArriveeMap: Map<string, Set<string>> = new Map<string, Set<string>>();  // Map pour stocker les paires nom_depart - ensemble de nom_arrivee
    let totalVolume = 0; // Initialiser le total du volume à 0
    let totalEstimation = 0; // Initialiser le total de l'estimation à 0

    // Parcourir les lignes sélectionnées
    for (const selectedRow of this.selectedDetails) {
      // Rechercher la ligne correspondante dans this.source
      const correspondingRow = this.sourceDetails.find(
        (sourceRow:any) => sourceRow.id == selectedRow.id
      );

      // Vérifier si une ligne correspondante a été trouvée
      if (correspondingRow) {
        const nomDepart = correspondingRow.nom_depart;
        let nomArrivee: string;

        // Conditionner `nomArrivee` selon le type de ligne
        if (
          ["livraison PF (dépôt)", "Livraison PSF", "livraison MP (tissu)", "Agencement et Matériels"].includes(
            correspondingRow.type_ligne
          )
        ) {
          nomArrivee = `${correspondingRow.nom_arrivee} à collecté à ${correspondingRow.H_depart}`;
        } else {
          nomArrivee = `${correspondingRow.nom_arrivee} à Livré à ${correspondingRow.H_arrivee}`;
        }

        // Vérifier si nom_depart existe déjà dans la Map
        if (nomsDepartArriveeMap.has(nomDepart)) {
          nomsDepartArriveeMap.get(nomDepart)?.add(nomArrivee);
        } else {
          nomsDepartArriveeMap.set(nomDepart, new Set([nomArrivee]));
        }

        // Ajouter le volume et l'estimation au total
        totalVolume += correspondingRow.volume;
        totalEstimation += correspondingRow.estimation;
      }
    }

    // Afficher les résultats dans la console
    console.log("Noms de départ et d'arrivée par départ :", nomsDepartArriveeMap);

    // Assembler les noms de départ et d'arrivée
    const departArriveeStrings = Array.from(nomsDepartArriveeMap.entries()).map(([nomDepart, nomsArrivee]) => {
      const nomsArriveeString = Array.from(nomsArrivee).join("/");
      return `${nomDepart} : ${nomsArriveeString}`;
    });

    this.resultatFinal = departArriveeStrings.join("; "); // Résultat final

    console.log("Résultat final :", this.resultatFinal);

    // Total du volume et de l'estimation
    this.tot = Math.floor(totalVolume + totalEstimation);
  }
  imprimerPDF() {

    const groupedData:any = {};
    const conducteur :any = this.conducteurList.find((conducteur:any) => conducteur.id = this.initialData.selectedConducteurId)
  
    // Boucle à travers chaque réservation 
    this.sourceDetails.forEach((item:any) => {
      // Convertir les noms de départ et d'arrivée en majuscules
      const depart = item.nom_depart.toUpperCase();
      const arrivee = item.nom_arrivee.toUpperCase();
      const kilometrage = item.kilometrage; // Récupérer le kilométrage
  
      // Créer une clé unique pour chaque combinaison départ-arrivée
      const key = `${depart}-${arrivee}`;
  
      // Vérifier si la combinaison départ-arrivée existe déjà
      if (!groupedData[key]) {
        // Si non, créer une nouvelle entrée avec le nom de départ, l'arrivée et le kilométrage
        groupedData[key] = { depart, arrivee, kilometrage };
      }
    });
  
    // Ouvrir une nouvelle fenêtre d'impression
    // const printWindow = window.open('', '_blank');
    // printWindow.document.open();
  
    // Construire le contenu HTML à imprimer avec les données groupées
    let contentToPrint = `
        <html>
            <head>
                <!-- Styles CSS pour l'impression -->
                <style>
                    @media print {
                        body {
                            font-size: 12pt;
                        }
                        body * {
                            visibility: hidden;
                        }
                        #ordre-mission, #ordre-mission * {
                            visibility: visible;
                        }
                        img {
                            max-width: 100%;
                            height: auto;
                        }
                    }
                    th {
                        font-weight: bold; /* Mise en gras pour les cellules th */
                        border: 1px solid black;
                    }
                </style>
            </head>
            <body>
                <!-- Contenu à imprimer -->
  
                <!-- En-tête -->
                <div id="ordre-mission">
  
                    <table style="border-collapse: collapse; width: 100%; margin: auto;">
  <tr>
      <!-- Colonne de gauche pour le logo et le nom de l'entreprise -->
      <td style="padding: 30px;">
          <div style="display: flex; align-items: center;flex-direction: column;">
              <div style="padding-right: 20px;">
                  <img src="assets/img/logo.png" alt="Logo" class="logo" />
              </div>
              <div>
                  <h3><b>STE ZEN LOGISTIC</b></h3>
              </div>
          </div>
      </td>
  
      <!-- Colonne de droite pour les informations de l'entreprise -->
      <td style="padding: 30px;">
          <div style="text-align: center; display: flex; justify-content: flex-start;">
              <div>
                  <b>MF: 1557718/G/A/M/000</b><br>
                  <b>ADRESSE :</b> Route Gremda km2,5 SFAX<br>
                  <b>TELEPHONE :</b> 70 147 680
              </div>
          </div>
      </td>
  </tr>
  </table>
  
  
                    <!-- Titre de l'ordre de mission -->
                    <div style="display: flex; justify-content: center;">
                        <h4><b>Ordre de Mission ${this.initialData.selectedDate}</b></h4>
                    </div>
  
                    <!-- Tableau des destinations -->
                    <table style="border-collapse: collapse; width: 100%; margin: auto; text-align: center; margin-bottom : 30px ">
                        <thead>
                            <tr>
                                <th style="border: 2px solid black;" >Depart</th>
                                <th style="border: 2px solid black;">Destination</th>
                                <th style="border: 2px solid black;">Kilometrage</th>
                            </tr>
                        </thead>
                        <tbody>`;
  
    // Ajouter les lignes de données groupées au contenu à imprimer
    for (const key in groupedData) {
      if (groupedData.hasOwnProperty(key)) {
        const { depart, arrivee, kilometrage } = groupedData[key]; // Récupérer les détails de la réservation
        contentToPrint += `
                    <tr style="border: 1px solid black;">
                        <td style="border: 1px solid black;">${depart}</td>
                        <td style="border: 1px solid black;">${arrivee}</td>
                        <td style="border: 1px solid black;">${kilometrage}</td>
                    </tr>`;
      }
    }
  
    // Ajouter la signature
    contentToPrint += `
                        </tbody>
                    </table>
  
                    <div class="footer" style="margin-bottom: 150px;margin-top: 150px;">
                        <p>Signature: ${conducteur.fullName}</p>
                    </div>
                </div>
            </body>
        </html>`;
  
    const logoSrc = environment.urlFront + '/assets/img/logo.png'; 
  
    contentToPrint = contentToPrint.replace('assets/img/logo.png', logoSrc);
  
    // Utiliser fetch pour récupérer le contenu HTML
    fetch('data:text/html;charset=utf-8,' + encodeURIComponent(contentToPrint))
      .then(response => response.blob())
      .then(blob => {
        // Créer un objet URL pour le blob
        const url = window.URL.createObjectURL(blob);
  
        // Créer un élément d'ancre pour le téléchargement automatique
        const link = document.createElement('a');
        link.href = url;
        link.download = `ordre_de_mission.html_${conducteur.fullName}_${this.initialData.selectedDate}.html`; // Nom du fichier à télécharger
  
        // Ajouter l'élément d'ancre au document et déclencher le clic automatique pour démarrer le téléchargement
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
  
        // Nettoyer après le téléchargement
        window.URL.revokeObjectURL(url);
        document.body.removeChild(link);
      })
      .catch(error => console.error('Erreur lors du téléchargement du fichier HTML :', error));
  };

}
