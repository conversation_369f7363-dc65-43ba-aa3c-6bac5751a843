import { Component, OnInit } from '@angular/core';
import { initial } from 'lodash-es';
import { ToastrService } from 'ngx-toastr';
import { FournisseurService } from 'src/services/fournisseur.service';
import { LigneCmdService } from 'src/services/ligne-cmd.service';
import { DynamicFormComponent } from 'src/shared/components/dynamic-form/dynamic-form.component';
import { FormModalComponent } from 'src/shared/components/form-modal/form-modal.component';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';
import { FormConfig } from 'src/shared/models/form.models';
import { ActionButton, TableColumn, TableConfig } from 'src/shared/models/table.models';

@Component({
  selector: 'app-ligne-to-facture',
  standalone: true,
  imports: [SmartContainerComponent,SmartTableComponent,FormModalComponent],
  templateUrl: './ligne-to-facture.component.html',
  styleUrl: './ligne-to-facture.component.scss'
})
export class LigneToFactureComponent implements OnInit{

  showModal: boolean = false;
  initialData: any = {};
  loading: boolean = false;
  Clientlist: any[] = [];
  LignesToFacture: any[] = [];
  selectedRow: any = {};

  TableColumns: TableColumn[] = [
    { name: 'id', displayName: 'ID', sortable: true },
    { name: 'date_voyage', displayName: 'Date du voyage', sortable: true },
    { name: 'nom_depart', displayName: 'Depart', sortable: true },
    { name: 'nom_arrivee', displayName: 'Arrivee', sortable: true },
    { name: 'nom_client', displayName: 'Client', sortable: true },
  ]
  TableActions: ActionButton[] = [
    { icon: 'cilPencil',tooltip: 'Modifier', color: 'warning',callback: (row:any) => this.openModal(row) },
    { icon: 'cilFile',tooltip: 'Pré-Facturer',callback: (row:any) => this.facturerLigne(row) },
    { icon: 'cil-comment-bubble',color: 'info',tooltip: 'Voir les commentaires',isCommentAction: true },
  ]
  TableConfig: TableConfig = {
    commentable: true,
    emptyMessage: 'Aucune ligne',
  }

  configForm: FormConfig = {
    title: 'Modifier la ligne',
    fields: [
      {
        name: 'client_id',
        label: 'Client',
        type: 'select',
        options: { objectArray: this.Clientlist, valueAttribute: 'id', labelAttribute: 'nom_fournisseur' },
        required: true
      }
    ],
    buttons: [
      { label: 'Annuler', color: 'secondary', onClick: () => this.closeModal() },
      { label: 'Modifier',color: 'primary', onClick: (formData:any) => this.ModifierLigne(formData) },
      
    ]
  }

  constructor(
    private ligneCmdService: LigneCmdService,
    private founisseurService: FournisseurService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.loadLignesToFacture();
    this.loadClients();
  }

  ModifierLigne(formData: any) {
    console.log('formData',formData);
    let data = {
      id:this.initialData.id,
      id_client: parseInt(formData.client_id)
    }
    this.ligneCmdService.updateClient(data).subscribe({
      next: (data:any) => {
        this.toastr.success('Ligne modifiée avec succès');
        this.loadLignesToFacture();
        this.closeModal();
      },
      error: (error:any) => {
        this.toastr.error('erreur lors de la modification de la ligne');
        console.error(error);
        this.closeModal();
      }
    })
  }
  openModal(row: any) {
    this.initialData = row;
    this.showModal = true;
  }
  closeModal() {
    this.showModal = false;
    this.initialData = {};
  }
  facturerLigne(row: any) {
    if (row.prix_tot == 0){
      this.toastr.error('Le prix ne peut pas etre null');
      return;
    }
    this.ligneCmdService.updateStatusAfacture(row.id).subscribe({
      next: (data:any) => {
        this.toastr.success('Ligne facturée avec succès');
        this.LignesToFacture = this.LignesToFacture.filter((item:any) => item.id != row.id);
      },
      error: (error:any) => {
        this.toastr.error('Erreur lors de la facturation de la ligne');
        console.error(error);
      }
    })
    
  }

  loadLignesToFacture() {
    this.loading = true;
    this.ligneCmdService.findAllAdjusted().subscribe({
      next: (data:any) => {
        this.LignesToFacture = data;
        console.log(this.LignesToFacture);
        this.loading = false;
      },
      error: (error:any) => {
        this.toastr.error('Error loading lignes to facture ');
        console.error(error);
        this.loading = false;
      }
    })
  }
  loadClients() {
    this.founisseurService.getAllFournisseur().subscribe({
      next: (data:any) => {
        this.Clientlist = data;
        const ClientField = this.configForm.fields?.find((field:any) => field.name === 'client_id');
        if (ClientField && ClientField.options) {
            ClientField.options.objectArray = this.Clientlist;
        }
        console.log(this.Clientlist);
      },
      error: (error:any) => {
        this.toastr.error('Error loading clients ');
        console.error(error);
      }
    })
  }

}
