import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ModalDismissReasons, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CommandeService } from 'app/services/commande.service';
import { CommentService } from 'app/services/commentaire.service';
import { DestinationService } from 'app/services/destination.service';
import { LigneCmdService } from 'app/services/ligneCmd.service';
import { mailService } from 'app/services/mail.service';
import { RegisterServiceService } from 'app/services/register-service.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-comm-a-livre',
  templateUrl: './comm-a-livre.component.html',
  styleUrls: ['./comm-a-livre.component.scss']
})
export class CommALivreComponent implements OnInit {
  @ViewChild('popupTemplate', { static: true }) popupTemplate: TemplateRef<any>;

  camionList: any = []
  selectedDate = ""
  selectedCamionId = ""
  reservationList: any = []
  selectedIds: number[] = [];
  previousCamionId = ""
  minDate = ""
  previousDate = ""
  Show = false
  selectedCamion: string = ""; // ou tout autre type qui convient à votre modèle
  selectedConducteur: any = "";
  selectedDateUpdate: any = ""
  conducteurList: any = []
  searchConducteur = ""
  previousConducteur = ""
  initialVolume: number;
  selectAll: boolean = false;
  source: any = []
  nouveauCommentaire = ""
  id_Comment
  commentList: any = []
  modalRef: any
  toVerify: any = {}
  destinationInfoMap: any
  mail = ""
  nom_locale = ""

  allowedTypes: string[] = [
    'Agencement et Matériels',
    'Livraison PSF',
    'Retour articles',
    'Transfert Administratif inter magasin',
    'Transfert administratif technique et matériel informatique',
    'Transfert administratif ZEN Group-Magasin ou dépôt'
  ];
  
  constructor(private listcamionService: RegisterServiceService,
    private listconducteurService: RegisterServiceService,
    private ligneCmdService: LigneCmdService,
    private commentService: CommentService,
    private mailService: mailService,
    private destinationService: DestinationService,
    private modalService: NgbModal,
    private toastr: ToastrService) { }

  async ngOnInit() {

    await this.listcamionService.findAllCamion().subscribe(data => {
      this.camionList = data;
      //console.log("camion:", this.camionList)
    })

    this.listconducteurService.findConducteur().subscribe(data => {
      this.conducteurList = data;
      //console.log("conducteurList:", this.conducteurList)

    })

    this.findVoyageListExp()

  }
  async findVoyageListExp(){
    await this.ligneCmdService.findVoyageListExp().subscribe(res => {
      this.source = res
    })
  }

  settings = {
    actions: {
      add: false,
      edit: false,
      delete: false,

    },
    columns: {
      nom: {
        title: "Nom Transporteur"
      },
      prenom: {
        title: 'Prenom Transporteur'
      },
      immatriculation: {
        title: 'Immatriculation'
      },
      date_voyage: {
        title: 'Date Voyage '
      }

    },
    // attr : les lignes dans le tableau 
    attr: {
      class: "table table-responsive"
    },

    delete: {
      confirmDelete: false,
    }

  }

  async search() {
    this.Show = false;

    // Vérifiez si les valeurs ont changé depuis la dernière recherche
    if (this.selectedCamionId !== this.previousCamionId || this.selectedDate !== this.previousDate || this.searchConducteur !== this.previousConducteur) {
      if (this.selectedCamionId !== "" && this.selectedDate !== "" && this.searchConducteur !== "") {
        const data = {
          id_camion: this.selectedCamionId,
          date: this.selectedDate,
          id_conducteur: this.searchConducteur
        };

        this.toVerify = data

        try {
          const result = await this.ligneCmdService.findExpediedByDateAndCamion(data).toPromise();
          this.reservationList = result;
          //console.log(result);

          // Mettez à jour les valeurs précédentes après une recherche réussie
          this.previousCamionId = this.selectedCamionId;
          this.previousDate = this.selectedDate;
          this.previousConducteur = this.searchConducteur;
        } catch (error) {
          console.error("Erreur lors de la recherche : ", error);
          this.toastr.error('Erreur lors de la recherche : ' + error.message);
        }
      } else {
        this.toastr.error('Veuillez saisir les champs obligatoire');
      }
    }
  }


  toggleSelectAll() {
    for (let item of this.reservationList) {
      item.selected = this.selectAll;
    }

    this.updateSelectedIds(); // Met à jour la liste des IDs sélectionnés
    //console.log(this.selectedIds); // Affiche la liste dans la console
  }

  getSelectedIds(): number[] {
    return this.reservationList
      .filter(item => item.selected)
      .map(item => item.id);
  }

  updateSelectedIds(): void {
    const updatedSelectedIds = this.getSelectedIds();
    this.selectedIds = updatedSelectedIds;
    //console.log(this.selectedIds); // Affiche la liste dans la console
  }

  toggleItemSelection(item): void {
    item.selected = !item.selected;
    this.updateSelectedIds(); // Met à jour la liste des IDs sélectionnés
  }


  async livred() {
    const currentDate = new Date();
    const previous = new Date(this.previousDate);
    //console.log(currentDate, "--------------", previous);
    //console.log(this.reservationList)
    if (previous > currentDate) {
      this.toastr.error("La date programmée de livraison n'est pas encore arrivée.");
      return;
    }


    const hasBlockedDelivery = this.reservationList.some(item =>
      !this.allowedTypes.includes(item.type_ligne) &&
      item.driver_deliver == false &&
      this.selectedIds.includes(item.id)
    );
    
  
    // if (hasBlockedDelivery) {
    //   this.toastr.error("Certaines réservations doivent être marquées comme livrées par le livreur.");
    //   return;
    // }

    const hasUnprocessedVolume = this.reservationList.some(item =>
      (item.type_ligne === 'Retour articles' || item.type_ligne === 'livraison PF (magasin)' || item.type_ligne === 'Transfert PF inter magasin') &&
      item.volume === 0
    );

    // if (hasUnprocessedVolume) {
    //   this.toastr.error('Tous les volumes doivent être traités avant la mise à jour');
    //   this.toastr.warning('Merci de contacter le responsable dépot');

    //   return;
    // }

    //console.log("IDs sélectionnés pour la livraison :", this.selectedIds);

    try {
      // Utiliser Promise.all pour effectuer des appels asynchrones de manière concurrente
      await Promise.all(
        this.selectedIds.map(async (id) => {
          await this.ligneCmdService.updateStatusLivred(id);
        })
      );
      await this.getInfoByDestination()

      // Supprimer les IDs de la liste reservationList
      this.reservationList = this.reservationList.filter(
        (reservation) => !this.selectedIds.includes(reservation.id)
      );
      console.log("---------------------------")
      // Si la liste des réservations est vide, on supprime les lignes correspondantes de `source`
      if (this.reservationList.length == 0) {
        console.log("---------------------------", this.toVerify);
      
        // Supprimer directement la ligne de `source` correspondant aux critères
        this.source = this.source.filter((row) =>
          !(row.id_camion == this.toVerify.id_camion &&
          row.date_voyage == this.toVerify.date &&  // Utilisation de 'date_voyage' ici
          row.id_conducteur == this.toVerify.id_conducteur)
        );
      
        // Afficher un message pour confirmer la suppression
        console.log('Ligne supprimée pour:', this.toVerify);
      }
      
      
      
      
      // Effacer les IDs sélectionnés
      this.selectedIds = [];

      // Afficher un message Toast en cas de succès
      this.toastr.success('Le statut de livraison a été mis à jour avec succès.');

      // Le reste de votre logique ici
    } catch (error) {
      console.error("Une erreur s'est produite lors de la mise à jour du statut de livraison :", error);

      // Afficher un message Toast en cas d'échec
      this.toastr.error('Une erreur s\'est produite lors de la mise à jour du statut de livraison.');

      // Gérer l'erreur selon vos besoins
    }
  }


  onRowSelect(event) {
    //console.log('Ligne sélectionnée:', event.data);
    // Faites ce que vous voulez avec les données de la ligne sélectionnée

    this.selectedCamionId = event.data.id_camion
    this.searchConducteur = event.data.id_conducteur
    this.selectedDate = event.data.date_voyage
  }

  async envoyerCommentaire() {
    if (this.nouveauCommentaire) {
      const data = {
        id_ligne: this.id_Comment,
        value: this.nouveauCommentaire,
        id_author: sessionStorage.getItem("iduser")
      };

      //console.log(data);

      try {
        // Ajoutez le commentaire
        const response = await this.commentService.addComment(data);
        //console.log("Succès:", response);
        this.toastr.success("Commentaire Envoyer");

        const result = await this.ligneCmdService.findExpediedByDateAndCamion(this.toVerify).toPromise();
        this.reservationList = result;

        // Réinitialisez le nouveau commentaire
        this.nouveauCommentaire = "";

        // Rechargez la liste des commentaires après l'ajout
        this.commentList = await this.commentService.findCommentsByLigne(this.id_Comment);
      } catch (error) {
        this.toastr.error("Problème de connexion");
        console.error("Erreur:", error);
        // Gérer l'erreur ici, si nécessaire
      }
    }


  }

  async openComment(id: number) {
    try {
      // Réinitialiser le nouveau commentaire et récupérer les commentaires associés à l'identifiant spécifié
      this.nouveauCommentaire = "";
      this.commentList = await this.commentService.findCommentsByLigne(id);

      // Afficher les commentaires récupérés dans la console pour le débogage
      //console.log("Commentaires récupérés:", this.commentList);

      // Ouvrir le modal avec les commentaires récupérés
      this.id_Comment = id;
      this.modalRef = this.modalService.open(this.popupTemplate, {
        ariaLabelledBy: 'modal-basic-title',
        windowClass: 'custom-modal-style' // Utilisez la classe CSS spécifique définie dans votre fichier SCSS de composant
      });
    } catch (error) {
      // Gérer les erreurs éventuelles lors de la récupération des commentaires
      console.error("Erreur lors de la récupération des commentaires:", error);
    }
  }

  truncateComment(commentaire: string): string {
    const maxLength = 15; // Limite de longueur du commentaire
    if (commentaire.length > maxLength) {
      return commentaire.substring(0, maxLength) + '...'; // Retourne les premiers maxLength caractères avec '...' ajouté à la fin
    } else {
      return commentaire; // Retourne le commentaire complet s'il est inférieur ou égal à la limite
    }
  }


  async getInfoByDestination() {
    this.destinationInfoMap = new Map<string, { date_voyage: string, type_ligne: string[], volume: number[], qte: number[] }>();

    // Parcourir la liste de réservations
    for (const reservation of this.reservationList) {
      const to_destination = reservation.to_destination;
      const date_voyage = reservation.date_voyage;
      const type_ligne = reservation.type_ligne;
      const volume = reservation.volume;
      const qte = reservation.quantite;

      // Vérifier si la destination existe déjà dans la map
      if (!this.destinationInfoMap.has(to_destination)) {
        // Si non, ajouter une nouvelle entrée dans la map
        this.destinationInfoMap.set(to_destination, {
          date_voyage: date_voyage,
          type_ligne: [type_ligne],
          volume: [volume],
          qte: [qte]
        });
      } else {
        // Si oui, obtenir les informations existantes
        const destinationInfo = this.destinationInfoMap.get(to_destination);
        const index = destinationInfo.type_ligne.indexOf(type_ligne);

        // Vérifier si le type de ligne existe déjà
        if (index === -1) {
          // Si non, ajouter le nouveau type de ligne
          destinationInfo.type_ligne.push(type_ligne);
          destinationInfo.volume.push(volume);
          destinationInfo.qte.push(qte);
        } else {
          // Si oui, cumuler le volume et la quantité existants
          destinationInfo.volume[index] += volume;
          destinationInfo.qte[index] += qte;
        }

        // Mettre à jour la map avec les informations mises à jour
        this.destinationInfoMap.set(to_destination, destinationInfo);
      }
    }
    //console.log("--------*************", this.destinationInfoMap);

    // Appeler la fonction sendMail() pour envoyer un e-mail avec les données préparées
    await this.sendMail();
  }

  async sendMail() {
    this.mail = ''
    const destinationInfoArray: [string, { date_voyage: string, type_ligne: string[], volume: number[], qte: number[] }][] = Array.from(this.destinationInfoMap.entries());

    for (const [destination, details] of destinationInfoArray) {

      const res = await this.destinationService.findDestinationById(destination);

      this.mail = res.destination[0].email;
      this.nom_locale = res.destination[0].nom_locale

      const date_voyage = details.date_voyage;
      const type_ligne = details.type_ligne;
      const volume = details.volume;
      const qte = details.qte;

      const emailContent = {
        nom_locale: this.nom_locale,
        Destination: this.mail,
        Date_voyage: date_voyage,
        Types_ligne: type_ligne,
        Volumes: volume,
        qte: qte
      };


      this.mailService.mailLivraison(emailContent)
        .then(response => {
        })
        .catch(error => {
        });



    }

  }

  async sendAnnulation(selectedIds :any[]) {
    const destinationsData = {}; 

    for (const id of selectedIds) {

      const destination = this.reservationList.find(item => item.id == id);

      if (destination) {
        try {
          const res = await this.destinationService.findDestinationById(destination.to_destination);
          this.mail = res.destination[0].email;
          this.nom_locale = res.destination[0].nom_locale;

          destinationsData[destination.to_destination] = {
            mail: this.mail,
            nom_locale: this.nom_locale
          };
        } catch (error) {
          console.error(`Une erreur s'est produite lors de la récupération des données pour l'ID ${id}:`, error);
        }
      }
    }

    const destinationsUniques = Object.values(destinationsData);

    for (const data of destinationsUniques) {
      try {
        await this.mailService.sendAnnulationMail(data);
      } catch (error) {
        console.error(`Une erreur s'est produite lors de l'envoi de l'e-mail d'annulation pour la destination ${data}:`, error);
      }
    }
  }

  async panne() {
    const confirmDialog = window.confirm('Êtes-vous sûr de vouloir mettre en panne ces éléments ?');
    if (confirmDialog) {

      try {
        await Promise.all(
          this.selectedIds.map(async (id) => {
            await this.ligneCmdService.updatePanne(id);

          })
        );
        await this.sendAnnulation(this.selectedIds)
        this.reservationList = this.reservationList.filter((reservation) => !this.selectedIds.includes(reservation.id));
        this.selectedIds = [];
        this.toastr.success('Le statut de livraison a été mis à jour avec succès.');
      } catch (error) {
        console.error("Une erreur s'est produite lors de la mise à jour du statut de livraison :", error);
        this.toastr.error('Une erreur s\'est produite lors de la mise à jour du statut de livraison.');
      }
    }

  }



}


