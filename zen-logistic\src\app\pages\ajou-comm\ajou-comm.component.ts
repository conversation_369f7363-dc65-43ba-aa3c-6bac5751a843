import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { RegisterServiceService } from "app/services/register-service.service";
import { data } from "app/shared/data/smart-data-table";
import { Md5 } from "md5-typescript";
import { LocalDataSource } from "ng2-smart-table";
import { DatePipe } from "@angular/common";
//import { NGXToastrService } from 'app/components/extra/toastr/toastr.service';
import { ToastrService } from 'ngx-toastr';
import { NgbModal, ModalDismissReasons }
  from '@ng-bootstrap/ng-bootstrap';
import { htmlAlert } from 'app/shared/data/sweet-alerts';
import { ReactiveFormsModule } from '@angular/forms';
import { ChangeDetectorRef } from '@angular/core';
import { FournisseurService } from 'app/services/fournisseur.service';
import { DestinationService } from 'app/services/destination.service';
import { FormControl } from '@angular/forms';
import { CommandeService } from 'app/services/commande.service';
import { LigneCmdService } from 'app/services/ligneCmd.service';
import { v4 as uuidv4 } from 'uuid';
import { PdfService } from 'app/services/pdf.service';
import { CircuitService } from 'app/services/circuit.service';

@Component({
  selector: 'app-ajou-comm',
  templateUrl: './ajou-comm.component.html',
  styleUrls: ['./ajou-comm.component.scss'],
  providers: 
  [
    ReactiveFormsModule
  ]

})
export class AjouCommComponent implements OnInit {
  registerForm: FormGroup;
  Formgroup1: FormGroup;
  Formgroup2: FormGroup;
  volumeCalculatedControl: FormControl = new FormControl();

  closeResult = '';
  submitted = false;
  err = false;
  selectedVille: String = "Sfax";
  marchandise: any = []
  condition: any = []
  errmessage: String = " veuillez remplir tous les champs ";
  loading = false;
  chaine: string;
  ind_charge: number = 0;
  //contentEmail="<div class='col-12 col-sm-6 col-md-4' class='m-auto' *ngIf='erreur' ><div class='card'> <div class='card-header'> <h4 class='card-title' class='text-center'> Bonjour ! </h4><p>Veuillez  compléter votre inscription en confirmant votre adresse e-mail . cliquer sur le bouton ci-dessous!</p></div> <div class='card-content'><div class='card-body text-center'><span class='badge badge-success'>Retour</span></div></div></div></div>"
  list_client = [];
  list_ville = [];
  list_region = [];
  listCharg = [];
  //pour les commandes enregistrées
  list_id = [];
  detail = [];
  lieux_Dechargement = []
  typeCmd = []
  departDestination = []
  villeDepartControl = new FormControl();
  fournisseurDepartControl = new FormControl();
  ligneTable = []
  labelle = false
  source2: LocalDataSource;
  correntCommande: any;
  isClient: boolean = false
  lieuLabel
  selectedVilleId = 1;
  fournisseur_depart = ""
  fournisseurList = []
  arriveDestination = [];
  tableToshow = []
  volumeCalculated = 0
  idCommande: any
  source1: LocalDataSource = new LocalDataSource(this.tableToshow);
  circuitList = []
  destinations = []
  showQte = false
  showQteSac = false
  showVolCamion = false
  colis = false
  userName = ""
  selectedVolume = 0
  Qte = 0
  showSaison = false
  ligneEditMode: number | null = null;
  ShowSku = false
  seasons :any = [];
  showSave =true
  selectedSeason = ""
  date_reference
  role=""
  constructor(
    private modalService: NgbModal,
    private toastr: ToastrService,
    private datePipe: DatePipe,
    private router: Router,
    private route: ActivatedRoute,
    private formBuilder: FormBuilder,
    private registerService: RegisterServiceService,
    private cdRef: ChangeDetectorRef,
    private fournisseurService: FournisseurService,
    private destinationService: DestinationService,
    private commandeService: CommandeService,
    private ligneCmdService: LigneCmdService,
    private pdfService: PdfService,
    private circuitService: CircuitService
  ) { }
  handleChange(event) {
    if (event.target.value == 'Oui') {
      this.labelle = true
      this.registerForm.get('modele').setValidators(Validators.required)
    } else {
      this.labelle = false
      this.registerForm.get('modele').clearValidators()

    }
    this.registerForm.get('modele').updateValueAndValidity()

  }
  async selectClient(event) {
    await this.registerService.getLienuxDecharByCustomer(this.f2.client.value).subscribe(res =>
      this.lieux_Dechargement = res
    )
  }

  addLieuDech(modal) {
    if (this.f2.client.value != '')
      this.open(modal)
  }
  async addLieu() {
    await this.registerService.addLieuxDechar(this.f2.client.value, this.lieuLabel).subscribe(res => console.log(res))
    await this.registerService.getLienuxDecharByCustomer(this.f2.client.value).subscribe(res =>
      this.lieux_Dechargement = res
    )
  }


  async ngOnInit() {

   this.role = sessionStorage.getItem('userRole')
      const idUser = sessionStorage.getItem('iduser');

  // Vérifiez si l'ID de l'utilisateur existe
  if (idUser) {
    // Si l'ID de l'utilisateur existe, effectuez la requête pour obtenir les informations de l'utilisateur
    try {
      const data = await this.registerService.findOne(idUser);
      // Traitez les données de l'utilisateur
      this.userName = data.nom_utilisateur;
      if(this.userName==null||this.userName==undefined||this.userName==''){
        this.router.navigate(['/pages/login']); // Remplacez '/login' par le chemin de votre page de connexion

      }
    } catch (error) {
      // En cas d'erreur lors de la récupération des données de l'utilisateur, redirigez vers la page de connexion
      this.router.navigate(['/pages/login']); // Remplacez '/login' par le chemin de votre page de connexion
    }
    
  } else {
    // Si l'ID de l'utilisateur n'existe pas dans le stockage local, redirigez vers la page de connexion
    this.router.navigate(['/pages/login']); // Remplacez '/login' par le chemin de votre page de connexion
  }



    this.commandeService.getAllSaison().subscribe(
      (data) => {

        this.seasons = data
      },
      (error) => {
        // Traitement des erreurs en cas d'échec de la requête
        console.error('Erreur lors de la recherche de l\'utilisateur :', error);
      }
    );

    this.typeCmd = await this.registerService.getAlltypeCmd().toPromise();
    //console.log(this.typeCmd)
    this.condition = await this.registerService.getAllCondition().toPromise();
    this.marchandise = await this.registerService.getAllMarchandise().toPromise();
    if(this.role=='GE'){
      this.typeCmd = this.typeCmd.filter(cmd => cmd.nom_type == "livraison PF (magasin)");
      this.condition = this.condition.filter(cond => cond.nom_condition == "Sac" || cond.nom_condition == "colis");
      this.marchandise = this.marchandise.filter(marchandise => marchandise.nom_marchandise == "Produit Fini");

    }
    //console.log(this.condition)

    this.registerForm = this.formBuilder.group({
      fournisseur_Depart: [""],
      fournisseur_Arrive: [""],
      kilometrage: [""],
      destination_Depart: [""],
      destination_Arrive: [""],
      phone: [""],
      // circuit:[""],
      volumeCalculated: this.volumeCalculatedControl,
      //id_client:[""],
      client_facturer: [""],
      id_type: ["", Validators.required],
      // type_camion: [" "],
      type_chargement: [" "],
      date_depart: ["", Validators.required],
      date_arrivee: ["", Validators.required],
      // heure_chargement: [""],
      // heure_dechargement_de: [""],
      // heure_dechargement_a: [""],
      type_conditionnement: ["", Validators.required],
      type_marchandise: ["", Validators.required],
      // poids: [""],
      volume: [""],
      quantite: ["", Validators.required],
      mention: [""],
      fragile: ["Non", Validators.required],
      enrg_commande: ["Non", Validators.required],
      code: ["choisir commande", Validators.required],
      modele: [""],
      ville_depart: [this.selectedVilleId.toString(), Validators.required],
      ville_arrivee: ["1", Validators.required],
      saisons : [""],
      sku : [""]

    });


    this.source1 = new LocalDataSource([]);
    this.source2 = new LocalDataSource([]);
    this.getUserRole()
  
   

    if(sessionStorage.getItem('userRole')=='Client'|| sessionStorage.getItem('userRole')=='GS'){
      //console.log('test')
      await this.registerService
      .findAllCommEnrg(sessionStorage.getItem("iduser"))
      .subscribe((data) => {
        //console.log(data);
        this.list_id = data;
      });
      }else if(sessionStorage.getItem('userRole')=='Chef Departement'){
        this.commandeService.findEnrgBychef(sessionStorage.getItem('iduser')).then(data => {
          this.list_id = data;
          //console.log(data)
        })
      }




    

    await this.registerService.getVilles().then((data) => {
      ////console.log(data) ;
      this.list_ville = data;
      //console.log("999999999999", this.list_ville)
    });

  }

  get f() {
    return this.registerForm.controls;
  }
  get f1() {
    return this.Formgroup1.controls;
  }
  get f2() {
    return this.Formgroup2.controls;
  }


  makeRandom(lengthOfCode: number) {
    let possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";

    let text = "";
    for (let i = 0; i < lengthOfCode; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
  }

  generate() {
    this.chaine = "";
    this.chaine = "E" + this.makeRandom(5);
    return this.chaine;
    return Md5.init(this.chaine);
  }

  async getRegionChargemet(event) {
    //console.log(this.Formgroup1.value.ville)
    // //console.log(event.target.value)
    let id = event.target.value.split("/")[0];
    ////console.log(   this.registerService.findRegion(id) )
    await this.registerService.findRegion(id).subscribe((data) => {
      // //console.log(this.Formgroup1.value) ;
      this.list_region = data;
    });
  }

  async getRegionDechargement(event) {
    //console.log("////////////", event)
    let id = event.target.value.split("/")[0];
    await this.registerService.findRegion(id).subscribe((data) => {
      this.list_region = data;
    });
  }

  onDeleteConfirm1(index: number, volume) {
    if (window.confirm('Etes-vous sûr que vous voulez supprimer cette ligne ?')) {
      //console.log(index);
      // Récupérer l'index de l'élément à supprimer dans ligneTable et tableToshow
      const indexToDelete = index;

      // Supprimer l'élément de ligneTable et tableToshow
      if (indexToDelete !== -1) {
        //console.log("xxxxxxxxxxxxxxx", this.ligneTable[indexToDelete])
        //console.log(volume)

        this.volumeCalculated -= volume;

        this.ligneTable.splice(indexToDelete, 1);
        this.volumeCalculatedControl.setValue(this.volumeCalculated);

      }

      //console.log(this.ligneTable);
      //console.log(this.tableToshow);

      // Supprimer l'élément de la source1 de ng2-smart-table
      this.source1.remove(this.ligneTable[indexToDelete]);
    }
  }

  async getCommEnrg(event) {
    //console.log(event)
    let id = event.target.value;
    await this.commandeService.findOneCommEnrg(id).subscribe((data) => {
      this.correntCommande = data;
      this.volumeCalculated = data.volume
      //console.log('er', data)
      this.registerForm.setValue({
        ville_depart: this.getVilleIdbyName("Sfax"),
        ville_arrivee: this.getVilleIdbyName("Sfax"),
        fournisseur_Depart: "",
        fournisseur_Arrive: "",
        id_type: "",
        kilometrage: 0,
        volume: 0,
        destination_Depart: "",
        destination_Arrive: "",
        phone: "",
        client_facturer: "",
        volumeCalculated: data.volume,
        type_chargement: "",
        date_depart: "",
        date_arrivee: "",
        type_conditionnement: data.type_conditionnement,
        type_marchandise: data.type_marchandise,
        quantite: data.quantite,
        mention: data.mention,
        fragile: data.fragile,
        enrg_commande: 'Non',
        code: data.id,
        modele: '',
        saisons : '',
        sku : ''
      });


      // this.CircuitChange(data.id_circuit)


      this.registerService.addCharDecharByIdCmd(id).subscribe(
        (data) => {
          //console.log(data);

          // Mettre à zéro la propriété volume de chaque élément
          this.ligneTable = data.map(item => {
            item.volume = 0;
            return item;
          });
        },
        (error) => {
          console.error('Erreur lors de la récupération des données : ', error);
        }
      );

    });
    this.labelle = false
    this.registerForm.get('modele').clearValidators()
    this.registerForm.get('modele').updateValueAndValidity()

  }

  compareDates(d1, d2) {
    const currentDate = new Date();
    const date1 = new Date(d1);
    const date2 = new Date(d2);

    // Réinitialiser les heures, minutes, secondes, etc. à 00:00:00
    date1.setHours(0, 0, 0, 0);
    date2.setHours(0, 0, 0, 0);
    currentDate.setHours(0, 0, 0, 0);

    return date1.getTime() >= currentDate.getTime() && date2.getTime() >= currentDate.getTime() && date2 >= date1;
  }


  format_date(date) {
    let d = new Date();
    let a = this.datePipe.transform(date, "dd-MM-yyyy");
    return a;
  }


  async getUserRole() {
    await this.registerService.findOne(sessionStorage.getItem('iduser')).then(data => {
      if (data.type_utilisateur == "Client") {
        this.isClient = true;
      } else this.isClient = false;
    })
  }

  open(content) {
    this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title', size: 'sm' }).result.then((result) => {
      this.closeResult = `Closed with: ${result}`;
    }, (reason) => {
      this.closeResult =
        `Dismissed ${this.getDismissReason(reason)}`;
    });
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }



  async onVilleDepartChange(selectedVilleId: number) {
    //console.log(selectedVilleId);
    this.selectedVilleId = selectedVilleId
    this.selectedVille = this.getVilleNameById(selectedVilleId)
    this.cdRef.detectChanges();
    if (selectedVilleId) {
      this.selectedVilleId = selectedVilleId;
      this.Formgroup1.get('ville').setValue(this.selectedVilleId.toString());
      this.cdRef.detectChanges();
      this.registerService.findRegion(this.selectedVilleId).subscribe((data) => {
        this.list_region = data;
        this.cdRef.detectChanges();
      });

    }
  }

  // Assurez-vous que cette méthode est correctement définie dans votre composant TypeScript



  getVilleNameById(villeId: number): string | undefined {
    if (!this.list_ville || this.list_ville.length === 0) {
      return undefined;
    }
    const selectedVille = this.list_ville.find(ville => ville.id == villeId);
    return selectedVille ? selectedVille.nom_ville : undefined;
  }


  getVilleIdbyName(villeName: String): Number | undefined {
    if (!this.list_ville || this.list_ville.length === 0) {
      return undefined;
    }
    const selectedVille = this.list_ville.find(ville => ville.nom_ville == villeName);
    return selectedVille ? selectedVille.id : undefined;
  }




  async getRegion(event) {
    //console.log(this.Formgroup1.value.ville)
    // //console.log(event.target.value)
    let id = event.target.value.split("/")[0];
    ////console.log(   this.registerService.findRegion(id) )
    await this.registerService.findRegion(id).subscribe((data) => {
      // //console.log(this.Formgroup1.value) ;
      this.list_region = data;
    });
  }

  getMinDate(): string {
    const today = new Date();
    

    // Désactiver le jour actuel si après 12h
    if (today.getHours() >= 12) {
      today.setDate(today.getDate() + 2);
    } else {
      today.setDate(today.getDate() + 1);
    }

    // Désactiver tous les dimanches
   this.date_reference = today.toISOString().split('T')[0]

    return today.toISOString().split('T')[0];
  }

  disableSundays(event: Event): void {
    const inputDate: string = (event.target as HTMLInputElement).value;
    const selectedDate: Date = new Date(inputDate);

    // Désactiver le dimanche (day === 0)
    if (selectedDate.getDay() === 0) {
      (event.target as HTMLInputElement).value = '';
      this.toastr.error("Les demandes doit etre dans les jours ouvrables")
    }
  }



  VilleDepartChange() {
    //console.log("ville", this.f.ville_depart.value)
    //console.log("fournisseur", this.f.fournisseur_Depart.value)

    this.getDestinatinDepart(this.f.ville_depart.value, this.f.fournisseur_Depart.value, this.f.id_type.value.id);

  }

  async typeChange() {
    // Fetch fournisseur list based on the selected type
    await this.fournisseurService.findFournisseurByType(this.f.id_type.value.id).subscribe(response => {
      this.fournisseurList = response.fournisseur;
    });
  
    // Reset visibility flags
    this.resetVisibilityFlags();
  
    // Update destination fields based on selected type
    this.updateDestinations();
  
    // Update visibility flags based on the selected type
    this.updateVisibilityFlags();
  }
  
  resetVisibilityFlags() {
    this.showQte = false;
    this.showQteSac = false;
    this.showVolCamion = false;
    this.colis = false;
    this.showSaison = false;
    this.ShowSku = false;
  }
  
  updateDestinations() {
    this.getDestinatinDepart(this.f.ville_depart.value, this.f.fournisseur_Depart.value, this.f.id_type.value.id);
    this.getDestinatinArrive(this.f.ville_arrivee.value, this.f.fournisseur_Arrive.value, this.f.id_type.value.id);
  }
  
  updateVisibilityFlags() {
    const nomType = this.f.id_type.value.nom_type;
  
    switch (nomType) {
      case "livraison PF (dépôt)":
        this.ShowSku = true;
        this.showSaison = true;
        this.Qte=0

        break;
      case "Transfert Administratif inter magasin":
      case "Transfert administratif technique et matériel informatique":
      case "Transfert administratif ZEN Group-Magasin ou dépôt":
        this.showQte = true;
        this.Qte=0

        break;
      case "Livraison PSF":
        this.showQteSac = true;
        this.ShowSku = true;
        this.Qte=0
        break;
      case "Agencement et Matériels":
      case "livraison MP (tissu)":
        this.showVolCamion = true;
        this.ShowSku = nomType === "livraison MP (tissu)";
        break;
      case "Livraison fourniture":
        this.colis = true;
        this.ShowSku = true;
        this.Qte=0

        break;
      case "Retour articles":
      case "livraison PF (magasin)":
      case "Transfert PF inter magasin":
        this.showSaison = true;
        this.Qte=0

        break;
      default:
        // No special conditions
        break;
    }
  }
  

  // Fonction appelée lorsque le fournisseur de départ change
  onFournisseurDepartChange() {
    this.getDestinatinDepart(this.f.ville_depart.value, this.f.fournisseur_Depart.value, this.f.id_type.value.id);
  }


  async getDestinatinDepart(idVille?, idFournisseur?, idtype?) {
    if (idVille && idFournisseur && idtype) {
      await this.destinationService.getDestinationByVilleAndFournisseur(idVille, idFournisseur, idtype).subscribe(response =>
        this.departDestination = response)
      //console.log("rrrrrrrrrrrrrrrrrrrrrrrrr", this.departDestination)
    }

  }



  VilleArriveChange() {
    //console.log("ville", this.f.ville_arrivee.value)
    //console.log("fournisseur", this.f.fournisseur_Arrive.value)

    this.getDestinatinArrive(this.f.ville_arrivee.value, this.f.fournisseur_Arrive.value, this.f.id_type.value.id);
  }

  // Fonction appelée lorsque le fournisseur de départ change
  onFournisseurArriveChange() {
    this.getDestinatinArrive(this.f.ville_arrivee.value, this.f.fournisseur_Arrive.value, this.f.id_type.value.id);
  }


  async getDestinatinArrive(idVille?, idFournisseur?, type?) {
    if (idVille && idFournisseur && type) {
      await this.destinationService.getDestinationByVilleAndFournisseur(idVille, idFournisseur, type).subscribe(response =>
        this.arriveDestination = response)
      //console.log("rrrrrrrrrrrrrrrrrrrrrrrrr", this.arriveDestination)
    }

  }




  onEditClick(index: number,ligne : any) {
    //console.log('onEditClick', index);
    //console.log('onEditClick', ligne);
    if(ligne.type_ligne == 'Retour articles' ||
    ligne.type_ligne === 'livraison PF (magasin)' ||
    ligne.type_ligne === 'Transfert PF inter magasin'){
      this.ligneEditMode = index;

    }


  }
  
  onSaveClick(index: number, quantite: number, saison: string) {
    if(quantite !< 0 || saison ==""){
      this.toastr.error("Merci de saisir les valeurs")
      return;
    }
    //console.log('onSaveClick', index, this.ligneTable[index].quantite=quantite);
    this.ligneTable[index].estimation=parseFloat((quantite/parseInt(saison)).toFixed(2))
    this.ligneTable[index].quantite=quantite

    //console.log(this.ligneTable[index])
    // Mettez en œuvre la logique de mise à jour ici
  
    // Réinitialiser la variable après la mise à jour
    this.ligneEditMode = null;
  }
  
  onCancelClick() {
    //console.log('onCancelClick');
    this.ligneEditMode = null;
  }
  
  isNonEditableType(type: string): boolean {
    return (
      type === 'Retour articles' ||
      type === 'livraison PF (magasin)' ||
      type === 'Transfert Administratif inter magasin'
    );
  }



  addLigneCmd() {
    //console.log(this.f.destination_Arrive.value, this.f.destination_Depart.value);
    
    this.Qte = this.f.quantite.value;
    this.markFieldsAsTouched(['destination_Depart', 'destination_Arrive', 'volume', 'id_type']);
    
    if (this.isFormValid()) {
      if (this.isCombinationExists()) {
        this.toastr.error("Combinaison déja existante !");
        return;
      }
      
      if (this.areDestinationsIdentical()) {
        this.toastr.error("Merci de vérifier les destinations !");
        return;
      }
      
      const toAdd = this.createLineItem();
      
      if (!this.handleSpecificConditions(toAdd)) {
        return;
      }
      
      if (!this.isSkuValid(toAdd)) {
        this.toastr.error("Veuillez entrer les références de suivi correctement. C'est indispensable pour continuer.");
        return;
      }
  
      this.ligneTable.push(toAdd);
      //console.log(this.ligneTable);
      this.resetForm();
    } else {
      this.toastr.error("Merci de remplir tous les champs obligatoires");
    }
  }
  
  markFieldsAsTouched(fields: string[]) {
    fields.forEach(field => this.f[field].markAsTouched());
  }
  
  isFormValid(): boolean {
    return this.f.destination_Arrive.value !== "" && this.f.destination_Depart.value !== "" && this.f.id_type.value !== "";
  }
  
  isCombinationExists(): boolean {
    return this.ligneTable.some(item => 
      item.to_destination.id == this.f.destination_Arrive.value.id &&
      item.from_destination.id == this.f.destination_Depart.value.id &&
      item.type_ligne == this.f.id_type.value.nom_type
    );
  }
  
  areDestinationsIdentical(): boolean {
    if (this.f.destination_Arrive.value.id == this.f.destination_Depart.value.id) {
      this.f.destination_Arrive.setValue("");
      this.f.destination_Depart.setValue("");
      return true;
    }
    return false;
  }
  
  createLineItem() {
    return {
      from_destination: this.f.destination_Depart.value,
      to_destination: this.f.destination_Arrive.value,
      kilometrage: this.f.kilometrage.value,
      type_ligne: this.f.id_type.value.nom_type,
      volume: 0,
      telephone: this.f.phone.value || null,
      quantite: 0,
      id_client: null,
      estimation: 0,
      sku: null
    };
  }
  
  handleSpecificConditions(toAdd: any): boolean {
    const type = this.f.id_type.value.nom_type;
  
    if (type == "livraison PF (dépôt)") {
      if (this.Qte === 0 || this.Qte < 100) {
        this.toastr.error(this.Qte === 0 ? "Merci de donner la quantité" : "La quantité minimale est 100 pièces");
        return false;
      }
      if (this.selectedSeason == "") {
        this.toastr.error("Merci de choisir la saison");
        return false;
      }
      toAdd.quantite = this.Qte;
      toAdd.volume = parseFloat((this.Qte / parseInt(this.selectedSeason)).toFixed(2));
    } else if (type == "Livraison PSF") {
      if (this.Qte == 0 ) {
        this.toastr.error("Merci de saisir la quantité");
        return false;
      }else if(this.Qte>50){
        this.toastr.error("Merci de vérifier la quantité saisie, Le volume d'un sac =0.3 m3");
        return false;
      }
      toAdd.volume = parseFloat((this.Qte * 0.3).toFixed(1));
      toAdd.quantite = this.Qte;
    } else if (["livraison MP (tissu)", "Agencement et Matériels"].includes(type)) {
      if (this.selectedVolume == 0) {
        this.toastr.error("Merci de saisir le volume");
        return false;
      }
      toAdd.volume = this.selectedVolume;
      toAdd.quantite = 1;

    } else if (["Livraison fourniture", "Transfert Administratif inter magasin", 
                "Transfert administratif technique et matériel informatique", 
                "Transfert administratif ZEN Group-Magasin ou dépôt"].includes(type)) {
      if (this.Qte === 0) {
        this.toastr.error("Merci de saisir la quantité");
        return false;
      }else if(this.Qte>50){
        this.toastr.error("Merci de vérifier la quantité saisie");
        return false;
      }
      toAdd.quantite = this.Qte;
      
      // Vérification spécifique pour "Livraison fourniture"
      if (type == "Livraison fourniture") {
        if (this.f.client_facturer.value == "") {
          this.toastr.error("Merci de saisir le client à facturer");
          return false;
        }
        toAdd.id_client = this.f.client_facturer.value;
      }
    } else if (["Retour articles", "livraison PF (magasin)", 
                "Transfert PF inter magasin"].includes(type)) {
      if (this.Qte == 0 || this.selectedSeason === "") {
        this.toastr.error(this.Qte === 0 ? "Merci de saisir la quantité" : "Merci de choisir la saison");
        return false;
      }
      toAdd.quantite = this.Qte;
      toAdd.estimation = parseFloat((this.Qte / parseInt(this.selectedSeason)).toFixed(2));
    }
  
    return true;
  }
  
  
  isSkuValid(toAdd: any): boolean {
    if (this.ShowSku && (!this.f.sku.value || this.f.sku.value.length < 4)) {
      return false;
    }
    toAdd.sku = this.f.sku.value;
    return true;
  }
  


  resetForm(): void {
    // Réinitialisation des champs du formulaire
    this.f.fournisseur_Arrive.setValue("");
    this.f.fournisseur_Depart.setValue("");
    this.f.destination_Depart.setValue("");
    this.f.destination_Arrive.setValue("");
    this.f.quantite.setValue(0);
    this.f.id_type.setValue("");
    this.f.client_facturer.setValue("");
    this.f.saisons.setValue("");
    this.f.sku.setValue("");
    this.selectedSeason=""
    // Réinitialisation des variables
    this.Qte = 0;
    this.selectedVolume = 0;
    this.showQteSac = false;
    this.showSaison = false;
    this.showVolCamion = false;
    this.showQte = false;
    this.ShowSku = false;
    this.colis = false;
  
    // Réinitialisation des listes
    this.fournisseurList = [];
    this.departDestination = [];
    this.arriveDestination = [];
  
    // Marquer tous les champs comme "non touchés"
    this.f.destination_Depart.markAsUntouched();
    this.f.destination_Arrive.markAsUntouched();
    this.f.volume.markAsUntouched();
    this.f.id_type.markAsUntouched();
    this.f.fournisseur_Arrive.markAsUntouched();
    this.f.fournisseur_Depart.markAsUntouched();
    this.f.client_facturer.markAsUntouched();
    this.f.sku.markAsUntouched();
  }
  





  async saveCommande() {
    this.showSave=false
    const controlsToCheck = ['date_depart', 'date_arrivee', 'type_conditionnement', 'type_marchandise'];

    // Mark specific form controls as touched
    controlsToCheck.forEach(controlName => this.f[controlName].markAsTouched());

    // Check if any of the specified form controls is invalid
    const isAnyControlInvalid = controlsToCheck.some(controlName => this.f[controlName].invalid);

    if (isAnyControlInvalid) {
      this.toastr.error("Merci de remplir tous les champs obligatoires");
      this.showSave=true

      return;
    }
    if (this.compareDates(this.f.date_depart.value, this.f.date_arrivee.value) == false) {

      this.toastr.error("Merci de vérifier les dates");
      this.f.date_depart.setErrors({ 'incorrectOrder': true });
      this.f.date_arrivee.setErrors({ 'incorrectOrder': true });
      this.showSave=true

      return;
    }

    if(this.f.date_depart.value<this.date_reference || this.f.date_arrivee.value<this.date_reference){
      this.toastr.error("Date Non autorisé");
      this.showSave=true

      return;

    }

    // Additional logic for the ligneTable
    if (this.ligneTable.length === 0) {
      this.toastr.error("Merci d'ajouter une ligne de commande");
      this.showSave=true

      return;
    }

    if(!this.checkLigneTableConditions()){
      return;
     }

     
    if (this.f.modele.value == "" && this.f.enrg_commande.value == "Oui") {
      this.showSave=true

      this.toastr.error("Merci d'ajouter un nom du modéle");
      return;

    }

 


    if (this.f.enrg_commande.value === "Oui") {
      let modeleError = false; // Variable pour vérifier si une erreur de modèle a déjà été signalée

      for (const ligne of this.ligneTable) {
        const typeLigne = ligne.type_ligne;

        if (
          typeLigne != "Retour articles" &&
          typeLigne != "Transfert PF inter magasin" &&
          typeLigne != "livraison PF (magasin)" &&
          typeLigne.trim() !== ""
        ) {
          // Faites quelque chose avec les lignes qui correspondent à la condition
          modeleError = true; // Définir la variable d'erreur à true
          this.toastr.warning("Le modèle ne peut pas être enregistré");
          break; // Sortir de la boucle dès qu'une erreur est trouvée
        }
      }

      if (modeleError) {
        this.f.modele.setValue("");
        this.f.enrg_commande.setValue("non");
        this.labelle = false;
      }
    }


    const cmdToAdd = {
      type_conditionnement: this.f.type_conditionnement.value,
      type_marchandise: this.f.type_marchandise.value,
      date_depart: this.f.date_depart.value,
      date_arrivee: this.f.date_arrivee.value,
      //poids:this.f.poids.value ,
      volume: null,
      quantite: this.f.quantite.value,
      mention: this.f.mention.value,
      fragile: this.f.fragile.value,
      code: this.generate(),
      enrg_commande: this.f.enrg_commande.value,
      ajoutee_par: sessionStorage.getItem("iduser"),
      statut: "en attente de confirmation",
      labelle: this.f.modele.value,
      //id_circuit : this.f.circuit.value,
      nom_conducteur: "",

    }
    //console.log(cmdToAdd)



    try {
      const response = await this.commandeService.addCommande(cmdToAdd);
      this.idCommande = response.id;
      //console.log(this.f.date_depart, this.f.date_arrivee)
      //console.log('Commande ajoutée avec succès:', response);

      // Add idCommande to each item in ligneTable
      this.ligneTable.forEach(item => {
        item.idCommande = this.idCommande;
        item.date_depart = this.f.date_depart.value;
        item.date_arrivee = this.f.date_arrivee.value;
        item.ajoutee_par = this.userName;
        item.id_demandeur = sessionStorage.getItem('iduser');

      });

      //console.log("tab after idCommande", this.ligneTable);

      // Use the returned idCommande to add lignes
      for (const ligne of this.ligneTable) {

        await this.ligneCmdService.addLigne(ligne);

      }
      this.toastr.success('Commande ajoutée avec succès');
      this.showSave=true
      this.ligneTable = []
      this.f.type_conditionnement.setValue("")
      this.f.date_arrivee.setValue("")
      this.f.date_depart.setValue("")
      this.f.type_marchandise.setValue("")
      this.toastr.warning("MERCI DE VALIDER LA COMMANDE")
    } catch (error) {
      this.commandeService.deleteCmdWithLignes(this.idCommande)
      console.error('Erreur lors de l\'ajout de la commande:', error);
      this.showSave=true
      this.toastr.error('Erreur lors de l\'ajout de la commande');
    }

  }

  onVolumeCamionChange(value: number) {
    this.selectedVolume = value;
  }

  onSeasonChange(event: any) {
    this.selectedSeason = event.target.value;
    //console.log('Saison sélectionnée:', this.selectedSeason);

    // Vous pouvez faire d'autres actions en fonction de la saison sélectionnée
  }

  calculateTotalVolume(): number {
    let totalVolume = 0;
    for (const item of this.ligneTable) {
      totalVolume += item.estimation + item.volume;
    }
    return parseFloat(totalVolume.toFixed(2));
  }



  checkLigneTableConditions() {
    for (const ligne of this.ligneTable) {
      const typesQuantiteSeule = [
        'Retour articles',
        'livraison PF (magasin)',
        'Transfert PF inter magasin',
        'Livraison fourniture',
        'Transfert Administratif inter magasin',
        'Transfert administratif technique et matériel informatique',
        'Transfert administratif ZEN Group-Magasin ou dépôt'
      ];
  
      if (typesQuantiteSeule.includes(ligne.type_ligne)) {
        if (ligne.quantite <= 0) {
          this.toastr.error(`La quantité doit être supérieure à 0 pour le type ${ligne.type_ligne}`);
          return false;
        }
      } else {
        if (ligne.quantite <= 0 || ligne.volume <= 0) {
          this.toastr.error(`Quantité et volume doivent être supérieurs à 0 pour le type ${ligne.type_ligne}`);
          return false;
        }
      }
    }
  

    return true;
  }
  



}
