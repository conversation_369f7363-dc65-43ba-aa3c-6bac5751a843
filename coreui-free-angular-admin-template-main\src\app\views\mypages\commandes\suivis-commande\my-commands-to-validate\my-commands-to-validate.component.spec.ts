import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MyCommandsToValidateComponent } from './my-commands-to-validate.component';

describe('MyCommandsToValidateComponent', () => {
  let component: MyCommandsToValidateComponent;
  let fixture: ComponentFixture<MyCommandsToValidateComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MyCommandsToValidateComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(MyCommandsToValidateComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
