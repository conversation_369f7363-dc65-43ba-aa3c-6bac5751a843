import { Component, OnInit } from '@angular/core';
import { ImpotService } from '../../../../../../services/impot.service';
import { ToastrService } from 'ngx-toastr';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { SmartButtonComponent } from 'src/shared/components/smart-button/smart-button.component';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';
import { CommonModule } from '@angular/common';
import { TableColumn } from 'src/shared/models/table.models';
import { FormModalComponent } from 'src/shared/components/form-modal/form-modal.component';
import { FormButton, FormConfig } from 'src/shared/models/form.models';
@Component({
  selector: 'app-tva-prestation',
  imports: [SmartContainerComponent, SmartButtonComponent, SmartTableComponent, CommonModule,FormModalComponent],
  templateUrl: './tva-prestation.component.html',
  styleUrl: './tva-prestation.component.scss'
})
export class TvaPrestationComponent implements OnInit {
  
  isPasswordCorrect = false;
  showModal = false;
  loading = false;
  impots: any[] = [];
  impotsKeyValueArray: any[] = [];
  newImpots: any[] = [];

  tableColumns :TableColumn[] = 
    [
      { name:'key',displayName:'Nom',sortable:true,filterable:true},
      { name:'value',displayName:'Valeur',sortable:true,filterable:true}
    ];

  formConfig : FormConfig = {
    title: 'Modifier les impots',
    fields: [
      { name: 'Pu_flux_Int', type: 'number', label: 'PU Flux Int', required: true},
      { name: 'ent_mp', type: 'number', label: 'Entrepôt MP', required: true },
      { name: 'ent_mpf', type: 'number', label: 'Entrepôt MPF', required: true },
      { name: 'ent_pf', type: 'number', label: 'Entrepôt PF', required: true },
      { name: 'ent_pff', type: 'number', label: 'Entrepôt PFF', required: true },
      { name: 'flux_mp', type: 'number', label: 'Flux MP', required: true },
      { name: 'flux_mpf', type: 'number', label: 'Flux MPF', required: true },
      { name: 'flux_pf', type: 'number', label: 'Flux PF', required: true },
      { name: 'flux_pff', type: 'number', label: 'Flux PFF', required: true },
      { name: 'moy_pf', type: 'number', label: 'Moyen PF', required: true },
      { name: 'moy_rouleau', type: 'number', label: 'Moyen Rouleau', required: true },
      { name: 'price_colis', type: 'number', label: 'Prix Colis', required: true },
      { name: 'station', type: 'number', label: 'Station', required: true },
      { name: 'timbre', type: 'number', label: 'Timbre', required: true },
      { name: 'tva_entreposage', type: 'number', label: 'TVA Entreposage', required: true },
      { name: 'tva_flux', type: 'number', label: 'TVA Flux', required: true },
      { name: 'tva_tr', type: 'number', label: 'TVA TR', required: true }
    ],
    buttons: [
      { label: 'Annuler',  color: 'secondary', onClick: () => this.closeModal()  },
      { label: 'Enregistrer', color: 'primary', onClick: (formData: any) => this.saveChanges(formData) }
    ]
    
  }

  constructor(
    private impotService: ImpotService,
    private toastr: ToastrService) 
    {}
  ngOnInit(): void {
    this.loadImpots();
  }

  checkPassword() {
    const input = document.querySelector('input[id="password2"]') as HTMLInputElement;
    let password = input?.value || "";
    if (password === 'zenAdmin') {
      this.isPasswordCorrect = true;
    }
    else {
      this.toastr.error('Mot de passe incorrect');
    }
  }
  saveChanges(formData: any): void {
    console.log("Hello")
    console.log(formData)
    formData = {...formData,id:1}
    this.impotService.updateById(1,formData).subscribe({
      next: (res: any) => {
        this.toastr.success('Impot mis à jour avec succès');
        this.closeModal();
      },
      error: (error: any) => {
        this.toastr.error('Une erreur est survenue lors de la mise à jour de l\'impot');
        console.log(error)
      },
      complete: () => {
        this.loadImpots();
      }
    });
  }
  closeModal(): void {
    this.showModal = false;
    
  }
  showFormModal(): void {
    this.showModal = true;
  }

  loadImpots() {
    this.loading = true;
    this.impotService.getImpot().subscribe({
      next: (res: any) => {
        this.impots = res;
        console.log("impots",this.impots);
        this.impotsKeyValueArray = Object.entries(this.impots[0]).map(([key, value]) => ({
          key,
          value
        }));
        this.impotsKeyValueArray = this.impotsKeyValueArray.filter((item: any) => item.key !== 'id');
        console.log(this.impotsKeyValueArray);
        
      },
      error: (error: any) => {
        this.toastr.error('Une erreur est survenue lors de la récupération des impots');
        console.log(error)
      },
      complete: () => {
        this.loading = false;
      }
    });
  }


}
