import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ModalDismissReasons, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { EntreposageService } from 'app/services/entreposage.service';
import { FluxService } from 'app/services/flux.service';
import { FournisseurService } from 'app/services/fournisseur.service';
import { InvoiceService } from 'app/services/invoice.service';
import { RegisterServiceService } from 'app/services/register-service.service';
import html2canvas from 'html2canvas';
import { content } from 'html2canvas/dist/types/css/property-descriptors/content';
import jsPDF from 'jspdf';
import { ToastrService } from 'ngx-toastr';
@Component({
  selector: 'app-list-facture',
  templateUrl: './list-facture.component.html',
  styleUrls: ['./list-facture.component.scss']
})
export class ListFactureComponent implements OnInit {
  @ViewChild('details', { static: true }) details: TemplateRef<any>;
  @ViewChild('detailsFlux', { static: true }) detailsFlux: TemplateRef<any>;
  source = [];
  closeResult = '';
  detail = 0;
  HT = 0
  TTC = 0
  TVA = 0
  pref = []
  montantEnLettres: any
  decEnLettres = ""
  foot = []
  timbre = 0
  adress = "";
  mat_fisc = "";
  nom_client = "";
  code = ""
  dateFac = ""
  somme = 0
  commandes = []
  date = ""
  totalPages: number;
  currentPage: number = 1;
  totalItems = 0
  verif: any = []
  detailsInvoice: any = []
  tva = 0
  selectedFactures = []
  showEntreposage: boolean = false;
  showFlux: boolean= false;
  showTransport: boolean;
  selectedClient: string = '';
monthYear: string = '';
clientsList=[]
  isFacturation: boolean = false;
  constructor(private toastr: ToastrService,
    private listconducteurService: RegisterServiceService,
    private modalService: NgbModal,
    private incoiceService: InvoiceService,
    private entreposageService: EntreposageService,
    private fluxService: FluxService,
    private fournisseurService: FournisseurService
  ) { }

 async ngOnInit() {
 this.isFacturation =  sessionStorage.getItem('userRole') == 'FACTURATION'
    this.listconducteurService.findOne(sessionStorage.getItem('iduser')).then(data => {

      if (data.type_utilisateur == "Expediteur") {
        // this.listconducteurService.getFactureE(sessionStorage.getItem('iduser')).subscribe(data => {
        //   this.source = data.data;
        //   this.totalItems = data.totalRows;

        // })

      }
      else if (data.type_utilisateur == "Administrateur" || data.type_utilisateur == "SuperAdmin" || data.type_utilisateur == "FACTURATION") {
        this.listconducteurService.getAllFacture(this.currentPage, 10).subscribe(data => {
          this.source = data.data;
          this.totalItems = data.totalRows;

          this.totalPages = Math.ceil(this.totalItems / 10);

          console.log(data)
        })
      }
    })

await this.loadClient()
  }

  async searchInvoice(){
    this.currentPage=1
    this.listconducteurService.getAllFacture(1, 10,this.selectedClient, this.monthYear).subscribe(response => {
      this.source= response.data
      console.log(response);
    });
    
  }


  async loadClient(){
   await this.fournisseurService.findClientInvoiced().subscribe(res=>{
      this.clientsList = res
    })
  }

  async loadData(page: number) {
    await this.listconducteurService.getAllFacture(page, 10,this.selectedClient, this.monthYear).subscribe(data => {
      this.source = data.data;
      this.totalItems = data.totalRows;

      this.source.forEach(facture => {
        const isSelected = this.selectedFactures.some(selected => selected.id_facture == facture.id_facture);
        facture.isChecked = isSelected;
      });
      this.totalPages = Math.ceil(this.totalItems / 10);
      console.log(data);
    });
  }


  changePage(newPage: number): void {
    if (newPage >= 1 && newPage <= this.totalPages) {
      this.currentPage = newPage;
      this.loadData(this.currentPage);
    }
  }

  getPages(): number[] {
    const pagesToShow = 5; // Nombre de pages à afficher à la fois
    const halfPagesToShow = Math.floor(pagesToShow / 2);

    let startPage = Math.max(1, this.currentPage - halfPagesToShow);
    let endPage = Math.min(this.totalPages, startPage + pagesToShow - 1);

    // Ajuster le début si on est proche de la dernière page
    if (endPage === this.totalPages) {
      startPage = Math.max(1, endPage - pagesToShow + 1);
    }

    return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i);
  }



  settings = {
    actions: {
      add: false,
      edit: false,
      delete: false,
      custom: [
        {
          name: 'openModal',
          title: '<i class="fa fa-list" ></i>',
          onClick: this.openModal.bind(this)
        }
      ]

    },
    columns: {
      code: {
        title: 'numero facture'
      },
      created_at: {
        title: "date création",
      },
      tot_facture: {
        title: ' Totale HT',
        valuePrepareFunction: (value) => {
          // Format 'tot_facture' with 3 decimal places
          return Number(value).toFixed(3);
        },
      },
      tot_factureTTC: {
        title: 'Totale TTC',
        valuePrepareFunction: (value) => {
          // Format 'tot_factureTTC' with 3 decimal places
          return Number(value).toFixed(3);
        },
      },
      nom_fournisseur: {
        title: 'Client',
      },
      status: {
        title: 'Statut',
        valuePrepareFunction: (value) => {
          return value ? 'Validé' : 'Non validé';
        }
      },
      validation: {
        title: 'Validation',
        valuePrepareFunction: (value) => {
          return value ? value : 'En attente de validation';
        }
      },
      type: {
        title: "Type Commande",
      },
    },
    attr: {
      class: "table table-responsive"
    },
    delete: {
      confirmDelete: true,
      deleteButtonContent: '<i class="ft-x danger font-medium-1 mr-2"></i>',
    }
  };

  async openModal(event) {
    if (event.type === "transport" || event.type === undefined || event.type === null) {
      await this.listconducteurService.getFacture(event.id_facture).subscribe(async res => {
        this.detailsInvoice = res
        console.log(this.detailsInvoice)
      });
      this.modalService.open(this.details);
    }
    else if (event.type === "flux") {
      await this.fluxService.getFluxByidFacture(event.id_facture).subscribe(res => {
        this.detailsInvoice = res;
        this.modalService.open(this.detailsFlux);
      })
    }
    else if (event.type === "Entreposage") {
      await this.entreposageService.getEntreposageByIdfacture(event.id_facture).subscribe(res => {
        this.detailsInvoice = res;
        this.modalService.open(this.detailsFlux);
      })
    }

  }

  async onUserRowSelect(event, content) {
    this.pref = [];
    let dateObject = new Date(event.created_at);
    let jour = dateObject.getDate();
    let mois = dateObject.getMonth() + 1; // Janvier est 0
    let annee = dateObject.getFullYear();
    this.dateFac =  event.invoice_date;

    // Assignation des valeurs
    this.code = event.code;
    this.adress = event.adresse;
    this.mat_fisc = event.mat_fisc;
    this.nom_client = event.nom_fournisseur;
    this.tva = event.tva;
    this.commandes = [];
    this.timbre = event.timbre;

    if (this.verif.length === 0) {
      this.verif.push(event.id_facture);

      // Vérification du type
      if (event.type === "transport" || event.type === undefined || event.type === null) {
        await this.handleTransport(event.id_facture, content);
      } else if (event.type === "flux") {
        await this.handleFlux(event.id_facture, content);
      } else if (event.type === "Entreposage") {
        await this.handleEntreposage(event.id_facture, content);
      }

      this.verif = [];
    }
  }


  initShow(){
    this.showTransport=false
    this.showFlux=false
    this.showEntreposage=false
  }


  async handleTransport(id_facture, content?) {
    this.showTransport = true
    let qte = 0;
    let price = 0;
    let prix = 0;
    let volume = 0;
    let priceDelivery = 0;
    let Qte = 0;
    this.HT = 0;
    this.TTC = 0;
    this.TVA = 0;

    // Convert subscribe to Promise
    const selectedLines = await this.listconducteurService.getFacture(id_facture).toPromise();

    selectedLines.forEach(line => {
      if (line.type_ligne === 'Transfert administratif ZEN Group-Magasin ou dépôt' || line.type_ligne === 'Transfert Administratif inter magasin') {
        prix += line.prix_tot;
        qte++;
      } else if (line.type_ligne === 'Transfert administratif technique et matériel informatique' || line.type_ligne === 'Livraison fourniture') {
        price += line.prix_tot;
        Qte += line.quantite;
      } else {

        volume += line.adapted_volume != null ? line.adapted_volume : line.volume;
          priceDelivery += line.prix_tot;
      
      }
    });

    await this.generatePref(prix, price, qte, Qte, volume, priceDelivery);
    await this.calculateInvoice(content);
  }

  async handleFlux(id_facture, content?) {
    this.showFlux=true

    let qteMP = 0, prixMP = 0;
    let qtePF = 0, prixPF = 0;
    let qteFournitureMP = 0, prixFournitureMP = 0;
    let qteFourniturePF = 0, prixFourniturePF = 0;


    let puMP = 0,puPF=0
    let puFourniturePF =0,puFournitureMP =0
    this.HT = 0;
    this.TVA = 0;
    this.TTC = 0;

    // Convert subscribe to Promise
    const selectedLines = await this.fluxService.getFluxByidFacture(id_facture).toPromise();

    selectedLines.forEach(line => {
        if (line.type === 'MP') {
            prixMP += line.prix_tot;
            qteMP += line.qte;
            if(line.PU>puMP){
              puMP=line.PU
            }
          } else if (line.type === 'PF') {
            prixPF += line.prix_tot;
            qtePF += line.qte;
            if(line.PU>puPF){
              puPF= line.PU
            }
        } else if (line.type === 'MP_F') {
            prixFournitureMP += line.prix_tot;
            qteFournitureMP += line.qte;
            if(line.PU>puFournitureMP){
              puFournitureMP= line.PU
            }

        } else if (line.type === 'PF_F') {
            prixFourniturePF += line.prix_tot;
            qteFourniturePF += line.qte;
            if(line.PU>puFourniturePF){
              puFourniturePF = line.PU
            }
        }
    });

    // Ajouter MP
    if (qteMP > 0) {
        this.pref.push({
            designation: "GESTION DE FLUX MP",
            unite: "ROULEAU",
            volume: parseFloat(qteMP.toFixed(3)),
            prix: (parseFloat(qteMP.toFixed(3)) * puMP).toFixed(3),
            code: "SRV-10108",
            un: "M3",
            pu:puMP
        });
    }

    // Ajouter PF
    if (qtePF > 0) {
        this.pref.push({
            designation: "GESTION DE FLUX PF",
            unite: "pièces",
            volume: parseFloat(qtePF.toFixed(3)),
            prix: (parseFloat(qtePF.toFixed(3)) * puPF).toFixed(3),
            code: "SRV-10005",
            un: "UN",
            pu:puPF

        });
    }

    // Ajouter fourniture MP
    if (qteFournitureMP > 0) {
        this.pref.push({
            designation: "FOURNITURE FLUX MP",
            unite: "m³",
            volume: parseFloat(qteFournitureMP.toFixed(3)),
            prix: (parseFloat(qteFournitureMP.toFixed(3)) * puFournitureMP).toFixed(3),
            code: "FRN-0108",
            un: "M3",
            pu:puFournitureMP

        });
    }

    // Ajouter fourniture PF
    if (qteFourniturePF > 0) {
        this.pref.push({
            designation: "FOURNITURE FLUX PF",
            unite: "pièces",
            volume: parseFloat(qteFourniturePF.toFixed(3)),
            prix: (parseFloat(qteFourniturePF.toFixed(3)) * puFourniturePF).toFixed(3),
            code: "FRN-0112",
            un: "UN",
            pu:puFourniturePF

        });
    }


    this.pref.forEach(item => {
      const prixHT = parseFloat(item.prix);
      this.HT += isNaN(prixHT) ? 0 : prixHT;
    });
  this.HT = parseFloat(this.HT.toFixed(3));
    this.TVA = (this.HT * this.tva) / 100;
    this.TTC = this.HT + this.TVA + this.timbre;

    this.foot = [{
        HT: this.HT.toFixed(3),
        TVA: this.TVA.toFixed(3),
        timbre: this.timbre.toFixed(3),
        TTC: this.TTC.toFixed(3)
    }];

    const partieEntiere = Math.floor(this.TTC);
    const partieDecimale = (this.TTC - partieEntiere).toFixed(3).split('.')[1];

    // Convert to Promise
    this.decEnLettres = await this.incoiceService.convert(partieEntiere).toPromise()
        .then((response: any) => response.enLettres)
        .catch((error: any) => {
            console.error("Error converting total to words", error);
            return '';
        });

    this.montantEnLettres = this.decEnLettres + ` dinars et ${partieDecimale} Millimes `;

    if (content) {
        this.open(content);
    }
}

async handleEntreposage(id_facture, content?) {
  this.showEntreposage=true
  let qteMP = 0, prixMP = 0;
  let qtePF = 0, prixPF = 0;
  let qteFournitureMP = 0, prixFournitureMP = 0;
  let qteFourniturePF = 0, prixFourniturePF = 0;


    let totalVolumeMP = 0,totalVolumePF=0
    let totalVolumeFourniturePF =0,totalVolumeFournitureMP =0

    let puMP = 0,puPF=0
    let puFourniturePF =0,puFournitureMP =0

  this.HT = 0;
  this.TTC = 0;
  this.TVA = 0;

  // Convert subscribe to Promise
  const selectedLines = await this.entreposageService.getEntreposageByIdfacture(id_facture).toPromise();

  selectedLines.forEach(line => {
      if (line.type === 'MP') {
          prixMP += line.prix_tot;
          qteMP += line.qte;
          totalVolumeMP +=line.volume_history
          if(line.PU_Month>puMP){
            puMP=line.PU_Month
          }
      } else if (line.type === 'PF') {
          prixPF += line.prix_tot;
          qtePF += line.qte;
          totalVolumePF +=line.volume_history

          if(line.PU_Month>puPF){
            puPF=line.PU_Month
          }

      } else if (line.type === 'MP_F') {
          prixFournitureMP += line.prix_tot;
          qteFournitureMP += line.qte;
          totalVolumeFournitureMP +=line.volume_history

          if(line.PU_Month>puFournitureMP){
            puFournitureMP= line.PU_Month
          }

      } else if (line.type === 'PF_F') {
          prixFourniturePF += line.prix_tot;
          qteFourniturePF += line.qte;
          totalVolumeFourniturePF +=line.volume_history

          if(line.PU_Month>puFourniturePF){
            puFourniturePF=line.PU_Month
          }

      }
  });

  // Ajouter MP
  if (qteMP > 0) {
      this.pref.push({
          designation: "GESTION ENTREPOSAGE MP",
          unite: "m³",
          volume: parseFloat(qteMP.toFixed(3)),
          prix: ((parseFloat((totalVolumeMP / 300).toFixed(3)) * puMP*10).toFixed(3)),

          code: "SRV-10004",
          un: "M3",
          pu:puMP*10,
          totVolume : (totalVolumeMP/300)

      });
  }

  // Ajouter PF
  if (qtePF > 0) {
      this.pref.push({
          designation: "GESTION ENTREPOSAGE PF",
          unite: "m³",
          volume: parseFloat(qtePF.toFixed(3)),
          prix: ((parseFloat((totalVolumePF / 30).toFixed(3)) * puPF).toFixed(3)),
          code: "SRV-10062",
          un: "UN",
          pu:puPF,
          totVolume : (totalVolumePF/30)

      });
  }

  // Ajouter fourniture MP
  if (qteFournitureMP > 0) {
      this.pref.push({
          designation: "FOURNITURE ENTREPOSAGE MP",
          unite: "Rouleau",
          volume: parseFloat(qteFournitureMP.toFixed(3)),
          prix: ((parseFloat((totalVolumeFournitureMP / 30).toFixed(3)) * puFournitureMP).toFixed(3)),
          code: "FRN-0113",
          un: "M3",
          pu:puFournitureMP,
          totVolume : (totalVolumeFournitureMP/30)

      });
  }

  // Ajouter fourniture PF
  if (qteFourniturePF > 0) {
      this.pref.push({
          designation: "FOURNITURE ENTREPOSAGE PF",
          unite: "m³",
          volume: parseFloat(qteFourniturePF.toFixed(3)),
          prix: ((parseFloat((totalVolumeFourniturePF / 30).toFixed(3)) * puFourniturePF).toFixed(3)),
          code: "FRN-0114",
          un: "UN",
          pu:puFourniturePF,
          totVolume : (totalVolumeFourniturePF/30)

      });
  }

  this.pref.forEach(item => {
    const prixHT = parseFloat(item.prix);
    this.HT += isNaN(prixHT) ? 0 : prixHT;
  });
  this.HT = parseFloat(this.HT.toFixed(3));
  this.TVA = (this.HT * this.tva) / 100;
  this.TTC = this.HT + this.TVA + this.timbre;

  this.foot = [{
      HT: this.HT.toFixed(3),
      TVA: this.TVA.toFixed(3),
      timbre: this.timbre.toFixed(3),
      TTC: this.TTC.toFixed(3)
  }];

  const partieEntiere = Math.floor(this.TTC);
  const partieDecimale = (this.TTC - partieEntiere).toFixed(3).split('.')[1];

  this.decEnLettres = await this.incoiceService.convert(partieEntiere).toPromise()
      .then((response: any) => response.enLettres)
      .catch((error: any) => {
          console.error("Error converting total to words", error);
          return '';
      });

  this.montantEnLettres = this.decEnLettres + ` dinars et ${partieDecimale} Millimes `;

  console.log(this.pref);

  if (content) {
      this.open(content);
  }
}




  // Méthode pour générer les pref (transfert administratif)
  generatePref(prix, price, qte, Qte, volume, priceDelivery) {
    if (prix !== 0) {
      this.pref.push({
        designation: "Transfert administratif",
        unite: "colis",
        volume: qte,
        prix: prix.toFixed(3),
        code:"SRV-10371",
        un:"COL"
      });
    }
    if (price !== 0) {
      this.pref.push({
        designation: "Transfert administratif technique et matériel informatique",
        unite: "colis",
        volume: Qte,
        prix: price.toFixed(3),
        code:"SRV-10372",
        un:"COL"
      });
    }
    if (priceDelivery !== 0) {
      this.pref.push({
        designation: "Livraison",
        unite: "m³",
        volume: parseFloat(volume.toFixed(3)),
        prix: parseFloat(priceDelivery.toFixed(3)),
        code:"SRV-10007",
        un:"M3"
      });
    }
 
  }

  // Méthode pour calculer la facture
  async calculateInvoice(content) {
    for (let i = 0; i < this.pref.length; i++) {
      const prixHT = parseFloat(this.pref[i].prix);
      this.HT += isNaN(prixHT) ? 0 : prixHT;
    }

    console.log('Somme des prixHT:', this.HT.toFixed(3));
    this.HT = parseFloat(this.HT.toFixed(3));
    this.timbre = 1;
    this.TVA = (this.HT * this.tva) / 100;
    this.TTC = this.HT + this.TVA + this.timbre;
    this.foot = [{ HT: this.HT.toFixed(3), TVA: this.TVA.toFixed(3), timbre: this.timbre.toFixed(3), TTC: this.TTC.toFixed(3) }];

    const partieEntiere = Math.floor(this.TTC);
    const partieDecimale = (this.TTC - partieEntiere).toFixed(3).split('.')[1];

    await this.incoiceService.convert(partieEntiere).subscribe(
      (response: any) => {
        this.decEnLettres = response.enLettres;
        this.montantEnLettres = this.decEnLettres + ` dinars et ${partieDecimale} Millimes `;
      },
      (error: any) => {
        console.error(error);
      }
    );
    if (content) {
      this.open(content);

    }
  }



  // par défaut m3a el popo uppa 
  open(content) {
    this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      this.closeResult = `Closed with: ${result}`;
    }, (reason) => {
      this.closeResult =
        `Dismissed ${this.getDismissReason(reason)}`;
    });
  }

  // aparament par défaut m3a el pop appa 
  private getDismissReason(reason: any): string {
    this.initShow()

    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }

  }

  findConducteur(event) {
    this.detail = event.data
  };


  // telecharger(){
  //   var data = document.getElementById('facture-content');

  //   html2canvas(data, { allowTaint: true }).then(canvas => {
  //     var HTML_Width = canvas.width;
  //     var HTML_Height = canvas.height;
  //     var top_left_margin = 15;
  //     var PDF_Width = HTML_Width + (top_left_margin * 2);
  //     var PDF_Height = (PDF_Width * 1.5) + (top_left_margin * 2);
  //     var canvas_image_width = HTML_Width;
  //     var canvas_image_height = HTML_Height;

  //     var totalPDFPages = Math.ceil(HTML_Height / PDF_Height) - 1;
  //     canvas.getContext('2d');

  //     const contentDataURL = canvas.toDataURL('image/png', 1.0)
  //      var pdf = new jsPDF('p', 'pt', [PDF_Width, PDF_Height]);

  //     pdf.addImage(contentDataURL, 'JPG', top_left_margin, top_left_margin, canvas_image_width, canvas_image_height);

  //     for (var i = 1; i <= totalPDFPages; i++) {
  //       pdf.addPage();
  //       pdf.addImage(contentDataURL, 'JPG', top_left_margin, -(PDF_Height * i) + (top_left_margin * 4), canvas_image_width, canvas_image_height);
  //     }
  //     pdf.save(`Facture N-${this.code}.pdf`);


  //   });
  // }

  imprimerFacture() {
    const printWindow = window.open('', `${this.code}-${this.nom_client}`);
    printWindow.document.open();
    printWindow.document.write(`
      <html>
        <head>
          <style>
            @media print {
              body {
                font-size: 12pt;
              }
  
              body * {
                visibility: hidden;
              }
  
              #facture-content, #facture-content * {
                visibility: visible;
                margin-top: 20px; /* Ajustez la valeur en pixels selon vos besoins */
              }
  
              img {
                max-width: 100%;
                height: auto;
              }
            }
          </style>
        </head>
        <body>
          <div id="facture-content">
            ${document.getElementById('facture-content').innerHTML}
          </div>
        </body>
      </html>
    `);
    printWindow.document.close();
    // Assurez-vous que le logo est chargé avant l'impression
    const logo = new Image();
    logo.src = 'assets/img/logo.png'; // Ajustez le chemin en conséquence
    logo.onload = () => {
      printWindow.print();
    };
  }


  // sendEmail() {
  //   // Generate the PDF content and send it
  //   this.generateAndSendPdf();
  // }

  // generateAndSendPdf() {
  //   const pdfContent = document.getElementById('facture-content');

  //   const scale = 2; // Adjust the scale to improve resolution
  //   const options = {
  //     scale: scale,
  //     useCORS: true, // Enable cross-origin resource sharing
  //   };

  //   html2canvas(pdfContent, options).then(canvas => {
  //     // Convert the canvas to a base64 PNG string
  //     const base64PNG = canvas.toDataURL('image/png');

  //     // Send the base64 PNG string to the server
  //     this.incoiceService.sendEmailInvoice(base64PNG).subscribe(response => {
  //       console.log(response);
  //       // You can handle the response as needed
  //     });
  //   });
  // }


  updateSelectedFactures() {
    this.source.forEach(facture => {
      if (facture.isChecked) {
        if (!this.selectedFactures.some(selected => selected.id_facture === facture.id_facture)) {
          this.selectedFactures.push(facture);
        }
      } else {
        this.selectedFactures = this.selectedFactures.filter(selected => selected.id_facture !== facture.id_facture);
      }
    });

    console.log(this.selectedFactures);
  }



  async sendToSage() {
    try {
      const jsonData = []; // Array to hold the JSON objects for each facture
  
      // Promise.all for concurrent fournisseur fetching
      const fournisseurPromises = this.selectedFactures.map(async (selectedFacture) => {
        const fournisseur = await this.fournisseurService.findFournisseurById(selectedFacture.id_client).toPromise();
        const Client = fournisseur;
  
        let dataToSendBase = {
          id_facture: selectedFacture.id_facture,
          NTAXE: selectedFacture.type === "transport" ? "SRED" : "NOR",
          ref: selectedFacture.code,
          site: 'ZLG00',
          type: 'FAC',
          client: Client.code_sageX3 || null,
          devise: 'TND',
          timbre: selectedFacture.timbre,
          date: this.formatDateJJMMAAAA(selectedFacture.invoice_date),
          client_type: Client.code_sageX3.length === 3 ? 2 : 1,
        };
  
        this.pref = [];  // Reset pref for each facture
  
        // Process facture based on type
        if (selectedFacture.type === "transport") {
          await this.handleTransport(selectedFacture.id_facture);
        } else if (selectedFacture.type === "Entreposage") {
          await this.handleEntreposage(selectedFacture.id_facture);
        } else if (selectedFacture.type === "flux") {
          await this.handleFlux(selectedFacture.id_facture);
        }
  
        // Create invoice data for each item
        for (let index = 0; index < this.pref.length; index++) {
          const item = this.pref[index];
          let dataToSend = {
            ...dataToSendBase, // Spread the common fields
            items: [{
              article: item.designation,
              unite: item.un,
              qte: (typeof item.volume === 'number' ? item.volume : parseFloat(item.volume)).toFixed(3).replace('.', ','),
              ht: (typeof item.prix === 'number' ? item.prix : parseFloat(item.prix)).toFixed(3).replace('.', ','),
              code: item.code
            }],
          };
          jsonData.push(dataToSend);
        }
      });
  
      // Wait for all fournisseur data fetches to complete
      await Promise.all(fournisseurPromises);
  
      console.log("Data to send:", jsonData);
  
      // Send data to Sage X3
      this.incoiceService.sendToSage(jsonData).subscribe(res => {
        this.toastr.success(res.message);
        this.loadData(this.currentPage);
        this.selectedFactures = [];
      });
  
    } catch (error) {
      console.log(error)
      this.toastr.error("Erreur d'envoi vers SAGE X3");
    }
  }
  



  formatDateJJMMAAAA = (dateString) => {
    const date = new Date(dateString); 
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); 
    const year = date.getFullYear();  

    return `${day}${month}${year}`;
};


async downloadExcel() {
  try {
    let excelContent = `ID\tDate\tNom Depot\tQuantite\tPrix Totale\n`;

    this.detailsInvoice.forEach(row => {
      excelContent += `${row.id}\t${this.replaceSpecialChars(row.date)}\t${this.replaceSpecialChars(row.nom_depot)}\t${row.qte}\t${row.prix_tot}\n`;
    });

    const blob = new Blob([excelContent], { type: 'application/vnd.ms-excel;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'Details_Facture.xls');
    document.body.appendChild(link);
    link.click();
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Une erreur s\'est produite lors de l\'exportation :', error);
  }
}

replaceSpecialChars(str: string): string {
  return str.replace(/[éèô/]/g, (char) => {
      switch (char) {
          case 'é':
          case 'è':
              return 'e';
          case 'ô':
              return 'o';
              case '/':
              return '-';
          default:
              return char;
      }
  });
}



async downloadExcelTransport() {
  try {
    let excelContent = `ID\tDate du voyage\tDepart\tArrivee\tEmplacement\tDate Voyage\tKilometrage\tType de ligne\tQuantite\tPrix total\n`;

    this.detailsInvoice.forEach(row => {
      let quantite = row.volume ? `${row.volume} m³` : `${row.quantite} pcs`;

      excelContent += `${row.id}\t${row.date_voyage}\t${this.replaceSpecialChars(row.nom_depart)}\t${this.replaceSpecialChars(row.nom_arrivee)}\t${this.replaceSpecialChars(row.emplacement)}\t${row.date_voyage}\t${row.kilometrage}\t${this.replaceSpecialChars(row.type_ligne)}\t${quantite}\t${row.prix_tot}\n`;
    });

    const blob = new Blob([excelContent], { type: 'application/vnd.ms-excel;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'Details_Voyage.xls');
    document.body.appendChild(link);
    link.click();
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Une erreur s\'est produite lors de l\'exportation :', error);
  }
}






}


