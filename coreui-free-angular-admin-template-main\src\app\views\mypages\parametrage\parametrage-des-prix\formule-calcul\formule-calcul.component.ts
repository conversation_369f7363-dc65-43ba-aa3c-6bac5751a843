import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SmartContainerComponent } from '../../../../../../shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from '../../../../../../shared/components/smart-table/smart-table.component';
import { ToastrService } from 'ngx-toastr';
import { TableColumn, ActionButton } from '../../../../../../shared/models/table.models';
import { FluxService } from '../../../../../../services/flux.service';
import { DestinationService } from '../../../../../../services/destination.service';
import { FormsModule } from '@angular/forms';
import { ConfirmationDialogService } from '../../../../../../services/confirmation-dialog.service';
import { SmartButtonComponent } from '../../../../../../shared/components/smart-button/smart-button.component';

interface Marque {
  id: number;
  name: string;
}

interface Warehouse {
  id: number;
  name: string;
}

@Component({
  selector: 'app-formule-calcul',
  standalone: true,
  imports: [FormsModule, SmartContainerComponent, SmartTableComponent, SmartButtonComponent, CommonModule],
  templateUrl: './formule-calcul.component.html',
  styleUrls: ['./formule-calcul.component.scss']
})
export class FormuleCalculComponent {
  isPasswordCorrect = false;
  selectedFromMarque: Marque | null = null;
  selectedFromWarehouse: Warehouse | null = null;
  selectedFromType: string | null = null;
  selectedNature: string | null = null;
  selectedToMarque: Marque | null = null;
  selectedToWarehouse: Warehouse | null = null;
  selectedToType: string | null = null;
  selectedSteFacturation: Marque | null = null;
  
  warehousesEmit: Warehouse[] = [];
  warehousesReception: Warehouse[] = [];
  formulaList: any[] = [];
  marques: Marque[] = [];
  loading: boolean = false;

  tableColumns: TableColumn[] = [
    { name: 'invoiced_name', displayName: 'A l\'ordre de', sortable: true, filterable: true },
    { name: 'name_em_wharehouse', displayName: 'Émetteur', sortable: true, filterable: true },
    { name: 'nature', displayName: 'Nature', sortable: true, filterable: true },
    { name: 'name_rec_wharehouse', displayName: 'Récepteur', sortable: true, filterable: true },
  ];

  tableActions: ActionButton[] = [
    {
      label: 'Supprimer',
      icon: 'cil-trash',
      color: 'danger',
      callback: (row: any) => this.deleteFormula(row)
    }
  ];

  constructor(
    private toastr: ToastrService,
    private fluxService: FluxService,
    private destinationService: DestinationService,
    private confirmationDialogService: ConfirmationDialogService
  ) { }

  checkPassword() {
    const input = document.querySelector('input[id="password3"]') as HTMLInputElement;
    const password = input?.value || "";
    if (password === 'zenAdmin') {
      this.isPasswordCorrect = true;
      this.getAllBrand();
      this.getAllFormula();
    } else {
      this.toastr.error('Mot de passe incorrect');
    }
  }

  getAllBrand() {
    this.fluxService.getAllBrand().subscribe(res => {
      this.marques = res;
    });
  }

  onEmitterChange() {
    if (this.selectedFromMarque && this.selectedFromType) {
      this.destinationService.findWarehousesByBrand(this.selectedFromMarque.id, this.selectedFromType)
        .subscribe(res => {
          this.warehousesEmit = res;
          this.selectedFromWarehouse = null; // Reset warehouse selection when type changes
        });
    } else {
      this.warehousesEmit = [];
      this.selectedFromWarehouse = null;
    }
  }

  onReceiverChange() {
    if (this.selectedToMarque && this.selectedToType) {
      this.destinationService.findWarehousesByBrand(this.selectedToMarque.id, this.selectedToType)
        .subscribe(res => {
          this.warehousesReception = res;
          this.selectedToWarehouse = null; // Reset warehouse selection when type changes
        });
    } else {
      this.warehousesReception = [];
      this.selectedToWarehouse = null;
    }
  }

  addFormula() {
    const requiredFields = [
      { name: 'selectedFromMarque', label: 'Marque Émetteur' },
      { name: 'selectedFromWarehouse', label: 'Dépôt Émetteur' },
      { name: 'selectedFromType', label: 'Type de Marque Émetteur' },
      { name: 'selectedNature', label: 'Nature' },
      { name: 'selectedToMarque', label: 'Marque Récepteur' },
      { name: 'selectedToWarehouse', label: 'Dépôt Récepteur' },
      { name: 'selectedToType', label: 'Type de Marque Récepteur' },
      { name: 'selectedSteFacturation', label: 'Société de Facturation' }
    ];

    for (const field of requiredFields) {
      if (!this[field.name as keyof this]) {
        this.toastr.error(`Veuillez choisir ${field.label}.`);
        return;
      }
    }

    const formula = {
      invoiced_for: this.selectedSteFacturation!.id,
      invoiced_name: this.selectedSteFacturation!.name,
      nature: this.selectedNature,
      id_em_brand: this.selectedFromMarque!.id,
      name_em_brand: this.selectedFromMarque!.name,
      type_em: this.selectedFromType,
      id_em_wharehouse: this.selectedFromWarehouse!.id,
      name_em_wharehouse: this.selectedFromWarehouse!.name,
      id_rec_brand: this.selectedToMarque!.id,
      name_rec_brand: this.selectedToMarque!.name,
      type_rec: this.selectedToType,
      id_rec_wharehouse: this.selectedToWarehouse!.id,
      name_rec_wharehouse: this.selectedToWarehouse!.name,
      enabled: true
    };

    this.fluxService.addFormula(formula).subscribe({
      next: () => {
        this.resetForm();
        this.getAllFormula();
        this.toastr.success('Formule ajoutée avec succès.');
      },
      error: () => {
        this.toastr.error('Une erreur est survenue lors de l\'ajout de la formule.');
      }
    });
  }

  private resetForm() {
    this.selectedFromMarque = null;
    this.selectedFromWarehouse = null;
    this.selectedFromType = null;
    this.selectedNature = null;
    this.selectedToMarque = null;
    this.selectedToWarehouse = null;
    this.selectedToType = null;
    this.selectedSteFacturation = null;
    this.warehousesEmit = [];
    this.warehousesReception = [];
  }

  getAllFormula() {
    this.loading = true;
    this.fluxService.getAllFormula().subscribe({
      next: (res) => {
        this.formulaList = res;
      },
      error: () => {
        this.toastr.error('Une erreur est survenue lors de la récupération des formules');
      },
      complete: () => {
        this.loading = false;
      }
    });
  }

  deleteFormula(row: any) {
    this.confirmationDialogService.confirmDelete(`Voulez-vous vraiment supprimer ce type de facturation ${row.nom_facturation} ?`)
      .then((confirmed) => {
        if (confirmed) {
          this.fluxService.deleteFormula(row.id).subscribe({
            next: () => {
              this.toastr.success('Le Type facturation a été supprimé avec succès');
              this.getAllFormula();
            },
            error: () => {
              this.toastr.error('Une erreur est survenue lors de la suppression du type facturation');
            }
          });
        }
      });
  }
}