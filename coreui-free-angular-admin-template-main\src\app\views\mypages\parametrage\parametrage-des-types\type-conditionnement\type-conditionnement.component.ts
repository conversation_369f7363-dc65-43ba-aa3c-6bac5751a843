import { Component } from '@angular/core';
import { SmartButtonComponent } from '../../../../../../shared/components/smart-button/smart-button.component';
import { SmartContainerComponent } from '../../../../../../shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from '../../../../../../shared/components/smart-table/smart-table.component';
import { CommonModule } from '@angular/common';
import { ActionButton, TableColumn } from 'src/shared/models/table.models';
import { ConditionnementService } from '../../../../../../services/conditionnement.service';
import { ToastrService } from 'ngx-toastr';
import { ConfirmationDialogService } from 'src/services/confirmation-dialog.service';
@Component({
  selector: 'app-type-conditionnement',
  imports: [SmartContainerComponent, SmartTableComponent, SmartButtonComponent, CommonModule],
  templateUrl: './type-conditionnement.component.html',
  styleUrl: './type-conditionnement.component.scss'
})
export class TypeConditionnementComponent {
  loading: boolean = true;
  conditionnement: any[] = [];
  showTable: boolean = false;
  tableColumns: TableColumn[] = [
    { name: 'nom_condition', displayName: 'Nom Conditionnement' ,sortable: true,filterable: true},
  ];

  tableaction: ActionButton[] = [
    { icon: 'cil-trash', color: 'danger', tooltip: 'Supprimer', callback: (row: any) => this.deleteConditionnement(row) },
  ];
  
  constructor(private conditionnementService: ConditionnementService, 
    private toastr: ToastrService,
    private confirmDialogService: ConfirmationDialogService
  
  ) { }



  addConditionnement() {
    const input = document.querySelector('input[name="typeConditionnement"]') as HTMLInputElement;
    let typeConditionnement = input?.value || "";
    console.log(typeConditionnement);
    if (typeConditionnement !== "") {
      this.conditionnementService.addConditionType(typeConditionnement).subscribe(
        {
          next: (res) => {
            this.toastr.success('Le Type conditionnement sauvegarde avec succès');
            input.value = "";
            typeConditionnement = "";
            if (this.showTable) {
              this.loadConditionnement();
            } 

          },
          error: (error) => {
            console.log(error)
            if (error.status === 500 && error.error && error.error.message.includes("ER_DUP_ENTRY")) {
              this.toastr.error('Le Type conditionnement existe déjà');
            } else {
              console.log("Une erreur inattendue s'est produite.");
            }
          }
        }
      );
    }
    else {
      this.toastr.error('Inseré le type à ajouté');

    }
  }
  deleteConditionnement(row: any): void {
    this.confirmDialogService.confirmDelete(`voulez-vous vraiment supprimer ce type de conditionnement ${row.nom_conditionnement} ?`)
    .then((res) => {
      if (res) {
        this.conditionnementService.deleteConditionType(row.id).subscribe({
          next: (res) => {
            this.toastr.success('Le Type conditionnement supprimé avec succès');
            this.loadConditionnement();
          },
          error: (error) => {
            console.log(error)
            this.toastr.error('Une erreur est survenue lors de la suppression du type conditionnement');
          }
        });
      }
    });
  }

  showTableConditionnement() {
    this.showTable = true;
    this.loading = true;
    this.loadConditionnement();
  }


  loadConditionnement() {
    
    this.conditionnementService.getAllConditionTypes().subscribe({
      next: (res:any) => {
        this.conditionnement = res;
        console.log(this.conditionnement);
      },
      error: (error:any) => {
        this.toastr.error('Une erreur est survenue lors de la récupération des conditions');
        console.log(error)
      },
      complete: () => {
        this.loading = false;
      }
    });
  }

}
