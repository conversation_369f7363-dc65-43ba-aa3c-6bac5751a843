import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};

@Injectable({
  providedIn: 'root'
})
export class CircuitService {
  private apiURL = environment.apiURL;

  constructor(private http: HttpClient) { }

  /**
   * Add new circuit
   * @param circuitData - Circuit data
   * @returns Observable with creation result
   * @originalName addCircuit
   */
  addCircuit(circuitData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'circuit', circuitData, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Get all circuits
   * @returns Observable with circuits list
   * @originalName getAllCircuits
   */
  getAllCircuits(): Observable<any[]> {
    return this.http.get<any[]>(this.apiURL + 'circuit', httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Adjust voyage
   * @param adjustData - Adjustment data
   * @returns Observable with adjustment result
   * @originalName adjustVoyage
   */
  adjustVoyage(adjustData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'circuit/adjustVoyage', adjustData, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Update circuit by ID
   * @param id - Circuit ID
   * @param circuitData - Updated circuit data
   * @returns Observable with update result
   * @originalName updateCircuitById
   */
  updateCircuitById(id: any, circuitData: any): Observable<any> {
    const url = `${this.apiURL}circuit/${id}`;
    return this.http.put(url, circuitData, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Handle HTTP errors
   * @param error - Error object
   * @returns Observable error
   */
  private handleError(error: any): Observable<never> {
    console.error('An error occurred:', error);
    return throwError(() => new Error(error.message || 'Server error'));
  }
  
}
