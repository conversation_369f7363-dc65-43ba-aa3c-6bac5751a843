import { ComponentFixture, TestBed } from '@angular/core/testing';

import { InspectionColisDetailsModalComponent } from './inspection-colis-details-modal.component';

describe('InspectionColisDetailsModalComponent', () => {
  let component: InspectionColisDetailsModalComponent;
  let fixture: ComponentFixture<InspectionColisDetailsModalComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [InspectionColisDetailsModalComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(InspectionColisDetailsModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
