import { Component, OnInit,ViewChild,TemplateRef } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CommentService } from 'app/services/commentaire.service';
import { FournisseurService } from 'app/services/fournisseur.service';
import { LigneCmdService } from 'app/services/ligneCmd.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-to-be-invoiced',
  templateUrl: './to-be-invoiced.component.html',
  styleUrls: ['./to-be-invoiced.component.scss']
})
export class ToBeInvoicedComponent implements OnInit {
  @ViewChild('popupTemplate', { static: true }) popupTemplate: TemplateRef<any>;
  @ViewChild('details', { static: true }) details: TemplateRef<any>;

  ligneTable :any=[]
  editingItem: any = null; // Element en cours d'édition
  id_client: any
  clientList: any = []
  modalRef : any
  nouveauCommentaire=""
  id_Comment
  commentList : any =[]
  selectedItem: any;
  constructor( private ligneCmdService : LigneCmdService,
   private fournisseurService :FournisseurService,
   private commentService : CommentService,
   private modalService: NgbModal,


   private toastr : ToastrService) { }

async  ngOnInit() {

  await this.ligneCmdService.findAllAdjusted().subscribe(res=>{
    this.ligneTable=res
    console.log(this.ligneTable)
  })

  this.fournisseurService.getAllFournisseur()
      .toPromise()
      .then((res) => {
        this.clientList = res;
      })
      .catch((error) => {
        // Handle the error if needed
        console.error('Error while fetching fournisseurs:', error);
      });


  }

  toggleEdit(item: any) {
    if (this.editingItem) {
      // Si un élément est déjà en cours d'édition, annulez l'édition
      this.editingItem.editing = false;
    }

    item.editing = !item.editing;
    this.editingItem = item;
  }

  cancelEdit(item: any) {
    item.editing = false;
    this.editingItem = null;
  }

  async saveEdit(item: any) {
    console.log(item)
    item.editing = false;
    this.editingItem = null;

    const data = {
      id: item.id,
      id_client: this.id_client.id
    }
    
    try {
      // Appel de la fonction de mise à jour avec gestion des erreurs
      await this.ligneCmdService.updateClient(data);
      this.toastr.success("Mise à jour avec succes ")
      // Le code ici sera exécuté si la mise à jour réussit
      console.log('Mise à jour réussie !');
      item.nom_client = this.id_client.nom_fournisseur; // Assurez-vous que la propriété correcte est utilisée
      this.id_client = ""
    } catch (error) {
      // Le code ici sera exécuté en cas d'erreur
      this.toastr.error("Erreur de mise à jour")
      this.id_client = ""

      console.error('Erreur lors de la mise à jour :', error);
    }
    // Logique pour enregistrer les modifications dans la base de données ou ailleurs
  }


  async ToBeInvoiced(item) {
    console.log(item);
  if(item.prix_tot==0){
    this.toastr.error("Le prix ne peut pas etre null")
    return
  }
    try {
      await this.ligneCmdService.updateStatusAfacture(item.id);
      // Si la promesse est résolue sans erreur, affiche un message de succès
      this.toastr.success('La mise à jour du statut a été effectuée avec succès');
      // Désactiver le bouton "Facturé" pour cet article spécifique
      item.isFactured = true;
    } catch (error) {
      // Si une erreur est levée, affiche un message d'erreur avec Toastr
      this.toastr.error('Une erreur est survenue lors de la mise à jour du statut : ' + error.message);
    }
  }

  async openComment(id: number) {
    try {
      // Réinitialiser le nouveau commentaire et récupérer les commentaires associés à l'identifiant spécifié
      this.nouveauCommentaire = "";
      this.commentList = await this.commentService.findCommentsByLigne(id);
  
      // Afficher les commentaires récupérés dans la console pour le débogage
      console.log("Commentaires récupérés:", this.commentList);
  
      // Ouvrir le modal avec les commentaires récupérés
      this.id_Comment = id;
      this.modalRef = this.modalService.open(this.popupTemplate, {
        ariaLabelledBy: 'modal-basic-title',
        windowClass: 'custom-modal-style' // Utilisez la classe CSS spécifique définie dans votre fichier SCSS de composant
      });
    } catch (error) {
      // Gérer les erreurs éventuelles lors de la récupération des commentaires
      console.error("Erreur lors de la récupération des commentaires:", error);
    }
  }
  
  
  
  async envoyerCommentaire() {
    if (this.nouveauCommentaire) {
      const data = {
        id_ligne: this.id_Comment,
        value: this.nouveauCommentaire,
        id_author: sessionStorage.getItem("iduser")
      };
  
      console.log(data);
  
      try {
        // Ajoutez le commentaire
        const response = await this.commentService.addComment(data);
        console.log("Succès:", response);
        this.toastr.success("Commentaire Envoyer");
  
        // Réinitialisez le nouveau commentaire
        this.nouveauCommentaire = "";
         // Rechargez la liste des commentaires après l'ajout
        this.commentList = await this.commentService.findCommentsByLigne(this.id_Comment);
      } catch (error) {
        this.toastr.error("Problème de connexion");
        console.error("Erreur:", error);
        // Gérer l'erreur ici, si nécessaire
      }
    }
  
  
  
  }

  truncateComment(commentaire: string): string {
    const maxLength = 15; // Limite de longueur du commentaire
    if (commentaire.length > maxLength) {
        return commentaire.substring(0, maxLength) + '...'; // Retourne les premiers maxLength caractères avec '...' ajouté à la fin
    } else {
        return commentaire; // Retourne le commentaire complet s'il est inférieur ou égal à la limite
    }
}

openDetails(item: any) {
  this.selectedItem = item; // Assigner l'élément sélectionné à selectedItem
  this.modalRef = this.modalService.open(this.details, {
    ariaLabelledBy: 'modal-basic-title',
    windowClass: 'custom-modal-style'
  });
}
dismissModal() {
  if (this.modalRef) {
    this.modalRef.dismiss(); // Ferme proprement le modal
  }
}



  
  
  

}














