import { Component, OnInit } from '@angular/core';
import { id } from '@swimlane/ngx-charts/release/utils';
import { RegisterServiceService } from 'app/services/register-service.service';
import { data } from 'app/shared/data/smart-data-table';
import { NgbModal, ModalDismissReasons }from '@ng-bootstrap/ng-bootstrap';
import { ClientDirIndirComponent } from '../client-dir-indir/client-dir-indir.component';
import { ToastrService } from 'ngx-toastr';

import { DomSanitizer } from '@angular/platform-browser';
import { catchError } from 'rxjs/operators';


@Component({
  selector: 'app-list-user',
  templateUrl: './list-user.component.html',
  styleUrls: ['./list-user.component.scss'],
  providers: []
})
export class ListUserComponent implements OnInit {

  // this.clientSettings = this.loadsettings();
  // statut : string ; 
  clientSelected
  affectedRows: number;
  source = [];
  clientSource = [];
  list_client: []
  closeResult = '';
  detail = 0;
  idExp
  imagePath
  constructor(private _sanitizer: DomSanitizer, private toastr: ToastrService, private listuserService: RegisterServiceService, private modalService: NgbModal) {
  }

  ngOnInit() {
    this.clientSettings = this.loadsettings()
    this.listuserService.findAllExp().subscribe(data => {
      this.source = data;
      //this.image=data[0].image_cin
      // btoa(String.fromCharCode.apply(null, new Uint8Array(data[0].image_cin)));
    })
  }

  clientSettings: Object = {
    hideSubHeader: true,
    actions: {
      add: false,
      edit: false,
      columnTitle: "",
    },
    delete: {
      confirmDelete: true,
    },
    pager: {
      display: false,
    },
    noDataMessage: "Chargement, veuillez patienter..",
  };

  settings = {
    actions: {
      add: false,
      edit: false,
      custom: [
        {
          name: 'validation user',
          title: '<i title="valider "   class="ft-check check font-medium-1 mr-2"></i>'
        },
        {
          name: 'list Clients',
          title: '<i title="Clients"> <label class="danger">Clients</label></i>'
        },
        {
          name: 'detail user',
          title: '<i title="valider "  class="bi bi-plus"></i>'
        },
        {
          name: 'voir détails',
          type: "custom",
          class: "btn btn-danger",
          title: '<i title="detail"> <label class="danger">Détails</label></i>'
        },
      ],
    },
    columns: {
      nom: {
        title: 'Nom'
      },
      prenom: {
        title: 'Prénom'
      },
      type_utilisateur: {
        title: 'type Utilisateur '
      },
      statut: {
        title: 'Statut',
      },
     
      // image_cin: {
      //   title: 'Image CIN',
      //   type: 'html',
      //   valuePrepareFunction: (picture: string) => {

      //     return `<img width="50px" width:"50px" src="https://zen-logistic.com//uploads//` + picture + `" />`;
      //   },
      // },
    },
    // attr : les lignes dans le tableau 
    attr: {
      class: "table table-responsive"
    },


    // l'ajout d'un bouton pour l'edit 
    edit: {
      editButtonContent: '<i class="ft-edit-2 info font-medium-1 mr-2"></i>'
    },
    // l'ajout d'un bouton pour la suppression 

    delete: {
      confirmDelete: true,
      deleteButtonContent: '<i class="ft-x danger font-medium-1 mr-2"></i>',
    }


  };

  getClientList() {
    this.listuserService.getAllClients().subscribe(res => this.list_client = res)
  }
  //  For confirm action On Delete
  onDeleteConfirm(event) {
    console.log("**********************", event)
    if (window.confirm('Etes-vous sûr que vous voulez supprimer cet utilisateurs ?')) {
      this.listuserService.disableClient(event.data.id).subscribe(data => {

        event.confirm.resolve();
        this.delateUser(event);
      }), error => { }

    } else {
      event.confirm.reject();
    }
  }
  openClientModale(idExp, clientModale) {
    this.idExp = idExp
    if (this.list_client == undefined) this.getClientList()
    this.getClientsByExp()
    clientModale
    this.open(clientModale);
  }

  confirmUser(event, c, cm) {
    // if (event.action == 'list Clients') {
    //   //ovrir modal 
    //   this.openClientModale(event.data.id, cm)

    // } else

    if (event.action == 'voir détails') {
      this.onUserRowSelect(event, c)
    }

    else {
      if (window.confirm('Etes-vous sûr que vous voulez activer cet expéditeur ?')) {

        if (event.data.statut == "activé") {
          this.listuserService.update({
            "statut": "activé",
            "client_direct": event.data.client_direct
          }, event.data.id).subscribe(data => {
            this.toastr.success('L expéditeur est activé avec succès ', 'Félécitation!', { closeButton: true })
            this.listuserService.findAllExp().subscribe(data => {
              this.source = data;
            })
            //console.log(data)
          }), error => { }
        }
        else if (event.data.client_direct != "") {

          this.listuserService.update({
            "statut": "activé",
            "client_direct": event.data.client_direct
          }, event.data.id).subscribe(data => {
            this.toastr.success('L expéditeur est activé avec succès', 'Félécitation!', { closeButton: true })

            this.listuserService.findAllExp().subscribe(data => {
              this.source = data;

            })
            //console.log(data)
          }), error => { }
        }
      }
      else {
        event.confirm.reject();
      }
    }
  };

  getClientsByExp() {
    this.listuserService.getClientByExp(this.idExp).then(res => {
      console.log(res)
      this.clientSource = res
    }).catch(err => {
      console.log(err)
      if (err.error.message.includes("Not found Customer with id")) {
        this.clientSource = []
      }
    })
  }

  add_Client() {
    if (this.clientSelected == undefined) return
    this.listuserService.setclientToExp(this.clientSelected, this.idExp).subscribe(res => { this.getClientsByExp() }
    )
  }

  onDeleteClient(event) {
    console.log(event)
    if (window.confirm('Etes-vous sûr que vous voulez supprimer cet expéditeur ?')) {
      this.listuserService.disableClient(event.data.id).subscribe(res => {
        this.getClientsByExp()
      })
    } else {
      event.confirm.reject();
    }
  }

  delateUser(event) {
    // console.log(event.data.id) ; 

  }

  findUser(event) {
    this.listuserService.findOne(event.data.id).then(data => {
      this.detail = data
      //console.log(event.data) ; 
    }), error => { }
  };



  onUserRowSelect(event, content): void {
    // console.log(event.data); 
    this.open(content);
    this.findUser(event);

  }


  open(content) {
    this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title', size: 'sm' }).result.then((result) => {
      this.closeResult = `Closed with: ${result}`;
    }, (reason) => {
      this.closeResult =
        `Dismissed ${this.getDismissReason(reason)}`;
    });
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  loadsettings() {
    return {
      hideSubHeader: true,
      actions: {
        add: false,
        edit: false,
        delete: {
          confirmDelete: true,
        },
        columnTitle: "Action"

      },
      delete: {
        confirmDelete: true,
      },
      pager: {
        display: false,
      },
      columns: {
        nom: {
          title: "nom",
        },
        prenom: {
          title: "prenom",
        },
        noDataMessage: "Chargement, veuillez patienter..",
      }
    }
  }





}







