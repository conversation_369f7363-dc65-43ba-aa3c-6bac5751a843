import { Routes } from "@angular/router";
import { authGuard } from "../../../../../guards/auth.guard";
import { roleGuard } from "../../../../../guards/role.guard";

export const routes:Routes = [
    {
        path:'chef-client',
        loadComponent: () => import('./affectation-client-to-chef/affectation-client-to-chef.component').then((m) => m.AffectationClientToChefComponent),
        canActivate: [authGuard, roleGuard],
        data: {
            title: 'Parametrage des types',
            roles: ['SuperAdmin', 'Administrateur']
        }
    },
    {
        path:'dest-client',
        loadComponent: () => import('./affectation-dest-to-user/affectation-dest-to-user.component').then((m) => m.AffectationDestToUserComponent),
        canActivate: [authGuard, roleGuard],
        data: {
            title: 'Parametrage des types',
            roles: ['SuperAdmin', 'Administrateur']
        }  
    }
]