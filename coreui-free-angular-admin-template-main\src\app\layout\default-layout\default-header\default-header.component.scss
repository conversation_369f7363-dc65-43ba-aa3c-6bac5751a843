// Notification dropdown styles
.notification-dropdown {
  .dropdown-item {
    &:hover {
      background-color: var(--cui-dropdown-link-hover-bg, #f8f9fa);
    }

    &.py-3 {
      padding-top: 0.75rem !important;
      padding-bottom: 0.75rem !important;
    }
  }

  .border-bottom {
    border-bottom: 1px solid var(--cui-border-color, #dee2e6) !important;

    &:last-child {
      border-bottom: none !important;
    }
  }
}

// Notification badge positioning
.position-relative {
  .badge {
    &.top-0.start-100 {
      top: -0.25rem !important;
      left: calc(100% - 0.25rem) !important;
    }
  }
}

// Notification bell icon hover effect
a[cNavLink] {
  &:hover {
    svg[cIcon] {
      color: var(--cui-primary, #321fdb);
    }
  }
}

// Responsive adjustments
@media (max-width: 767.98px) {
  .notification-dropdown {
    width: 300px !important;
    max-height: 350px !important;
  }
}