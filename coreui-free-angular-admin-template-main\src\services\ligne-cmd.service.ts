import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../environments/environment';
import { Observable } from 'rxjs';
const httpOptions = {
  headers: new HttpHeaders({
    'Content-Type': 'application/json',
    'Authorization': 'Basic ' + btoa('med:123456')
  })
};
@Injectable({
  providedIn: 'root'
})
export class LigneCmdService {
  private apiURL = environment.apiURL;

  constructor(private http: HttpClient) { }

  CountVolumeParVoyage(id_camion: any, id_conducteur: any, date_voyage: any): Observable<any> {
    return this.http.get<any>(this.apiURL + `volumeBytrip/${id_camion}/${id_conducteur}/${date_voyage}`, httpOptions);
  }

  findLigneDelivredByDriver(data: any): Observable<any> {

    return this.http.post<any>(this.apiURL + `findLigneDelivredByDriver`, data, httpOptions);
  }
  findLigneDelivredByTruck(data: any): Observable<any> {
    return this.http.post<any>(this.apiURL + `findDelivredByTruck`, data, httpOptions);
  }

  findLignesByIdVoyage(id: any): Observable<any> {

    return this.http.get<any>(this.apiURL + `findLignesByIdVoyage/${id}`, httpOptions);
  }

  findAllLivred(): Observable<any> {

    return this.http.get<any>(this.apiURL + `findAllLivred`, httpOptions);
  }

  findAllAdjusted(): Observable<any> {

    return this.http.get<any>(this.apiURL + `findAllAdjusted`, httpOptions);
  }

  updateClient(data: any): Observable<any> {
    const url = `${this.apiURL}ptchargement/client/${data.id}`;
    return this.http.put(url, data, httpOptions)
  }
  updatePanne(id: number): Observable<any> {
    const url = `${this.apiURL}ptchargement/updatePanne/${id}`;

    return this.http.put<any>(url, null, httpOptions)
  }

  findReservedByIdUser(id: any): Observable<any> {

    return this.http.get<any>(this.apiURL + `ligneCmdByDemand/${id}`, httpOptions);
  }
  findExpediedByIdUser(id: any): Observable<any> {

    return this.http.get<any>(this.apiURL + `ligneCmdExpByDemand/${id}`, httpOptions);
  }
  findDeliveredByIdUser(id: any): Observable<any> {
    return this.http.get<any>(this.apiURL + `ligneCmdLivredByDemand/${id}`, httpOptions);
  }

  updateStatusAfacture(id: any): Observable<any> {
    const url = `${this.apiURL}updateStatusToBeInvoiced/${id}`;
    return this.http.put(url, null, httpOptions);
  }
  fixPrice(data: any): Observable<any> {
    return this.http.post<any>(`${this.apiURL}fixPrice`, data, httpOptions);
  }
  findReservedByChef(idChef:any): Observable<any> {
    return this.http.get<any>(this.apiURL + `findReservedByChef/${idChef}`,httpOptions);
  }
  findExpediedByChef(idChef:any): Observable<any> {      
    return this.http.get<any>(this.apiURL + `findExpediedByChef/${idChef}`,httpOptions);
  }
  findDeliveredByChef(idChef:any): Observable<any> {
    return this.http.get<any>(this.apiURL + `findDeliveredByChef/${idChef}`,httpOptions);
  }
  findLignesByIdToVerification(id:any): Observable<any> {
    return this.http.get<any>(this.apiURL + `findLignesByIdToVerification/${id}`,httpOptions);
  }
  findBySku(data:any): Observable<any> {
    const endpoint = `${this.apiURL}searchBysku`;
    return this.http.post<any>(endpoint, data, httpOptions)
  }
  findLignesByweek(intervale:any): Observable<any> {   
    return this.http.get<any>(this.apiURL + `findLignesByweek/${intervale}`,httpOptions);
  }
  updateStatusCancel(id: number): Observable<any> {
    const url = `${this.apiURL}ptchargement/cancel/${id}`;
    return this.http.put(url,null, httpOptions)
  }


}
