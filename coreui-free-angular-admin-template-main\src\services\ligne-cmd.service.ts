import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../environments/environment';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
const httpOptions = {
  headers: new HttpHeaders({
    'Content-Type': 'application/json',
    'Authorization': 'Basic ' + btoa('med:123456')
  })
};
@Injectable({
  providedIn: 'root'
})
export class LigneCmdService {
  private apiURL = environment.apiURL;

  constructor(private http: HttpClient) { }

  CountVolumeParVoyage(id_camion: any, id_conducteur: any, date_voyage: any): Observable<any> {
    return this.http.get<any>(this.apiURL + `volumeBytrip/${id_camion}/${id_conducteur}/${date_voyage}`, httpOptions)
      .pipe(catchError(this.handleError));
  }

  findLigneDelivredByDriver(data: any): Observable<any> {
    return this.http.post<any>(this.apiURL + `findLigneDelivredByDriver`, data, httpOptions)
      .pipe(catchError(this.handleError));
  }

  findLigneDelivredByTruck(data: any): Observable<any> {
    return this.http.post<any>(this.apiURL + `findDelivredByTruck`, data, httpOptions)
      .pipe(catchError(this.handleError));
  }

  findLignesByIdVoyage(id: any): Observable<any> {
    return this.http.get<any>(this.apiURL + `findLignesByIdVoyage/${id}`, httpOptions)
      .pipe(catchError(this.handleError));
  }

  findAllLivred(): Observable<any> {
    return this.http.get<any>(this.apiURL + `findAllLivred`, httpOptions)
      .pipe(catchError(this.handleError));
  }

  findAllAdjusted(): Observable<any> {
    return this.http.get<any>(this.apiURL + `findAllAdjusted`, httpOptions)
      .pipe(catchError(this.handleError));
  }

  updateClient(data: any): Observable<any> {
    const url = `${this.apiURL}ptchargement/client/${data.id}`;
    return this.http.put(url, data, httpOptions)
  }
  updatePanne(id: number): Observable<any> {
    const url = `${this.apiURL}ptchargement/updatePanne/${id}`;

    return this.http.put<any>(url, null, httpOptions)
  }

  findReservedByIdUser(id: any): Observable<any> {

    return this.http.get<any>(this.apiURL + `ligneCmdByDemand/${id}`, httpOptions);
  }
  findExpediedByIdUser(id: any): Observable<any> {

    return this.http.get<any>(this.apiURL + `ligneCmdExpByDemand/${id}`, httpOptions);
  }
  findDeliveredByIdUser(id: any): Observable<any> {
    return this.http.get<any>(this.apiURL + `ligneCmdLivredByDemand/${id}`, httpOptions);
  }

  updateStatusAfacture(id: any): Observable<any> {
    const url = `${this.apiURL}updateStatusToBeInvoiced/${id}`;
    return this.http.put(url, null, httpOptions);
  }
  fixPrice(data: any): Observable<any> {
    return this.http.post<any>(`${this.apiURL}fixPrice`, data, httpOptions);
  }
  findReservedByChef(idChef:any): Observable<any> {
    return this.http.get<any>(this.apiURL + `findReservedByChef/${idChef}`,httpOptions);
  }
  findExpediedByChef(idChef:any): Observable<any> {      
    return this.http.get<any>(this.apiURL + `findExpediedByChef/${idChef}`,httpOptions);
  }
  findDeliveredByChef(idChef:any): Observable<any> {
    return this.http.get<any>(this.apiURL + `findDeliveredByChef/${idChef}`,httpOptions);
  }
  findLignesByIdToVerification(id:any): Observable<any> {
    return this.http.get<any>(this.apiURL + `findLignesByIdToVerification/${id}`,httpOptions);
  }
  findBySku(data:any): Observable<any> {
    const endpoint = `${this.apiURL}searchBysku`;
    return this.http.post<any>(endpoint, data, httpOptions)
  }
  findLignesByweek(intervale:any): Observable<any> {   
    return this.http.get<any>(this.apiURL + `findLignesByweek/${intervale}`,httpOptions);
  }
  updateStatusCancel(id: number): Observable<any> {
    const url = `${this.apiURL}ptchargement/cancel/${id}`;
    return this.http.put(url,null, httpOptions)
      .pipe(catchError(this.handleError));
  }

  // Dashboard statistics methods
  countValid(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'countValid', httpOptions)
      .pipe(catchError(this.handleError));
  }

  countReserved(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'countReserved', httpOptions)
      .pipe(catchError(this.handleError));
  }

  countExpedied(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'countExpedied', httpOptions)
      .pipe(catchError(this.handleError));
  }

  countDelivred(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'countDelivred', httpOptions)
      .pipe(catchError(this.handleError));
  }

  // Analytics methods
  findRetard(data?: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'findRetard', data || {}, httpOptions)
      .pipe(catchError(this.handleError));
  }

  getProductivity(data?: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'getProductivity', data || {}, httpOptions)
      .pipe(catchError(this.handleError));
  }

  MoyVolumePerVoyage(data?: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'MoyVolumePerVoyage', data || {}, httpOptions)
      .pipe(catchError(this.handleError));
  }

  voyagePerDestination(data?: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'voyagePerDestination', data || {}, httpOptions)
      .pipe(catchError(this.handleError));
  }

  getIndicateurByConducteur(data?: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'getIndicateurByConducteur', data || {}, httpOptions)
      .pipe(catchError(this.handleError));
  }

  /**
   * Handle HTTP errors
   * @param error - Error object
   * @returns Observable error
   */
  private handleError(error: any): Observable<never> {
    console.error('LigneCmdService error occurred:', error);
    return throwError(() => new Error(error.message || 'Server error'));
  }
}
