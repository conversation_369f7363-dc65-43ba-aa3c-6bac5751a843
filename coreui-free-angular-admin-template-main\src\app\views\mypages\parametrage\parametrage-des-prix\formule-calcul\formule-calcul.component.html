<app-smart-container title="Formule De Calcul gestion de Flux">
  <div slot="content">
      <div class="card-body" style="display: flex;" *ngIf="!isPasswordCorrect">
          <input id="password3" type="password" class="form-control" style="width: 300px;" placeholder="Entrez le mot de passe" name="password">
          <app-smart-button [label]="'Valider'" [color]="'primary'" (onClick)="checkPassword()"></app-smart-button>
      </div>

      <div class="card-content" *ngIf="isPasswordCorrect">
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th colspan="3" class="text-center">Facturation</th>
                </tr>
                <tr>
                  <td colspan="3">
                    <select class="form-control" [(ngModel)]="selectedSteFacturation">
                      <option [ngValue]="null" disabled selected>Choisissez Société de Facturation</option>
                      <option *ngFor="let marque of marques" [ngValue]="marque">
                        {{ marque.name }}
                      </option>
                    </select>
                  </td>
                </tr>
                <tr>
                  <th>Émetteur</th>
                  <th>Nature</th>
                  <th>Récepteur</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <!-- Émetteur -->
                  <td>
                    <select class="form-control" [(ngModel)]="selectedFromMarque" (ngModelChange)="onEmitterChange()">
                      <option [ngValue]="null" disabled selected>Choisissez Marque Émetteur</option>
                      <option *ngFor="let marque of marques" [ngValue]="marque">
                        {{ marque.name }}
                      </option>
                    </select>

                    <select class="form-control mt-2" [(ngModel)]="selectedFromType" (ngModelChange)="onEmitterChange()">
                      <option [ngValue]="null" disabled selected>Choisissez type</option>
                      <option [ngValue]="'Magasin'">Magasin</option>
                      <option [ngValue]="'Depot'">Dépôt</option>
                    </select>
                    
                    <select class="form-control mt-2" [(ngModel)]="selectedFromWarehouse" [disabled]="!selectedFromMarque || !selectedFromType">
                      <option [ngValue]="null" disabled selected>Choisissez Dépôt Émetteur</option>
                      <option *ngFor="let warehouse of warehousesEmit" [ngValue]="warehouse">
                        {{ warehouse.name }}
                      </option>
                    </select>
                  </td>

                  <!-- Nature -->
                  <td>
                    <select class="form-control" [(ngModel)]="selectedNature">
                      <option [ngValue]="null" disabled selected>Choisissez Nature</option>
                      <option [ngValue]="'TEM'">Transfert Émis</option>
                      <option [ngValue]="'TRE'">Transfert Reçu</option>
                      <option [ngValue]="'FFO'">Vente En Ligne</option>
                      <option [ngValue]="'BLF'">Livraison Fournisseur</option>
                    </select>
                  </td>

                  <!-- Récepteur -->
                  <td>
                    <select class="form-control" [(ngModel)]="selectedToMarque" (ngModelChange)="onReceiverChange()">
                      <option [ngValue]="null" disabled selected>Choisissez Marque Récepteur</option>
                      <option *ngFor="let marque of marques" [ngValue]="marque">
                        {{ marque.name }}
                      </option>
                    </select>

                    <select class="form-control mt-2" [(ngModel)]="selectedToType" (ngModelChange)="onReceiverChange()">
                      <option [ngValue]="null" disabled selected>Choisissez Type</option>
                      <option [ngValue]="'Magasin'">Magasin</option>
                      <option [ngValue]="'Depot'">Dépôt</option>
                    </select>

                    <select class="form-control mt-2" [(ngModel)]="selectedToWarehouse" [disabled]="!selectedToMarque || !selectedToType">
                      <option [ngValue]="null" disabled selected>Choisissez Dépôt Récepteur</option>
                      <option *ngFor="let warehouse of warehousesReception" [ngValue]="warehouse">
                        {{ warehouse.name }}
                      </option>
                    </select>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- Bouton Ajouter -->
            <div class="text-center">
              <button class="btn btn-primary mt-2" style="color: white !important;" (click)="addFormula()">Ajouter Formule</button>
            </div>

            <app-smart-table 
              [columns]="tableColumns" 
              [data]="formulaList" 
              [actionButtons]="tableActions">
            </app-smart-table>
          </div>
      </div>
  </div>
</app-smart-container>