import { Component, OnInit } from '@angular/core';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';
import { CommonModule } from '@angular/common';
import { DestinationService } from 'src/services/destination.service';
import { ToastrService } from 'ngx-toastr';
import { LocationService } from 'src/services/location.service';
import { FournisseurService } from 'src/services/fournisseur.service';
import { MarchandiseService } from 'src/services/marchandise.service';
import { FormConfig } from 'src/shared/models/form.models';
import { DynamicFormComponent } from 'src/shared/components/dynamic-form/dynamic-form.component';
import { ActionButton, TableColumn } from 'src/shared/models/table.models';
import { FormModalComponent } from '../../../../../../shared/components/form-modal/form-modal.component';
import { ConfirmationDialogService } from 'src/services/confirmation-dialog.service';

@Component({
  selector: 'app-gestion-destination',
  standalone: true,
  imports: [SmartContainerComponent, SmartTableComponent, CommonModule, DynamicFormComponent, FormModalComponent],
  templateUrl: './gestion-destination.component.html',
  styleUrl: './gestion-destination.component.scss'
})
export class GestionDestinationComponent implements OnInit {

  loading = false;
  showTable = false;
  showModal = false;

  regions: any[] = [];
  villes: any[] = [];
  fournisseurs: any[] = [];
  destinations!: any[];
  orderTypes: any[] = [];
  showMatricule: boolean = true;
  showCegid: boolean = false;
  selectedSocieteId: any = {};
  initialData: any = {};
  initialFormModalData: any = {};
  formConfig: FormConfig = {
    title: 'Ajouter Societe',
    fieldGroups: [
      {
        fields: [
          {
            name: 'fournisseurId',
            label: 'Fournisseur',
            type: 'select',
            placeholder: 'Selectionner un fournisseur',
            onChange: (value: any) =>{this.selectedSocieteId = value; this.selectSociete(value)},
            required: true,
            options: {
              objectArray: this.fournisseurs,
              valueAttribute: 'id',
              labelAttribute: 'nom_fournisseur'
            }
          }
        ]
      }
    ],
    fields: [
      {
        name: 'nom_fournisseur',
        label: 'Nom Fournisseur',
        placeholder: 'Nom Societe',
        disabled: true,
        type: 'text',
        required: true,
      },
      {
        name: 'adresse',
        label: 'Adresse Societe',
        placeholder: 'Adresse Societe',
        type: 'text',
        required: true,

      },
      {
        name: 'mat_fisc',
        label: 'Matricule Fiscale',
        disabled: true,
        placeholder: 'MF : *******/X/X/X/***',
        type: 'text',
        hidden: () => !this.showMatricule,
        validation: {
          maxLength: 17,
          pattern: '^([0-9]{7})\/([A-Z])\/([A-Z])\/([A-Z])\/([0-9]{3})$'
        }
      },
      {
        name: 'mail',
        label: 'Email Client',
        placeholder: 'Mail Société facturation',
        type: 'email',
        required: true,
        validation: {
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        }
      },
      {
        name: 'code_cegid',
        label: 'Code Cegid',
        placeholder: 'Code Cegid',
        type: 'text',
        hidden: () => !this.showCegid
      },
      {
        name: 'entreposage',
        label: 'Entreposage',
        type: 'checkbox',

      },
      {
        name: 'flux',
        label: 'Flux',
        type: 'checkbox',

      },
      {
        name: 'sous_traitant',
        label: 'Sous Traitant',
        type: 'checkbox',
      },
      {
        name: 'showFisc',
        label: 'Pas-facturé',
        type: 'checkbox',
      }

    ],
    buttons: [
      {
        label: 'Modifier',
        color: 'primary',
        onClick: (formData:any) => this.saveChanges(formData)
      }
    ]
  }
  tableColumns: TableColumn[] = [
    {
      name: 'nom',
      displayName: 'Nom',
      sortable: true,
    },
    {
      name: 'adresse',
      displayName: 'Adresse',
      sortable: true,
    },
    {
      name: 'phone',
      displayName: 'Phone',
      sortable: true,
    },
    {
      name: 'nom_region',
      displayName: 'Region',
      sortable: true,
    },
    {
      name: 'nom_ville',
      displayName: 'Ville',
      sortable: true,
    }

  ]
  tableActions: ActionButton[] = [
    {
      icon: 'cilPencil',
      tooltip: 'Modifier',
      color: 'warning',
      callback: (row: any) => { console.log("was here"); this.openModal(row) }
    },
    {
      icon: 'cilTrash',
      tooltip: 'Supprimer',
      color: 'danger',
      callback : (row: any) => this.deleteDestination(row)
    },

  ]

  formModalConfig: FormConfig = {
    title: 'Modifier Destination',
    fields: [
      {
        name: 'nom',
        label: 'Nom Locale',
        placeholder: 'Nom Locale',
        required: true,
        type: 'text'

      },
      {
        name: 'adresse',
        label: 'Adresse',
        placeholder: 'Adresse',
        required: true,
        type: 'text'
      },
      {
        name: 'phone',
        label: 'Phone',
        placeholder: 'Phone',
        required: true,
        type: 'text'
      },
      {
        name: 'email',
        label: 'Email',
        placeholder: 'Email',
        required: true,
        type: 'email',
        validation: {
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
        }
      },
      {
        name: 'types',
        label: 'Type Ordre',
        placeholder: 'Type Ordre',
        required: true,
        type: 'multiselect',
        options: {
          objectArray: this.orderTypes,
          valueAttribute: 'id',
          labelAttribute: 'nom_type',
          inline: true,
        }
      },
      {
        name: 'ville_id',
        label: 'Ville',
        placeholder: 'Ville',
        required: true,
        type: 'select',
        onChange: (value: any) => this.loadRegions(value),
        options: {
          objectArray: this.villes,
          valueAttribute: 'id',
          labelAttribute: 'nom_ville',
          inline: true,
        }
      },
      {
        name: 'region_id',
        label: 'Region',
        placeholder: 'Region',
        required: true,
        type: 'select',
        options: {
          objectArray: this.regions,
          valueAttribute: 'id',
          labelAttribute: 'nom_region',
          inline: true,
        }
      },
      {
        name: 'code_erp',
        label: 'Code ERP',
        placeholder: 'Code ERP',
        required: true,
        type: 'number'
      }


    ],
    buttons: [
      { label: 'Annuler', color: 'secondary', onClick: () => this.closeModal() },
      { label: 'Modifier',  color: 'primary', onClick: (formData:any ) => this.modifyDestination(formData) },
    ],
  };




  constructor(
    private destinationService: DestinationService,
    private toastr: ToastrService,
    private locationService: LocationService,
    private fournisseurService: FournisseurService,
    private marchandiseService: MarchandiseService,
    private confirmationDialogService: ConfirmationDialogService
  ) { }

  ngOnInit(): void {
    this.loadFournisseurs();
  }

  openModal(row: any): void {
    this.initialFormModalData = {};
    console.log('row', row);
    if (this.orderTypes.length == 0) {
      console.log("orderTypes is empty");
      this.loadMarchandises();
    }
    if (this.villes.length == 0) {
      console.log("villes is empty");
      this.loadVilles();
    }
    let rowNewTypes: number[] = [];
    row.types.forEach((type: any) => {
      console.log('type', type)
      rowNewTypes.push(type.id);
    });
    const Data= {
      ...row,
      types: rowNewTypes
    }
    console.log("rowNewTypes", rowNewTypes);
    console.log("Afficher new row", Data);

    this.loadRegions(row.ville_id);
    this.initialFormModalData = Data;
    console.log('initialFormModalData', this.initialFormModalData);
    this.showModal = true;
  }
  closeModal(): void {
    this.showModal = false;
  }


  modifyDestination(formData: any) {
    const data = {
      nom_locale: formData.nom,
      adresse: formData.adresse,
      phone: formData.phone,
      email: formData.email,
      id_region: formData.region_id,
      code_erp: formData.code_erp
    };
    this.destinationService.updateDestination(this.initialFormModalData.id, data).subscribe({
      next: (res: any) => {
        this.toastr.success("Destination mise à jour avec succès");
        this.destinationService.updateDestinationTypes(this.initialFormModalData.id, { listeIds: formData.types }).subscribe({
          next: (res: any) => {
            this.toastr.success('Les types ont été mis à jour avec succès.', 'Succès');
            this.selectSociete(this.selectedSocieteId);
            this.closeModal();
          },
          error: (error: any) => {
            console.error('Erreur lors de la mise à jour des types :', error);
            this.closeModal();
            this.toastr.error('Une erreur est survenue lors de la mise à jour des types. Veuillez réessayer.', 'Erreur');
          }
         });
      },
      error: (error: any) => {
        this.toastr.error("Erreur lors de la mise à jour de la destination");
        console.error("Erreur lors de la mise à jour de la destination", error);
        this.closeModal();
      }
    });
    
  }

  deleteDestination (row: any) {
    this.confirmationDialogService.confirmDelete('Voulez-vous vraiment supprimer cette destination ?').then((result: boolean) => {
      this.destinationService.deleteDestination(row.id).subscribe({
        next: (res: any) => {
          this.selectSociete(this.selectedSocieteId);
          this.toastr.success("Destination supprimée avec succès");
        },
        error: (error: any) => {
          this.toastr.error("Erreur lors de la suppression de la destination");
        }
      })
    })
  }

  loadMarchandises(): void {
    this.marchandiseService.getAllOrderTypes().subscribe({
      next: (res: any) => {
        this.orderTypes = res;
        const orderTypeField = this.formModalConfig.fields?.find((f: any) => f.name === 'types');
        if (orderTypeField && orderTypeField.options) {
          orderTypeField.options.objectArray = this.orderTypes;
        }
        console.log('orderTypes', this.orderTypes)
      },
      error: (error: any) => {
        console.log(error);
      }
    })
  }

  loadVilles(): void {
    this.locationService.getAllCities().subscribe({
      next: (res: any) => {
        this.villes = res;
        const villeField = this.formModalConfig.fields?.find((f: any) => f.name === 'ville_id');
        if (villeField && villeField.options) {
          villeField.options.objectArray = this.villes;
        }
        console.log('villes', this.villes)
      },
      error: (error: any) => {
        console.log(error);
      }
    })
  }
  loadRegions(value: any): void {
    this.locationService.findRegionByCityId(value).subscribe({
      next: (res: any) => {
        this.regions = res;
        const regionField = this.formModalConfig.fields?.find((f: any) => f.name === 'region_id');
        if (regionField && regionField.options) {
          regionField.options.objectArray = this.regions;
        }
        console.log('regions', this.regions)
      },
      error: (error: any) => {
        console.log(error);
      }
    })
  }


  saveChanges(formData: any) {
    console.log(formData)
    try {
      const data = {
        nom_locale: formData.nom_locale,
        adresse: formData.adresse,
        mat_fisc: formData.mat_fisc,
        mail: formData.mail,
        sous_traitant: formData.sous_traitant,
        flux: formData.flux,
        entreposage: formData.entreposage,
        code_cegid: formData.code_cegid,
      };

      this.fournisseurService.updateFournisseur(formData.fournisseurId, data).subscribe({
        next: (res: any) => {
          this.toastr.success('Les fournisseurs ont été mis à jour avec succès.', 'Succès');
          this.selectSociete(formData.fournisseurId);
        },
        error: (error: any) => {
          console.error('Erreur lors de la mise à jour des fournisseurs :', error);
          this.toastr.error('Une erreur est survenue lors de la mise à jour des fournisseurs. Veuillez réessayer.', 'Erreur');
        }
      });

    }

    catch (error: any) {
      console.error('Erreur lors de la mise à jour des fournisseurs :', error);
      this.toastr.error('Une erreur est survenue lors de la mise à jour des fournisseurs. Veuillez réessayer.', 'Erreur');
    }

  }

  onFormChange(event: any) {
    this.showMatricule = !event.showFisc;
    this.showCegid = event.entreposage || event.flux;
    console.log(event)
  }
  selectSociete(value: any): void {
    this.loading = true;
    this.showTable = true;
    
    console.log(value);
    this.fournisseurs.forEach((fournisseur: any) => {
      if (fournisseur.id == value) {
        this.initialData = {
          adresse: fournisseur.adresse,
          code_cegid: fournisseur.code_cegid,
          code_sageX3: fournisseur.code_sageX3,
          entreposage: fournisseur.entreposage,
          flux: fournisseur.flux,
          id: fournisseur.id,
          mail: fournisseur.mail,
          mat_fisc: fournisseur.mat_fisc,
          nom_fournisseur: fournisseur.nom_fournisseur,
          sous_traitant: fournisseur.sous_traitant,
          showFisc: fournisseur.mat_fisc == "",
        };
      }
    })
    this.destinationService.findByClientId(value).subscribe({
      next: (res: any) => {
        this.destinations = Object.values(res);
        console.log("destinations", this.destinations)
        this.loading = false;
      },
      error: (error: any) => {
        console.log(error);
      }
    })
    console.log('was here')

  }


  loadFournisseurs(): void {
    this.fournisseurService.getAllFournisseur().subscribe({
      next: (res: any) => {
        this.fournisseurs = res;

        const fournisseurFieldGroup = this.formConfig.fieldGroups?.find(f => f.fields[0].name === 'fournisseurId');
        if (fournisseurFieldGroup && fournisseurFieldGroup.fields[0].options) {
          fournisseurFieldGroup.fields[0].options.objectArray = this.fournisseurs;
        }
        console.log('fournisseurs', this.fournisseurs)
      },
      error: (error: any) => {
        console.log(error);
      }
    })
  }

}
