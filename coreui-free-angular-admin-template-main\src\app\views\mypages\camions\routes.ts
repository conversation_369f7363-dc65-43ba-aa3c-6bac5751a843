import { Routes } from "@angular/router";
import { authGuard } from "../../../../guards/auth.guard";
import { roleGuard } from "../../../../guards/role.guard";

export const routes: Routes = [
    {
        path: '',
        data: {
            title: 'Cam<PERSON>'
        },
        children: [
            {
                path: 'liste-camions',
                loadComponent: () => import('./list-camions/list-camions.component').then((c) => c.ListCamionsComponent),
                canActivate: [authGuard, roleGuard],
                data: {
                    title: 'Liste Camions',
                    roles: ['Cam<PERSON>', 'SuperAdmin', 'Administrateur']
                }
            }
        ]
    }];
