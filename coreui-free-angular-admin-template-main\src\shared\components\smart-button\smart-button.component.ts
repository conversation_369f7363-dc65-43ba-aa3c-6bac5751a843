import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ButtonDirective } from '@coreui/angular';
import { IconModule } from '@coreui/icons-angular';


@Component({
  standalone:true,
  selector: 'app-smart-button',
  imports: [ButtonDirective,CommonModule,IconModule],
  templateUrl: './smart-button.component.html',
  styleUrl: './smart-button.component.scss'
})
export class SmartButtonComponent {
  @Input() color: string = 'primary';
  @Input() icon: string = ''; 
  @Input() label: string = 'Button';
  @Input() disabled: boolean = false;
  @Output() onClick = new EventEmitter<any>();

  onButtonClick() {
    this.onClick.emit();
  }
}
