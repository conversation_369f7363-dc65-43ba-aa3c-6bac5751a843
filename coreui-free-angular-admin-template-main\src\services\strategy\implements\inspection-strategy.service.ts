import { Injectable } from '@angular/core';
import { ServiceStrategy } from '../service-strategy';
import { landingPage } from '../enum/landing-page.enum';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class InspectionStrategyService implements ServiceStrategy{

  constructor() { }
  getLandingPage(): landingPage {
    return landingPage.Inspection
  }
  findReservedLineByUserId(userId: string): Observable<any> {
    return new Observable((observer) => {
      observer.next([this.getLandingPage()]);
      observer.complete();
    })
  }

  findExpediedLineByUserId(userId: string): Observable<any> {
    return new Observable((observer) => {
      observer.next([this.getLandingPage()]);
      observer.complete();
    })
  }

  findDeliveredLineByUserId(userId: string): Observable<any> {
    return new Observable((observer) => {
      observer.next([this.getLandingPage()]);
      observer.complete();
    })
  }
  findEnrgCommandsByUserId(userId: string): Observable<any> {
    return new Observable((observer) => {
      observer.next([this.getLandingPage()]);
      observer.complete();
    })  
  }
  getNonReservedCommands(userId: string): Observable<any> {
    return new Observable((observer) => {
      observer.next([this.getLandingPage()]);
      observer.complete();
    })
  }
  findCommandeToValidate(userId: string): Observable<any>{
    return new Observable()
  }
  findValidCommands(userId: string): Observable<any>{
    return new Observable()
  }

}
