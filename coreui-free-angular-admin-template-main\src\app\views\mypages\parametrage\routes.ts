import {Routes} from '@angular/router';
import { authGuard } from '../../../../guards/auth.guard';
import { roleGuard } from '../../../../guards/role.guard';

export const routes:Routes = [
    {
        path: 'parametrage-types',
        loadComponent: () => import('./parametrage-des-types/types-wrapper/types-wrapper.component').then((m) => m.TypesWrapperComponent),
        canActivate: [authGuard, roleGuard],
        data: {
            title: 'Parametrage des types',
            roles: ['SuperAdmin', 'Administrateur']
        }
    },
    {
        path: 'parametrage-affectation',
        loadChildren: () => import('./parametrage-affectation/routes').then((m) => m.routes),
        canActivate: [authGuard, roleGuard],
        data: {
            title: 'Parametrage des affectations',
            roles: ['SuperAdmin', 'Administrateur']
        }
    },
    {
        path: 'parametrage-prix',
        loadComponent: () => import('./parametrage-des-prix/wrapper-parametrage-prix/wrapper-parametrage-prix.component').then((m) => m.WrapperParametragePrixComponent),
        canActivate: [authGuard, roleGuard],
        data: {
            title: 'Parametrage des prix',
            roles: ['SuperAdmin', 'Administrateur']
        }
    },
    {
        path: 'parametrage-societe-destination',
        loadComponent: () => import('./parametrage-societe-destination/wrapper-societe-destination/wrapper-societe-destination.component').then((m) => m.WrapperSocieteDestinationComponent),
        canActivate: [authGuard, roleGuard],
        data: {
            title: 'Parametrage des societes',
            roles: ['SuperAdmin', 'Administrateur']
        }
    },
    {
        path: 'parametrage-other',
        loadComponent: () => import('./parametrage-other/wrapper-parametre-other/wrapper-parametre-other.component').then((m) => m.WrapperParametreOtherComponent),
        canActivate: [authGuard, roleGuard],
        data: {
            title: 'Parametrage des autres',
            roles: ['SuperAdmin', 'Administrateur']
        }
    }
]