import { HttpClient, HttpEvent, HttpHeaders, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../environments/environment';
const httpOptions = {
    headers: new HttpHeaders({ 'content-type': 'application/json', Authorization: 'basic ' + btoa('med:123456') })
  };
  
  @Injectable({
    providedIn: 'root'
  })

  export class FluxService {
    apiURL = environment.apiURL
    constructor(private http: HttpClient) { }

    
      getByClientAndDate(data:any): Observable<any> {
        return this.http.post<any>(this.apiURL + `getByClientAndDate`,data, httpOptions);
      }

      getFluxByidFacture(id:any): Observable<any> {
        return this.http.get<any>(this.apiURL + `getFluxByidFacture/${id}`, httpOptions);
      }

     
      getAllBrand(): Observable<any> {
        return this.http.get<any>(this.apiURL + `brandFlux`, httpOptions);
      }
      
      addFormula(data:any): Observable<any> {
        return this.http.post<any>(this.apiURL + `addFormula`,data, httpOptions);
      }


      getAllFormula(): Observable<any> {
        return this.http.get<any>(this.apiURL + `getAllFormula`, httpOptions);
      }

      deleteFormula(id:any): Observable<any> {
        return this.http.put<any>(this.apiURL + `disableFormula/${id}`,null, httpOptions);
      }

  }