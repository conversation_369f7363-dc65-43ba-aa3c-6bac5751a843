import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { MarchandiseService } from 'src/services';
import { LigneCmdService } from 'src/services/ligne-cmd.service';
import { DynamicExportButtonComponent } from 'src/shared/components/dynamic-export-button/dynamic-export-button.component';
import { DynamicFormComponent } from 'src/shared/components/dynamic-form/dynamic-form.component';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';
import { FormConfig } from 'src/shared/models/form.models';
import { TableColumn } from 'src/shared/models/table.models';

@Component({
  selector: 'app-list-verification',
  standalone:true,
  imports: [SmartContainerComponent,SmartTableComponent,DynamicExportButtonComponent,DynamicFormComponent,CommonModule],
  templateUrl: './list-verification.component.html',
  styleUrl: './list-verification.component.scss'
})
export class ListVerificationComponent implements OnInit{
  SourceData: any[] = []
  loading:boolean=false
  showTable:boolean=false
  orderTypes: any[] =[]

  DataToExport: any[] = []
  formConfig :FormConfig ={
    fieldGroups: [
      {
        fields: [
          {
            name: 'searchById',
            placeholder : 'Chercher par ID',
            label: 'Rechercher par ID: ',
            type: 'number',
            required: true
          }
        ]
      }
    ],
    buttons :[
      {
       label:'Rechercher',
       color:'primary',
       icon:'cil-search',
       onClick:(formData:any)=>{
        this.findById(formData)
       }
      }
    ]
  }
  formConfig2:FormConfig ={
    fields:[
      {
        name:'selectedOption',
        required:true,
        type:'select',
        options:{
          objectArray:this.orderTypes,
          valueAttribute:'id',
          labelAttribute:'nom_type'
        }
      },
      {
        name:'searchQuery',
        placeholder:'Entrer Référence',
        required:true,
        type:'text',
      }
    ],
    buttons :[
      {
       label:'Rechercher',
       color:'primary',
       icon:'cil-search',
       onClick:(formData:any)=>{this.search(formData)}
      }
    ]
  }
  TableColumns: TableColumn[] = [
    {
      name: 'id',
      displayName: 'ID',
      sortable:true,
      filterable:true
    },
    {
      name: 'nom_depart',
      displayName: 'Départ',
      sortable:true,
      filterable:true
    },
    {
      name: 'nom_arrivee',
      displayName: 'Arrivée',
      sortable:true,
      filterable:true
    },
    {
      name: 'sku',
      displayName: 'Référence',
      sortable:true,
      filterable:true
    },
    {
      name: 'status',
      displayName: 'Statut',
      sortable:true,
      filterable:true
    },
    {
      name: 'date_creation',
      displayName: 'Date de création',
      sortable:true,
      filterable:true
    },
    {
      name: 'quantite',
      displayName: 'Quantité',
      sortable:true,
      filterable:true
    },
    {
      name: 'volume',
      displayName: 'Volume',
      sortable:true,
      filterable:true
    },
    {
      name: 'estimation',
      displayName: 'Volume Estimé',
      sortable:true,
      filterable:true
    },
    {
      name: 'ajoutee_par',
      displayName: 'Demandeur',
      sortable:true,
      filterable:true
    },
    {
      name: 'date_voyage',
      displayName: 'Date Voyage',
      sortable:true,
      filterable:true
    },
    {
      name: 'conducteur_nom_complet',
      displayName: 'Conducteur ',
      sortable:true,
      filterable:true
    },
  ]
  constructor(
    private marchandiseService: MarchandiseService,
    private ligneCmdService: LigneCmdService,
    private toastr: ToastrService
  ){}

  ngOnInit(): void {
    this.loadMarchandisetypes()
  }
  onIntervalChange(event :any) {
    this.ligneCmdService.findLignesByweek(event.target.value).subscribe({
      next: (data) => {
        this.DataToExport = data.data
        if(this.DataToExport.length === 0){
          this.toastr.error("Aucune donnée trouvée")
        }
        this.loading=false
      },
      error: (error) => {
        console.log(error)
        this.toastr.error("Une erreur est survenue")
        this.loading=false
      }
    })
  } 
  search(formData:any){
    this.loading=true
    this.showTable=true
    console.log(formData)
    const data = {
      type: parseInt(formData.selectedOption),
      sku: parseInt(formData.searchQuery)
    }
    this.ligneCmdService.findBySku(data).subscribe({
      next: (data) => {
        this.SourceData = data
        if(this.SourceData.length === 0){
          this.toastr.error("Aucune donnée trouvée")
        }
        this.loading=false
      },
      error: (error) => {
        console.log(error)
        this.toastr.error("Une erreur est survenue")
        this.loading=false
      }
    })
  }
  loadMarchandisetypes() {
    this.marchandiseService.getAllOrderTypes().subscribe({
      next: (res:any)=>{
        this.orderTypes = res
        console.log(this.orderTypes)
        const orderTypesField = this.formConfig2.fields?.find((field:any)=>field.name === 'selectedOption')
        if(orderTypesField?.options){
          orderTypesField.options.objectArray = this.orderTypes
        }
      },
      error: (error:any)=>{
        console.log(error)
      }
    })
  }
  findById(formData:any){
    this.loading=true
    this.showTable=true
    this.ligneCmdService.findLignesByIdToVerification(formData.searchById).subscribe({
      next: (data) => {
        this.SourceData = data
        if(this.SourceData.length === 0){
          this.toastr.error("Aucune donnée trouvée")
        }
        this.loading=false
      },
      error: (error) => {
        console.log(error)
        this.toastr.error("Une erreur est survenue")
        this.loading=false
      }
    })
  }
  
}
