import { Component, OnInit, Injectable, ViewChild, TemplateRef, ChangeDetectorRef } from '@angular/core';
import { NgbDateStruct, NgbDatepickerI18n, NgbCalendar, NgbModalRef, NgbModal, ModalDismissReasons } from '@ng-bootstrap/ng-bootstrap';
import { RegisterServiceService } from 'app/services/register-service.service';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { ToastrService } from 'ngx-toastr';
import { BrowserModule } from '@angular/platform-browser'
import { LigneCmdService } from 'app/services/ligneCmd.service';
import { FournisseurService } from 'app/services/fournisseur.service';
import { InvoiceService } from 'app/services/invoice.service';
import { TranslateService } from '@ngx-translate/core';
import { ImpotService } from 'app/services/impot.service';

const now = new Date();
const I18N_VALUES = {
  en: {
    weekdays: ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'],
    months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
  },
};

// Range datepicker Start 
const equals = (one: NgbDateStruct, two: NgbDateStruct) =>
  one && two && two.year === one.year && two.month === one.month && two.day === one.day;

const before = (one: NgbDateStruct, two: NgbDateStruct) =>
  !one || !two ? false : one.year === two.year ? one.month === two.month ? one.day === two.day
    ? false : one.day < two.day : one.month < two.month : one.year < two.year;

const after = (one: NgbDateStruct, two: NgbDateStruct) =>
  !one || !two ? false : one.year === two.year ? one.month === two.month ? one.day === two.day
    ? false : one.day > two.day : one.month > two.month : one.year > two.year;
// Range datepicker Ends

@Component({
  selector: 'app-g-facture',
  templateUrl: './g-facture.component.html',
  styleUrls: ['./g-facture.component.scss']
})
export class GFactureComponent implements OnInit {
  private modalRef: NgbModalRef;

  content: any // Déclaration de la propriété content

  // Variable declaration
  d: any;
  d2: any;
  d3: any;
  model: NgbDateStruct;
  popupModel;
  date: { year: number, month: number };
  displayMonths = 2;
  navigation = 'select';
  disabledModel: NgbDateStruct = { year: now.getFullYear(), month: now.getMonth() + 1, day: now.getDate() };
  disabled = true;
  customModel: NgbDateStruct;
  source = []
  configModal;    // Global configuration of datepickers
  clientList: any = []
  typeList: any = []
  typeCmd: any = []
  searchClient = ""
  searchType = ""
  toFact: any = []
  toUpdate: any = []
  selectedIds = []
  id_client: any
  validationList: any = []
  mail = ""
  closeResult = '';
  adress = ""
  mat_fisc = ""
  nom_client = ""
  adresseTrouvee = ""
  // Range datepicker start
  hoveredDate: NgbDateStruct;
  selectAll: boolean = false;
  editingItem: any = null; // Element en cours d'édition
  previousId = ""
  previousData: any
  invoice = []
  popup = false
  pref = []
  foot = []
  HT = 0
  TVA = 0
  TTC = 0
  timbre = 0
  montantEnLettres: any
  decEnLettres = ""
  listImpot = []
  selectedClient = []
  dateDebut = ''
  dateFin = ''
  dateFacturation=''
  isFacturation=false
  constructor(private registerService: RegisterServiceService,
    private toastr: ToastrService,
    private ligneCmdService: LigneCmdService,
    private fournisseurService: FournisseurService,
    private cd: ChangeDetectorRef,
    private incoiceService: InvoiceService,
    private translate: TranslateService,
    private impotService: ImpotService,
    private modalService: NgbModal) { }






  commandes = []



  // Range datepicker ends


  // Selects today's date
  selectToday() {
    this.model = { year: now.getFullYear(), month: now.getMonth() + 1, day: now.getDate() };
  }

  // Custom Day View Starts
  isWeekend(date: NgbDateStruct) {
    const d = new Date(date.year, date.month - 1, date.day);
    return d.getDay() === 0 || d.getDay() === 6;
  }

  isDisabled(date: NgbDateStruct, current: { month: number }) {
    return date.month !== current.month;
  }
  // Custom Day View Ends  
  async getImpot() {
    await this.impotService.getImpot()
      .toPromise()
      .then((res) => {
        this.listImpot = res;
        //console.log(this.listImpot)
      })
      .catch((error) => {
        // Handle the error if needed
        this.toastr.error('Error while fetching fournisseurs:', error);
      });
  }

  async ngOnInit() {

    this.isFacturation = sessionStorage.getItem('userRole') == "FACTURATION"

    this.selectToday();
    this.getImpot()

    this.fournisseurService.findClientToInvoiceTransport()
      .toPromise()
      .then((res) => {
        this.clientList = res;
      })
      .catch((error) => {
        // Handle the error if needed
        console.error('Error while fetching fournisseurs:', error);
      });
      

    this.typeCmd = await this.registerService.getAlltypeCmd().toPromise();










  }
  somme = 0;
  generatePDF() {

    /* //console.log(this.commandes)
     var data = document.getElementById('contentToConvert1');
      
     html2canvas(data, { allowTaint: true }).then(canvas => {
       var HTML_Width = canvas.width;
       var HTML_Height = canvas.height;
       var top_left_margin = 15;
       var PDF_Width = HTML_Width + (top_left_margin * 2);
       var PDF_Height = (PDF_Width * 1.5) + (top_left_margin * 2);
       var canvas_image_width = HTML_Width;
       var canvas_image_height = HTML_Height;
 
       var totalPDFPages = Math.ceil(HTML_Height / PDF_Height) - 1;
       canvas.getContext('2d');
       
       const contentDataURL = canvas.toDataURL('image/png')
        var pdf = new jsPDF('p', 'pt', [PDF_Width, PDF_Height]);
 
       
       pdf.addImage(contentDataURL, 'JPG', top_left_margin, top_left_margin, canvas_image_width, canvas_image_height);
 
       for (var i = 1; i <= totalPDFPages; i++) {
         pdf.addPage();
         pdf.addImage(contentDataURL, 'JPG', top_left_margin, -(PDF_Height * i) + (top_left_margin * 4), canvas_image_width, canvas_image_height);
       }
       pdf.save('newPDF.pdf');
  
 
 
     });
 
     */

    this.commandes = []

  }




  async searchByClient() {
    if (!this.searchClient || !this.dateDebut || !this.dateFin) {
      this.toastr.error('Merci de Remplir tout les champs');
      return; // Vous pouvez choisir de retourner une erreur ou d'afficher un message
    }

    if (new Date(this.dateDebut) >= new Date(this.dateFin)) {
      this.toastr.error('La date de début doit être inférieure à la date de fin.');
      return;
    }
    this.selectAll = false
    this.toUpdate = []
    this.selectedIds = []
    if ( this.searchClient) {
      this.previousId = this.searchClient
      const data = {
         date_debut: this.dateDebut,
         date_fin: this.dateFin,
        id_client: this.searchClient
      };
      this.previousData = data
      await this.ligneCmdService.findLivredByDateAndClient(data).toPromise()
        .then((res) => {
          this.toFact = res;
          //console.log(this.toFact);
          if (this.toFact.length === 0) {
            this.toastr.error("Pas de données avec ces filtres");
          }
        })
        .catch((error) => {
          // Gérez l'erreur si nécessaire
          this.toastr.error("Erreur lors de la récupération des données");
          console.error('Error while fetching fournisseurs:', error);
        });
    } else {
      this.toastr.error("Merci d'utiliser les filtres");
    }
  }



  // async searchByClient() {
  //   this.selectAll = false
  //   this.toUpdate = []
  //   this.selectedIds = []

  //   //console.log(this.fromDate, this.toDate);
  //   if (this.fromDate && !this.toDate) {
  //     this.toDate = this.fromDate
  //   }
  //   if (this.fromDate && this.searchClient) {
  //     // Convertir les objets de date en chaînes de caractères
  //     const fromDateStr = this.fromDate.year + "-" + this.fromDate.month + "-" + this.fromDate.day;
  //     const toDateStr = this.toDate.year + "-" + this.toDate.month + "-" + this.toDate.day;
  //     const fromDate = new Date(fromDateStr);
  //     const toDate = new Date(toDateStr);

  //     this.previousId = this.searchClient



  //     const data = {
  //       // date_debut: fromDate,
  //       // date_fin: toDate,
  //       id_client: this.searchClient
  //     };

  //     this.previousData = data

  //     await this.ligneCmdService.findLivredByDateAndClient(data).toPromise()
  //       .then((res) => {
  //         this.toFact = res;
  //         //console.log(this.toFact);
  //         if (this.toFact.length === 0) {
  //           this.toastr.error("Pas de données avec ces filtres");
  //         }
  //       })
  //       .catch((error) => {
  //         // Gérez l'erreur si nécessaire
  //         this.toastr.error("Erreur lors de la récupération des données");
  //         console.error('Error while fetching fournisseurs:', error);
  //       });
  //   } else {
  //     this.toastr.error("Merci d'utiliser les filtres");
  //   }
  // }


  toggleSelectAll() {
    for (let item of this.toFact) {
      item.selected = this.selectAll;
    }

    this.updateSelectedIds(); // Met à jour la liste des IDs sélectionnés
    //console.log(this.selectedIds); // Affiche la liste dans la console
  }

  getSelectedIds(): number[] {
    return this.toFact
      .filter(item => item.selected)
      .map(item => item.id);
  }

  updateSelectedIds(): void {
    const updatedSelectedIds = this.getSelectedIds();
    this.selectedIds = updatedSelectedIds;
    //console.log(this.selectedIds); // Affiche la liste dans la console
  }

  toggleItemSelection(item): void {
    item.selected = !item.selected;
    this.updateSelectedIds(); // Met à jour la liste des IDs sélectionnés
  }




  toggleEdit(item: any) {
    if (this.editingItem) {
      // Si un élément est déjà en cours d'édition, annulez l'édition
      this.editingItem.editing = false;
    }

    item.editing = !item.editing;
    this.editingItem = item;
  }

  cancelEdit(item: any) {
    item.editing = false;
    this.editingItem = null;
  }

  async saveEdit(item: any) {
    //console.log(item)
    item.editing = false;
    this.editingItem = null;

    const data = {
      id: item.id,
      id_client: this.id_client.id
    }
    try {
      // Appel de la fonction de mise à jour avec gestion des erreurs
      await this.ligneCmdService.updateClient(data);
      this.toastr.success("Mise à jour avec succes ")
      // Le code ici sera exécuté si la mise à jour réussit
      //console.log('Mise à jour réussie !');
      item.nom_client = this.id_client.nom_fournisseur; // Assurez-vous que la propriété correcte est utilisée
      this.id_client = ""
    } catch (error) {
      // Le code ici sera exécuté en cas d'erreur
      this.toastr.error("Erreur de mise à jour")
      this.id_client = ""

      console.error('Erreur lors de la mise à jour :', error);
    }
    // Logique pour enregistrer les modifications dans la base de données ou ailleurs
  }



  async generateInvoice() {
    if (!this.dateFacturation) {
      this.toastr.error("Merci de spécifier la date de facturation");
      return;
    }
  
    const data = { date: this.dateFacturation };
    
    try {
      const res = await this.incoiceService.findInvoiceByDate(data).toPromise();
      if (res.length > 0) {
        this.toastr.error("Il existe des factures après cette date.");
        return;
      }
  
      // Afficher une boîte de dialogue de confirmation
      const confirmation = window.confirm('Êtes-vous sûr de vouloir générer la facture?');
  
      // Vérifier la réponse de l'utilisateur
      if (confirmation) {
        await this.generateInvoiceLogic();
      } else {
        //console.log('Opération annulée.');
      }
    } catch (err) {
      // Si une erreur survient pendant l'appel à l'API
      this.toastr.error("Une erreur s'est produite lors de la vérification des factures.");
      console.error(err);
    }
  }
  



  async generateInvoiceLogic() {
    const ligneToSearch = {
      ids: this.selectedIds
    };
    //console.log(ligneToSearch)
    // Utilisation de subscribe
    await this.incoiceService.searchLigneFactureted(ligneToSearch).subscribe(
      (res) => {
        this.validationList = res;

        if (this.validationList.length > 0) {
          this.toastr.error("Problème de facturation");
          this.ligneCmdService.findLivredByDateAndClient(this.previousData).toPromise()
            .then((res) => {
              this.toFact = res;
              //console.log(this.toFact);
              if (this.toFact.length === 0) {
                this.toastr.error("Pas de données avec ces filtres");
              }
            })
            .catch((error) => {
              this.toastr.error("Erreur lors de la récupération des données");
              console.error('Error while fetching fournisseurs:', error);
            });
        } else {
          const selectedLines = this.toFact.filter(line => this.selectedIds.includes(line.id));
          this.clientList.forEach(client => {
            //console.log(client, this.previousId)

            // Vérifier si l'ID de l'élément actuel correspond à previousId
            if (client.id == this.previousId) {
              // Si c'est le cas, stocker l'adresse de cet élément
              this.adresseTrouvee = client.adresse;
              this.mail = client.mail
              //console.log(client.adresse)
            }
            return
          });
          
          // Calculer la somme des prix_tot
          const totalPrixTot = selectedLines.reduce((sum, line) => sum + line.prix_tot, 0);
          const data = {
            id_client: parseInt(this.previousId),
            tot_facture: totalPrixTot,
            tot_factureTTC: totalPrixTot + ((totalPrixTot / 100) * this.listImpot[0].tva_tr) + this.listImpot[0].timbre,
            created_by: parseInt(sessionStorage.getItem('iduser')),
            ligne_ids: this.selectedIds,
            adresse: this.adresseTrouvee,
            tva: this.listImpot[0].tva_tr,
            timbre: this.listImpot[0].timbre,
            type: 'transport',
            invoice_date : this.dateFacturation
          };

          this.incoiceService.addInvoice(data).subscribe(
            (response) => {
              this.toastr.success("Facture générée avec succès")

              this.SendEmailContent(response.code, response.created_at,response.id)
              this.toFact = this.toFact.filter(line => !this.selectedIds.includes(line.id));

              this.selectedIds = [];
              this.dateFacturation=""
              //console.log("Réponse du service :", response);
            },
            (error) => {
              this.toastr.error("Erreur lors de génération de la facture")
              console.error("Erreur du service :", error);
            }
          );
        }

      },
      (error) => {
        this.toastr.error("Erreur lors de géneration du facture")
        console.error('Erreur lors de la recherche des lignes de facture:', error);
      }
    );
  }




  async openInvoiceDetailsModal(content) {
    //console.log(content)
    this.pref = []
    const selectedLines = this.toFact.filter((line :any) => this.selectedIds.includes(line.id));
    let qte = 0;
    let price = 0;
    let prix = 0;
    let volume = 0;
    let priceHors = 0
    let priceDelivery = 0
    let Qte = 0
    this.HT = 0
    this.TTC = 0
    this.TVA = 0

    selectedLines.forEach(line => {
      this.adress = line.adresse;
      this.mat_fisc = line.mat_fisc;
      this.nom_client = line.nom_client;
      if (line.type_ligne === 'Transfert administratif ZEN Group-Magasin ou dépôt' || line.type_ligne === 'Transfert Administratif inter magasin') {
        prix += line.prix_tot;
        qte ++
      } else if (line.type_ligne === 'Transfert administratif technique et matériel informatique' || line.type_ligne === 'Livraison fourniture') {
        price += line.prix_tot;
        Qte += line.quantite
      } else {
        volume += line.adapted_volume != null ? line.adapted_volume : line.volume;
        priceDelivery += line.prix_tot ;
       
      }
    });

    if (prix !== 0) {
      this.pref.push({
        designation: "Transfert administratif",
        unite: "colis",
        volume: qte,
        prix: prix.toFixed(3)
      });
    }

    if (price !== 0) {
      this.pref.push({
        designation: "Transfert administratif technique et matériel informatique",
        unite: "colis",
        volume: Qte,
        prix: price.toFixed(3)
      });
    }

    if (priceDelivery !== 0) {
      this.pref.push({
        designation: "Livraison",
        unite: "m³",
        volume: volume,
        prix: priceDelivery.toFixed(3)
      });
    }

    
    for (let i = 0; i < this.pref.length; i++) {
      const prixHT = parseFloat(this.pref[i].prix);
      this.HT += isNaN(prixHT) ? 0 : prixHT;
    }

    //console.log('Somme des prixHT:', this.HT.toFixed(3));
    this.HT = parseFloat(this.HT.toFixed(3));

    const TVA_PERCENTAGE =this.listImpot[0].tva_tr ;
    this.timbre = this.listImpot[0].timbre;
    this.TVA = (this.HT * TVA_PERCENTAGE) / 100;
    this.TTC = this.HT + this.TVA + this.timbre;
    this.foot = [{ HT: this.HT.toFixed(3), TVA: this.TVA.toFixed(3), timbre: this.timbre.toFixed(3), TTC: this.TTC.toFixed(3) }]
    const partieEntiere = Math.floor(this.TTC);
    const partieDecimale = (this.TTC - partieEntiere).toFixed(3).split('.')[1];
    await this.incoiceService.convert(partieEntiere).subscribe(
      (response: any) => {
        this.decEnLettres = response.enLettres;
        //console.log("///////////////", response.enLettres)
        //console.log("///////////////", response)
        this.montantEnLettres = this.decEnLettres + ` dinars et ${partieDecimale}  Millimes `
      },
      (error: any) => {
        console.error(error);
      }
    );




    this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
    }, (reason) => {
    });
    this.popup = true
    this.cd.detectChanges();

  }

  open(content) {
    this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      this.closeResult = `Closed with: ${result}`;
    }, (reason) => {
      this.closeResult = `Dismissed ${this.getDismissReason(reason)}`;
    });
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  imprimerFacture() {
    const printWindow = window.open('', '_blank');
    printWindow.document.open();
    printWindow.document.write(`
      <html>
        <head>
          <style>
            @media print {
              body {
                font-size: 12pt;
              }
  
              body * {
                visibility: hidden;
              }
  
              #facture-content, #facture-content * {
                visibility: visible;
              }
  
              img {
                max-width: 100%;
                height: auto;
              }
            }
          </style>
        </head>
        <body>
          <div id="facture-content">
            ${document.getElementById('facture-content').innerHTML}
          </div>
        </body>
      </html>
    `);
    printWindow.document.close();
    // Ensure the logo is loaded before printing
    const logo = new Image();
    logo.src = 'assets/img/logo.png'; // Adjust the path accordingly
    logo.onload = () => {
      printWindow.print();
    };
  }


  onFilterChange() {
    this.initialiseData()
    const selectedClient = this.clientList.find(client => client.id == this.searchClient);
    this.selectedClient.push(selectedClient)
    //console.log(this.selectedClient)
  }

 initialiseData() {
    this.selectAll = false
    this.selectedIds = []
    this.toFact = []
  }

  getCurrentDate(): string {
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');  // Ajout de zéro pour les mois < 10
    const day = today.getDate().toString().padStart(2, '0');  // Ajout de zéro pour les jours < 10
    return `${year}-${month}-${day}`;
  }

  telecharger() {
    var data = document.getElementById('contentToConvert1');

    html2canvas(data, { allowTaint: true }).then(canvas => {
      var HTML_Width = canvas.width;
      var HTML_Height = canvas.height;
      var top_left_margin = 15;
      var PDF_Width = HTML_Width + (top_left_margin * 2);
      var PDF_Height = (PDF_Width * 1.5) + (top_left_margin * 2);
      var canvas_image_width = HTML_Width;
      var canvas_image_height = HTML_Height;

      var totalPDFPages = Math.ceil(HTML_Height / PDF_Height) - 1;
      canvas.getContext('2d');


      const contentDataURL = canvas.toDataURL('image/png', 1.0)
      var pdf = new jsPDF('p', 'pt', [PDF_Width, PDF_Height]);


      pdf.addImage(contentDataURL, 'JPG', top_left_margin, top_left_margin, canvas_image_width, canvas_image_height);

      for (var i = 1; i <= totalPDFPages; i++) {
        pdf.addPage();
        pdf.addImage(contentDataURL, 'JPG', top_left_margin, -(PDF_Height * i) + (top_left_margin * 4), canvas_image_width, canvas_image_height);
      }
      pdf.save('newPDF.pdf');



    });
  }

  async SendEmailContent(code, created_at,idFacture) {

    this.pref = []
    const selectedLines = this.toFact.filter(line => this.selectedIds.includes(line.id));
    let qte = 0;
    let price = 0;
    let prix = 0;
    let volume = 0;
    let priceHors = 0
    let priceDelivery = 0
    let Qte = 0
    this.HT = 0
    this.TTC = 0
    this.TVA = 0

    selectedLines.forEach(line => {
      this.adress = line.adresse;
      this.mat_fisc = line.mat_fisc;
      this.nom_client = line.nom_client;
      if (line.type_ligne === 'Transfert administratif ZEN Group-Magasin ou dépôt' || line.type_ligne === 'Transfert Administratif inter magasin') {
        prix += line.prix_tot;
        qte ++
      } else if (line.type_ligne === 'Transfert administratif technique et matériel informatique' || line.type_ligne === 'Livraison fourniture') {
        price += line.prix_tot;
        Qte += line.quantite
      } else {
        volume += line.adapted_volume != null ? line.adapted_volume : line.volume;
        priceDelivery += line.prix_tot ;
       
      }
    });

    if (prix !== 0) {
      this.pref.push({
        designation: "Transfert administratif",
        unite: "colis",
        volume: qte,
        prix: prix.toFixed(3)
      });
    }

    if (price !== 0) {
      this.pref.push({
        designation: "Transfert administratif technique et matériel informatique",
        unite: "colis",
        volume: Qte,
        prix: price.toFixed(3)
      });
    }

    if (priceDelivery !== 0) {
      this.pref.push({
        designation: "Livraison",
        unite: "m³",
        volume: volume,
        prix: priceDelivery.toFixed(3)
      });
    }
    for (let i = 0; i < this.pref.length; i++) {
      const prixHT = parseFloat(this.pref[i].prix);
      this.HT += isNaN(prixHT) ? 0 : prixHT;
    }

    //console.log('Somme des prixHT:', this.HT.toFixed(3));
    this.HT = parseFloat(this.HT.toFixed(3));

    const TVA_PERCENTAGE = this.listImpot[0].tva_tr;
    this.timbre = this.listImpot[0].timbre;
    this.TVA = (this.HT * TVA_PERCENTAGE) / 100;
    this.TTC = this.HT + this.TVA + this.timbre;
    this.foot = [{ HT: this.HT.toFixed(3), TVA: this.TVA.toFixed(3), timbre: this.timbre.toFixed(3), TTC: this.TTC.toFixed(3) }]
    const partieEntiere = Math.floor(this.TTC);
    const partieDecimale = (this.TTC - partieEntiere).toFixed(3).split('.')[1];
    await this.incoiceService.convert(partieEntiere).subscribe(
      (response: any) => {
        this.decEnLettres = response.enLettres;
        //console.log("///////////////", response.enLettres)
        //console.log("///////////////", response)
        this.montantEnLettres = this.decEnLettres + ` dinars et ${partieDecimale}  Millimes `
        const data = {
          ligneFacture: this.pref,
          lettre: this.montantEnLettres,
          TVA: this.TVA,
          TTC: this.TTC,
          HT: this.HT,
          timbre: this.timbre,
          adresse: this.adress,
          mat_fisc: this.mat_fisc,
          client: this.nom_client,
          code: code,
          created_at: created_at,
          mail: this.mail,
          id :idFacture,
          tva: this.listImpot[0].tva_tr

        }
    
        this.incoiceService.sendFac(data)
        .then(res => {
          //console.log('E-mail envoyé avec succès :', res);
          this.toastr.success('E-mail envoyé avec succès')
        })
        .catch(err => {
          this.toastr.error('Erreur d\'envoi de mail')
          console.error('Erreur lors de l\'envoi de l\'e-mail :', err);
        });
      
      },
      (error: any) => {
        console.error(error);
      }
    );



  }


  async downloadExcelTransport() {
    try {
      let excelContent = `ID\tDate du voyage\tDepart\tArrivee\tEmplacement\tDate Voyage\tKilometrage\tType de ligne\tQuantite\tPrix total\n`;
  
      this.toFact.forEach(row => {
        let quantite = row.volume ? `${row.volume} ${this.replaceSpecialChars('m3')}` : `${row.quantite} pcs`;
  
        excelContent += `${row.id}\t${row.date_voyage}\t${this.replaceSpecialChars(row.nom_depart)}\t${this.replaceSpecialChars(row.nom_arrivee)}\t${this.replaceSpecialChars(row.emplacement)}\t${row.date_voyage}\t${row.kilometrage}\t${this.replaceSpecialChars(row.type_ligne)}\t${quantite}\t${row.prix_tot}\n`;
      });
  
      const blob = new Blob([excelContent], { type: 'application/vnd.ms-excel;charset=utf-8' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'Details_Voyage.xls');
      document.body.appendChild(link);
      link.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Une erreur s\'est produite lors de l\'exportation :', error);
    }
  }


  replaceSpecialChars(str: string): string {
    return str.replace(/[éèô/]/g, (char) => {
        switch (char) {
            case 'é':
            case 'è':
                return 'e';
            case 'ô':
                return 'o';
                case '/':
                return '-';
                case 'Â':
                  return ''
            default:
                return char;
        }
    });
  }





}
