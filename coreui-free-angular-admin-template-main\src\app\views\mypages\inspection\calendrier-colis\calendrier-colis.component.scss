/* calendrier-colis.component.scss */
@use '@coreui/coreui/scss/mixins' as *;

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

#calendar {
  .card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.2s ease;

    &:hover {
      box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    &-header {
      font-weight: 600;
      padding: 1rem 1.5rem;
    }

    &-body {
      padding: 1.5rem;
    }
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .hover-shadow {
    transition: box-shadow 0.2s ease;

    &:hover {
      box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }
  }

  .transition-all {
    transition: all 0.2s ease;
  }

  /* Stats cards */
  .stat-card {
    border-left: 4px solid var(--cui-primary);
    transition: transform 0.2s ease;

    &:hover {
      transform: translateY(-2px);
    }

    .stat-value {
      font-size: 1.75rem;
      font-weight: 600;
    }
  }

  /* Calendar controls */
  .btn-group {
    .btn {
      min-width: 2.5rem;
    }
  }

  /* Modal styles */
  ::ng-deep .modal-xl {
    max-width: 95vw;
    min-height: 80vh;
  }

  /* Responsive adjustments */
  @include media-breakpoint-down(md) {
    .card-body {
      padding: 1rem;
    }

    .btn-group {
      margin-bottom: 1rem;
    }
  }
}