import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { CommandeService } from 'src/services/commande.service';
import { ConfirmationDialogService } from 'src/services/confirmation-dialog.service';
import { RoleService } from 'src/services/role.service';
import { ContextService } from 'src/services/strategy/access-context.service';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';
import { ActionButton, TableColumn } from 'src/shared/models/table.models';

@Component({
  selector: 'app-modele',
  standalone:true,
  imports: [SmartContainerComponent,SmartTableComponent],
  templateUrl: './modele.component.html',
  styleUrl: './modele.component.scss'
})
export class ModeleComponent implements OnInit{
  ModelList: any[] = []

  TableColumns: TableColumn[] = [
    {
      name: 'labelle',
      displayName: 'Labelle',
      sortable: true,
      filterable:true
    },
  ]
  TableActions: ActionButton[] = [
    {
      icon: 'cil-trash',
      color: 'danger',
      tooltip: 'Supprimer',
      condition: () => this.roleService.getRole() === 'Chef Departement',
      callback: (row: any) => {this.deleteModel(row)}
    }
  ]

  constructor(
    private contextService: ContextService,
    private commandeService:CommandeService,
    private confirmationDialogService:ConfirmationDialogService,
    private roleService: RoleService,
    private toastr:ToastrService
  ){}
  ngOnInit(): void {
    this.loadModels()
  }
  deleteModel(row :any){
    this.confirmationDialogService.confirmDelete(`Voulez-vous vraiment supprimer le modèle ${row.labelle} ?`).then((confirmed) => {
      if (confirmed) {
        this.commandeService.deleteCommandModele(row.id).subscribe({
          next: (res:any)=>{
            this.toastr.success('Modèle supprimé avec succès')
            this.loadModels()
          },
          error: (error:any)=>{
            this.toastr.error('Erreur lors de la suppression du modèle')
            console.log(error)
          }
        })
      }
    })
  
    
  }
  loadModels(){
    this.contextService.findEnrgCommandsByUserId().subscribe({
      next: (res:any)=>{
        this.ModelList = res
        console.log("source",this.ModelList)
      },
      error: (error:any)=>{
        this.toastr.error('Erreur lors de la chargement des modèles')
        console.log(error)
      }
    })
  }
  

}
