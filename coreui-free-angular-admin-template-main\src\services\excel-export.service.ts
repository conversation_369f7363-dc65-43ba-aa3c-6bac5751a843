// services/excel-export.service.ts
import { Injectable } from '@angular/core';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';

@Injectable({
  providedIn: 'root'
})
export class ExcelExportService {
  
  constructor() { }

  /**
   * Exports data to Excel with dynamic options
   * @param data Array of objects to export
   * @param fileName Name of the output file (without extension)
   * @param options Configuration options
   */
  exportToExcel(
    data: any[],
    fileName: string = 'export',
    options: {
      sheetName?: string;
      columnHeaders?: Record<string, string>;
      excludeColumns?: string[];
      dateFields?: string[];
      dateFormat?: string;
    } = {}
  ): void {
    if (!data || data.length === 0) {
      console.warn('No data to export');
      return;
    }

    // Apply transformations
    let exportData = this.processData(data, options);

    // Create worksheet
    const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(exportData);

    // Apply column width (auto-size approximation)
    const wscols = Object.keys(exportData[0]).map(() => ({ width: 20 }));
    worksheet['!cols'] = wscols;

    // Create workbook
    const workbook: XLSX.WorkBook = {
      Sheets: { [options.sheetName || 'Sheet1']: worksheet },
      SheetNames: [options.sheetName || 'Sheet1']
    };

    // Generate Excel file
    const excelBuffer: any = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array'
    });

    // Trigger download
    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    saveAs(blob, `${fileName}_${new Date().toISOString().slice(0, 10)}.xlsx`);
  }

  private processData(
    data: any[],
    options: {
      columnHeaders?: Record<string, string>;
      excludeColumns?: string[];
      dateFields?: string[];
      dateFormat?: string;
    }
  ): any[] {
    return data.map(item => {
      const newItem: any = {};

      Object.keys(item).forEach(key => {
        // Skip excluded columns
        if (options.excludeColumns?.includes(key)) return;

        // Apply custom header names or format default names
        const displayKey = options.columnHeaders?.[key] || 
          key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());

        // Format dates if specified
        if (options.dateFields?.includes(key) && item[key]) {
          newItem[displayKey] = this.formatDate(
            item[key], 
            options.dateFormat || 'yyyy-MM-dd'
          );
        } else {
          newItem[displayKey] = item[key];
        }
      });

      return newItem;
    });
  }

  private formatDate(value: any, format: string): string {
    const date = value instanceof Date ? value : new Date(value);
    if (isNaN(date.getTime())) return value;

    const pad = (num: number) => num.toString().padStart(2, '0');

    return format
      .replace('yyyy', date.getFullYear().toString())
      .replace('MM', pad(date.getMonth() + 1))
      .replace('dd', pad(date.getDate()))
      .replace('HH', pad(date.getHours()))
      .replace('mm', pad(date.getMinutes()))
      .replace('ss', pad(date.getSeconds()));
  }
}