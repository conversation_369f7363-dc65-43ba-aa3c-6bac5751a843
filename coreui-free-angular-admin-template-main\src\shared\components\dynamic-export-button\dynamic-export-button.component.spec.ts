import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DynamicExportButtonComponent } from './dynamic-export-button.component';

describe('DynamicExportButtonComponent', () => {
  let component: DynamicExportButtonComponent;
  let fixture: ComponentFixture<DynamicExportButtonComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DynamicExportButtonComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(DynamicExportButtonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
