import { Routes } from "@angular/router";
import { authGuard } from "../../../../guards/auth.guard";
import { roleGuard } from "../../../../guards/role.guard";

export const routes: Routes = [
    {
        path: '',
        data: {
            title: 'Utilisateurs'
        },
        children: [
            {
                path: 'liste-utilisateurs',
                loadComponent: () => import('./list-utilisateurs/list-utilisateurs.component').then((c) => c.ListUtilisateursComponent),
                canActivate: [authGuard, roleGuard],
                data: {
                    title: 'Liste Utilisateurs',
                    roles: ['SuperAdmin', 'Administrateur']
                }
            }
        ]
    }
];
