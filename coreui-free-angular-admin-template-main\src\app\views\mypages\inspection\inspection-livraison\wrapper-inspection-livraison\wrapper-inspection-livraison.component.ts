import { Component } from '@angular/core';
import { InspectionAllComponent } from '../inspection-all/inspection-all.component';
import { InspectionByConducteurComponent } from '../inspection-by-conducteur/inspection-by-conducteur.component';
import { InspectionByCamionComponent } from '../inspection-by-camion/inspection-by-camion.component';
import { InspectionByVoyageComponent } from '../inspection-by-voyage/inspection-by-voyage.component';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { ButtonDirective, ButtonGroupComponent, FormCheckLabelDirective } from '@coreui/angular';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-wrapper-inspection-livraison',
  standalone: true,
  imports: [
    SmartContainerComponent,
    CommonModule,
    InspectionAllComponent,
    InspectionByConducteurComponent,
    InspectionByCamionComponent,
    InspectionByVoyageComponent,
    ReactiveFormsModule,
    ButtonGroupComponent,
    FormCheckLabelDirective,
    ButtonDirective],
  templateUrl: './wrapper-inspection-livraison.component.html',
  styleUrl: './wrapper-inspection-livraison.component.scss'
})
export class WrapperInspectionLivraisonComponent {
  formRadio = new UntypedFormGroup({
    radio: new UntypedFormControl('all')
  });

  constructor(
    private formBuilder: UntypedFormBuilder
  ) { }

  setRadioValue(value: string): void {
    this.formRadio.setValue({ radio: value });
    console.log(this.formRadio.value)
  }
}

