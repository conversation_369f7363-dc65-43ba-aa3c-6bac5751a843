// shared/components/form-modal/form-modal.component.ts
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormButton, FormConfig } from '../../models/form.models';
import { DynamicFormComponent } from '../dynamic-form/dynamic-form.component';

@Component({
  selector: 'app-form-modal',
  standalone: true,
  imports: [CommonModule, DynamicFormComponent],
  templateUrl: './form-modal.component.html',
  styleUrls: ['./form-modal.component.scss']
})
export class FormModalComponent {
  @Input() config!: FormConfig;
  @Input() initialData!: any;

  
  @Input() size: 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() visible = false;
  @Output() formChange = new EventEmitter<any>();
  @Output() visibleChange = new EventEmitter<boolean>();

  constructor() {}

  get modalSizeClass() {
    return {
      'sm': 'modal-sm',
      'md': '',
      'lg': 'modal-lg',
      'xl': 'modal-xl'
    }[this.size];
  }

 

  onFormChange(formData: any) {
    this.formChange.emit(formData);
    console.log('FormChange:' ,formData)
  }


  

  close() {
    this.visible = false;
    this.visibleChange.emit(false);
  }
}