{"version": 3, "sources": ["../../../../../../node_modules/@mattlewis92/dom-autoscroller/dist/bundle.es.js", "../../../../../../node_modules/angular-draggable-droppable/fesm2020/angular-draggable-droppable.mjs"], "sourcesContent": ["function getDef(f, d) {\n    if (typeof f === 'undefined') {\n        return typeof d === 'undefined' ? f : d;\n    }\n\n    return f;\n}\nfunction boolean(func, def) {\n\n    func = getDef(func, def);\n\n    if (typeof func === 'function') {\n        return function f() {\n            var arguments$1 = arguments;\n\n            for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n                args[_key] = arguments$1[_key];\n            }\n\n            return !!func.apply(this, args);\n        };\n    }\n\n    return !!func ? function () {\n        return true;\n    } : function () {\n        return false;\n    };\n}\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol ? \"symbol\" : typeof obj; };\n\n/**\n * Returns `true` if provided input is Element.\n * @name isElement\n * @param {*} [input]\n * @returns {boolean}\n */\nvar isElement$1 = function (input) {\n  return input != null && (typeof input === 'undefined' ? 'undefined' : _typeof(input)) === 'object' && input.nodeType === 1 && _typeof(input.style) === 'object' && _typeof(input.ownerDocument) === 'object';\n};\n\nfunction indexOfElement(elements, element){\n    element = resolveElement(element, true);\n    if(!isElement$1(element)) { return -1; }\n    for(var i=0; i<elements.length; i++){\n        if(elements[i] === element){\n            return i;\n        }\n    }\n    return -1;\n}\n\nfunction hasElement(elements, element){\n    return -1 !== indexOfElement(elements, element);\n}\n\nfunction pushElements(elements, toAdd){\n\n    for(var i=0; i<toAdd.length; i++){\n        if(!hasElement(elements, toAdd[i]))\n            { elements.push(toAdd[i]); }\n    }\n\n    return toAdd;\n}\n\nfunction addElements(elements){\n    var arguments$1 = arguments;\n\n    var toAdd = [], len = arguments.length - 1;\n    while ( len-- > 0 ) { toAdd[ len ] = arguments$1[ len + 1 ]; }\n\n    toAdd = toAdd.map(resolveElement);\n    return pushElements(elements, toAdd);\n}\n\nfunction removeElements(elements){\n    var arguments$1 = arguments;\n\n    var toRemove = [], len = arguments.length - 1;\n    while ( len-- > 0 ) { toRemove[ len ] = arguments$1[ len + 1 ]; }\n\n    return toRemove.map(resolveElement).reduce(function (last, e){\n\n        var index = indexOfElement(elements, e);\n\n        if(index !== -1)\n            { return last.concat(elements.splice(index, 1)); }\n        return last;\n    }, []);\n}\n\nfunction resolveElement(element, noThrow){\n    if(typeof element === 'string'){\n        try{\n            return document.querySelector(element);\n        }catch(e){\n            throw e;\n        }\n\n    }\n\n    if(!isElement$1(element) && !noThrow){\n        throw new TypeError((element + \" is not a DOM element.\"));\n    }\n    return element;\n}\n\nfunction createPointCB(object, options) {\n\n    // A persistent object (as opposed to returned object) is used to save memory\n    // This is good to prevent layout thrashing, or for games, and such\n\n    // NOTE\n    // This uses IE fixes which should be OK to remove some day. :)\n    // Some speed will be gained by removal of these.\n\n    // pointCB should be saved in a variable on return\n    // This allows the usage of element.removeEventListener\n\n    options = options || {};\n\n    var allowUpdate = boolean(options.allowUpdate, true);\n\n    /*if(typeof options.allowUpdate === 'function'){\n        allowUpdate = options.allowUpdate;\n    }else{\n        allowUpdate = function(){return true;};\n    }*/\n\n    return function pointCB(event) {\n\n        event = event || window.event; // IE-ism\n        object.target = event.target || event.srcElement || event.originalTarget;\n        object.element = this;\n        object.type = event.type;\n\n        if (!allowUpdate(event)) {\n            return;\n        }\n\n        // Support touch\n        // http://www.creativebloq.com/javascript/make-your-site-work-touch-devices-51411644\n\n        if (event.targetTouches) {\n            object.x = event.targetTouches[0].clientX;\n            object.y = event.targetTouches[0].clientY;\n            object.pageX = event.targetTouches[0].pageX;\n            object.pageY = event.targetTouches[0].pageY;\n            object.screenX = event.targetTouches[0].screenX;\n            object.screenY = event.targetTouches[0].screenY;\n        } else {\n\n            // If pageX/Y aren't available and clientX/Y are,\n            // calculate pageX/Y - logic taken from jQuery.\n            // (This is to support old IE)\n            // NOTE Hopefully this can be removed soon.\n\n            if (event.pageX === null && event.clientX !== null) {\n                var eventDoc = event.target && event.target.ownerDocument || document;\n                var doc = eventDoc.documentElement;\n                var body = eventDoc.body;\n\n                object.pageX = event.clientX + (doc && doc.scrollLeft || body && body.scrollLeft || 0) - (doc && doc.clientLeft || body && body.clientLeft || 0);\n                object.pageY = event.clientY + (doc && doc.scrollTop || body && body.scrollTop || 0) - (doc && doc.clientTop || body && body.clientTop || 0);\n            } else {\n                object.pageX = event.pageX;\n                object.pageY = event.pageY;\n            }\n\n            // pageX, and pageY change with page scroll\n            // so we're not going to use those for x, and y.\n            // NOTE Most browsers also alias clientX/Y with x/y\n            // so that's something to consider down the road.\n\n            object.x = event.clientX;\n            object.y = event.clientY;\n\n            object.screenX = event.screenX;\n            object.screenY = event.screenY;\n        }\n\n        object.clientX = object.x;\n        object.clientY = object.y;\n    };\n\n    //NOTE Remember accessibility, Aria roles, and labels.\n}\n\nfunction createWindowRect() {\n    var props = {\n        top: { value: 0, enumerable: true },\n        left: { value: 0, enumerable: true },\n        right: { value: window.innerWidth, enumerable: true },\n        bottom: { value: window.innerHeight, enumerable: true },\n        width: { value: window.innerWidth, enumerable: true },\n        height: { value: window.innerHeight, enumerable: true },\n        x: { value: 0, enumerable: true },\n        y: { value: 0, enumerable: true }\n    };\n\n    if (Object.create) {\n        return Object.create({}, props);\n    } else {\n        var rect = {};\n        Object.defineProperties(rect, props);\n        return rect;\n    }\n}\n\nfunction getClientRect(el) {\n    if (el === window) {\n        return createWindowRect();\n    } else {\n        try {\n            var rect = el.getBoundingClientRect();\n            if (rect.x === undefined) {\n                rect.x = rect.left;\n                rect.y = rect.top;\n            }\n            return rect;\n        } catch (e) {\n            throw new TypeError(\"Can't call getBoundingClientRect on \" + el);\n        }\n    }\n}\n\nfunction pointInside(point, el) {\n    var rect = getClientRect(el);\n    return point.y > rect.top && point.y < rect.bottom && point.x > rect.left && point.x < rect.right;\n}\n\nvar objectCreate = void 0;\nif (typeof Object.create != 'function') {\n  objectCreate = function (undefined$1) {\n    var Temp = function Temp() {};\n    return function (prototype, propertiesObject) {\n      if (prototype !== Object(prototype) && prototype !== null) {\n        throw TypeError('Argument must be an object, or null');\n      }\n      Temp.prototype = prototype || {};\n      var result = new Temp();\n      Temp.prototype = null;\n      if (propertiesObject !== undefined$1) {\n        Object.defineProperties(result, propertiesObject);\n      }\n\n      // to imitate the case of Object.create(null)\n      if (prototype === null) {\n        result.__proto__ = null;\n      }\n      return result;\n    };\n  }();\n} else {\n  objectCreate = Object.create;\n}\n\nvar objectCreate$1 = objectCreate;\n\nvar mouseEventProps = ['altKey', 'button', 'buttons', 'clientX', 'clientY', 'ctrlKey', 'metaKey', 'movementX', 'movementY', 'offsetX', 'offsetY', 'pageX', 'pageY', 'region', 'relatedTarget', 'screenX', 'screenY', 'shiftKey', 'which', 'x', 'y'];\n\nfunction createDispatcher(element) {\n\n    var defaultSettings = {\n        screenX: 0,\n        screenY: 0,\n        clientX: 0,\n        clientY: 0,\n        ctrlKey: false,\n        shiftKey: false,\n        altKey: false,\n        metaKey: false,\n        button: 0,\n        buttons: 1,\n        relatedTarget: null,\n        region: null\n    };\n\n    if (element !== undefined) {\n        element.addEventListener('mousemove', onMove);\n    }\n\n    function onMove(e) {\n        for (var i = 0; i < mouseEventProps.length; i++) {\n            defaultSettings[mouseEventProps[i]] = e[mouseEventProps[i]];\n        }\n    }\n\n    var dispatch = function () {\n        if (MouseEvent) {\n            return function m1(element, initMove, data) {\n                var evt = new MouseEvent('mousemove', createMoveInit(defaultSettings, initMove));\n\n                //evt.dispatched = 'mousemove';\n                setSpecial(evt, data);\n\n                return element.dispatchEvent(evt);\n            };\n        } else if (typeof document.createEvent === 'function') {\n            return function m2(element, initMove, data) {\n                var settings = createMoveInit(defaultSettings, initMove);\n                var evt = document.createEvent('MouseEvents');\n\n                evt.initMouseEvent(\"mousemove\", true, //can bubble\n                true, //cancelable\n                window, //view\n                0, //detail\n                settings.screenX, //0, //screenX\n                settings.screenY, //0, //screenY\n                settings.clientX, //80, //clientX\n                settings.clientY, //20, //clientY\n                settings.ctrlKey, //false, //ctrlKey\n                settings.altKey, //false, //altKey\n                settings.shiftKey, //false, //shiftKey\n                settings.metaKey, //false, //metaKey\n                settings.button, //0, //button\n                settings.relatedTarget //null //relatedTarget\n                );\n\n                //evt.dispatched = 'mousemove';\n                setSpecial(evt, data);\n\n                return element.dispatchEvent(evt);\n            };\n        } else if (typeof document.createEventObject === 'function') {\n            return function m3(element, initMove, data) {\n                var evt = document.createEventObject();\n                var settings = createMoveInit(defaultSettings, initMove);\n                for (var name in settings) {\n                    evt[name] = settings[name];\n                }\n\n                //evt.dispatched = 'mousemove';\n                setSpecial(evt, data);\n\n                return element.dispatchEvent(evt);\n            };\n        }\n    }();\n\n    function destroy() {\n        if (element) { element.removeEventListener('mousemove', onMove, false); }\n        defaultSettings = null;\n    }\n\n    return {\n        destroy: destroy,\n        dispatch: dispatch\n    };\n}\n\nfunction createMoveInit(defaultSettings, initMove) {\n    initMove = initMove || {};\n    var settings = objectCreate$1(defaultSettings);\n    for (var i = 0; i < mouseEventProps.length; i++) {\n        if (initMove[mouseEventProps[i]] !== undefined) { settings[mouseEventProps[i]] = initMove[mouseEventProps[i]]; }\n    }\n\n    return settings;\n}\n\nfunction setSpecial(e, data) {\n    console.log('data ', data);\n    e.data = data || {};\n    e.dispatched = 'mousemove';\n}\n\nvar prefix = [ 'webkit', 'moz', 'ms', 'o' ];\n\nvar requestFrame = (function () {\n\n    if (typeof window === \"undefined\") {\n        return function () {};\n    }\n\n    for ( var i = 0, limit = prefix.length ; i < limit && ! window.requestAnimationFrame ; ++i ) {\n        window.requestAnimationFrame = window[ prefix[ i ] + 'RequestAnimationFrame' ];\n    }\n\n    if ( ! window.requestAnimationFrame ) {\n        var lastTime = 0;\n\n        window.requestAnimationFrame = function (callback) {\n            var now   = new Date().getTime();\n            var ttc   = Math.max( 0, 16 - now - lastTime );\n            var timer = window.setTimeout( function () { return callback( now + ttc ); }, ttc );\n\n            lastTime = now + ttc;\n\n            return timer;\n        };\n    }\n\n    return window.requestAnimationFrame.bind( window );\n})();\n\nvar cancelFrame = (function () {\n\n    if (typeof window === \"undefined\") {\n        return function () {};\n    }\n\n    for ( var i = 0, limit = prefix.length ; i < limit && ! window.cancelAnimationFrame ; ++i ) {\n        window.cancelAnimationFrame = window[ prefix[ i ] + 'CancelAnimationFrame' ] || window[ prefix[ i ] + 'CancelRequestAnimationFrame' ];\n    }\n\n    if ( ! window.cancelAnimationFrame ) {\n        window.cancelAnimationFrame = function (timer) {\n            window.clearTimeout( timer );\n        };\n    }\n\n    return window.cancelAnimationFrame.bind( window );\n})();\n\nfunction AutoScroller(elements, options){\n    if ( options === void 0 ) options = {};\n\n    var self = this;\n    var maxSpeed = 4, scrolling = false;\n\n    if (typeof options.margin !== 'object') {\n        var margin = options.margin || -1;\n\n        this.margin = {\n            left: margin,\n            right: margin,\n            top: margin,\n            bottom: margin\n        };\n    } else {\n        this.margin = options.margin;\n    }\n\n    //this.scrolling = false;\n    this.scrollWhenOutside = options.scrollWhenOutside || false;\n\n    var point = {},\n        pointCB = createPointCB(point),\n        dispatcher = createDispatcher(),\n        down = false;\n\n    window.addEventListener('mousemove', pointCB, false);\n    window.addEventListener('touchmove', pointCB, false);\n\n    if(!isNaN(options.maxSpeed)){\n        maxSpeed = options.maxSpeed;\n    }\n\n    if (typeof maxSpeed !== 'object') {\n        maxSpeed = {\n            left: maxSpeed,\n            right: maxSpeed,\n            top: maxSpeed,\n            bottom: maxSpeed\n        };\n    }\n\n    this.autoScroll = boolean(options.autoScroll);\n    this.syncMove = boolean(options.syncMove, false);\n\n    this.destroy = function(forceCleanAnimation) {\n        window.removeEventListener('mousemove', pointCB, false);\n        window.removeEventListener('touchmove', pointCB, false);\n        window.removeEventListener('mousedown', onDown, false);\n        window.removeEventListener('touchstart', onDown, false);\n        window.removeEventListener('mouseup', onUp, false);\n        window.removeEventListener('touchend', onUp, false);\n        window.removeEventListener('pointerup', onUp, false);\n        window.removeEventListener('mouseleave', onMouseOut, false);\n\n        window.removeEventListener('mousemove', onMove, false);\n        window.removeEventListener('touchmove', onMove, false);\n\n        window.removeEventListener('scroll', setScroll, true);\n        elements = [];\n        if(forceCleanAnimation){\n          cleanAnimation();\n        }\n    };\n\n    this.add = function(){\n        var element = [], len = arguments.length;\n        while ( len-- ) element[ len ] = arguments[ len ];\n\n        addElements.apply(void 0, [ elements ].concat( element ));\n        return this;\n    };\n\n    this.remove = function(){\n        var element = [], len = arguments.length;\n        while ( len-- ) element[ len ] = arguments[ len ];\n\n        return removeElements.apply(void 0, [ elements ].concat( element ));\n    };\n\n    var hasWindow = null, windowAnimationFrame;\n\n    if(Object.prototype.toString.call(elements) !== '[object Array]'){\n        elements = [elements];\n    }\n\n    (function(temp){\n        elements = [];\n        temp.forEach(function(element){\n            if(element === window){\n                hasWindow = window;\n            }else {\n                self.add(element);\n            }\n        });\n    }(elements));\n\n    Object.defineProperties(this, {\n        down: {\n            get: function(){ return down; }\n        },\n        maxSpeed: {\n            get: function(){ return maxSpeed; }\n        },\n        point: {\n            get: function(){ return point; }\n        },\n        scrolling: {\n            get: function(){ return scrolling; }\n        }\n    });\n\n    var current = null, animationFrame;\n\n    window.addEventListener('mousedown', onDown, false);\n    window.addEventListener('touchstart', onDown, false);\n    window.addEventListener('mouseup', onUp, false);\n    window.addEventListener('touchend', onUp, false);\n\n    /*\n    IE does not trigger mouseup event when scrolling.\n    It is a known issue that Microsoft won't fix.\n    https://connect.microsoft.com/IE/feedback/details/783058/scrollbar-trigger-mousedown-but-not-mouseup\n    IE supports pointer events instead\n    */\n    window.addEventListener('pointerup', onUp, false);\n\n    window.addEventListener('mousemove', onMove, false);\n    window.addEventListener('touchmove', onMove, false);\n\n    window.addEventListener('mouseleave', onMouseOut, false);\n\n    window.addEventListener('scroll', setScroll, true);\n\n    function setScroll(e){\n\n        for(var i=0; i<elements.length; i++){\n            if(elements[i] === e.target){\n                scrolling = true;\n                break;\n            }\n        }\n\n        if(scrolling){\n            requestFrame(function (){ return scrolling = false; });\n        }\n    }\n\n    function onDown(){\n        down = true;\n    }\n\n    function onUp(){\n        down = false;\n        cleanAnimation();\n    }\n    function cleanAnimation(){\n      cancelFrame(animationFrame);\n      cancelFrame(windowAnimationFrame);\n    }\n    function onMouseOut(){\n        down = false;\n    }\n\n    function getTarget(target){\n        if(!target){\n            return null;\n        }\n\n        if(current === target){\n            return target;\n        }\n\n        if(hasElement(elements, target)){\n            return target;\n        }\n\n        while(target = target.parentNode){\n            if(hasElement(elements, target)){\n                return target;\n            }\n        }\n\n        return null;\n    }\n\n    function getElementUnderPoint(){\n        var underPoint = null;\n\n        for(var i=0; i<elements.length; i++){\n            if(inside(point, elements[i])){\n                underPoint = elements[i];\n            }\n        }\n\n        return underPoint;\n    }\n\n\n    function onMove(event){\n\n        if(!self.autoScroll()) { return; }\n\n        if(event['dispatched']){ return; }\n\n        var target = event.target, body = document.body;\n\n        if(current && !inside(point, current)){\n            if(!self.scrollWhenOutside){\n                current = null;\n            }\n        }\n\n        if(target && target.parentNode === body){\n            //The special condition to improve speed.\n            target = getElementUnderPoint();\n        }else {\n            target = getTarget(target);\n\n            if(!target){\n                target = getElementUnderPoint();\n            }\n        }\n\n\n        if(target && target !== current){\n            current = target;\n        }\n\n        if(hasWindow){\n            cancelFrame(windowAnimationFrame);\n            windowAnimationFrame = requestFrame(scrollWindow);\n        }\n\n\n        if(!current){\n            return;\n        }\n\n        cancelFrame(animationFrame);\n        animationFrame = requestFrame(scrollTick);\n    }\n\n    function scrollWindow(){\n        autoScroll(hasWindow);\n\n        cancelFrame(windowAnimationFrame);\n        windowAnimationFrame = requestFrame(scrollWindow);\n    }\n\n    function scrollTick(){\n\n        if(!current){\n            return;\n        }\n\n        autoScroll(current);\n\n        cancelFrame(animationFrame);\n        animationFrame = requestFrame(scrollTick);\n\n    }\n\n\n    function autoScroll(el){\n        var rect = getClientRect(el), scrollx, scrolly;\n\n        if(point.x < rect.left + self.margin.left){\n            scrollx = Math.floor(\n                Math.max(-1, (point.x - rect.left) / self.margin.left - 1) * self.maxSpeed.left\n            );\n        }else if(point.x > rect.right - self.margin.right){\n            scrollx = Math.ceil(\n                Math.min(1, (point.x - rect.right) / self.margin.right + 1) * self.maxSpeed.right\n            );\n        }else {\n            scrollx = 0;\n        }\n\n        if(point.y < rect.top + self.margin.top){\n            scrolly = Math.floor(\n                Math.max(-1, (point.y - rect.top) / self.margin.top - 1) * self.maxSpeed.top\n            );\n        }else if(point.y > rect.bottom - self.margin.bottom){\n            scrolly = Math.ceil(\n                Math.min(1, (point.y - rect.bottom) / self.margin.bottom + 1) * self.maxSpeed.bottom\n            );\n        }else {\n            scrolly = 0;\n        }\n\n        if(self.syncMove()){\n            /*\n            Notes about mousemove event dispatch.\n            screen(X/Y) should need to be updated.\n            Some other properties might need to be set.\n            Keep the syncMove option default false until all inconsistencies are taken care of.\n            */\n            dispatcher.dispatch(el, {\n                pageX: point.pageX + scrollx,\n                pageY: point.pageY + scrolly,\n                clientX: point.x + scrollx,\n                clientY: point.y + scrolly\n            });\n        }\n\n        setTimeout(function (){\n\n            if(scrolly){\n                scrollY(el, scrolly);\n            }\n\n            if(scrollx){\n                scrollX(el, scrollx);\n            }\n\n        });\n    }\n\n    function scrollY(el, amount){\n        if(el === window){\n            window.scrollTo(el.pageXOffset, el.pageYOffset + amount);\n        }else {\n            el.scrollTop += amount;\n        }\n    }\n\n    function scrollX(el, amount){\n        if(el === window){\n            window.scrollTo(el.pageXOffset + amount, el.pageYOffset);\n        }else {\n            el.scrollLeft += amount;\n        }\n    }\n\n}\n\nfunction AutoScrollerFactory(element, options){\n    return new AutoScroller(element, options);\n}\n\nfunction inside(point, el, rect){\n    if(!rect){\n        return pointInside(point, el);\n    }else {\n        return (point.y > rect.top && point.y < rect.bottom &&\n                point.x > rect.left && point.x < rect.right);\n    }\n}\n\n/*\ngit remote add origin https://github.com/hollowdoor/dom_autoscroller.git\ngit push -u origin master\n*/\n\nexport default AutoScrollerFactory;\n", "import * as i0 from '@angular/core';\nimport { Injectable, Directive, EventEmitter, Optional, Inject, Input, Output, NgModule } from '@angular/core';\nimport { Subject, Observable, ReplaySubject, merge, combineLatest, fromEvent } from 'rxjs';\nimport { filter, mergeMap, startWith, map, share, takeUntil, take, takeLast, count, pairwise, distinctUntilChanged } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\nimport autoScroll from '@mattlewis92/dom-autoscroller';\nfunction addClass(renderer, element, classToAdd) {\n  if (classToAdd) {\n    classToAdd.split(' ').forEach(className => renderer.addClass(element.nativeElement, className));\n  }\n}\nfunction removeClass(renderer, element, classToRemove) {\n  if (classToRemove) {\n    classToRemove.split(' ').forEach(className => renderer.removeClass(element.nativeElement, className));\n  }\n}\nclass DraggableHelper {\n  constructor() {\n    this.currentDrag = new Subject();\n  }\n}\nDraggableHelper.ɵfac = function DraggableHelper_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || DraggableHelper)();\n};\nDraggableHelper.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DraggableHelper,\n  factory: DraggableHelper.ɵfac,\n  providedIn: 'root'\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DraggableHelper, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * If the window isn't scrollable, then place this on the scrollable container that draggable elements are inside. e.g.\n * ```html\n  <div style=\"overflow: scroll\" mwlDraggableScrollContainer>\n    <div mwlDraggable>Drag me!</div>\n  </div>\n  ```\n */\nclass DraggableScrollContainerDirective {\n  /**\n   * @hidden\n   */\n  constructor(elementRef) {\n    this.elementRef = elementRef;\n  }\n}\nDraggableScrollContainerDirective.ɵfac = function DraggableScrollContainerDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || DraggableScrollContainerDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\nDraggableScrollContainerDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: DraggableScrollContainerDirective,\n  selectors: [[\"\", \"mwlDraggableScrollContainer\", \"\"]],\n  standalone: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DraggableScrollContainerDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mwlDraggableScrollContainer]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\nclass DraggableDirective {\n  /**\n   * @hidden\n   */\n  constructor(element, renderer, draggableHelper, zone, vcr, scrollContainer, document) {\n    this.element = element;\n    this.renderer = renderer;\n    this.draggableHelper = draggableHelper;\n    this.zone = zone;\n    this.vcr = vcr;\n    this.scrollContainer = scrollContainer;\n    this.document = document;\n    /**\n     * The axis along which the element is draggable\n     */\n    this.dragAxis = {\n      x: true,\n      y: true\n    };\n    /**\n     * Snap all drags to an x / y grid\n     */\n    this.dragSnapGrid = {};\n    /**\n     * Show a ghost element that shows the drag when dragging\n     */\n    this.ghostDragEnabled = true;\n    /**\n     * Show the original element when ghostDragEnabled is true\n     */\n    this.showOriginalElementWhileDragging = false;\n    /**\n     * The cursor to use when hovering over a draggable element\n     */\n    this.dragCursor = '';\n    /*\n     * Options used to control the behaviour of auto scrolling: https://www.npmjs.com/package/dom-autoscroller\n     */\n    this.autoScroll = {\n      margin: 20\n    };\n    /**\n     * Called when the element can be dragged along one axis and has the mouse or pointer device pressed on it\n     */\n    this.dragPointerDown = new EventEmitter();\n    /**\n     * Called when the element has started to be dragged.\n     * Only called after at least one mouse or touch move event.\n     * If you call $event.cancelDrag$.emit() it will cancel the current drag\n     */\n    this.dragStart = new EventEmitter();\n    /**\n     * Called after the ghost element has been created\n     */\n    this.ghostElementCreated = new EventEmitter();\n    /**\n     * Called when the element is being dragged\n     */\n    this.dragging = new EventEmitter();\n    /**\n     * Called after the element is dragged\n     */\n    this.dragEnd = new EventEmitter();\n    /**\n     * @hidden\n     */\n    this.pointerDown$ = new Subject();\n    /**\n     * @hidden\n     */\n    this.pointerMove$ = new Subject();\n    /**\n     * @hidden\n     */\n    this.pointerUp$ = new Subject();\n    this.eventListenerSubscriptions = {};\n    this.destroy$ = new Subject();\n    this.timeLongPress = {\n      timerBegin: 0,\n      timerEnd: 0\n    };\n  }\n  ngOnInit() {\n    this.checkEventListeners();\n    const pointerDragged$ = this.pointerDown$.pipe(filter(() => this.canDrag()), mergeMap(pointerDownEvent => {\n      // fix for https://github.com/mattlewis92/angular-draggable-droppable/issues/61\n      // stop mouse events propagating up the chain\n      if (pointerDownEvent.event.stopPropagation && !this.scrollContainer) {\n        pointerDownEvent.event.stopPropagation();\n      }\n      // hack to prevent text getting selected in safari while dragging\n      const globalDragStyle = this.renderer.createElement('style');\n      this.renderer.setAttribute(globalDragStyle, 'type', 'text/css');\n      this.renderer.appendChild(globalDragStyle, this.renderer.createText(`\n          body * {\n           -moz-user-select: none;\n           -ms-user-select: none;\n           -webkit-user-select: none;\n           user-select: none;\n          }\n        `));\n      requestAnimationFrame(() => {\n        this.document.head.appendChild(globalDragStyle);\n      });\n      const startScrollPosition = this.getScrollPosition();\n      const scrollContainerScroll$ = new Observable(observer => {\n        const scrollContainer = this.scrollContainer ? this.scrollContainer.elementRef.nativeElement : 'window';\n        return this.renderer.listen(scrollContainer, 'scroll', e => observer.next(e));\n      }).pipe(startWith(startScrollPosition), map(() => this.getScrollPosition()));\n      const currentDrag$ = new Subject();\n      const cancelDrag$ = new ReplaySubject();\n      if (this.dragPointerDown.observers.length > 0) {\n        this.zone.run(() => {\n          this.dragPointerDown.next({\n            x: 0,\n            y: 0\n          });\n        });\n      }\n      const dragComplete$ = merge(this.pointerUp$, this.pointerDown$, cancelDrag$, this.destroy$).pipe(share());\n      const pointerMove = combineLatest([this.pointerMove$, scrollContainerScroll$]).pipe(map(([pointerMoveEvent, scroll]) => {\n        return {\n          currentDrag$,\n          transformX: pointerMoveEvent.clientX - pointerDownEvent.clientX,\n          transformY: pointerMoveEvent.clientY - pointerDownEvent.clientY,\n          clientX: pointerMoveEvent.clientX,\n          clientY: pointerMoveEvent.clientY,\n          scrollLeft: scroll.left,\n          scrollTop: scroll.top,\n          target: pointerMoveEvent.event.target\n        };\n      }), map(moveData => {\n        if (this.dragSnapGrid.x) {\n          moveData.transformX = Math.round(moveData.transformX / this.dragSnapGrid.x) * this.dragSnapGrid.x;\n        }\n        if (this.dragSnapGrid.y) {\n          moveData.transformY = Math.round(moveData.transformY / this.dragSnapGrid.y) * this.dragSnapGrid.y;\n        }\n        return moveData;\n      }), map(moveData => {\n        if (!this.dragAxis.x) {\n          moveData.transformX = 0;\n        }\n        if (!this.dragAxis.y) {\n          moveData.transformY = 0;\n        }\n        return moveData;\n      }), map(moveData => {\n        const scrollX = moveData.scrollLeft - startScrollPosition.left;\n        const scrollY = moveData.scrollTop - startScrollPosition.top;\n        return {\n          ...moveData,\n          x: moveData.transformX + scrollX,\n          y: moveData.transformY + scrollY\n        };\n      }), filter(({\n        x,\n        y,\n        transformX,\n        transformY\n      }) => !this.validateDrag || this.validateDrag({\n        x,\n        y,\n        transform: {\n          x: transformX,\n          y: transformY\n        }\n      })), takeUntil(dragComplete$), share());\n      const dragStarted$ = pointerMove.pipe(take(1), share());\n      const dragEnded$ = pointerMove.pipe(takeLast(1), share());\n      dragStarted$.subscribe(({\n        clientX,\n        clientY,\n        x,\n        y\n      }) => {\n        if (this.dragStart.observers.length > 0) {\n          this.zone.run(() => {\n            this.dragStart.next({\n              cancelDrag$\n            });\n          });\n        }\n        this.scroller = autoScroll([this.scrollContainer ? this.scrollContainer.elementRef.nativeElement : this.document.defaultView], {\n          ...this.autoScroll,\n          autoScroll() {\n            return true;\n          }\n        });\n        addClass(this.renderer, this.element, this.dragActiveClass);\n        if (this.ghostDragEnabled) {\n          const rect = this.element.nativeElement.getBoundingClientRect();\n          const clone = this.element.nativeElement.cloneNode(true);\n          if (!this.showOriginalElementWhileDragging) {\n            this.renderer.setStyle(this.element.nativeElement, 'visibility', 'hidden');\n          }\n          if (this.ghostElementAppendTo) {\n            this.ghostElementAppendTo.appendChild(clone);\n          } else {\n            this.element.nativeElement.parentNode.insertBefore(clone, this.element.nativeElement.nextSibling);\n          }\n          this.ghostElement = clone;\n          this.document.body.style.cursor = this.dragCursor;\n          this.setElementStyles(clone, {\n            position: 'fixed',\n            top: `${rect.top}px`,\n            left: `${rect.left}px`,\n            width: `${rect.width}px`,\n            height: `${rect.height}px`,\n            cursor: this.dragCursor,\n            margin: '0',\n            willChange: 'transform',\n            pointerEvents: 'none'\n          });\n          if (this.ghostElementTemplate) {\n            const viewRef = this.vcr.createEmbeddedView(this.ghostElementTemplate);\n            clone.innerHTML = '';\n            viewRef.rootNodes.filter(node => node instanceof Node).forEach(node => {\n              clone.appendChild(node);\n            });\n            dragEnded$.subscribe(() => {\n              this.vcr.remove(this.vcr.indexOf(viewRef));\n            });\n          }\n          if (this.ghostElementCreated.observers.length > 0) {\n            this.zone.run(() => {\n              this.ghostElementCreated.emit({\n                clientX: clientX - x,\n                clientY: clientY - y,\n                element: clone\n              });\n            });\n          }\n          dragEnded$.subscribe(() => {\n            clone.parentElement.removeChild(clone);\n            this.ghostElement = null;\n            this.renderer.setStyle(this.element.nativeElement, 'visibility', '');\n          });\n        }\n        this.draggableHelper.currentDrag.next(currentDrag$);\n      });\n      dragEnded$.pipe(mergeMap(dragEndData => {\n        const dragEndData$ = cancelDrag$.pipe(count(), take(1), map(calledCount => ({\n          ...dragEndData,\n          dragCancelled: calledCount > 0\n        })));\n        cancelDrag$.complete();\n        return dragEndData$;\n      })).subscribe(({\n        x,\n        y,\n        dragCancelled\n      }) => {\n        this.scroller.destroy();\n        if (this.dragEnd.observers.length > 0) {\n          this.zone.run(() => {\n            this.dragEnd.next({\n              x,\n              y,\n              dragCancelled\n            });\n          });\n        }\n        removeClass(this.renderer, this.element, this.dragActiveClass);\n        currentDrag$.complete();\n      });\n      merge(dragComplete$, dragEnded$).pipe(take(1)).subscribe(() => {\n        requestAnimationFrame(() => {\n          this.document.head.removeChild(globalDragStyle);\n        });\n      });\n      return pointerMove;\n    }), share());\n    merge(pointerDragged$.pipe(take(1), map(value => [, value])), pointerDragged$.pipe(pairwise())).pipe(filter(([previous, next]) => {\n      if (!previous) {\n        return true;\n      }\n      return previous.x !== next.x || previous.y !== next.y;\n    }), map(([previous, next]) => next)).subscribe(({\n      x,\n      y,\n      currentDrag$,\n      clientX,\n      clientY,\n      transformX,\n      transformY,\n      target\n    }) => {\n      if (this.dragging.observers.length > 0) {\n        this.zone.run(() => {\n          this.dragging.next({\n            x,\n            y\n          });\n        });\n      }\n      requestAnimationFrame(() => {\n        if (this.ghostElement) {\n          const transform = `translate3d(${transformX}px, ${transformY}px, 0px)`;\n          this.setElementStyles(this.ghostElement, {\n            transform,\n            '-webkit-transform': transform,\n            '-ms-transform': transform,\n            '-moz-transform': transform,\n            '-o-transform': transform\n          });\n        }\n      });\n      currentDrag$.next({\n        clientX,\n        clientY,\n        dropData: this.dropData,\n        target\n      });\n    });\n  }\n  ngOnChanges(changes) {\n    if (changes.dragAxis) {\n      this.checkEventListeners();\n    }\n  }\n  ngOnDestroy() {\n    this.unsubscribeEventListeners();\n    this.pointerDown$.complete();\n    this.pointerMove$.complete();\n    this.pointerUp$.complete();\n    this.destroy$.next();\n  }\n  checkEventListeners() {\n    const canDrag = this.canDrag();\n    const hasEventListeners = Object.keys(this.eventListenerSubscriptions).length > 0;\n    if (canDrag && !hasEventListeners) {\n      this.zone.runOutsideAngular(() => {\n        this.eventListenerSubscriptions.mousedown = this.renderer.listen(this.element.nativeElement, 'mousedown', event => {\n          this.onMouseDown(event);\n        });\n        this.eventListenerSubscriptions.mouseup = this.renderer.listen('document', 'mouseup', event => {\n          this.onMouseUp(event);\n        });\n        this.eventListenerSubscriptions.touchstart = this.renderer.listen(this.element.nativeElement, 'touchstart', event => {\n          this.onTouchStart(event);\n        });\n        this.eventListenerSubscriptions.touchend = this.renderer.listen('document', 'touchend', event => {\n          this.onTouchEnd(event);\n        });\n        this.eventListenerSubscriptions.touchcancel = this.renderer.listen('document', 'touchcancel', event => {\n          this.onTouchEnd(event);\n        });\n        this.eventListenerSubscriptions.mouseenter = this.renderer.listen(this.element.nativeElement, 'mouseenter', () => {\n          this.onMouseEnter();\n        });\n        this.eventListenerSubscriptions.mouseleave = this.renderer.listen(this.element.nativeElement, 'mouseleave', () => {\n          this.onMouseLeave();\n        });\n      });\n    } else if (!canDrag && hasEventListeners) {\n      this.unsubscribeEventListeners();\n    }\n  }\n  onMouseDown(event) {\n    if (event.button === 0) {\n      if (!this.eventListenerSubscriptions.mousemove) {\n        this.eventListenerSubscriptions.mousemove = this.renderer.listen('document', 'mousemove', mouseMoveEvent => {\n          this.pointerMove$.next({\n            event: mouseMoveEvent,\n            clientX: mouseMoveEvent.clientX,\n            clientY: mouseMoveEvent.clientY\n          });\n        });\n      }\n      this.pointerDown$.next({\n        event,\n        clientX: event.clientX,\n        clientY: event.clientY\n      });\n    }\n  }\n  onMouseUp(event) {\n    if (event.button === 0) {\n      if (this.eventListenerSubscriptions.mousemove) {\n        this.eventListenerSubscriptions.mousemove();\n        delete this.eventListenerSubscriptions.mousemove;\n      }\n      this.pointerUp$.next({\n        event,\n        clientX: event.clientX,\n        clientY: event.clientY\n      });\n    }\n  }\n  onTouchStart(event) {\n    let startScrollPosition;\n    let isDragActivated;\n    let hasContainerScrollbar;\n    if (this.touchStartLongPress) {\n      this.timeLongPress.timerBegin = Date.now();\n      isDragActivated = false;\n      hasContainerScrollbar = this.hasScrollbar();\n      startScrollPosition = this.getScrollPosition();\n    }\n    if (!this.eventListenerSubscriptions.touchmove) {\n      const contextMenuListener = fromEvent(this.document, 'contextmenu').subscribe(e => {\n        e.preventDefault();\n      });\n      const touchMoveListener = fromEvent(this.document, 'touchmove', {\n        passive: false\n      }).subscribe(touchMoveEvent => {\n        if (this.touchStartLongPress && !isDragActivated && hasContainerScrollbar) {\n          isDragActivated = this.shouldBeginDrag(event, touchMoveEvent, startScrollPosition);\n        }\n        if (!this.touchStartLongPress || !hasContainerScrollbar || isDragActivated) {\n          touchMoveEvent.preventDefault();\n          this.pointerMove$.next({\n            event: touchMoveEvent,\n            clientX: touchMoveEvent.targetTouches[0].clientX,\n            clientY: touchMoveEvent.targetTouches[0].clientY\n          });\n        }\n      });\n      this.eventListenerSubscriptions.touchmove = () => {\n        contextMenuListener.unsubscribe();\n        touchMoveListener.unsubscribe();\n      };\n    }\n    this.pointerDown$.next({\n      event,\n      clientX: event.touches[0].clientX,\n      clientY: event.touches[0].clientY\n    });\n  }\n  onTouchEnd(event) {\n    if (this.eventListenerSubscriptions.touchmove) {\n      this.eventListenerSubscriptions.touchmove();\n      delete this.eventListenerSubscriptions.touchmove;\n      if (this.touchStartLongPress) {\n        this.enableScroll();\n      }\n    }\n    this.pointerUp$.next({\n      event,\n      clientX: event.changedTouches[0].clientX,\n      clientY: event.changedTouches[0].clientY\n    });\n  }\n  onMouseEnter() {\n    this.setCursor(this.dragCursor);\n  }\n  onMouseLeave() {\n    this.setCursor('');\n  }\n  canDrag() {\n    return this.dragAxis.x || this.dragAxis.y;\n  }\n  setCursor(value) {\n    if (!this.eventListenerSubscriptions.mousemove) {\n      this.renderer.setStyle(this.element.nativeElement, 'cursor', value);\n    }\n  }\n  unsubscribeEventListeners() {\n    Object.keys(this.eventListenerSubscriptions).forEach(type => {\n      this.eventListenerSubscriptions[type]();\n      delete this.eventListenerSubscriptions[type];\n    });\n  }\n  setElementStyles(element, styles) {\n    Object.keys(styles).forEach(key => {\n      this.renderer.setStyle(element, key, styles[key]);\n    });\n  }\n  getScrollElement() {\n    if (this.scrollContainer) {\n      return this.scrollContainer.elementRef.nativeElement;\n    } else {\n      return this.document.body;\n    }\n  }\n  getScrollPosition() {\n    if (this.scrollContainer) {\n      return {\n        top: this.scrollContainer.elementRef.nativeElement.scrollTop,\n        left: this.scrollContainer.elementRef.nativeElement.scrollLeft\n      };\n    } else {\n      return {\n        top: window.pageYOffset || this.document.documentElement.scrollTop,\n        left: window.pageXOffset || this.document.documentElement.scrollLeft\n      };\n    }\n  }\n  shouldBeginDrag(event, touchMoveEvent, startScrollPosition) {\n    const moveScrollPosition = this.getScrollPosition();\n    const deltaScroll = {\n      top: Math.abs(moveScrollPosition.top - startScrollPosition.top),\n      left: Math.abs(moveScrollPosition.left - startScrollPosition.left)\n    };\n    const deltaX = Math.abs(touchMoveEvent.targetTouches[0].clientX - event.touches[0].clientX) - deltaScroll.left;\n    const deltaY = Math.abs(touchMoveEvent.targetTouches[0].clientY - event.touches[0].clientY) - deltaScroll.top;\n    const deltaTotal = deltaX + deltaY;\n    const longPressConfig = this.touchStartLongPress;\n    if (deltaTotal > longPressConfig.delta || deltaScroll.top > 0 || deltaScroll.left > 0) {\n      this.timeLongPress.timerBegin = Date.now();\n    }\n    this.timeLongPress.timerEnd = Date.now();\n    const duration = this.timeLongPress.timerEnd - this.timeLongPress.timerBegin;\n    if (duration >= longPressConfig.delay) {\n      this.disableScroll();\n      return true;\n    }\n    return false;\n  }\n  enableScroll() {\n    if (this.scrollContainer) {\n      this.renderer.setStyle(this.scrollContainer.elementRef.nativeElement, 'overflow', '');\n    }\n    this.renderer.setStyle(this.document.body, 'overflow', '');\n  }\n  disableScroll() {\n    /* istanbul ignore next */\n    if (this.scrollContainer) {\n      this.renderer.setStyle(this.scrollContainer.elementRef.nativeElement, 'overflow', 'hidden');\n    }\n    this.renderer.setStyle(this.document.body, 'overflow', 'hidden');\n  }\n  hasScrollbar() {\n    const scrollContainer = this.getScrollElement();\n    const containerHasHorizontalScroll = scrollContainer.scrollWidth > scrollContainer.clientWidth;\n    const containerHasVerticalScroll = scrollContainer.scrollHeight > scrollContainer.clientHeight;\n    return containerHasHorizontalScroll || containerHasVerticalScroll;\n  }\n}\nDraggableDirective.ɵfac = function DraggableDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || DraggableDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(DraggableHelper), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(DraggableScrollContainerDirective, 8), i0.ɵɵdirectiveInject(DOCUMENT));\n};\nDraggableDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: DraggableDirective,\n  selectors: [[\"\", \"mwlDraggable\", \"\"]],\n  inputs: {\n    dropData: \"dropData\",\n    dragAxis: \"dragAxis\",\n    dragSnapGrid: \"dragSnapGrid\",\n    ghostDragEnabled: \"ghostDragEnabled\",\n    showOriginalElementWhileDragging: \"showOriginalElementWhileDragging\",\n    validateDrag: \"validateDrag\",\n    dragCursor: \"dragCursor\",\n    dragActiveClass: \"dragActiveClass\",\n    ghostElementAppendTo: \"ghostElementAppendTo\",\n    ghostElementTemplate: \"ghostElementTemplate\",\n    touchStartLongPress: \"touchStartLongPress\",\n    autoScroll: \"autoScroll\"\n  },\n  outputs: {\n    dragPointerDown: \"dragPointerDown\",\n    dragStart: \"dragStart\",\n    ghostElementCreated: \"ghostElementCreated\",\n    dragging: \"dragging\",\n    dragEnd: \"dragEnd\"\n  },\n  standalone: false,\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DraggableDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mwlDraggable]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: DraggableHelper\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: DraggableScrollContainerDirective,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, {\n    dropData: [{\n      type: Input\n    }],\n    dragAxis: [{\n      type: Input\n    }],\n    dragSnapGrid: [{\n      type: Input\n    }],\n    ghostDragEnabled: [{\n      type: Input\n    }],\n    showOriginalElementWhileDragging: [{\n      type: Input\n    }],\n    validateDrag: [{\n      type: Input\n    }],\n    dragCursor: [{\n      type: Input\n    }],\n    dragActiveClass: [{\n      type: Input\n    }],\n    ghostElementAppendTo: [{\n      type: Input\n    }],\n    ghostElementTemplate: [{\n      type: Input\n    }],\n    touchStartLongPress: [{\n      type: Input\n    }],\n    autoScroll: [{\n      type: Input\n    }],\n    dragPointerDown: [{\n      type: Output\n    }],\n    dragStart: [{\n      type: Output\n    }],\n    ghostElementCreated: [{\n      type: Output\n    }],\n    dragging: [{\n      type: Output\n    }],\n    dragEnd: [{\n      type: Output\n    }]\n  });\n})();\nfunction isCoordinateWithinRectangle(clientX, clientY, rect) {\n  return clientX >= rect.left && clientX <= rect.right && clientY >= rect.top && clientY <= rect.bottom;\n}\nclass DroppableDirective {\n  constructor(element, draggableHelper, zone, renderer, scrollContainer) {\n    this.element = element;\n    this.draggableHelper = draggableHelper;\n    this.zone = zone;\n    this.renderer = renderer;\n    this.scrollContainer = scrollContainer;\n    /**\n     * Called when a draggable element starts overlapping the element\n     */\n    this.dragEnter = new EventEmitter();\n    /**\n     * Called when a draggable element stops overlapping the element\n     */\n    this.dragLeave = new EventEmitter();\n    /**\n     * Called when a draggable element is moved over the element\n     */\n    this.dragOver = new EventEmitter();\n    /**\n     * Called when a draggable element is dropped on this element\n     */\n    this.drop = new EventEmitter(); // eslint-disable-line  @angular-eslint/no-output-native\n  }\n  ngOnInit() {\n    this.currentDragSubscription = this.draggableHelper.currentDrag.subscribe(drag$ => {\n      addClass(this.renderer, this.element, this.dragActiveClass);\n      const droppableElement = {\n        updateCache: true\n      };\n      const deregisterScrollListener = this.renderer.listen(this.scrollContainer ? this.scrollContainer.elementRef.nativeElement : 'window', 'scroll', () => {\n        droppableElement.updateCache = true;\n      });\n      let currentDragEvent;\n      const overlaps$ = drag$.pipe(map(({\n        clientX,\n        clientY,\n        dropData,\n        target\n      }) => {\n        currentDragEvent = {\n          clientX,\n          clientY,\n          dropData,\n          target\n        };\n        if (droppableElement.updateCache) {\n          droppableElement.rect = this.element.nativeElement.getBoundingClientRect();\n          if (this.scrollContainer) {\n            droppableElement.scrollContainerRect = this.scrollContainer.elementRef.nativeElement.getBoundingClientRect();\n          }\n          droppableElement.updateCache = false;\n        }\n        const isWithinElement = isCoordinateWithinRectangle(clientX, clientY, droppableElement.rect);\n        const isDropAllowed = !this.validateDrop || this.validateDrop({\n          clientX,\n          clientY,\n          target,\n          dropData\n        });\n        if (droppableElement.scrollContainerRect) {\n          return isWithinElement && isDropAllowed && isCoordinateWithinRectangle(clientX, clientY, droppableElement.scrollContainerRect);\n        } else {\n          return isWithinElement && isDropAllowed;\n        }\n      }));\n      const overlapsChanged$ = overlaps$.pipe(distinctUntilChanged());\n      let dragOverActive; // TODO - see if there's a way of doing this via rxjs\n      overlapsChanged$.pipe(filter(overlapsNow => overlapsNow)).subscribe(() => {\n        dragOverActive = true;\n        addClass(this.renderer, this.element, this.dragOverClass);\n        if (this.dragEnter.observers.length > 0) {\n          this.zone.run(() => {\n            this.dragEnter.next(currentDragEvent);\n          });\n        }\n      });\n      overlaps$.pipe(filter(overlapsNow => overlapsNow)).subscribe(() => {\n        if (this.dragOver.observers.length > 0) {\n          this.zone.run(() => {\n            this.dragOver.next(currentDragEvent);\n          });\n        }\n      });\n      overlapsChanged$.pipe(pairwise(), filter(([didOverlap, overlapsNow]) => didOverlap && !overlapsNow)).subscribe(() => {\n        dragOverActive = false;\n        removeClass(this.renderer, this.element, this.dragOverClass);\n        if (this.dragLeave.observers.length > 0) {\n          this.zone.run(() => {\n            this.dragLeave.next(currentDragEvent);\n          });\n        }\n      });\n      drag$.subscribe({\n        complete: () => {\n          deregisterScrollListener();\n          removeClass(this.renderer, this.element, this.dragActiveClass);\n          if (dragOverActive) {\n            removeClass(this.renderer, this.element, this.dragOverClass);\n            if (this.drop.observers.length > 0) {\n              this.zone.run(() => {\n                this.drop.next(currentDragEvent);\n              });\n            }\n          }\n        }\n      });\n    });\n  }\n  ngOnDestroy() {\n    if (this.currentDragSubscription) {\n      this.currentDragSubscription.unsubscribe();\n    }\n  }\n}\nDroppableDirective.ɵfac = function DroppableDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || DroppableDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DraggableHelper), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(DraggableScrollContainerDirective, 8));\n};\nDroppableDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: DroppableDirective,\n  selectors: [[\"\", \"mwlDroppable\", \"\"]],\n  inputs: {\n    dragOverClass: \"dragOverClass\",\n    dragActiveClass: \"dragActiveClass\",\n    validateDrop: \"validateDrop\"\n  },\n  outputs: {\n    dragEnter: \"dragEnter\",\n    dragLeave: \"dragLeave\",\n    dragOver: \"dragOver\",\n    drop: \"drop\"\n  },\n  standalone: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DroppableDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mwlDroppable]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: DraggableHelper\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: DraggableScrollContainerDirective,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, {\n    dragOverClass: [{\n      type: Input\n    }],\n    dragActiveClass: [{\n      type: Input\n    }],\n    validateDrop: [{\n      type: Input\n    }],\n    dragEnter: [{\n      type: Output\n    }],\n    dragLeave: [{\n      type: Output\n    }],\n    dragOver: [{\n      type: Output\n    }],\n    drop: [{\n      type: Output\n    }]\n  });\n})();\nclass DragAndDropModule {}\nDragAndDropModule.ɵfac = function DragAndDropModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || DragAndDropModule)();\n};\nDragAndDropModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: DragAndDropModule,\n  declarations: [DraggableDirective, DroppableDirective, DraggableScrollContainerDirective],\n  exports: [DraggableDirective, DroppableDirective, DraggableScrollContainerDirective]\n});\nDragAndDropModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragAndDropModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [DraggableDirective, DroppableDirective, DraggableScrollContainerDirective],\n      exports: [DraggableDirective, DroppableDirective, DraggableScrollContainerDirective]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of angular-draggable-droppable\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DragAndDropModule, DraggableDirective, DraggableScrollContainerDirective, DroppableDirective };\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS,OAAO,GAAG,GAAG;AAClB,MAAI,OAAO,MAAM,aAAa;AAC1B,WAAO,OAAO,MAAM,cAAc,IAAI;AAAA,EAC1C;AAEA,SAAO;AACX;AACA,SAAS,QAAQ,MAAM,KAAK;AAExB,SAAO,OAAO,MAAM,GAAG;AAEvB,MAAI,OAAO,SAAS,YAAY;AAC5B,WAAO,SAAS,IAAI;AAChB,UAAI,cAAc;AAElB,eAAS,OAAO,UAAU,QAAQ,OAAO,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACjF,aAAK,IAAI,IAAI,YAAY,IAAI;AAAA,MACjC;AAEA,aAAO,CAAC,CAAC,KAAK,MAAM,MAAM,IAAI;AAAA,IAClC;AAAA,EACJ;AAEA,SAAO,CAAC,CAAC,OAAO,WAAY;AACxB,WAAO;AAAA,EACX,IAAI,WAAY;AACZ,WAAO;AAAA,EACX;AACJ;AAEA,IAAI,UAAU,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,WAAW,SAAU,KAAK;AAAE,SAAO,OAAO;AAAK,IAAI,SAAU,KAAK;AAAE,SAAO,OAAO,OAAO,WAAW,cAAc,IAAI,gBAAgB,SAAS,WAAW,OAAO;AAAK;AAQ/O,IAAI,cAAc,SAAU,OAAO;AACjC,SAAO,SAAS,SAAS,OAAO,UAAU,cAAc,cAAc,QAAQ,KAAK,OAAO,YAAY,MAAM,aAAa,KAAK,QAAQ,MAAM,KAAK,MAAM,YAAY,QAAQ,MAAM,aAAa,MAAM;AACtM;AAEA,SAAS,eAAe,UAAU,SAAQ;AACtC,YAAU,eAAe,SAAS,IAAI;AACtC,MAAG,CAAC,YAAY,OAAO,GAAG;AAAE,WAAO;AAAA,EAAI;AACvC,WAAQ,IAAE,GAAG,IAAE,SAAS,QAAQ,KAAI;AAChC,QAAG,SAAS,CAAC,MAAM,SAAQ;AACvB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,WAAW,UAAU,SAAQ;AAClC,SAAO,OAAO,eAAe,UAAU,OAAO;AAClD;AAEA,SAAS,aAAa,UAAU,OAAM;AAElC,WAAQ,IAAE,GAAG,IAAE,MAAM,QAAQ,KAAI;AAC7B,QAAG,CAAC,WAAW,UAAU,MAAM,CAAC,CAAC,GAC7B;AAAE,eAAS,KAAK,MAAM,CAAC,CAAC;AAAA,IAAG;AAAA,EACnC;AAEA,SAAO;AACX;AAEA,SAAS,YAAY,UAAS;AAC1B,MAAI,cAAc;AAElB,MAAI,QAAQ,CAAC,GAAG,MAAM,UAAU,SAAS;AACzC,SAAQ,QAAQ,GAAI;AAAE,UAAO,GAAI,IAAI,YAAa,MAAM,CAAE;AAAA,EAAG;AAE7D,UAAQ,MAAM,IAAI,cAAc;AAChC,SAAO,aAAa,UAAU,KAAK;AACvC;AAEA,SAAS,eAAe,UAAS;AAC7B,MAAI,cAAc;AAElB,MAAI,WAAW,CAAC,GAAG,MAAM,UAAU,SAAS;AAC5C,SAAQ,QAAQ,GAAI;AAAE,aAAU,GAAI,IAAI,YAAa,MAAM,CAAE;AAAA,EAAG;AAEhE,SAAO,SAAS,IAAI,cAAc,EAAE,OAAO,SAAU,MAAM,GAAE;AAEzD,QAAI,QAAQ,eAAe,UAAU,CAAC;AAEtC,QAAG,UAAU,IACT;AAAE,aAAO,KAAK,OAAO,SAAS,OAAO,OAAO,CAAC,CAAC;AAAA,IAAG;AACrD,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AAEA,SAAS,eAAe,SAAS,SAAQ;AACrC,MAAG,OAAO,YAAY,UAAS;AAC3B,QAAG;AACC,aAAO,SAAS,cAAc,OAAO;AAAA,IACzC,SAAO,GAAE;AACL,YAAM;AAAA,IACV;AAAA,EAEJ;AAEA,MAAG,CAAC,YAAY,OAAO,KAAK,CAAC,SAAQ;AACjC,UAAM,IAAI,UAAW,UAAU,wBAAyB;AAAA,EAC5D;AACA,SAAO;AACX;AAEA,SAAS,cAAc,QAAQ,SAAS;AAYpC,YAAU,WAAW,CAAC;AAEtB,MAAI,cAAc,QAAQ,QAAQ,aAAa,IAAI;AAQnD,SAAO,SAAS,QAAQ,OAAO;AAE3B,YAAQ,SAAS,OAAO;AACxB,WAAO,SAAS,MAAM,UAAU,MAAM,cAAc,MAAM;AAC1D,WAAO,UAAU;AACjB,WAAO,OAAO,MAAM;AAEpB,QAAI,CAAC,YAAY,KAAK,GAAG;AACrB;AAAA,IACJ;AAKA,QAAI,MAAM,eAAe;AACrB,aAAO,IAAI,MAAM,cAAc,CAAC,EAAE;AAClC,aAAO,IAAI,MAAM,cAAc,CAAC,EAAE;AAClC,aAAO,QAAQ,MAAM,cAAc,CAAC,EAAE;AACtC,aAAO,QAAQ,MAAM,cAAc,CAAC,EAAE;AACtC,aAAO,UAAU,MAAM,cAAc,CAAC,EAAE;AACxC,aAAO,UAAU,MAAM,cAAc,CAAC,EAAE;AAAA,IAC5C,OAAO;AAOH,UAAI,MAAM,UAAU,QAAQ,MAAM,YAAY,MAAM;AAChD,YAAI,WAAW,MAAM,UAAU,MAAM,OAAO,iBAAiB;AAC7D,YAAI,MAAM,SAAS;AACnB,YAAI,OAAO,SAAS;AAEpB,eAAO,QAAQ,MAAM,WAAW,OAAO,IAAI,cAAc,QAAQ,KAAK,cAAc,MAAM,OAAO,IAAI,cAAc,QAAQ,KAAK,cAAc;AAC9I,eAAO,QAAQ,MAAM,WAAW,OAAO,IAAI,aAAa,QAAQ,KAAK,aAAa,MAAM,OAAO,IAAI,aAAa,QAAQ,KAAK,aAAa;AAAA,MAC9I,OAAO;AACH,eAAO,QAAQ,MAAM;AACrB,eAAO,QAAQ,MAAM;AAAA,MACzB;AAOA,aAAO,IAAI,MAAM;AACjB,aAAO,IAAI,MAAM;AAEjB,aAAO,UAAU,MAAM;AACvB,aAAO,UAAU,MAAM;AAAA,IAC3B;AAEA,WAAO,UAAU,OAAO;AACxB,WAAO,UAAU,OAAO;AAAA,EAC5B;AAGJ;AAEA,SAAS,mBAAmB;AACxB,MAAI,QAAQ;AAAA,IACR,KAAK,EAAE,OAAO,GAAG,YAAY,KAAK;AAAA,IAClC,MAAM,EAAE,OAAO,GAAG,YAAY,KAAK;AAAA,IACnC,OAAO,EAAE,OAAO,OAAO,YAAY,YAAY,KAAK;AAAA,IACpD,QAAQ,EAAE,OAAO,OAAO,aAAa,YAAY,KAAK;AAAA,IACtD,OAAO,EAAE,OAAO,OAAO,YAAY,YAAY,KAAK;AAAA,IACpD,QAAQ,EAAE,OAAO,OAAO,aAAa,YAAY,KAAK;AAAA,IACtD,GAAG,EAAE,OAAO,GAAG,YAAY,KAAK;AAAA,IAChC,GAAG,EAAE,OAAO,GAAG,YAAY,KAAK;AAAA,EACpC;AAEA,MAAI,OAAO,QAAQ;AACf,WAAO,OAAO,OAAO,CAAC,GAAG,KAAK;AAAA,EAClC,OAAO;AACH,QAAI,OAAO,CAAC;AACZ,WAAO,iBAAiB,MAAM,KAAK;AACnC,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,cAAc,IAAI;AACvB,MAAI,OAAO,QAAQ;AACf,WAAO,iBAAiB;AAAA,EAC5B,OAAO;AACH,QAAI;AACA,UAAI,OAAO,GAAG,sBAAsB;AACpC,UAAI,KAAK,MAAM,QAAW;AACtB,aAAK,IAAI,KAAK;AACd,aAAK,IAAI,KAAK;AAAA,MAClB;AACA,aAAO;AAAA,IACX,SAAS,GAAG;AACR,YAAM,IAAI,UAAU,yCAAyC,EAAE;AAAA,IACnE;AAAA,EACJ;AACJ;AAEA,SAAS,YAAY,OAAO,IAAI;AAC5B,MAAI,OAAO,cAAc,EAAE;AAC3B,SAAO,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,KAAK,UAAU,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,KAAK;AAChG;AAEA,IAAI,eAAe;AACnB,IAAI,OAAO,OAAO,UAAU,YAAY;AACtC,iBAAe,yBAAU,aAAa;AACpC,QAAI,OAAO,SAASA,QAAO;AAAA,IAAC;AAC5B,WAAO,SAAU,WAAW,kBAAkB;AAC5C,UAAI,cAAc,OAAO,SAAS,KAAK,cAAc,MAAM;AACzD,cAAM,UAAU,qCAAqC;AAAA,MACvD;AACA,WAAK,YAAY,aAAa,CAAC;AAC/B,UAAI,SAAS,IAAI,KAAK;AACtB,WAAK,YAAY;AACjB,UAAI,qBAAqB,aAAa;AACpC,eAAO,iBAAiB,QAAQ,gBAAgB;AAAA,MAClD;AAGA,UAAI,cAAc,MAAM;AACtB,eAAO,YAAY;AAAA,MACrB;AACA,aAAO;AAAA,IACT;AAAA,EACF,EAAE;AACJ,OAAO;AACL,iBAAe,OAAO;AACxB;AAEA,IAAI,iBAAiB;AAErB,IAAI,kBAAkB,CAAC,UAAU,UAAU,WAAW,WAAW,WAAW,WAAW,WAAW,aAAa,aAAa,WAAW,WAAW,SAAS,SAAS,UAAU,iBAAiB,WAAW,WAAW,YAAY,SAAS,KAAK,GAAG;AAElP,SAAS,iBAAiB,SAAS;AAE/B,MAAI,kBAAkB;AAAA,IAClB,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,eAAe;AAAA,IACf,QAAQ;AAAA,EACZ;AAEA,MAAI,YAAY,QAAW;AACvB,YAAQ,iBAAiB,aAAa,MAAM;AAAA,EAChD;AAEA,WAAS,OAAO,GAAG;AACf,aAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC7C,sBAAgB,gBAAgB,CAAC,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;AAAA,IAC9D;AAAA,EACJ;AAEA,MAAI,WAAW,WAAY;AACvB,QAAI,YAAY;AACZ,aAAO,SAAS,GAAGC,UAAS,UAAU,MAAM;AACxC,YAAI,MAAM,IAAI,WAAW,aAAa,eAAe,iBAAiB,QAAQ,CAAC;AAG/E,mBAAW,KAAK,IAAI;AAEpB,eAAOA,SAAQ,cAAc,GAAG;AAAA,MACpC;AAAA,IACJ,WAAW,OAAO,SAAS,gBAAgB,YAAY;AACnD,aAAO,SAAS,GAAGA,UAAS,UAAU,MAAM;AACxC,YAAI,WAAW,eAAe,iBAAiB,QAAQ;AACvD,YAAI,MAAM,SAAS,YAAY,aAAa;AAE5C,YAAI;AAAA,UAAe;AAAA,UAAa;AAAA;AAAA,UAChC;AAAA;AAAA,UACA;AAAA;AAAA,UACA;AAAA;AAAA,UACA,SAAS;AAAA;AAAA,UACT,SAAS;AAAA;AAAA,UACT,SAAS;AAAA;AAAA,UACT,SAAS;AAAA;AAAA,UACT,SAAS;AAAA;AAAA,UACT,SAAS;AAAA;AAAA,UACT,SAAS;AAAA;AAAA,UACT,SAAS;AAAA;AAAA,UACT,SAAS;AAAA;AAAA,UACT,SAAS;AAAA;AAAA,QACT;AAGA,mBAAW,KAAK,IAAI;AAEpB,eAAOA,SAAQ,cAAc,GAAG;AAAA,MACpC;AAAA,IACJ,WAAW,OAAO,SAAS,sBAAsB,YAAY;AACzD,aAAO,SAAS,GAAGA,UAAS,UAAU,MAAM;AACxC,YAAI,MAAM,SAAS,kBAAkB;AACrC,YAAI,WAAW,eAAe,iBAAiB,QAAQ;AACvD,iBAAS,QAAQ,UAAU;AACvB,cAAI,IAAI,IAAI,SAAS,IAAI;AAAA,QAC7B;AAGA,mBAAW,KAAK,IAAI;AAEpB,eAAOA,SAAQ,cAAc,GAAG;AAAA,MACpC;AAAA,IACJ;AAAA,EACJ,EAAE;AAEF,WAAS,UAAU;AACf,QAAI,SAAS;AAAE,cAAQ,oBAAoB,aAAa,QAAQ,KAAK;AAAA,IAAG;AACxE,sBAAkB;AAAA,EACtB;AAEA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,SAAS,eAAe,iBAAiB,UAAU;AAC/C,aAAW,YAAY,CAAC;AACxB,MAAI,WAAW,eAAe,eAAe;AAC7C,WAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC7C,QAAI,SAAS,gBAAgB,CAAC,CAAC,MAAM,QAAW;AAAE,eAAS,gBAAgB,CAAC,CAAC,IAAI,SAAS,gBAAgB,CAAC,CAAC;AAAA,IAAG;AAAA,EACnH;AAEA,SAAO;AACX;AAEA,SAAS,WAAW,GAAG,MAAM;AACzB,UAAQ,IAAI,SAAS,IAAI;AACzB,IAAE,OAAO,QAAQ,CAAC;AAClB,IAAE,aAAa;AACnB;AAEA,IAAI,SAAS,CAAE,UAAU,OAAO,MAAM,GAAI;AAE1C,IAAI,eAAgB,WAAY;AAE5B,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO,WAAY;AAAA,IAAC;AAAA,EACxB;AAEA,WAAU,IAAI,GAAG,QAAQ,OAAO,QAAS,IAAI,SAAS,CAAE,OAAO,uBAAwB,EAAE,GAAI;AACzF,WAAO,wBAAwB,OAAQ,OAAQ,CAAE,IAAI,uBAAwB;AAAA,EACjF;AAEA,MAAK,CAAE,OAAO,uBAAwB;AAClC,QAAI,WAAW;AAEf,WAAO,wBAAwB,SAAU,UAAU;AAC/C,UAAI,OAAQ,oBAAI,KAAK,GAAE,QAAQ;AAC/B,UAAI,MAAQ,KAAK,IAAK,GAAG,KAAK,MAAM,QAAS;AAC7C,UAAI,QAAQ,OAAO,WAAY,WAAY;AAAE,eAAO,SAAU,MAAM,GAAI;AAAA,MAAG,GAAG,GAAI;AAElF,iBAAW,MAAM;AAEjB,aAAO;AAAA,IACX;AAAA,EACJ;AAEA,SAAO,OAAO,sBAAsB,KAAM,MAAO;AACrD,EAAG;AAEH,IAAI,cAAe,WAAY;AAE3B,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO,WAAY;AAAA,IAAC;AAAA,EACxB;AAEA,WAAU,IAAI,GAAG,QAAQ,OAAO,QAAS,IAAI,SAAS,CAAE,OAAO,sBAAuB,EAAE,GAAI;AACxF,WAAO,uBAAuB,OAAQ,OAAQ,CAAE,IAAI,sBAAuB,KAAK,OAAQ,OAAQ,CAAE,IAAI,6BAA8B;AAAA,EACxI;AAEA,MAAK,CAAE,OAAO,sBAAuB;AACjC,WAAO,uBAAuB,SAAU,OAAO;AAC3C,aAAO,aAAc,KAAM;AAAA,IAC/B;AAAA,EACJ;AAEA,SAAO,OAAO,qBAAqB,KAAM,MAAO;AACpD,EAAG;AAEH,SAAS,aAAa,UAAU,SAAQ;AACpC,MAAK,YAAY,OAAS,WAAU,CAAC;AAErC,MAAI,OAAO;AACX,MAAI,WAAW,GAAG,YAAY;AAE9B,MAAI,OAAO,QAAQ,WAAW,UAAU;AACpC,QAAI,SAAS,QAAQ,UAAU;AAE/B,SAAK,SAAS;AAAA,MACV,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,QAAQ;AAAA,IACZ;AAAA,EACJ,OAAO;AACH,SAAK,SAAS,QAAQ;AAAA,EAC1B;AAGA,OAAK,oBAAoB,QAAQ,qBAAqB;AAEtD,MAAI,QAAQ,CAAC,GACT,UAAU,cAAc,KAAK,GAC7B,aAAa,iBAAiB,GAC9B,OAAO;AAEX,SAAO,iBAAiB,aAAa,SAAS,KAAK;AACnD,SAAO,iBAAiB,aAAa,SAAS,KAAK;AAEnD,MAAG,CAAC,MAAM,QAAQ,QAAQ,GAAE;AACxB,eAAW,QAAQ;AAAA,EACvB;AAEA,MAAI,OAAO,aAAa,UAAU;AAC9B,eAAW;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,QAAQ;AAAA,IACZ;AAAA,EACJ;AAEA,OAAK,aAAa,QAAQ,QAAQ,UAAU;AAC5C,OAAK,WAAW,QAAQ,QAAQ,UAAU,KAAK;AAE/C,OAAK,UAAU,SAAS,qBAAqB;AACzC,WAAO,oBAAoB,aAAa,SAAS,KAAK;AACtD,WAAO,oBAAoB,aAAa,SAAS,KAAK;AACtD,WAAO,oBAAoB,aAAa,QAAQ,KAAK;AACrD,WAAO,oBAAoB,cAAc,QAAQ,KAAK;AACtD,WAAO,oBAAoB,WAAW,MAAM,KAAK;AACjD,WAAO,oBAAoB,YAAY,MAAM,KAAK;AAClD,WAAO,oBAAoB,aAAa,MAAM,KAAK;AACnD,WAAO,oBAAoB,cAAc,YAAY,KAAK;AAE1D,WAAO,oBAAoB,aAAa,QAAQ,KAAK;AACrD,WAAO,oBAAoB,aAAa,QAAQ,KAAK;AAErD,WAAO,oBAAoB,UAAU,WAAW,IAAI;AACpD,eAAW,CAAC;AACZ,QAAG,qBAAoB;AACrB,qBAAe;AAAA,IACjB;AAAA,EACJ;AAEA,OAAK,MAAM,WAAU;AACjB,QAAI,UAAU,CAAC,GAAG,MAAM,UAAU;AAClC,WAAQ,MAAQ,SAAS,GAAI,IAAI,UAAW,GAAI;AAEhD,gBAAY,MAAM,QAAQ,CAAE,QAAS,EAAE,OAAQ,OAAQ,CAAC;AACxD,WAAO;AAAA,EACX;AAEA,OAAK,SAAS,WAAU;AACpB,QAAI,UAAU,CAAC,GAAG,MAAM,UAAU;AAClC,WAAQ,MAAQ,SAAS,GAAI,IAAI,UAAW,GAAI;AAEhD,WAAO,eAAe,MAAM,QAAQ,CAAE,QAAS,EAAE,OAAQ,OAAQ,CAAC;AAAA,EACtE;AAEA,MAAI,YAAY,MAAM;AAEtB,MAAG,OAAO,UAAU,SAAS,KAAK,QAAQ,MAAM,kBAAiB;AAC7D,eAAW,CAAC,QAAQ;AAAA,EACxB;AAEA,GAAC,SAAS,MAAK;AACX,eAAW,CAAC;AACZ,SAAK,QAAQ,SAAS,SAAQ;AAC1B,UAAG,YAAY,QAAO;AAClB,oBAAY;AAAA,MAChB,OAAM;AACF,aAAK,IAAI,OAAO;AAAA,MACpB;AAAA,IACJ,CAAC;AAAA,EACL,GAAE,QAAQ;AAEV,SAAO,iBAAiB,MAAM;AAAA,IAC1B,MAAM;AAAA,MACF,KAAK,WAAU;AAAE,eAAO;AAAA,MAAM;AAAA,IAClC;AAAA,IACA,UAAU;AAAA,MACN,KAAK,WAAU;AAAE,eAAO;AAAA,MAAU;AAAA,IACtC;AAAA,IACA,OAAO;AAAA,MACH,KAAK,WAAU;AAAE,eAAO;AAAA,MAAO;AAAA,IACnC;AAAA,IACA,WAAW;AAAA,MACP,KAAK,WAAU;AAAE,eAAO;AAAA,MAAW;AAAA,IACvC;AAAA,EACJ,CAAC;AAED,MAAI,UAAU,MAAM;AAEpB,SAAO,iBAAiB,aAAa,QAAQ,KAAK;AAClD,SAAO,iBAAiB,cAAc,QAAQ,KAAK;AACnD,SAAO,iBAAiB,WAAW,MAAM,KAAK;AAC9C,SAAO,iBAAiB,YAAY,MAAM,KAAK;AAQ/C,SAAO,iBAAiB,aAAa,MAAM,KAAK;AAEhD,SAAO,iBAAiB,aAAa,QAAQ,KAAK;AAClD,SAAO,iBAAiB,aAAa,QAAQ,KAAK;AAElD,SAAO,iBAAiB,cAAc,YAAY,KAAK;AAEvD,SAAO,iBAAiB,UAAU,WAAW,IAAI;AAEjD,WAAS,UAAU,GAAE;AAEjB,aAAQ,IAAE,GAAG,IAAE,SAAS,QAAQ,KAAI;AAChC,UAAG,SAAS,CAAC,MAAM,EAAE,QAAO;AACxB,oBAAY;AACZ;AAAA,MACJ;AAAA,IACJ;AAEA,QAAG,WAAU;AACT,mBAAa,WAAW;AAAE,eAAO,YAAY;AAAA,MAAO,CAAC;AAAA,IACzD;AAAA,EACJ;AAEA,WAAS,SAAQ;AACb,WAAO;AAAA,EACX;AAEA,WAAS,OAAM;AACX,WAAO;AACP,mBAAe;AAAA,EACnB;AACA,WAAS,iBAAgB;AACvB,gBAAY,cAAc;AAC1B,gBAAY,oBAAoB;AAAA,EAClC;AACA,WAAS,aAAY;AACjB,WAAO;AAAA,EACX;AAEA,WAAS,UAAU,QAAO;AACtB,QAAG,CAAC,QAAO;AACP,aAAO;AAAA,IACX;AAEA,QAAG,YAAY,QAAO;AAClB,aAAO;AAAA,IACX;AAEA,QAAG,WAAW,UAAU,MAAM,GAAE;AAC5B,aAAO;AAAA,IACX;AAEA,WAAM,SAAS,OAAO,YAAW;AAC7B,UAAG,WAAW,UAAU,MAAM,GAAE;AAC5B,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,WAAO;AAAA,EACX;AAEA,WAAS,uBAAsB;AAC3B,QAAI,aAAa;AAEjB,aAAQ,IAAE,GAAG,IAAE,SAAS,QAAQ,KAAI;AAChC,UAAG,OAAO,OAAO,SAAS,CAAC,CAAC,GAAE;AAC1B,qBAAa,SAAS,CAAC;AAAA,MAC3B;AAAA,IACJ;AAEA,WAAO;AAAA,EACX;AAGA,WAAS,OAAO,OAAM;AAElB,QAAG,CAAC,KAAK,WAAW,GAAG;AAAE;AAAA,IAAQ;AAEjC,QAAG,MAAM,YAAY,GAAE;AAAE;AAAA,IAAQ;AAEjC,QAAI,SAAS,MAAM,QAAQ,OAAO,SAAS;AAE3C,QAAG,WAAW,CAAC,OAAO,OAAO,OAAO,GAAE;AAClC,UAAG,CAAC,KAAK,mBAAkB;AACvB,kBAAU;AAAA,MACd;AAAA,IACJ;AAEA,QAAG,UAAU,OAAO,eAAe,MAAK;AAEpC,eAAS,qBAAqB;AAAA,IAClC,OAAM;AACF,eAAS,UAAU,MAAM;AAEzB,UAAG,CAAC,QAAO;AACP,iBAAS,qBAAqB;AAAA,MAClC;AAAA,IACJ;AAGA,QAAG,UAAU,WAAW,SAAQ;AAC5B,gBAAU;AAAA,IACd;AAEA,QAAG,WAAU;AACT,kBAAY,oBAAoB;AAChC,6BAAuB,aAAa,YAAY;AAAA,IACpD;AAGA,QAAG,CAAC,SAAQ;AACR;AAAA,IACJ;AAEA,gBAAY,cAAc;AAC1B,qBAAiB,aAAa,UAAU;AAAA,EAC5C;AAEA,WAAS,eAAc;AACnB,eAAW,SAAS;AAEpB,gBAAY,oBAAoB;AAChC,2BAAuB,aAAa,YAAY;AAAA,EACpD;AAEA,WAAS,aAAY;AAEjB,QAAG,CAAC,SAAQ;AACR;AAAA,IACJ;AAEA,eAAW,OAAO;AAElB,gBAAY,cAAc;AAC1B,qBAAiB,aAAa,UAAU;AAAA,EAE5C;AAGA,WAAS,WAAW,IAAG;AACnB,QAAI,OAAO,cAAc,EAAE,GAAG,SAAS;AAEvC,QAAG,MAAM,IAAI,KAAK,OAAO,KAAK,OAAO,MAAK;AACtC,gBAAU,KAAK;AAAA,QACX,KAAK,IAAI,KAAK,MAAM,IAAI,KAAK,QAAQ,KAAK,OAAO,OAAO,CAAC,IAAI,KAAK,SAAS;AAAA,MAC/E;AAAA,IACJ,WAAS,MAAM,IAAI,KAAK,QAAQ,KAAK,OAAO,OAAM;AAC9C,gBAAU,KAAK;AAAA,QACX,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,SAAS,KAAK,OAAO,QAAQ,CAAC,IAAI,KAAK,SAAS;AAAA,MAChF;AAAA,IACJ,OAAM;AACF,gBAAU;AAAA,IACd;AAEA,QAAG,MAAM,IAAI,KAAK,MAAM,KAAK,OAAO,KAAI;AACpC,gBAAU,KAAK;AAAA,QACX,KAAK,IAAI,KAAK,MAAM,IAAI,KAAK,OAAO,KAAK,OAAO,MAAM,CAAC,IAAI,KAAK,SAAS;AAAA,MAC7E;AAAA,IACJ,WAAS,MAAM,IAAI,KAAK,SAAS,KAAK,OAAO,QAAO;AAChD,gBAAU,KAAK;AAAA,QACX,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,UAAU,KAAK,OAAO,SAAS,CAAC,IAAI,KAAK,SAAS;AAAA,MAClF;AAAA,IACJ,OAAM;AACF,gBAAU;AAAA,IACd;AAEA,QAAG,KAAK,SAAS,GAAE;AAOf,iBAAW,SAAS,IAAI;AAAA,QACpB,OAAO,MAAM,QAAQ;AAAA,QACrB,OAAO,MAAM,QAAQ;AAAA,QACrB,SAAS,MAAM,IAAI;AAAA,QACnB,SAAS,MAAM,IAAI;AAAA,MACvB,CAAC;AAAA,IACL;AAEA,eAAW,WAAW;AAElB,UAAG,SAAQ;AACP,gBAAQ,IAAI,OAAO;AAAA,MACvB;AAEA,UAAG,SAAQ;AACP,gBAAQ,IAAI,OAAO;AAAA,MACvB;AAAA,IAEJ,CAAC;AAAA,EACL;AAEA,WAAS,QAAQ,IAAI,QAAO;AACxB,QAAG,OAAO,QAAO;AACb,aAAO,SAAS,GAAG,aAAa,GAAG,cAAc,MAAM;AAAA,IAC3D,OAAM;AACF,SAAG,aAAa;AAAA,IACpB;AAAA,EACJ;AAEA,WAAS,QAAQ,IAAI,QAAO;AACxB,QAAG,OAAO,QAAO;AACb,aAAO,SAAS,GAAG,cAAc,QAAQ,GAAG,WAAW;AAAA,IAC3D,OAAM;AACF,SAAG,cAAc;AAAA,IACrB;AAAA,EACJ;AAEJ;AAEA,SAAS,oBAAoB,SAAS,SAAQ;AAC1C,SAAO,IAAI,aAAa,SAAS,OAAO;AAC5C;AAEA,SAAS,OAAO,OAAO,IAAI,MAAK;AAC5B,MAAG,CAAC,MAAK;AACL,WAAO,YAAY,OAAO,EAAE;AAAA,EAChC,OAAM;AACF,WAAQ,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,KAAK,UACrC,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,KAAK;AAAA,EAClD;AACJ;AAOA,IAAO,oBAAQ;;;AC/vBf,SAAS,SAAS,UAAU,SAAS,YAAY;AAC/C,MAAI,YAAY;AACd,eAAW,MAAM,GAAG,EAAE,QAAQ,eAAa,SAAS,SAAS,QAAQ,eAAe,SAAS,CAAC;AAAA,EAChG;AACF;AACA,SAAS,YAAY,UAAU,SAAS,eAAe;AACrD,MAAI,eAAe;AACjB,kBAAc,MAAM,GAAG,EAAE,QAAQ,eAAa,SAAS,YAAY,QAAQ,eAAe,SAAS,CAAC;AAAA,EACtG;AACF;AACA,IAAM,kBAAN,MAAsB;AAAA,EACpB,cAAc;AACZ,SAAK,cAAc,IAAI,QAAQ;AAAA,EACjC;AACF;AACA,gBAAgB,OAAO,SAAS,wBAAwB,mBAAmB;AACzE,SAAO,KAAK,qBAAqB,iBAAiB;AACpD;AACA,gBAAgB,QAA0B,mBAAmB;AAAA,EAC3D,OAAO;AAAA,EACP,SAAS,gBAAgB;AAAA,EACzB,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAM,oCAAN,MAAwC;AAAA;AAAA;AAAA;AAAA,EAItC,YAAY,YAAY;AACtB,SAAK,aAAa;AAAA,EACpB;AACF;AACA,kCAAkC,OAAO,SAAS,0CAA0C,mBAAmB;AAC7G,SAAO,KAAK,qBAAqB,mCAAsC,kBAAqB,UAAU,CAAC;AACzG;AACA,kCAAkC,OAAyB,kBAAkB;AAAA,EAC3E,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,+BAA+B,EAAE,CAAC;AAAA,EACnD,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mCAAmC,CAAC;AAAA,IAC1G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,qBAAN,MAAyB;AAAA;AAAA;AAAA;AAAA,EAIvB,YAAY,SAAS,UAAU,iBAAiB,MAAM,KAAK,iBAAiBC,WAAU;AACpF,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,kBAAkB;AACvB,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,SAAK,kBAAkB;AACvB,SAAK,WAAWA;AAIhB,SAAK,WAAW;AAAA,MACd,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAIA,SAAK,eAAe,CAAC;AAIrB,SAAK,mBAAmB;AAIxB,SAAK,mCAAmC;AAIxC,SAAK,aAAa;AAIlB,SAAK,aAAa;AAAA,MAChB,QAAQ;AAAA,IACV;AAIA,SAAK,kBAAkB,IAAI,aAAa;AAMxC,SAAK,YAAY,IAAI,aAAa;AAIlC,SAAK,sBAAsB,IAAI,aAAa;AAI5C,SAAK,WAAW,IAAI,aAAa;AAIjC,SAAK,UAAU,IAAI,aAAa;AAIhC,SAAK,eAAe,IAAI,QAAQ;AAIhC,SAAK,eAAe,IAAI,QAAQ;AAIhC,SAAK,aAAa,IAAI,QAAQ;AAC9B,SAAK,6BAA6B,CAAC;AACnC,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,gBAAgB;AAAA,MACnB,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,oBAAoB;AACzB,UAAM,kBAAkB,KAAK,aAAa,KAAK,OAAO,MAAM,KAAK,QAAQ,CAAC,GAAG,SAAS,sBAAoB;AAGxG,UAAI,iBAAiB,MAAM,mBAAmB,CAAC,KAAK,iBAAiB;AACnE,yBAAiB,MAAM,gBAAgB;AAAA,MACzC;AAEA,YAAM,kBAAkB,KAAK,SAAS,cAAc,OAAO;AAC3D,WAAK,SAAS,aAAa,iBAAiB,QAAQ,UAAU;AAC9D,WAAK,SAAS,YAAY,iBAAiB,KAAK,SAAS,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAOjE,CAAC;AACJ,4BAAsB,MAAM;AAC1B,aAAK,SAAS,KAAK,YAAY,eAAe;AAAA,MAChD,CAAC;AACD,YAAM,sBAAsB,KAAK,kBAAkB;AACnD,YAAM,yBAAyB,IAAI,WAAW,cAAY;AACxD,cAAM,kBAAkB,KAAK,kBAAkB,KAAK,gBAAgB,WAAW,gBAAgB;AAC/F,eAAO,KAAK,SAAS,OAAO,iBAAiB,UAAU,OAAK,SAAS,KAAK,CAAC,CAAC;AAAA,MAC9E,CAAC,EAAE,KAAK,UAAU,mBAAmB,GAAG,IAAI,MAAM,KAAK,kBAAkB,CAAC,CAAC;AAC3E,YAAM,eAAe,IAAI,QAAQ;AACjC,YAAM,cAAc,IAAI,cAAc;AACtC,UAAI,KAAK,gBAAgB,UAAU,SAAS,GAAG;AAC7C,aAAK,KAAK,IAAI,MAAM;AAClB,eAAK,gBAAgB,KAAK;AAAA,YACxB,GAAG;AAAA,YACH,GAAG;AAAA,UACL,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,gBAAgB,MAAM,KAAK,YAAY,KAAK,cAAc,aAAa,KAAK,QAAQ,EAAE,KAAK,MAAM,CAAC;AACxG,YAAM,cAAc,cAAc,CAAC,KAAK,cAAc,sBAAsB,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,kBAAkB,MAAM,MAAM;AACtH,eAAO;AAAA,UACL;AAAA,UACA,YAAY,iBAAiB,UAAU,iBAAiB;AAAA,UACxD,YAAY,iBAAiB,UAAU,iBAAiB;AAAA,UACxD,SAAS,iBAAiB;AAAA,UAC1B,SAAS,iBAAiB;AAAA,UAC1B,YAAY,OAAO;AAAA,UACnB,WAAW,OAAO;AAAA,UAClB,QAAQ,iBAAiB,MAAM;AAAA,QACjC;AAAA,MACF,CAAC,GAAG,IAAI,cAAY;AAClB,YAAI,KAAK,aAAa,GAAG;AACvB,mBAAS,aAAa,KAAK,MAAM,SAAS,aAAa,KAAK,aAAa,CAAC,IAAI,KAAK,aAAa;AAAA,QAClG;AACA,YAAI,KAAK,aAAa,GAAG;AACvB,mBAAS,aAAa,KAAK,MAAM,SAAS,aAAa,KAAK,aAAa,CAAC,IAAI,KAAK,aAAa;AAAA,QAClG;AACA,eAAO;AAAA,MACT,CAAC,GAAG,IAAI,cAAY;AAClB,YAAI,CAAC,KAAK,SAAS,GAAG;AACpB,mBAAS,aAAa;AAAA,QACxB;AACA,YAAI,CAAC,KAAK,SAAS,GAAG;AACpB,mBAAS,aAAa;AAAA,QACxB;AACA,eAAO;AAAA,MACT,CAAC,GAAG,IAAI,cAAY;AAClB,cAAM,UAAU,SAAS,aAAa,oBAAoB;AAC1D,cAAM,UAAU,SAAS,YAAY,oBAAoB;AACzD,eAAO,iCACF,WADE;AAAA,UAEL,GAAG,SAAS,aAAa;AAAA,UACzB,GAAG,SAAS,aAAa;AAAA,QAC3B;AAAA,MACF,CAAC,GAAG,OAAO,CAAC;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,MAAM,CAAC,KAAK,gBAAgB,KAAK,aAAa;AAAA,QAC5C;AAAA,QACA;AAAA,QACA,WAAW;AAAA,UACT,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,MACF,CAAC,CAAC,GAAG,UAAU,aAAa,GAAG,MAAM,CAAC;AACtC,YAAM,eAAe,YAAY,KAAK,KAAK,CAAC,GAAG,MAAM,CAAC;AACtD,YAAM,aAAa,YAAY,KAAK,SAAS,CAAC,GAAG,MAAM,CAAC;AACxD,mBAAa,UAAU,CAAC;AAAA,QACtB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,MAAM;AACJ,YAAI,KAAK,UAAU,UAAU,SAAS,GAAG;AACvC,eAAK,KAAK,IAAI,MAAM;AAClB,iBAAK,UAAU,KAAK;AAAA,cAClB;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,aAAK,WAAW,kBAAW,CAAC,KAAK,kBAAkB,KAAK,gBAAgB,WAAW,gBAAgB,KAAK,SAAS,WAAW,GAAG,iCAC1H,KAAK,aADqH;AAAA,UAE7H,aAAa;AACX,mBAAO;AAAA,UACT;AAAA,QACF,EAAC;AACD,iBAAS,KAAK,UAAU,KAAK,SAAS,KAAK,eAAe;AAC1D,YAAI,KAAK,kBAAkB;AACzB,gBAAM,OAAO,KAAK,QAAQ,cAAc,sBAAsB;AAC9D,gBAAM,QAAQ,KAAK,QAAQ,cAAc,UAAU,IAAI;AACvD,cAAI,CAAC,KAAK,kCAAkC;AAC1C,iBAAK,SAAS,SAAS,KAAK,QAAQ,eAAe,cAAc,QAAQ;AAAA,UAC3E;AACA,cAAI,KAAK,sBAAsB;AAC7B,iBAAK,qBAAqB,YAAY,KAAK;AAAA,UAC7C,OAAO;AACL,iBAAK,QAAQ,cAAc,WAAW,aAAa,OAAO,KAAK,QAAQ,cAAc,WAAW;AAAA,UAClG;AACA,eAAK,eAAe;AACpB,eAAK,SAAS,KAAK,MAAM,SAAS,KAAK;AACvC,eAAK,iBAAiB,OAAO;AAAA,YAC3B,UAAU;AAAA,YACV,KAAK,GAAG,KAAK,GAAG;AAAA,YAChB,MAAM,GAAG,KAAK,IAAI;AAAA,YAClB,OAAO,GAAG,KAAK,KAAK;AAAA,YACpB,QAAQ,GAAG,KAAK,MAAM;AAAA,YACtB,QAAQ,KAAK;AAAA,YACb,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,eAAe;AAAA,UACjB,CAAC;AACD,cAAI,KAAK,sBAAsB;AAC7B,kBAAM,UAAU,KAAK,IAAI,mBAAmB,KAAK,oBAAoB;AACrE,kBAAM,YAAY;AAClB,oBAAQ,UAAU,OAAO,UAAQ,gBAAgB,IAAI,EAAE,QAAQ,UAAQ;AACrE,oBAAM,YAAY,IAAI;AAAA,YACxB,CAAC;AACD,uBAAW,UAAU,MAAM;AACzB,mBAAK,IAAI,OAAO,KAAK,IAAI,QAAQ,OAAO,CAAC;AAAA,YAC3C,CAAC;AAAA,UACH;AACA,cAAI,KAAK,oBAAoB,UAAU,SAAS,GAAG;AACjD,iBAAK,KAAK,IAAI,MAAM;AAClB,mBAAK,oBAAoB,KAAK;AAAA,gBAC5B,SAAS,UAAU;AAAA,gBACnB,SAAS,UAAU;AAAA,gBACnB,SAAS;AAAA,cACX,CAAC;AAAA,YACH,CAAC;AAAA,UACH;AACA,qBAAW,UAAU,MAAM;AACzB,kBAAM,cAAc,YAAY,KAAK;AACrC,iBAAK,eAAe;AACpB,iBAAK,SAAS,SAAS,KAAK,QAAQ,eAAe,cAAc,EAAE;AAAA,UACrE,CAAC;AAAA,QACH;AACA,aAAK,gBAAgB,YAAY,KAAK,YAAY;AAAA,MACpD,CAAC;AACD,iBAAW,KAAK,SAAS,iBAAe;AACtC,cAAM,eAAe,YAAY,KAAK,MAAM,GAAG,KAAK,CAAC,GAAG,IAAI,iBAAgB,iCACvE,cADuE;AAAA,UAE1E,eAAe,cAAc;AAAA,QAC/B,EAAE,CAAC;AACH,oBAAY,SAAS;AACrB,eAAO;AAAA,MACT,CAAC,CAAC,EAAE,UAAU,CAAC;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,MACF,MAAM;AACJ,aAAK,SAAS,QAAQ;AACtB,YAAI,KAAK,QAAQ,UAAU,SAAS,GAAG;AACrC,eAAK,KAAK,IAAI,MAAM;AAClB,iBAAK,QAAQ,KAAK;AAAA,cAChB;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,oBAAY,KAAK,UAAU,KAAK,SAAS,KAAK,eAAe;AAC7D,qBAAa,SAAS;AAAA,MACxB,CAAC;AACD,YAAM,eAAe,UAAU,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AAC7D,8BAAsB,MAAM;AAC1B,eAAK,SAAS,KAAK,YAAY,eAAe;AAAA,QAChD,CAAC;AAAA,MACH,CAAC;AACD,aAAO;AAAA,IACT,CAAC,GAAG,MAAM,CAAC;AACX,UAAM,gBAAgB,KAAK,KAAK,CAAC,GAAG,IAAI,WAAS,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,gBAAgB,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC,UAAU,IAAI,MAAM;AAChI,UAAI,CAAC,UAAU;AACb,eAAO;AAAA,MACT;AACA,aAAO,SAAS,MAAM,KAAK,KAAK,SAAS,MAAM,KAAK;AAAA,IACtD,CAAC,GAAG,IAAI,CAAC,CAAC,UAAU,IAAI,MAAM,IAAI,CAAC,EAAE,UAAU,CAAC;AAAA,MAC9C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,MAAM;AACJ,UAAI,KAAK,SAAS,UAAU,SAAS,GAAG;AACtC,aAAK,KAAK,IAAI,MAAM;AAClB,eAAK,SAAS,KAAK;AAAA,YACjB;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,4BAAsB,MAAM;AAC1B,YAAI,KAAK,cAAc;AACrB,gBAAM,YAAY,eAAe,UAAU,OAAO,UAAU;AAC5D,eAAK,iBAAiB,KAAK,cAAc;AAAA,YACvC;AAAA,YACA,qBAAqB;AAAA,YACrB,iBAAiB;AAAA,YACjB,kBAAkB;AAAA,YAClB,gBAAgB;AAAA,UAClB,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,mBAAa,KAAK;AAAA,QAChB;AAAA,QACA;AAAA,QACA,UAAU,KAAK;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,UAAU;AACpB,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,0BAA0B;AAC/B,SAAK,aAAa,SAAS;AAC3B,SAAK,aAAa,SAAS;AAC3B,SAAK,WAAW,SAAS;AACzB,SAAK,SAAS,KAAK;AAAA,EACrB;AAAA,EACA,sBAAsB;AACpB,UAAM,UAAU,KAAK,QAAQ;AAC7B,UAAM,oBAAoB,OAAO,KAAK,KAAK,0BAA0B,EAAE,SAAS;AAChF,QAAI,WAAW,CAAC,mBAAmB;AACjC,WAAK,KAAK,kBAAkB,MAAM;AAChC,aAAK,2BAA2B,YAAY,KAAK,SAAS,OAAO,KAAK,QAAQ,eAAe,aAAa,WAAS;AACjH,eAAK,YAAY,KAAK;AAAA,QACxB,CAAC;AACD,aAAK,2BAA2B,UAAU,KAAK,SAAS,OAAO,YAAY,WAAW,WAAS;AAC7F,eAAK,UAAU,KAAK;AAAA,QACtB,CAAC;AACD,aAAK,2BAA2B,aAAa,KAAK,SAAS,OAAO,KAAK,QAAQ,eAAe,cAAc,WAAS;AACnH,eAAK,aAAa,KAAK;AAAA,QACzB,CAAC;AACD,aAAK,2BAA2B,WAAW,KAAK,SAAS,OAAO,YAAY,YAAY,WAAS;AAC/F,eAAK,WAAW,KAAK;AAAA,QACvB,CAAC;AACD,aAAK,2BAA2B,cAAc,KAAK,SAAS,OAAO,YAAY,eAAe,WAAS;AACrG,eAAK,WAAW,KAAK;AAAA,QACvB,CAAC;AACD,aAAK,2BAA2B,aAAa,KAAK,SAAS,OAAO,KAAK,QAAQ,eAAe,cAAc,MAAM;AAChH,eAAK,aAAa;AAAA,QACpB,CAAC;AACD,aAAK,2BAA2B,aAAa,KAAK,SAAS,OAAO,KAAK,QAAQ,eAAe,cAAc,MAAM;AAChH,eAAK,aAAa;AAAA,QACpB,CAAC;AAAA,MACH,CAAC;AAAA,IACH,WAAW,CAAC,WAAW,mBAAmB;AACxC,WAAK,0BAA0B;AAAA,IACjC;AAAA,EACF;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,MAAM,WAAW,GAAG;AACtB,UAAI,CAAC,KAAK,2BAA2B,WAAW;AAC9C,aAAK,2BAA2B,YAAY,KAAK,SAAS,OAAO,YAAY,aAAa,oBAAkB;AAC1G,eAAK,aAAa,KAAK;AAAA,YACrB,OAAO;AAAA,YACP,SAAS,eAAe;AAAA,YACxB,SAAS,eAAe;AAAA,UAC1B,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,WAAK,aAAa,KAAK;AAAA,QACrB;AAAA,QACA,SAAS,MAAM;AAAA,QACf,SAAS,MAAM;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,UAAU,OAAO;AACf,QAAI,MAAM,WAAW,GAAG;AACtB,UAAI,KAAK,2BAA2B,WAAW;AAC7C,aAAK,2BAA2B,UAAU;AAC1C,eAAO,KAAK,2BAA2B;AAAA,MACzC;AACA,WAAK,WAAW,KAAK;AAAA,QACnB;AAAA,QACA,SAAS,MAAM;AAAA,QACf,SAAS,MAAM;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AAClB,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,KAAK,qBAAqB;AAC5B,WAAK,cAAc,aAAa,KAAK,IAAI;AACzC,wBAAkB;AAClB,8BAAwB,KAAK,aAAa;AAC1C,4BAAsB,KAAK,kBAAkB;AAAA,IAC/C;AACA,QAAI,CAAC,KAAK,2BAA2B,WAAW;AAC9C,YAAM,sBAAsB,UAAU,KAAK,UAAU,aAAa,EAAE,UAAU,OAAK;AACjF,UAAE,eAAe;AAAA,MACnB,CAAC;AACD,YAAM,oBAAoB,UAAU,KAAK,UAAU,aAAa;AAAA,QAC9D,SAAS;AAAA,MACX,CAAC,EAAE,UAAU,oBAAkB;AAC7B,YAAI,KAAK,uBAAuB,CAAC,mBAAmB,uBAAuB;AACzE,4BAAkB,KAAK,gBAAgB,OAAO,gBAAgB,mBAAmB;AAAA,QACnF;AACA,YAAI,CAAC,KAAK,uBAAuB,CAAC,yBAAyB,iBAAiB;AAC1E,yBAAe,eAAe;AAC9B,eAAK,aAAa,KAAK;AAAA,YACrB,OAAO;AAAA,YACP,SAAS,eAAe,cAAc,CAAC,EAAE;AAAA,YACzC,SAAS,eAAe,cAAc,CAAC,EAAE;AAAA,UAC3C,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,WAAK,2BAA2B,YAAY,MAAM;AAChD,4BAAoB,YAAY;AAChC,0BAAkB,YAAY;AAAA,MAChC;AAAA,IACF;AACA,SAAK,aAAa,KAAK;AAAA,MACrB;AAAA,MACA,SAAS,MAAM,QAAQ,CAAC,EAAE;AAAA,MAC1B,SAAS,MAAM,QAAQ,CAAC,EAAE;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,KAAK,2BAA2B,WAAW;AAC7C,WAAK,2BAA2B,UAAU;AAC1C,aAAO,KAAK,2BAA2B;AACvC,UAAI,KAAK,qBAAqB;AAC5B,aAAK,aAAa;AAAA,MACpB;AAAA,IACF;AACA,SAAK,WAAW,KAAK;AAAA,MACnB;AAAA,MACA,SAAS,MAAM,eAAe,CAAC,EAAE;AAAA,MACjC,SAAS,MAAM,eAAe,CAAC,EAAE;AAAA,IACnC,CAAC;AAAA,EACH;AAAA,EACA,eAAe;AACb,SAAK,UAAU,KAAK,UAAU;AAAA,EAChC;AAAA,EACA,eAAe;AACb,SAAK,UAAU,EAAE;AAAA,EACnB;AAAA,EACA,UAAU;AACR,WAAO,KAAK,SAAS,KAAK,KAAK,SAAS;AAAA,EAC1C;AAAA,EACA,UAAU,OAAO;AACf,QAAI,CAAC,KAAK,2BAA2B,WAAW;AAC9C,WAAK,SAAS,SAAS,KAAK,QAAQ,eAAe,UAAU,KAAK;AAAA,IACpE;AAAA,EACF;AAAA,EACA,4BAA4B;AAC1B,WAAO,KAAK,KAAK,0BAA0B,EAAE,QAAQ,UAAQ;AAC3D,WAAK,2BAA2B,IAAI,EAAE;AACtC,aAAO,KAAK,2BAA2B,IAAI;AAAA,IAC7C,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB,SAAS,QAAQ;AAChC,WAAO,KAAK,MAAM,EAAE,QAAQ,SAAO;AACjC,WAAK,SAAS,SAAS,SAAS,KAAK,OAAO,GAAG,CAAC;AAAA,IAClD,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,iBAAiB;AACxB,aAAO,KAAK,gBAAgB,WAAW;AAAA,IACzC,OAAO;AACL,aAAO,KAAK,SAAS;AAAA,IACvB;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,QAAI,KAAK,iBAAiB;AACxB,aAAO;AAAA,QACL,KAAK,KAAK,gBAAgB,WAAW,cAAc;AAAA,QACnD,MAAM,KAAK,gBAAgB,WAAW,cAAc;AAAA,MACtD;AAAA,IACF,OAAO;AACL,aAAO;AAAA,QACL,KAAK,OAAO,eAAe,KAAK,SAAS,gBAAgB;AAAA,QACzD,MAAM,OAAO,eAAe,KAAK,SAAS,gBAAgB;AAAA,MAC5D;AAAA,IACF;AAAA,EACF;AAAA,EACA,gBAAgB,OAAO,gBAAgB,qBAAqB;AAC1D,UAAM,qBAAqB,KAAK,kBAAkB;AAClD,UAAM,cAAc;AAAA,MAClB,KAAK,KAAK,IAAI,mBAAmB,MAAM,oBAAoB,GAAG;AAAA,MAC9D,MAAM,KAAK,IAAI,mBAAmB,OAAO,oBAAoB,IAAI;AAAA,IACnE;AACA,UAAM,SAAS,KAAK,IAAI,eAAe,cAAc,CAAC,EAAE,UAAU,MAAM,QAAQ,CAAC,EAAE,OAAO,IAAI,YAAY;AAC1G,UAAM,SAAS,KAAK,IAAI,eAAe,cAAc,CAAC,EAAE,UAAU,MAAM,QAAQ,CAAC,EAAE,OAAO,IAAI,YAAY;AAC1G,UAAM,aAAa,SAAS;AAC5B,UAAM,kBAAkB,KAAK;AAC7B,QAAI,aAAa,gBAAgB,SAAS,YAAY,MAAM,KAAK,YAAY,OAAO,GAAG;AACrF,WAAK,cAAc,aAAa,KAAK,IAAI;AAAA,IAC3C;AACA,SAAK,cAAc,WAAW,KAAK,IAAI;AACvC,UAAM,WAAW,KAAK,cAAc,WAAW,KAAK,cAAc;AAClE,QAAI,YAAY,gBAAgB,OAAO;AACrC,WAAK,cAAc;AACnB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,eAAe;AACb,QAAI,KAAK,iBAAiB;AACxB,WAAK,SAAS,SAAS,KAAK,gBAAgB,WAAW,eAAe,YAAY,EAAE;AAAA,IACtF;AACA,SAAK,SAAS,SAAS,KAAK,SAAS,MAAM,YAAY,EAAE;AAAA,EAC3D;AAAA,EACA,gBAAgB;AAEd,QAAI,KAAK,iBAAiB;AACxB,WAAK,SAAS,SAAS,KAAK,gBAAgB,WAAW,eAAe,YAAY,QAAQ;AAAA,IAC5F;AACA,SAAK,SAAS,SAAS,KAAK,SAAS,MAAM,YAAY,QAAQ;AAAA,EACjE;AAAA,EACA,eAAe;AACb,UAAM,kBAAkB,KAAK,iBAAiB;AAC9C,UAAM,+BAA+B,gBAAgB,cAAc,gBAAgB;AACnF,UAAM,6BAA6B,gBAAgB,eAAe,gBAAgB;AAClF,WAAO,gCAAgC;AAAA,EACzC;AACF;AACA,mBAAmB,OAAO,SAAS,2BAA2B,mBAAmB;AAC/E,SAAO,KAAK,qBAAqB,oBAAuB,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAkB,eAAe,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,gBAAgB,GAAM,kBAAkB,mCAAmC,CAAC,GAAM,kBAAkB,QAAQ,CAAC;AAC7U;AACA,mBAAmB,OAAyB,kBAAkB;AAAA,EAC5D,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,EACpC,QAAQ;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,kCAAkC;AAAA,IAClC,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,sBAAsB;AAAA,IACtB,sBAAsB;AAAA,IACtB,qBAAqB;AAAA,IACrB,YAAY;AAAA,EACd;AAAA,EACA,SAAS;AAAA,IACP,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,qBAAqB;AAAA,IACrB,UAAU;AAAA,IACV,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,oBAAoB;AACpC,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,MACR,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,QAAQ;AAAA,MACjB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kCAAkC,CAAC;AAAA,MACjC,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,4BAA4B,SAAS,SAAS,MAAM;AAC3D,SAAO,WAAW,KAAK,QAAQ,WAAW,KAAK,SAAS,WAAW,KAAK,OAAO,WAAW,KAAK;AACjG;AACA,IAAM,qBAAN,MAAyB;AAAA,EACvB,YAAY,SAAS,iBAAiB,MAAM,UAAU,iBAAiB;AACrE,SAAK,UAAU;AACf,SAAK,kBAAkB;AACvB,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,kBAAkB;AAIvB,SAAK,YAAY,IAAI,aAAa;AAIlC,SAAK,YAAY,IAAI,aAAa;AAIlC,SAAK,WAAW,IAAI,aAAa;AAIjC,SAAK,OAAO,IAAI,aAAa;AAAA,EAC/B;AAAA,EACA,WAAW;AACT,SAAK,0BAA0B,KAAK,gBAAgB,YAAY,UAAU,WAAS;AACjF,eAAS,KAAK,UAAU,KAAK,SAAS,KAAK,eAAe;AAC1D,YAAM,mBAAmB;AAAA,QACvB,aAAa;AAAA,MACf;AACA,YAAM,2BAA2B,KAAK,SAAS,OAAO,KAAK,kBAAkB,KAAK,gBAAgB,WAAW,gBAAgB,UAAU,UAAU,MAAM;AACrJ,yBAAiB,cAAc;AAAA,MACjC,CAAC;AACD,UAAI;AACJ,YAAM,YAAY,MAAM,KAAK,IAAI,CAAC;AAAA,QAChC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,MAAM;AACJ,2BAAmB;AAAA,UACjB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,YAAI,iBAAiB,aAAa;AAChC,2BAAiB,OAAO,KAAK,QAAQ,cAAc,sBAAsB;AACzE,cAAI,KAAK,iBAAiB;AACxB,6BAAiB,sBAAsB,KAAK,gBAAgB,WAAW,cAAc,sBAAsB;AAAA,UAC7G;AACA,2BAAiB,cAAc;AAAA,QACjC;AACA,cAAM,kBAAkB,4BAA4B,SAAS,SAAS,iBAAiB,IAAI;AAC3F,cAAM,gBAAgB,CAAC,KAAK,gBAAgB,KAAK,aAAa;AAAA,UAC5D;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AACD,YAAI,iBAAiB,qBAAqB;AACxC,iBAAO,mBAAmB,iBAAiB,4BAA4B,SAAS,SAAS,iBAAiB,mBAAmB;AAAA,QAC/H,OAAO;AACL,iBAAO,mBAAmB;AAAA,QAC5B;AAAA,MACF,CAAC,CAAC;AACF,YAAM,mBAAmB,UAAU,KAAK,qBAAqB,CAAC;AAC9D,UAAI;AACJ,uBAAiB,KAAK,OAAO,iBAAe,WAAW,CAAC,EAAE,UAAU,MAAM;AACxE,yBAAiB;AACjB,iBAAS,KAAK,UAAU,KAAK,SAAS,KAAK,aAAa;AACxD,YAAI,KAAK,UAAU,UAAU,SAAS,GAAG;AACvC,eAAK,KAAK,IAAI,MAAM;AAClB,iBAAK,UAAU,KAAK,gBAAgB;AAAA,UACtC,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,gBAAU,KAAK,OAAO,iBAAe,WAAW,CAAC,EAAE,UAAU,MAAM;AACjE,YAAI,KAAK,SAAS,UAAU,SAAS,GAAG;AACtC,eAAK,KAAK,IAAI,MAAM;AAClB,iBAAK,SAAS,KAAK,gBAAgB;AAAA,UACrC,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,uBAAiB,KAAK,SAAS,GAAG,OAAO,CAAC,CAAC,YAAY,WAAW,MAAM,cAAc,CAAC,WAAW,CAAC,EAAE,UAAU,MAAM;AACnH,yBAAiB;AACjB,oBAAY,KAAK,UAAU,KAAK,SAAS,KAAK,aAAa;AAC3D,YAAI,KAAK,UAAU,UAAU,SAAS,GAAG;AACvC,eAAK,KAAK,IAAI,MAAM;AAClB,iBAAK,UAAU,KAAK,gBAAgB;AAAA,UACtC,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,YAAM,UAAU;AAAA,QACd,UAAU,MAAM;AACd,mCAAyB;AACzB,sBAAY,KAAK,UAAU,KAAK,SAAS,KAAK,eAAe;AAC7D,cAAI,gBAAgB;AAClB,wBAAY,KAAK,UAAU,KAAK,SAAS,KAAK,aAAa;AAC3D,gBAAI,KAAK,KAAK,UAAU,SAAS,GAAG;AAClC,mBAAK,KAAK,IAAI,MAAM;AAClB,qBAAK,KAAK,KAAK,gBAAgB;AAAA,cACjC,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,yBAAyB;AAChC,WAAK,wBAAwB,YAAY;AAAA,IAC3C;AAAA,EACF;AACF;AACA,mBAAmB,OAAO,SAAS,2BAA2B,mBAAmB;AAC/E,SAAO,KAAK,qBAAqB,oBAAuB,kBAAqB,UAAU,GAAM,kBAAkB,eAAe,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,SAAS,GAAM,kBAAkB,mCAAmC,CAAC,CAAC;AAClQ;AACA,mBAAmB,OAAyB,kBAAkB;AAAA,EAC5D,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,EACpC,QAAQ;AAAA,IACN,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,cAAc;AAAA,EAChB;AAAA,EACA,SAAS;AAAA,IACP,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,MAAM;AAAA,EACR;AAAA,EACA,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oBAAN,MAAwB;AAAC;AACzB,kBAAkB,OAAO,SAAS,0BAA0B,mBAAmB;AAC7E,SAAO,KAAK,qBAAqB,mBAAmB;AACtD;AACA,kBAAkB,OAAyB,iBAAiB;AAAA,EAC1D,MAAM;AAAA,EACN,cAAc,CAAC,oBAAoB,oBAAoB,iCAAiC;AAAA,EACxF,SAAS,CAAC,oBAAoB,oBAAoB,iCAAiC;AACrF,CAAC;AACD,kBAAkB,OAAyB,iBAAiB,CAAC,CAAC;AAAA,CAC7D,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,oBAAoB,oBAAoB,iCAAiC;AAAA,MACxF,SAAS,CAAC,oBAAoB,oBAAoB,iCAAiC;AAAA,IACrF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["Temp", "element", "document"]}