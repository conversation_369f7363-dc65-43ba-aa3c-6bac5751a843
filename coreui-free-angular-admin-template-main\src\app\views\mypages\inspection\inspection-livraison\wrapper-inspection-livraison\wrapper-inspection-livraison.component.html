<p>wrapper-inspection-livraison works!</p>
<app-smart-container [title]="'Inspection des livraisons'">
    <div slot="actions">
        <form [formGroup]="formRadio">
            <c-button-group aria-label="Basic radio toggle button group" role="group">
                <input class="btn-check" formControlName="radio" type="radio" value="all" />
                <label (click)="setRadioValue('all')" cButton cFormCheckLabel variant="outline">Tout</label>

                <input class="btn-check" formControlName="radio" type="radio" value="voyage" />
                <label (click)="setRadioValue('voyage')" cButton cFormCheckLabel variant="outline">Inspection par Voyage</label>

                <input class="btn-check" formControlName="radio" type="radio" value="conducteur" />
                <label (click)="setRadioValue('conducteur')" cButton cFormCheckLabel variant="outline">Inspection par Conducteur</label>

                <input class="btn-check" formControlName="radio" type="radio" value="camion" />
                <label (click)="setRadioValue('camion')" cButton cFormCheckLabel variant="outline">Inspection par Camion</label>
            </c-button-group>
        </form>
    </div>
    <div slot="content">

        <div [hidden]="formRadio.value['radio'] === 'all'">
            <app-inspection-all></app-inspection-all>
        </div>
        <div [hidden]="formRadio.value['radio'] === 'voyage'">
            <app-inspection-by-voyage></app-inspection-by-voyage>
        </div>
        <div [hidden]="formRadio.value['radio'] === 'conducteur'">
            <app-inspection-by-conducteur></app-inspection-by-conducteur>
        </div>
        <div [hidden]="formRadio.value['radio'] === 'camion'">
            <app-inspection-by-camion></app-inspection-by-camion>
        </div>
    </div>
</app-smart-container>