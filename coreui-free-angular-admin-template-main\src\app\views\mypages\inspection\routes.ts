import {Routes} from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    data: {
      title: 'Inspection'
    },
    children: [
        {
            path: 'inspection-colis',
            loadComponent: () => import('./inspection-colis/inspection-colis.component').then(m => m.InspectionColisComponent),
            data: {
              title: 'Inspection des colis'
            }
          },
        {
            path: 'inspection-livraison',
            loadComponent: () => import('./inspection-livraison/wrapper-inspection-livraison/wrapper-inspection-livraison.component').then(m => m.WrapperInspectionLivraisonComponent),
            data: {
              title: 'Inspection des livraisons'
            }
          },
          {
            path: 'calendrier-colis',
            loadComponent: () => import('./calendrier-colis/calendrier-colis.component').then(m => m.CalendrierColisComponent),
            data: {
              title: 'Calendrier des colis'
            }
          }
    ]
  }
];
