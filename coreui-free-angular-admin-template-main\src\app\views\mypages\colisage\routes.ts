import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    data: {
      title: 'colisage'
    },
    children: [
      {
        path: 'list-colisage',
        loadComponent: () => import('./list-colisage/list-colisage.component').then(m => m.ListColisageComponent),
        data: {
          title: 'Liste des colis'
        }
      },
      {
        path: 'insertion-colisage',
        loadComponent: () => import('./insertion-colisage/insertion-colisage.component').then(m => m.InsertionColisageComponent),
        data: {
          title: 'Insertion des colis'
        }
      }
    ]
  }
];
