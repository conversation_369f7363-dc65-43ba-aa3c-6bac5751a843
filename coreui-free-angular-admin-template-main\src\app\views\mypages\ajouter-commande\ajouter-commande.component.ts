import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DynamicFormComponent } from '../../../../shared/components/dynamic-form/dynamic-form.component';
import { SmartTableComponent } from '../../../../shared/components/smart-table/smart-table.component';
import { SmartContainerComponent } from '../../../../shared/components/smart-container/smart-container.component';
import { SmartButtonComponent } from '../../../../shared/components/smart-button/smart-button.component';
import { CommonModule } from '@angular/common';
import { ApiResponse, LoginFormData, User } from '../../../../models/auth.interfaces';
import { FormConfig } from '../../../../shared/models/form.models';
import { TableColumn, ActionButton } from '../../../../shared/models/table.models';

@Component({
  selector: 'app-ajouter-commande',
  standalone : true,
  imports: [
    FormsModule,
    DynamicFormComponent,
    SmartTableComponent,
    SmartContainerComponent,
    SmartButtonComponent,
    CommonModule
  ],
  templateUrl: './ajouter-commande.component.html',
  styleUrls: ['./ajouter-commande.component.scss']
})
export class AjouterCommandeComponent {
  // Form configuration
  formConfig: FormConfig = {
    title: 'Add New Order',
    fields: [
      {
        name: 'orderNumber',
        label: 'Order Number',
        type: 'text',
        required: true
      },
      {
        name: 'customerName',
        label: 'Customer Name',
        type: 'text',
        required: true
      },
      {
        name: 'orderDate',
        label: 'Order Date',
        type: 'date',
        required: true
      },
      {
        name: 'deliveryAddress',
        label: 'Delivery Address',
        type: 'textarea'
      }
    ],
    buttons: [
      {
        label: 'Clear',
        color: 'secondary',
        onClick: () => this.resetForm()
      }
    ]
  };

  // Table configuration
  tableColumns: TableColumn[] = [
    { name: 'orderNumber', displayName: 'Order #', sortable: true },
    { name: 'customerName', displayName: 'Customer', sortable: true },
    { name: 'orderDate', displayName: 'Date', dataType: 'date', sortable: true },
    { name: 'status', displayName: 'Status', dataType: 'status' }
  ];

  actionButtons: ActionButton[] = [
    {
      label: 'Edit',
      color: 'primary',
      callback: (row) => this.editOrder(row)
    },
    {
      label: 'Delete',
      color: 'danger',
      callback: (row) => this.deleteOrder(row)
    }
  ];

  tableData: any[] = []; // Will hold your table data

  editOrder(row: any) {
    // Implement edit functionality
  }

  deleteOrder(row: any) {
    // Implement delete functionality
  }

  resetForm() {
    // Reset form logic will be implemented when form data binding is set up
    console.log('Form reset requested');
  }
  saveCommande() {
    // Implement save functionality
    console.log('Commande saved');
  }
}
