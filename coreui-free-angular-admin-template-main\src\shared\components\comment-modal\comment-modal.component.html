<div class="modal fade show d-block" tabindex="-1" style="background: rgba(0,0,0,0.5);" role="dialog">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Commentaires</h4>
        <button type="button" class="btn-close" aria-label="Close" (click)="onClose()"></button>
      </div>

      <div class="modal-body d-flex flex-column" style="height: 300px; overflow: auto;">
        <div style="flex: 1 1 auto; overflow-y: auto;">
          <table class="table">
            <tbody>
              <tr *ngFor="let commentaire of commentList">
                <td style="width: 200px;">{{ commentaire.date_creation | date:'medium' }}</td>
                <td style="width: 150px;">{{ commentaire.nom_author }}</td>
                <td>{{ commentaire.value }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="form-group mt-3" style="flex-shrink: 0;">
          <div class="input-group">
            <input type="text" class="form-control"
                   (keyup.enter)="onSaveComment()"
                   [(ngModel)]="nouveauCommentaire"
                   placeholder="Ajouter un commentaire...">
            <button class="btn btn-primary"
                    type="button"
                    (click)="onSaveComment()"
                    [disabled]="!nouveauCommentaire">
              <c-icon name="cilPaperPlane"></c-icon>
            </button>
          </div>
        </div>
      </div>

      <div class="modal-footer" style="display: flex; justify-content: flex-end;">
        <button type="button" class="btn btn-secondary" (click)="onClose()">Fermer</button>
      </div>
    </div>
  </div>
</div>