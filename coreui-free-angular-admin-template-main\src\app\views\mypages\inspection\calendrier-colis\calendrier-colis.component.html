<section id="calendar" class="container-fluid px-0">
    <!-- Loading Indicator -->
    <div *ngIf="isLoading" class="loading-overlay d-flex justify-content-center align-items-center">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <!-- Header -->
    <div class="card mb-4">
        <div class="card-header bg-body-secondary">
            <h5 class="card-title mb-0">
                <i class="cil-package me-2"></i>Suivi des Colis par Date de Création
            </h5>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body" style="text-align: center;">
                    <div class="row row-cols-1 row-cols-md-3 row-cols-lg-5 g-3 ">
                        <div class="col" *ngFor="let stat of statData">
                            <div class="card h-100 shadow-sm cursor-pointer hover-shadow transition-all"
                                (click)="redirectToInspection(stat.status,stat.date_min_status,stat.date_max_status)">
                                <div class="card-body text-center">
                                    <div class="text-muted small mb-2">{{ stat.status | titlecase }}</div>
                                    <h3 class="mb-1">{{ stat.total }}</h3>
                                    <div class="text-primary small">
                                        <i class="cil-clock me-1"></i> {{ stat.moyenne_formatee }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendar Controls -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-center gap-3">
                <div class="btn-group">
                    <button class="btn btn-outline-primary" mwlCalendarPreviousView [view]="view" [(viewDate)]="viewDate">
                        <c-icon name="cil-chevron-left"></c-icon>
                        <span class="sr-only">Previous month</span>
                    </button>
                    <button class="btn btn-outline-primary" mwlCalendarToday [(viewDate)]="viewDate">
                        Aujourd'hui
                    </button>
                    <button class="btn btn-outline-primary" mwlCalendarNextView [view]="view" [(viewDate)]="viewDate">
                        <span class="sr-only">Next month</span>
                        <c-icon name="cil-chevron-right"></c-icon>
                    </button>
                </div>
                
                <h5 class="mb-0 text-center text-primary">
                    {{ viewDate | calendarDate:(view + 'ViewTitle'):'en' }}
                </h5>

                <div class="btn-group">
                    <!-- View toggle buttons if needed -->
                </div>
            </div>
        </div>
    </div>

    <!-- Calendar View -->
    <div class="card">
        <div class="card-body p-0">
            <div [ngSwitch]="view">
                <mwl-calendar-month-view *ngSwitchCase="'month'" 
                    [viewDate]="viewDate" 
                    [events]="events"
                    [refresh]="refresh" 
                    [activeDayIsOpen]="activeDayIsOpen"
                    (dayClicked)="dayClicked($event.day)" 
                    (eventClicked)="handleEvent('Clicked', $event.event)">
                </mwl-calendar-month-view>
            </div>
        </div>
    </div>
</section>

<!-- Modal -->
<ng-template #modalContent let-close="close">
    <div class="modal-header bg-body-secondary">
        <h4 class="modal-title">{{ title }}</h4>
        <button type="button" class="btn-close" (click)="close()" aria-label="Close"></button>
    </div>
    <div class="modal-body p-0">
        <div class="container-fluid p-4">
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-md-center mb-3 gap-2">
                <h5 class="mb-0">Package Details</h5>
                <app-export-button
                    [data]="packageList"
                    [fileName]="title"
                    [buttonText]="'Export'"
                    [buttonColor]="'primary'"
                    [buttonSize]="'sm'"
                    [buttonIcon]="'cil-cloud-download'">
                </app-export-button>
            </div>
            
            <div class="table-responsive">
                <app-smart-table
                    [config]="TableConfig"
                    [columns]="TableColumns"
                    [data]="packageList">
                </app-smart-table>
            </div>
        </div>
    </div>
    <div class="modal-footer bg-body-tertiary">
        <button type="button" class="btn btn-secondary" (click)="close()">
            <i class="cil-x me-1"></i> Close
        </button>
    </div>
</ng-template>