import { Component, OnInit } from '@angular/core';
import { SmartContainerComponent } from '../../../../../../shared/components/smart-container/smart-container.component';
import { SmartButtonComponent } from '../../../../../../shared/components/smart-button/smart-button.component';
import { SmartTableComponent } from '../../../../../../shared/components/smart-table/smart-table.component';
import { CircuitService } from '../../../../../../services/circuit.service';
import { PriceService } from '../../../../../../services/price.service';
import { ToastrService } from 'ngx-toastr';
import { CommonModule } from '@angular/common';
import { ActionButton, TableColumn } from '../../../../../../shared/models/table.models';
import { FormButton, FormConfig } from '../../../../../../shared/models/form.models';
import { FormModalComponent } from '../../../../../../shared/components/form-modal/form-modal.component';
@Component({
  selector: 'app-transport',
  imports: [SmartContainerComponent, SmartButtonComponent, SmartTableComponent, CommonModule, FormModalComponent],
  templateUrl: './transport.component.html',
  styleUrl: './transport.component.scss'
})
export class TransportComponent implements OnInit {
  
  showModal: boolean = false;
  isPasswordCorrect = false;
  initialData: any;
  enteredPassword = '';

  priceskeyValueArray: any[] = [];

  prices: any[] = [];
  loading = false;

  tableColumns: TableColumn[] = [
    { name: 'nom_circuit', displayName: 'Nom Circuit', sortable: true, filterable: true },
    { name: 'price_without_margin', displayName: 'Prix Sans Marge', sortable: true, filterable: true },
    { name: 'margin', displayName: 'Marge', sortable: true, filterable: true },
    { name: 'price', displayName: 'Prix', sortable: true, filterable: true },
  ];

  formConfig: FormConfig =
    {
      title: 'Modifier le prix',
      fields: [
        { name: 'nom_circuit', label: 'Nom Circuit', type: 'text'},
        { name: 'price_without_margin', label: 'Prix Sans Marge', type: 'number' ,onChange: (event: any) => console.log(event)},
        { name: 'margin', label: 'Marge', type: 'number' },
        { name: 'price', label: 'Prix', type: 'number' ,disabled: true},
      ],
      buttons: [
        { label: 'Annuler', color: 'secondary', onClick: () => this.closeModal() },
        { label: 'Modifier', color: 'warning', onClick: (formData: any) => this.ModifyPrice(formData) },
      ],
    }
    constructor(private circuitService: CircuitService,
      private priceService: PriceService,
      private toastr: ToastrService) { }

  ngOnInit(): void {
    if (this.isPasswordCorrect) {
      this.loadPrices();
    }
  }
  ModifyPrice(formData: any): void {
    this.priceService.updatePrices(formData.id, formData)
      .subscribe({
        next: () => {
          this.toastr.success('Prix mis à jour avec succès', 'Succès');
        },
        error: (error) => {
          console.error('Erreur lors de la mise à jour du circuit :', error);
          this.toastr.error('Une erreur est survenue lors de la mise à jour', 'Erreur');
        }
      });
  }

  onButtonClick(event: { button: FormButton, formData: any }) {
    if (event.button.label === 'Annuler') {
      this.closeModal();
    }
    if (event.button.label === 'Modifier') {
      this.saveChanges(event.formData);
    }
  }
  onFormChange(formData: any) {
    let newinitialData = {
      ...this.initialData,
      nom_circuit: formData.nom_circuit,
      price_without_margin: formData.price_without_margin,
      margin: formData.margin,
      price: formData.price_without_margin * (formData.margin / 100) + formData.price_without_margin
    }
    this.initialData = newinitialData;
  }
  closeModal() {
    this.showModal = false
    this.initialData = false
  }

  openModal(row: any) {
    this.initialData = row
    this.showModal = true

  }




  tableaction: ActionButton[] = [
    { icon: 'cil-pencil', color: 'warning', tooltip: 'Modifier', callback: (row: any) => this.openModal(row) },
  ];

  

  checkPassword() {
    const input = document.querySelector('input[id="password"]') as HTMLInputElement;
    let password = input?.value || "";
    if (password === 'zenAdmin') {
      this.isPasswordCorrect = true;
    }
    else {
      this.toastr.error('Mot de passe incorrect');
    }
    this.loadPrices();
  }


  loadPrices() {
    this.loading = true;
    this.priceService.getAllPrices().subscribe({
      next: (res: any) => {
        this.prices = res;
        console.log(this.prices);
      },
      error: (error: any) => {
        this.toastr.error('Une erreur est survenue lors de la récupération des circuits');
        console.log(error)
      },
      complete: () => {
        this.loading = false;
      }
    });
  }

  calculateUpdatePrice(price: any): void {
    price.price = Math.ceil(price.price_without_margin + (price.margin / 100) * price.price_without_margin);
  }

  public saveChanges(row: any): void {
    const updatedData = {
      nom_circuit: row.nom_circuit,
      price_without_margin: row.price_without_margin,
      margin: row.margin,
      price: row.price
    };

    this.circuitService.updateCircuitById(this.initialData.id, updatedData)
      .subscribe({
        next: () => {
          this.toastr.success('Circuit mis à jour avec succès', 'Succès');
          this.closeModal();
        },
        error: (error) => {
          console.error('Erreur lors de la mise à jour du circuit :', error);
          this.toastr.error('Une erreur est survenue lors de la mise à jour', 'Erreur');
          this.closeModal();
        }
      });
  }


}
