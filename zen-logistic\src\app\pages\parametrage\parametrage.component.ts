import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';
import { NgForm } from '@angular/forms';
import { Route, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CircuitService } from 'app/services/circuit.service';
import { CommandeService } from 'app/services/commande.service';
import { DestinationService } from 'app/services/destination.service';
import { FluxService } from 'app/services/flux.service';
import { FournisseurService } from 'app/services/fournisseur.service';
import { ImpotService } from 'app/services/impot.service';
import { PricesService } from 'app/services/prices.service';
import { RegisterServiceService } from 'app/services/register-service.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-parametrage',
  templateUrl: './parametrage.component.html',
  styleUrls: ['./parametrage.component.scss'],
  providers: []

})
export class ParametrageComponent implements OnInit {

  @ViewChild('userList', { static: true }) userList: TemplateRef<any>;

  newCircuit = {
    nom_circuit: '',
    price_without_margin: null,
    margin: null,
    price: null
  };
  editRow: number | null = null;
  typeMarchandise: String = "";
  typeCondition: String = "";
  LieuDech: String = "";
  marchandise: any = [];
  condition: any = [];
  villes: any = [];
  nom_locale: string;
  adresse: string;
  phone: string;
  email: string;
  ville: string = "";
  id_region: string = "";
  regions: any = [];
  nom_fournisseur = "";
  mat_fisc = "";
  matriculeFormatError: boolean = false;
  fournisseurList: any = []
  fournisseurAffected = ""
  nom_circuit = ""
  kilometrage = 0
  circuitList = []
  circuitAffected = ""
  typeDest: any = []
  selectedTypeDest: number[] = []
  show = false
  sous_traitant = false
  entreposage = false
  flux = false
  adress_client = ""
  prices: any = []
  enteredPassword: string = '';
  enteredPasswordFormula: string = ''
  isPasswordCorrect: boolean = false;
  isPasswordTvaCorrect: boolean = false;
  isPasswordFormulaCorrect: boolean = false;

  selectedPrice: any | null = null; // Pour stocker le prix sélectionné pour la modification
  editedRowIndex: number | null = null;
  selectedRow: number | null = null;
  originalPrices: any[] = [];
  distance = 0
  defaultPrixCourt = 0
  defaultPrixLong = 0
  saison: any = []
  email_client = ""
  // chefs : any =[]
  // clients: any =[]
  modalRef: any;
  clients: any[] = [];
  chefs: any[] = [];
  selectedChef: any = "";
  selectedUsers: any[] = [];
  selectedToAdd: any[] = [];
  selectedChefId = ''
  showFisc = true
  noUpdate = true
  users = []
  destinations = []
  selectedValues = []
  selectedUser = ""
  showDeletePopup = false
  deleteList = []
  code_cegid = ""
  enteredPasswordTva = ""

  impot: any = [];
  numSage = ""
  warehouses = [];
  warehousesEmit = [];
  warehousesReception = [];
  newWarehouse = { name: '', code: '', depot: true };
  selectedSteFacturation: any ="";
  selectedFromMarque: any ="";
  selectedFromWarehouse: any ="";
  selectedNature: string ="";
  selectedToMarque: any ="";
  selectedToWarehouse: any ="";
  selectedFromType:any =""
  selectedToType:any =""
  
  formulaList: any[] = [];


  marques= [];

  
  constructor(private registerService: RegisterServiceService,
    private destinationService: DestinationService,
    private fournisseurService: FournisseurService,
    private toastr: ToastrService,
    private modalService: NgbModal,
    private circuitService: CircuitService,
    private ligneCommandeService: CommandeService,
    private priceService: PricesService,
    private commandeService: CommandeService,
    private router: Router,
    private impotService: ImpotService,
    private fluxService :FluxService

  ) { }

  settings = {
    columns: {
      nom_marchandise: {
        title: 'Nom Marchandise'
      }
    },
    actions: {
      delete: true,   // Activer l'action de suppression
      add: false,     // Désactiver l'action d'ajout
      edit: false     // Désactiver l'action d'édition
    },
    delete: {
      confirmDelete: true,
      deleteButtonContent: '<i class="ft-x danger font-medium-1 mr-2"></i>',
    }
  };

  conditions = {
    columns: {
      nom_condition: {
        title: 'Nom Condition'
      }
    },
    actions: {
      delete: true,   // Activer l'action de suppression
      add: false,     // Désactiver l'action d'ajout
      edit: false     // Désactiver l'action d'édition
    },
    delete: {
      confirmDelete: true,
      deleteButtonContent: '<i class="ft-x danger font-medium-1 mr-2"></i>',
    }
  };



  async ngOnInit() {
    try {
      this.villes = await this.registerService.getVilles();
    } catch (error) {
      console.error("Erreur lors de la récupération des villes et des régions", error);
    }
    this.fournisseurService.getAllFournisseur().subscribe(response => (this.fournisseurList = response))
    this.typeDest = await this.registerService.getAlltypeCmd().toPromise();
    this.priceService.getAllPrices().subscribe(response => (
      this.prices = response
    ))
    this.originalPrices = this.prices.map(price => ({ ...price }));
    this.loadSaisons();
    this.registerService.findAllChef().subscribe(res => {
      this.chefs = res
    })
    this.registerService.findAllClient().subscribe(res => {
      this.clients = res
    })

    this.destinationService.findAllWarhouses().subscribe(res => {
      this.warehouses = res
    })

    this.getAllBrand()
  }

 async getAllBrand(){
  this.fluxService.getAllBrand().subscribe(res=>{
    this.marques = res
  })

  }

  loadSaisons() {
    this.commandeService.getAllSaison().subscribe(res => {
      this.saison = res;
    });
  }

  toggleEdit(saisonToEdit) {
    this.saison.forEach(oneSaison => {
      if (oneSaison === saisonToEdit) {
        if (!oneSaison.editing) {
          oneSaison.originalValue = oneSaison.value;
        }
        oneSaison.editing = !oneSaison.editing;
        oneSaison.editedValue = oneSaison.value;
      } else {
        oneSaison.editing = false;
      }
    });
  }

  cancelEditQte(saison) {
    saison.editing = false;
    saison.editedValue = saison.originalValue;
  }

  async saveValue(saison) {
    saison.editing = false;
    const ancienneValeur = saison.value; 
    saison.value = saison.editedValue; 
    console.log(saison);
    try {
      await this.commandeService.updateQteSaison(saison.id, saison.value);
      this.toastr.success('La quantité Moyenne a été mise à jour avec succès !');
    } catch (error) {
      saison.value = ancienneValeur;
      this.toastr.error('Une erreur s\'est produite lors de la mise à jour de la quantité de saison.');
      console.error('Erreur lors de la mise à jour de la quantité de saison:', error);
    }

  }






  async onDeleteConfirm(event: any): Promise<void> {
    console.log('Deleting ID:', event.data.id);

    if (window.confirm('Are you sure you want to delete?')) {
      try {
        await this.registerService.deleteMarchandise(event.data.id);
        console.log('Delete successful');
        event.confirm.resolve();
      } catch (error) {
        console.error('Delete error:', error);
        // Handle error, if needed
        event.confirm.reject();
      }
    } else {
      event.confirm.reject();
    }
  }

  async onDeleteCondition(event: any): Promise<void> {
    console.log('Deleting ID:', event.data.id);

    if (window.confirm('Are you sure you want to delete?')) {
      try {
        await this.registerService.deleteCondition(event.data.id);
        console.log('Delete successful');
        event.confirm.resolve();
      } catch (error) {
        console.error('Delete error:', error);
        // Handle error, if needed
        event.confirm.reject();
      }
    } else {
      event.confirm.reject();
    }
  }

  async addMaarchandise() {
    if (this.typeMarchandise !== "") {
      await this.registerService.addTypeMarchandise(this.typeMarchandise).subscribe(
        (res) => {
          this.toastr.success('Le Type marchandise sauvegarde avec succès');
          this.typeMarchandise = ""
        },
        (error) => {
          console.log(error)
          if (error.status === 500 && error.error && error.error.message.includes("ER_DUP_ENTRY")) {
            this.toastr.error('Le Type marchandise existe déjà');
            this.typeMarchandise = ""
          } else {
            console.log("Une erreur inattendue s'est produite.");
            this.typeMarchandise = ""
          }
        }
      );
    }
    else {
      this.toastr.error('Inseré le type à ajouté');

    }
  }

  async addCondition() {
    if (this.typeCondition !== "") {
      await this.registerService.addTypeCondition(this.typeCondition).subscribe(
        (res) => {
          this.toastr.success('Le Type conditionnement sauvegarde avec succès');
          this.typeCondition = ""
        },
        (error) => {
          console.log(error)
          if (error.status === 500 && error.error && error.error.message.includes("ER_DUP_ENTRY")) {
            this.toastr.error('Le Type Condition existe déjà');
            this.typeCondition = ""
          } else {
            console.log("Une erreur inattendue s'est produite.");
            this.typeCondition = ""
          }
        }
      );
    }
    else {
      this.toastr.error('Inseré le Condition à ajouté');

    }
  }




  async openModalCondition(content) {
    this.condition = await this.registerService.getAllCondition().toPromise();

    this.modalService.open(content, { centered: true });
  }

  async openModal(content) {
    this.marchandise = await this.registerService.getAllMarchandise().toPromise();

    this.modalService.open(content, { centered: true });
  }

  async onVilleChange(idVille) {
    console.log(idVille)
    await this.registerService.findRegion(idVille).subscribe(
      (data: any) => {
        // Logique à exécuter lorsque les données sont disponibles
        this.regions = data;
      },
      (error: any) => {
        // Gérer les erreurs ici
        console.error('Erreur lors de la récupération des régions :', error);
      }
    );

  }

  async addDestination() {

    // Récupérer les IDs des types sélectionnés à partir de this.typeDest
    const selectedTypeIds = this.typeDest
      .filter(typeDest => typeDest.selected)
      .map(selectedType => selectedType.id);

    if (selectedTypeIds.length == 0) {
      this.toastr.error("Merci de choisir les types destination");
      return
    }

    if (
      this.nom_locale !== "" &&
      this.adresse !== "" &&
      this.id_region !== "" &&
      this.fournisseurAffected !== "" &&
      selectedTypeIds.length !== 0
    ) {
      // Créer l'objet de données à envoyer au service
      const data = {
        nom_locale: this.nom_locale,
        adresse: this.adresse,
        phone: this.phone,
        email: this.email,
        id_region: this.id_region,
        id_fournisseur: [this.fournisseurAffected],
        id_types: selectedTypeIds
      };

      // Appeler le service pour ajouter la destination
      await this.destinationService.addDestination(data).subscribe(
        response => {
          // Gérer ici la réponse réussie, si nécessaire
          console.log('Destination ajoutée avec succès', response);
          // Réinitialiser les valeurs des champs
          this.nom_locale = "";
          this.adresse = "";
          this.phone = "";
          this.email = "";
          this.id_region = "";

          this.regions = []
          this.ville = ""
          this.show = false;
          // Décocher toutes les cases à cocher
          this.typeDest.forEach(typeDest => typeDest.selected = false);

          // Afficher un message Toast de succès
          this.toastr.success("Destination ajoutée avec succès");

        },
        error => {
          // Gérer ici les erreurs lors de l'appel au service
          console.error('Erreur lors de l\'ajout de la destination', error);
          // Afficher un message Toast d'erreur
          this.toastr.error("Erreur lors de l'ajout de la destination");
        }
      );
    } else {
      // Afficher un message d'erreur si toutes les propriétés ne sont pas remplies
      console.error('Toutes les propriétés doivent être renseignées');
      this.toastr.error("Merci de remplir tout les champs du nouvelle destination");
      // Vous pouvez également informer l'utilisateur ici, si nécessaire
    }
  }



  async addFournisseur() {
    if ((this.entreposage || this.flux) && this.code_cegid == "") {
      this.toastr.error('Code Cegid est obligatoire');
      return;
    }
    const status = await this.validateMatriculeFormat();
    console.log(status)
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(this.email_client)) {
      this.toastr.error('Adresse e-mail invalide');
      return;
    }

    if (status || this.mat_fisc === "0000") {
      if (this.mat_fisc === "" || this.adress_client === "" || this.email_client === "") {
        this.toastr.error('Merci de remplir tous les champs obligatoires');
        return;
      }
      if (this.mat_fisc != "0000") {

      }
      const fournisseurData = {
        nom_fournisseur: this.nom_fournisseur,
        mat_fisc: this.mat_fisc,
        sous_traitant: this.sous_traitant,
        adresse: this.adress_client,
        mail: this.email_client,
        flux: this.flux,
        entreposage: this.entreposage,
        code_cegid: this.code_cegid,
        code_sageX3: this.numSage
      };

      if (this.mat_fisc == "0000") {
        await this.addFournisseurToDB(fournisseurData);
      } else {
        await this.checkAndAddFournisseur(fournisseurData);
      }
    } else {
      this.toastr.error("Probléme au niveau matricule");
      return;

    }

  }

  async addFournisseurToDB(data) {
    try {
      const addResponse = await this.fournisseurService.addFournisseur(data).toPromise();
      console.log('Fournisseur ajouté avec succès', addResponse);

      this.resetFormFields();
      this.toastr.success("Fournisseur ajouté avec succès");
      this.updateFournisseurList();
    } catch (error) {
      this.handleServerError("Erreur lors de l'ajout du Fournisseur");
    }
  }

  async checkAndAddFournisseur(data) {
    try {
      const response = await this.fournisseurService.findFournisseurByMatricule(this.mat_fisc).toPromise();

      if (response && response.fournisseur) {
        this.toastr.error(`Fournisseur ${response.fournisseur[0].nom_fournisseur} déjà existant`);
      } else {
        await this.addFournisseurToDB(data);
      }
    } catch (error) {
      this.handleServerError("Erreur lors de la recherche du Fournisseur");
    }
  }

  resetFormFields() {
    this.nom_fournisseur = "";
    this.mat_fisc = "";
    this.adress_client = "";
    this.sous_traitant = false;
    this.email_client = ""
    this.showFisc = true
    this.entreposage = false
    this.flux = false
    this.numSage = ""
  }

  updateFournisseurList() {
    this.fournisseurService.getAllFournisseur().subscribe((response) => {
      this.fournisseurList = response;
    });
  }

  handleServerError(message: string) {
    this.toastr.error(`Erreur côté serveur : ${message}`);
  }





  async validateMatriculeFormat(): Promise<boolean> {
    const matriculeRegex = /^([0-9]{7})\/([A-Z])\/[A-Z]\/[A-Z]\/[0-9]{3}$/;
    this.mat_fisc = this.mat_fisc.toUpperCase();
    console.log("Original matricule:", this.mat_fisc);
  
    const match = this.mat_fisc.match(matriculeRegex);
    if (match) {
      const sevenDigits = match[1]+match[2];
      const firstLetter = match[2];
      console.log("Extracted 7 digits:", sevenDigits);
      console.log("Extracted first letter:", firstLetter);
  
      return new Promise(async (resolve) => {
        this.fournisseurService.findClientFromSageByMat(sevenDigits).subscribe(
          (res) => {
            if (res.length > 0) {
              if (res.length > 1) {
                this.toastr.error("Au niveau Sage, plusieurs clients avec cette matricule");
                return resolve(false); 
              }
              this.numSage = res[0].BPRNUM_0;
              resolve(true);
            } else {
              this.toastr.error("Ajouter le client au niveau ERP de facturation SAGE X3");
              resolve(false);
            }
          },
          (error) => {
            this.toastr.error("Error retrieving client: " + error);
            resolve(false);
          }
        );
      });
    } else {
      console.log("Invalid matricule format");
      return false;
    }
  }
  

  // validateMatriculeFormat(): boolean {
  //   // Expression régulière avec le premier groupe de chiffres en format "0000" en option
  //   const matriculeRegex = /^([0-9]{7}\/[A-Z]\/[A-Z]\/[A-Z]\/[0-9]{3})$/;
  //   // Convertir le matricule en majuscules
  //   this.mat_fisc = this.mat_fisc.toUpperCase();
  //   console.log(this.mat_fisc)
  //   // Vérifier la validité du matricule en utilisant la regex
  //   if (matriculeRegex != null && !matriculeRegex.test(this.mat_fisc) && this.mat_fisc != "0000") {
  //     return false;
  //   }

  //   return true;
  // }


  async addCircuit() {
    console.log(this.nom_circuit, this.kilometrage);

    try {
      if (this.nom_circuit !== "" && this.kilometrage > 0) {
        const data = {
          nom_circuit: this.nom_circuit,
          kilometrage: this.kilometrage
        };

        await this.circuitService.addCircuit(data);
        this.toastr.success('Circuit ajouté avec succès');
        this.nom_circuit = ""
        this.kilometrage = 0
        this.circuitList = await this.circuitService.getAllCircuits();

      } else {
        this.toastr.error('Merci de remplir tous les champs du circuit');
      }
    } catch (error) {
      console.error('Erreur lors de l\'ajout du circuit', error);
      this.toastr.error('Une erreur est survenue lors de l\'ajout du circuit');
    }
  }

  showList() {
    this.show = true
  }

  onMatriculeInput(event: any) {
    let input = event.target.value.replace(/[^a-zA-Z0-9]/g, '');

    let formattedMatricule = '';
    for (let i = 0; i < input.length; i++) {
      if (i === 7 || i === 8 || i === 9 || i === 10) {
        if (input[i] !== '/') {
          formattedMatricule += '/';
        }
      }
      formattedMatricule += input[i];
    }

    // Mettre à jour la valeur du matricule dans le modèle
    this.mat_fisc = formattedMatricule;
  }

  checkPassword() {
    this.isPasswordCorrect = this.enteredPassword === 'zenAdmin';
    if (this.enteredPassword !== 'zenAdmin') {
      this.toastr.error("Mot de passe incorrecte")
    }
  }

  async checkPasswordTva() {
    this.isPasswordTvaCorrect = this.enteredPasswordTva === 'zenAdmin';
    if (this.enteredPasswordTva !== 'zenAdmin') {
      this.toastr.error("Mot de passe incorrecte")
      return
    }
    this.impotService.getImpot().subscribe(res => {
      this.impot = res
      console.log(this.impot)
    })
  }

  async checkPasswordFormula() {
    this.isPasswordFormulaCorrect = this.enteredPasswordFormula === 'zenAdmin';
    if (this.enteredPasswordFormula !== 'zenAdmin') {
      this.toastr.error("Mot de passe incorrecte")
      return
    }
    this.getAllFormula()

  }

  async updateImpot(updatedImpot: any) {
    console.log('Mise à jour des données :', updatedImpot);
  
    const isValid = (obj: any) => {
      for (const key in obj) {
        if (obj[key] === null || obj[key] === '') {
          return false;
        }
      }
      return true;
    };
  
    if (!isValid(updatedImpot)) {
      this.toastr.error('Tous les champs doivent être remplis', 'Erreur');
      return;
    }
  
    const hasZeroValue = Object.values(updatedImpot).some(value => value === 0);
  
    if (hasZeroValue) {
      const confirmation = window.confirm('Êtes-vous sûr d\'enregistrer un prix à 0 ?');
      if (!confirmation) {
        this.toastr.info('Mise à jour annulée', 'Info');
        return;
      }
    }
  
    try {
      const res = await this.impotService.updateById(updatedImpot.id, updatedImpot);
      this.toastr.success('Les données ont été mises à jour avec succès', 'Succès');
    } catch (error) {
      console.error('Erreur lors de la mise à jour :', error);
      this.toastr.error('Une erreur est survenue lors de la mise à jour', 'Erreur');
    }
  }
  


  selectRow(index: number, id: number, distance: number, pc: number, pl: number, type_ligne: string) {
    this.noUpdate = true
    console.log(index);
    this.selectedRow = index;
    this.distance = distance
    this.defaultPrixCourt = pc
    this.defaultPrixLong = pl
    if (type_ligne == "Transfert administratif ZEN Group-Magasin ou dépôt" ||
      type_ligne == "Transfert administratif technique et matériel informatique" ||
      type_ligne == "Livraison fourniture" ||
      type_ligne == "Transfert Administratif inter magasin") {
      this.noUpdate = false
    }

  }

  async saveEditedPrice() {
    // Implémentez la logique pour sauvegarder les modifications dans votre backend
    console.log('Prix modifié :', this.prices[this.selectedRow]);
    console.log('Prix modifié :', this.prices[this.selectedRow].id);
    if (this.prices[this.selectedRow].pourcentage > 100) {
      this.prices[this.selectedRow].pourcentage = 100
    }
    const data = {
      distance: this.prices[this.selectedRow].distance,
      prix_court_distance: this.prices[this.selectedRow].prix_court_distance,
      prix_long_distance: this.prices[this.selectedRow].prix_long_distance,
      montant: this.prices[this.selectedRow].montant,
      pourcentage: this.prices[this.selectedRow].pourcentage,

    }
    console.log(data)
    await this.priceService.updatePrices(this.prices[this.selectedRow].id, data)

    this.selectedRow = null; 
    this.noUpdate = true

  }

  cancelEdit() {
    if (this.selectedRow !== null) {
      this.prices[this.selectedRow].distance = this.distance;
      this.prices[this.selectedRow].prix_court_distance = this.defaultPrixCourt;
      this.prices[this.selectedRow].prix_long_distance = this.defaultPrixLong;

      this.selectedRow = null; 
    }
  }
  redirectDestinations() {
    this.router.navigate(['pages/gestion-dest']);
  }

  openModalChef() {
    this.modalRef = this.modalService.open(this.userList, {
      ariaLabelledBy: 'modal-basic-title',
      windowClass: 'custom-modal-style',
      backdrop: 'static'
    });
  }

  onChefChange(selectedChefId: any) {
    this.selectedChefId = selectedChefId

    this.registerService.findUsersByChef(selectedChefId).subscribe(res => {
      this.selectedUsers = res
    })
  }

  onUserToAddChange(selectedUser: any) {
    if (Array.isArray(selectedUser) && selectedUser.length > 0) {
      const userToAdd = selectedUser[0];
      console.log(userToAdd);
      console.log(userToAdd.nom_utilisateur);

      const indexInClients = this.clients.findIndex(user => user.id === userToAdd.id);
      if (indexInClients !== -1) {
        this.clients.splice(indexInClients, 1);
      }

      if (!this.selectedUsers.includes(userToAdd)) {
        this.selectedUsers.push(userToAdd);
        const indexInSelectedToAdd = this.selectedToAdd.indexOf(userToAdd);
        if (indexInSelectedToAdd !== -1) {
          this.selectedToAdd.splice(indexInSelectedToAdd, 1);
        }
      }
    }
  }


  removeUser(user: any) {
    const index = this.selectedUsers.indexOf(user);
    if (index !== -1) {
      this.selectedUsers.splice(index, 1);
      this.clients.push(user);
      const indexInSelectedToAdd = this.selectedToAdd.indexOf(user);
      if (indexInSelectedToAdd !== -1) {
        this.selectedToAdd.splice(indexInSelectedToAdd, 1);
      }
    }
  }

  cancel() {
    this.selectedUsers = [];
    this.selectedToAdd = this.clients.slice(); 
    this.selectedChef = "";
    this.modalRef.dismiss();
  }

  save() {
    if (this.selectedUsers.length == 0 || this.selectedChefId == "") {
      this.toastr.error("Merci de choisir un chef et utilisateur")
      return;
    }
    const selectedUserIds = this.selectedUsers.map(user => user.id);
    console.log('ID des utilisateurs sélectionnés :', selectedUserIds);
    const data = {
      id_chef: this.selectedChefId,
      id_users: selectedUserIds
    };
    this.registerService.createChefUser(data).then(response => {
      // Gérer la réponse de la création des utilisateurs chef ici
      console.log('Réponse de la création des utilisateurs chef :', response);
      this.toastr.success("Groupe créé avec succès !");
      this.selectedChef = "";
      this.selectedUsers = []

    }).catch(error => {
      // Gérer les erreurs ici
      console.error('Erreur lors de la création des utilisateurs chef :', error);
      this.toastr.success("Erreur de création !!");

    });
    this.modalRef.close();
  }

  noFacturation() {
    // Si la case à cocher est cochée, définir mat_fisc sur "0000", sinon, le laisser vide
    if (this.showFisc) {
      this.mat_fisc = ""
    }
    this.mat_fisc = this.showFisc ? "0000" : "";
    this.showFisc = !this.showFisc
  }



  async openModalDestination(content) {
    //this.condition = await this.registerService.getAllCondition().toPromise();
    this.registerService.listCustomers().subscribe(res => {
      this.users = res
    })
    this.modalService.open(content, { centered: true });
  }

  async openModalCircuit(content) {

    this.modalService.open(content, { centered: true });
  }

  onSelectFournisseur(selectedValue: any) {
    this.selectedValues = []

    console.log('Fournisseur sélectionné :', selectedValue);

    this.destinationService.findDestinationByCompany(selectedValue).subscribe(res => {
      this.destinations = res.destination;
      if (this.destinations.length === 0) {
        console.log("Aucune donnée trouvée !");
        this.toastr.error('Aucune données trouvées');
      }
    });
  }

  onSelectDestinations(selectedOptions: HTMLOptionsCollection) {
    this.selectedValues = Array.from(selectedOptions).map((option) => option.value);
    console.log('Destinations sélectionnées :', this.selectedValues);
    // Faites ce que vous voulez avec les valeurs sélectionnées
  }

  saveUserByDestination() {
    if (this.selectedUser) {
      this.destinationService.assignDestinationToUser(this.selectedUser, this.selectedValues).subscribe(
        (res) => {
          // Message de succès
          this.toastr.success('La destination affecté avec succès.');
        },
        (err) => {
          // Message d'erreur
          this.toastr.error('Erreur lors de la affectation de la destination.', 'Erreur');
        }
      );

    } else {
      this.toastr.error('Merci de séléctionner l\'utilisateur concerné');

    }

  }

  showDeletePop() {
    this.showDeletePopup = true
    this.destinations = []
    this.deleteList = []
    this.selectedUser = ""
  }
  showAddPop() {
    this.showDeletePopup = false
    this.destinations = []
    this.deleteList = []

  }
  selectedUserDelete(selectedValue: any) {
    this.destinationService.findDestinationByUser(selectedValue).subscribe(res => {
      this.deleteList = res.destination
      console.log(this.deleteList)

    })  

  }

  removeDestinationFromUser(id: number): void {
    this.destinationService.deleteDestinationByUser(id).subscribe(
      (res) => {
        // Message de succès
        this.toastr.success('La destination a été supprimée avec succès.');

        // Supprimer l'élément de `deleteList` qui a cet ID
        const index = this.deleteList.findIndex((item) => item.id === id);
        if (index !== -1) {
          // Utiliser splice pour supprimer l'élément à cet index
          this.deleteList.splice(index, 1);
        }
      },
      (err) => {
        // Message d'erreur
        this.toastr.error('Erreur lors de la suppression de la destination.');
      }
    );
  }

  async saveCircuit(circuitForm: NgForm) {
    console.log(circuitForm.valid)
    if (circuitForm.valid) {
      try {
        console.log('Circuit ajouté', this.newCircuit);
        await this.priceService.addCircuit(this.newCircuit).subscribe(res => {
          console.log(res)
        });

        this.newCircuit = {
          nom_circuit: '',
          price_without_margin: null,
          margin: null,
          price: null
        };

        this.modalService.dismissAll();

        this.toastr.success('Le circuit a été ajouté avec succès !');
        setTimeout(() => {
          this.priceService.getAllPrices().subscribe(response => {
            this.prices = response;
          });
        }, 2000);

      } catch (error) {
        console.error('Erreur lors de l\'ajout du circuit :', error);
        this.toastr.error('Une erreur est survenue lors de l\'ajout du circuit. Veuillez réessayer.');
      }
    } else {
      this.toastr.error('Veuillez remplir tous les champs obligatoires.');
    }
  }

  calculatePrice() {
    const { price_without_margin, margin } = this.newCircuit;
    this.newCircuit.price = Math.ceil(price_without_margin + (margin / 100) * price_without_margin);
  }
  calculateUpdatePrice(price: any): void {
    price.price = Math.ceil(price.price_without_margin + (price.margin / 100) * price.price_without_margin);
  }

  addWarehouse(): void {
    if (this.newWarehouse.name && this.newWarehouse.code) {
      console.log('Depot ajouté', this.newWarehouse);
      this.destinationService.addWarehouse(this.newWarehouse).subscribe(
        () => {
          this.toastr.success('Le dépôt a été ajouté avec succès !');
          this.warehouses.push({ ...this.newWarehouse });

        },
        (error) => {
          this.toastr.error('Erreur lors de l\'ajout du dépôt !');
        }
      );
      this.newWarehouse = { name: '', code: '', depot: true };
    } else {
      this.toastr.error('Veuillez remplir les champs Nom et Code.');
    }
  }


  public saveChanges(price: any): void {
    const updatedData = {
      nom_circuit: price.nom_circuit,
      price_without_margin: price.price_without_margin,
      margin: price.margin,
      price: price.price
    };

    this.circuitService.updateCircuitById(price.id, updatedData)
      .then(() => {
        this.toastr.success('Circuit mis à jour avec succès', 'Succès');
        this.editRow = null;
      })
      .catch(error => {
        console.error('Erreur lors de la mise à jour du circuit :', error);
        this.toastr.error('Une erreur est survenue lors de la mise à jour', 'Erreur');
      });
  }
  
 

  onEmitterChange() {
    if(this.selectedFromMarque && this.selectedFromType){
      this.destinationService.findWarhousesByBrand(this.selectedFromMarque.id,this.selectedFromType).subscribe(res=>{
        this.warehousesEmit=res
      })
    }
  }
  
  onReceiverChange() {

    if(this.selectedToMarque && this.selectedToType){
      this.destinationService.findWarhousesByBrand(this.selectedToMarque.id,this.selectedToType).subscribe(res=>{
        this.warehousesReception=res
      })
    }
}


addFormula() {
  // Liste des champs à vérifier
  const fields = [
    { name: 'selectedFromMarque', label: 'Marque Émetteur' },
    { name: 'selectedFromWarehouse', label: 'Dépôt Émetteur' },
    { name: 'selectedFromType', label: 'Type de Marque Émetteur' },
    { name: 'selectedNature', label: 'Nature' },
    { name: 'selectedToMarque', label: 'Marque Récepteur' },
    { name: 'selectedToWarehouse', label: 'Dépôt Récepteur' },
    { name: 'selectedToType', label: 'Type de Marque Récepteur' },
    { name: 'selectedSteFacturation', label: 'Société de Facturation' }
  ];

  // Vérification des champs non remplis
  for (const field of fields) {
    if (!this[field.name]) {
      this.toastr.error(`Veuillez choisir la ${field.label}.`);
      return;
    }
  }

  const formula = {
    invoiced_for: this.selectedSteFacturation.id,
    invoiced_name: this.selectedSteFacturation.name,
    nature: this.selectedNature,
    id_em_brand: this.selectedFromMarque.id,
    name_em_brand: this.selectedFromMarque.name,
    type_em: this.selectedFromType,
    id_em_wharehouse: this.selectedFromWarehouse.id,
    name_em_wharehouse: this.selectedFromWarehouse.name,
    id_rec_brand: this.selectedToMarque.id,
    name_rec_brand: this.selectedToMarque.name,
    type_rec: this.selectedToType,
    id_rec_wharehouse : this.selectedToWarehouse.id,
    name_rec_wharehouse: this.selectedToWarehouse.name,
    enabled: true
  };

  this.fluxService.addFormula(formula).subscribe(
    (res) => {
      this.getAllFormula()
      this.selectedFromMarque = "";
      this.selectedFromWarehouse = "";
      this.selectedFromType = "";
      this.selectedNature = "";
      this.selectedToMarque = "";
      this.selectedToWarehouse = "";
      this.selectedToType = "";
      this.selectedSteFacturation = "";
      this.toastr.success('Formule ajoutée avec succès.');
    },
    (err) => {
      this.toastr.error('Une erreur est survenue lors de l\'ajout de la formule.');
      console.log("Error: ", err);
    }
  );
}

async getAllFormula(){
  this.fluxService.getAllFormula().subscribe(res=>{
    this.formulaList=res
  })
}


async deleteFormula(id: number) {
  try {
    const confirmation = confirm("Voulez-vous vraiment supprimer cette formule ?");
    if (!confirmation) return;
    await this.fluxService.deleteFormula(id).toPromise();
        this.formulaList = this.formulaList.filter(f => f.id !== id);
        this.toastr.success('La formule a été supprimée avec succès.');

  } catch (error) {
    console.error("Erreur lors de la suppression :", error);
    this.toastr.error('Une erreur est survenue lors de la suppression de la formule.');
  }
}

async disableWharehouse(id: number) {
  try {
    const confirmation = confirm("Voulez-vous vraiment supprimer ce dépôt ?");
    if (!confirmation) return;

    await this.destinationService.disableWharehouse(id).toPromise();
    this.warehouses = this.warehouses.filter(warehouse => warehouse.id != id);

    // Message de succès
    this.toastr.success('Le dépôt a été supprimé avec succès.');

  } catch (error) {
    console.error("Erreur lors de la suppression :", error);
    
    // Message d'erreur
    this.toastr.error('Une erreur est survenue lors de la suppression du dépôt.');
  }
}




  
}



