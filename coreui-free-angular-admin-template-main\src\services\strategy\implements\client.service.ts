import { Injectable } from '@angular/core';
import { ServiceStrategy } from '../service-strategy';
import { landingPage } from '../enum/landing-page.enum';
import { LigneCmdService } from '../../ligne-cmd.service';
import { Observable } from 'rxjs';
import { CommandeService } from '../../commande.service';

@Injectable({
  providedIn: 'root'
})
export class ClientService implements ServiceStrategy {

  constructor(
    private ligneService: LigneCmdService,
    private commandeService: CommandeService
  ) { }

  getLandingPage(): landingPage {
    // Example implementation: return a default landing page enum value
    return landingPage.Client;
  }

  findReservedLineByUserId(userId: string): Observable<any> {
    return this.ligneService.findReservedByIdUser(userId);
  }
  findExpediedLineByUserId(userId: string): Observable<any> {
    return this.ligneService.findExpediedByIdUser(userId);
  }

  findDeliveredLineByUserId(userId: string): Observable<any> {
    return this.ligneService.findDeliveredByIdUser(userId);
  }
  findEnrgCommandsByUserId(userId: string): Observable<any> {
    return this.commandeService.findEnrgCommandeModele(userId);
  }
  getNonReservedCommands(userId: string): Observable<any> {
    return this.commandeService.getAllOrdersByUser(userId);
  }
  findCommandeToValidate(userId: string): Observable<any> {
    return this.commandeService.findComToValidateByUser(userId);
  }
  findValidCommands(userId: string): Observable<any> {
    return this.commandeService.findAllComValidByUser(userId);
  }
}


