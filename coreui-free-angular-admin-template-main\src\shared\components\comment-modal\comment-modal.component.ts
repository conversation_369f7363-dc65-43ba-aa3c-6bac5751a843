// comment-modal.component.ts
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconModule } from '@coreui/icons-angular';

@Component({
  selector: 'app-comment-modal',
  standalone: true,
  imports: [CommonModule, FormsModule, IconModule],
  templateUrl: './comment-modal.component.html',
  styleUrls: ['./comment-modal.component.scss']
})
export class CommentModalComponent {
  @Input() commentList: any[] = [];
  @Input() rowId: number | null = null;
  @Output() saveComment = new EventEmitter<{id: number, comment: string}>();
  @Output() closeModal = new EventEmitter<void>();

  nouveauCommentaire = '';

  onSaveComment() {
    if (this.nouveauCommentaire && this.rowId) {
      this.saveComment.emit({
        id: this.rowId,
        comment: this.nouveauCommentaire
      });
      this.nouveauCommentaire = '';
    }
  }

  onClose() {
    this.closeModal.emit();
  }
}