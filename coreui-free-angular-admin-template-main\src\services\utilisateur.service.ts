import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../environments/environment';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};
@Injectable({
  providedIn: 'root'
})
export class UtilisateurService {
  private apiURL = environment.apiURL;

  constructor(private http: HttpClient) { }

  getAllClients(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'getAllClients', httpOptions);
  }

  disableClient(customerId:any): Observable<any> {
    return this.http.get<any>(this.apiURL + `customers/disable/${customerId}`, httpOptions);
  }

  findAllExpediteur(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'customer/Expediteur', httpOptions);
  }
  update(data:any, id : any): Observable<any> {
    return this.http.put<any>(this.apiURL + 'cust/' + id, data, httpOptions);
  }

  setclientToExp(idClient:any, idExp:any) : Observable<any> {
    const  data = {
      idExp: idExp,
      idClient: idClient
    }
    return this.http.post<any>(this.apiURL + 'customers/setclient', data, httpOptions);
  }

  updateClientColor(id:any, data:any): Observable<any> {
    return this.http.put<any>(this.apiURL + 'updateClientColor/' + id, data, httpOptions);
  }

  updateUserStatus(user: any): Observable<any> {
    return this.http.put<any>(this.apiURL + 'custumm/'+user.id, user, httpOptions);
  }

  updateUserInfo(data: any): Observable<any> {
    return this.http.put<any>(this.apiURL + 'updateUserById/' + data.id, data, httpOptions);
  }

  findUserByMail(email:any): Observable<any> {
    return this.http.get<any>(this.apiURL +`getUserByMail/${email}`,httpOptions);
  }

  addUsers(data : any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'customers', data, httpOptions);
  }

  findAllChef(): Observable<any> {
    return this.http.get<any>(this.apiURL +`findAllChef`,httpOptions);
  }




  createChefUser(data:any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'createChefUser', data,  httpOptions);
  }

  findUsersByChefId(selectedChefId:any): Observable<any> {
    return this.http.get<any>(this.apiURL +`findUsersByChef/${selectedChefId}`,httpOptions);
  }
  
}
