// dynamic-modal.component.scss
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;

  &.show {
    opacity: 1;
    visibility: visible;
  }

  &.backdrop {
    background-color: rgba(0, 0, 0, 0.5);
  }
}

.modal-content {
  background: #fff;
  border-radius: 0.3rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  width: 100%;
  transition: transform 0.3s ease, opacity 0.3s ease;

  &.modal-sm {
    max-width: 300px;
  }

  &.modal-md {
    max-width: 500px;
  }

  &.modal-lg {
    max-width: 800px;
  }

  &.modal-xl {
    max-width: 90vw;
    margin-left: auto;
    margin-right: auto;
  }

  &.modal-centered {
    align-self: center;
  }

  &.modal-scrollable {
    overflow-y: auto;
  }

  &.modal-fade {
    opacity: 0;
    transform: translateY(-20px);

    .show & {
      opacity: 1;
      transform: translateY(0);
    }
  }

  &.modal-slide {
    transform: translateY(50px);

    .show & {
      transform: translateY(0);
    }
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
}

.modal-title {
  margin-bottom: 0;
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 1rem;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 1rem;
  border-top: 1px solid #dee2e6;
  gap: 0.5rem;
}

.close {
  float: right;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.5;
  background: transparent;
  border: 0;
  cursor: pointer;

  &:hover {
    opacity: 0.75;
  }
}