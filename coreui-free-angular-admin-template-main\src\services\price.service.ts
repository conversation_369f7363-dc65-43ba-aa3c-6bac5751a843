import { HttpClient, HttpEvent, HttpHeaders, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { catchError, Observable, throwError } from 'rxjs';
const httpOptions = {
    headers: new HttpHeaders({ 'content-type': 'application/json', Authorization: 'basic ' + btoa('med:123456') })
};

@Injectable({
    providedIn: 'root'
})

export class PriceService {
    apiURL = environment.apiURL
    constructor(private http: HttpClient) { }


    getAllPrices(): Observable<any> {

        return this.http.get<any>(this.apiURL + `circuit`, httpOptions);
    }


    updatePrices(id: any, data: any): Observable<any> {
        const url = `${this.apiURL}prices/${id}`;

        return this.http.put<any>(url, data, httpOptions)
            .pipe(
                catchError(this.handleError)
            )
    }
    private handleError(error: any): Observable<never> {
        console.error('An error occurred:', error);
        return throwError(() => new Error(error.message || 'Server error'));
    }




}