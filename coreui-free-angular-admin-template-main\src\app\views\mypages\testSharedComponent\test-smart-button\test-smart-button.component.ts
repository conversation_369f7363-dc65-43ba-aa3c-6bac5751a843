import { Component } from '@angular/core';
import { SmartButtonComponent } from '../../../../../shared/components/smart-button/smart-button.component';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { IconModule } from '@coreui/icons-angular';
;

@Component({
  selector: 'app-test-smart-button',
  imports: [SmartButtonComponent,SmartContainerComponent,IconModule],
  templateUrl: './test-smart-button.component.html',
  styleUrl: './test-smart-button.component.scss'
})
export class TestSmartButtonComponent {

  saveData() {
  console.log('Saving data...');
  }

  deleteData() {
    console.log('Deleting data...');
  }


}
