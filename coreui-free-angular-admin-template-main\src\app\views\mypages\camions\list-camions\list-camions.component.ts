import { Component, OnInit } from '@angular/core';
import { environment } from 'src/environments/environment';
import { SessionStorageService } from 'src/services';
import { CamionService } from 'src/services/camion.service';
import { ConfirmationDialogService } from 'src/services/confirmation-dialog.service';
import { FormModalComponent } from 'src/shared/components/form-modal/form-modal.component';
import { SmartButtonComponent } from 'src/shared/components/smart-button/smart-button.component';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';
import { FormButton, FormConfig } from 'src/shared/models/form.models';
import { ActionButton } from 'src/shared/models/table.models';

@Component({
  selector: 'app-list-camions',
  standalone: true,
  imports: [SmartContainerComponent, SmartButtonComponent, SmartTableComponent, FormModalComponent],
  templateUrl: './list-camions.component.html',
  styleUrl: './list-camions.component.scss'
})
export class ListCamionsComponent implements OnInit {
  listCamions: any[] = [];
  showModal: boolean = false;
  vehicleOptions: any = [
    { value: "fourgonnette_5m3" },
    { value: "fourgonnette_8m3" },
    { value: "fourgonnette_10m3" },
    { value: "camion_plateau_ridelles_3_5t" },
    { value: "camion_plateau_ridelles_5t" },
    { value: "camion_plateau_ridelles_7t" },
    { value: "camion_plateau_ridelles_10t" },
    { value: "camion_plateau_ridelles_15t" },
    { value: "camion_cabine_3_5t_20_25m3" },
    { value: "camion_cabine_7t_40m3" },
    { value: "camion_cabine_9_10t_60m3" },
    { value: "camion_a_cabine_20m3" },
    { value: "camion_a_cabine_40m3" },
    { value: "camion_a_cabine_60m3" },
    { value: "camion_a_cabine_80m3" },
    { value: "camion_plateau_5t" },
    { value: "camion_plateau_7t" },
    { value: "camion_plateau_10t" },
    { value: "camion_plateau_10t_avec_ridelles" },
    { value: "camion_plateau_gt_10t" },
    { value: "camion_semi_remorque_plateau_gt_20t" },
    { value: "camion_semi_remorque_plateau_ridelles" },
    { value: "camion_semi_remorque_benna_transport" },
    { value: "camion_semi_remorque_benna_cereale" },
    { value: "camion_semi_remorque_citerne_hydrocarbure" },
    { value: "camion_semi_remorque_plateau_alimentaire" },
    { value: "camion_semi_remorque_plateau_bitune" },
  ];
  immatriculationOptions: any = [
    { value: "RS" },
    { value: "TN" }
  ];
  TonnageOptions: any = [
    { value: "KG" },
    { value: "TONNES" }
  ];
  VolumeOptions: any = [
    { value: "M3" },
  ];
  PaletteOptions: any = [
    {value: "MET"},
    {value: "EUR"}
  ]
  constructor(private camionService: CamionService,
    private sessionStrorage:SessionStorageService,
    private confirmationDialogService: ConfirmationDialogService
  ) {}
  ngOnInit(): void {
    this.loadCamions();
  }


  loadCamions() {
    this.camionService.findAllCamion().subscribe((data) => {
      this.listCamions = data;
    });
  }

  TableColumns  = [
    { name: 'type_camion', displayName: 'Type Camion', sortable: true },
    { name: 'immatriculation', displayName: 'Immatriculation', sortable: true },
    { name: 'poids', displayName: 'Poids Max', sortable: true },
    { name: 'unite_poids', displayName: 'Unité Poids', sortable: true },
    { name: 'volume', displayName: 'Volume Max en M³', sortable: true },
  ]
  actionButtons: ActionButton[] = [
    { tooltip:'Détails', icon: 'cilZoom', color: 'primary', callback: (item: any) => this.showDetails(item) },
    { tooltip:'Supprimer', icon: 'cil-trash', color: 'danger', callback: (item: any) => this.deleteCamion(item) }
  ]

  formConfig: FormConfig = {
    title: 'Ajouter Camion',
    fieldGroups: [
      {
        fields: [{  label: 'Type Camion', name: 'type_camion', type: 'select', 
        options: { objectArray: this.vehicleOptions, labelAttribute: 'value', valueAttribute: 'value' } 
      }]
      },
      {
        groupLabel: 'Immatriculation: ',
        fields: [
          {
            name: 'immatriculation', 
            type: 'text',
            placeholder: 'Immatriculation'
          },
          {
            name: 'immat_rs_tn',
            type: 'select',
            placeholder: 'Choisir',
            options : {
              objectArray: this.immatriculationOptions,
              labelAttribute: 'value',
              valueAttribute: 'value'
            }
          }
        ]
      },
      {
        groupLabel: 'Poids Max du camion:',
        fields: [
          {
            name: 'poids',
            type: 'number',
            placeholder:'Poids du camion',
            validation : {
              min: 0
            }
          },
          {
            name: 'unite_poids',
            type: 'select',
            options: {
              objectArray: this.TonnageOptions,
              labelAttribute: 'value',
              valueAttribute: 'value',
            }
          }
        ]
      },
      {
        groupLabel: 'Nombre De palettes:',
        fields: [
          {
            name:'nombre_palettes',
            type: 'number',
            placeholder: 'Nombre de palettes',
            validation: {min:0}
          },
          {
            name: 'palette_met_eur',
            type: 'select',
            options: {
              objectArray: this.PaletteOptions,
              valueAttribute: 'value',
              labelAttribute: 'value'
            }
          }
        ]
      },
      {
        groupLabel:'Dimension de plateau:',
        fields: [{
          label:'Longueur',
          name:'longeur',
          type:'number',
          placeholder: 'Longueur',
          validation:{min:0}
        },
        {
          label: 'largeur',
          name:'largeur',
          type:'number',
          placeholder: 'Largeur',
          validation:{min:0}
        },
        {
          label:'Hauteur',
          name:'hauteur',
          type:'number',
          placeholder: 'Hauteur',
          validation:{min:0}

        }
    ]
      }
    ],
    fields: [
      { label: 'Volume Max en M³: ', name: 'volume', type: 'number', placeholder: 'Volume du camion',validation:{min:0} },
      { label: 'Date de premiére mise en circulation :',name: 'date_circulation',type:'date'},
      {
        label: 'Ajouter l image de catre grise de votre camion ici:',
        name: 'image_carte_grise',
        type: 'image',
      }
    ],
    buttons:[
      { label: 'Annuler',  color: 'secondary', onClick: () => this.closeModal() },
      { label: 'Ajouter', color: 'primary' , onClick: (camion: any) => this.onFormSubmit(camion) }
    ]
  }

  deleteCamion(item: any) {
    this.confirmationDialogService.confirmDelete(`Voulez-vous vraiment supprimer le camion ${item.immatriculation} ?`)
      .then((confirmed) => {
        if (confirmed) {
          console.log('Deleting camion with ID:', item.id);
          this.camionService.deleteCamion(item.id).subscribe(() => {
            this.loadCamions();
          }, error => {
            console.error('Error deleting camion:', error);
          });
        }
      });
  }

  showDetails(item: any) {
    // Implement the logic to show details of the camion
    console.log('Showing details for camion:', item);
  }

  openModal() {
    this.showModal = true;
    console.log('Opening modal to add camion', this.showModal);
  }



  onFormSubmit(camion: any) {
    let filename = camion.image_carte_grise.name + new Date().getTime();
    camion = {...camion, ajoutee_par: this.sessionStrorage.getSessionValue("iduser"), image_carte_grise: filename}
    console.log('camion',camion)
    this.camionService.addCamion(camion).subscribe({
    next: (response) => {
      this.loadCamions();
      this.closeModal()
    },
    error: (error) => {
      console.error('Error adding camion:', error);
      this.closeModal()
    },
  });
  }

  closeModal() {
    this.showModal= false;
    console.log("show modal",this.showModal);
  }



}
