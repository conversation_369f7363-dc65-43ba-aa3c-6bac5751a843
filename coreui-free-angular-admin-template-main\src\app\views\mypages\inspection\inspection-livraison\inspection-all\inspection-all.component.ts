import { Component, OnInit, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { OrderLineService, VoyageService } from 'src/services';
import { ConfirmationDialogService } from 'src/services/confirmation-dialog.service';
import { DynamicExportButtonComponent } from 'src/shared/components/dynamic-export-button/dynamic-export-button.component';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';
import { ActionButton, TableColumn, TableConfig } from 'src/shared/models/table.models';

@Component({
  selector: 'app-inspection-all',
  standalone: true,
  imports: [SmartContainerComponent, SmartTableComponent, DynamicExportButtonComponent],
  templateUrl: './inspection-all.component.html',
  styleUrl: './inspection-all.component.scss'
})
export class InspectionAllComponent implements OnInit {

  loading = false;
  lignes: any[] = [];
  tableColumns: TableColumn[] = [
    {
      name: 'id',
      displayName: 'ID',
      sortable: true,
      filterable: true,
    },
    {
      name: 'id_voyage',
      displayName: 'Id Voyage',
      sortable: true,
      filterable: true,
    },
    {
      name: 'nom_depart',
      displayName: 'Nom de départ',
      sortable: true,
      filterable: true,
    },
    {
      name: 'nom_arrivee',
      displayName: 'Nom d\'arrivée',
      sortable: true,
      filterable: true,
    },
    {
      name: 'date_livraison',
      displayName: 'Date de Livraison',
      sortable: true,
      filterable: true,
    },
    {
      name: 'date_voyage',
      displayName: 'Date du voyage',
      dataType: 'date',
      sortable: true,
      filterable: true,
    },
    {
      name: 'kilometrage',
      displayName: 'Kilométrage',
      sortable: true,
      filterable: true,
    },
    {
      name: 'status',
      displayName: 'Status',
      sortable: true,
      filterable: true,
    },
    {
      name: 'type_ligne',
      displayName: 'Type de ligne',
      sortable: true,
      filterable: true,
    },
    {
      name: 'camion_immatriculation',
      displayName: 'Immatriculation',
      sortable: true,
      filterable: true,
    }
  ];

  tableConfig: TableConfig = {
    emptyMessage: 'Aucune ligne trouvée',
    pageSize: 5,
  } 
  tableActions: ActionButton[] = [
        {
          icon: 'cil-trash',
          color: 'danger',
          callback: (row: any) => {this.handleVoyageCancellation(row)}
        }
      ]

  constructor(
    private confirmationDialogService: ConfirmationDialogService,
    private ordrelignesService: OrderLineService,
    private toastr: ToastrService,
    private voyageService: VoyageService,
    
  ){}

  ngOnInit(): void {
    this.loadAllToInspection();
  }
  loadAllToInspection() {
    this.loading = true;
    this.ordrelignesService.findAllToInspection().subscribe({
      next: (data: any) => {
        this.lignes = data;
        console.log(this.lignes);
      },
      error: (error: any) => {
        console.error(error);
      },
      complete: () => {
        this.loading = false;
      }
    });
  }
  handleVoyageCancellation(row: any): void {
    
    const voyageId = row.id;
    this.confirmationDialogService.confirmDelete("Voulez-vous vraiment annuler ce voyage ?").then(
      (data: any) => {
        this.voyageService.cancelVoyage(voyageId).subscribe({
            next: (data: any) => {
              this.toastr.success('Voyage annulé avec succès');
              this.lignes = this.lignes.filter((ligne: any) => ligne.id !== voyageId);
            },
            error: (error: any) => {
              console.error(error);
            },
            complete: () => {
              this.loading = false;
            }
          })
        }
    )  
  }


}
