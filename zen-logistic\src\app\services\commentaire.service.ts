import { HttpClient, HttpEvent, HttpHeaders, HttpParams, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
const httpOptions = {
  headers: new HttpHeaders({ 'content-type': 'application/json', Authorization: 'basic ' + btoa('med:123456') })
};

@Injectable({
  providedIn: 'root'
})

export class CommentService {
  apiURL = environment.apiURL
  constructor(private http: HttpClient) { }

  addComment(data): Promise<any> {
    console.log('comment',data)
    return this.http.post<any>(this.apiURL + 'commentaire', data, httpOptions)
      .toPromise();
  }



  findCommentsByLigne(id): Promise<any[]> {
    return this.http.get<any[]>(this.apiURL + `commentaire/${id}`, httpOptions)
      .toPromise();
  }

  findAllComments(page: number, limit: number): Observable<any[]> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    return this.http.get<any[]>(this.apiURL + 'findAllComments', { params, ...httpOptions });
  }




}