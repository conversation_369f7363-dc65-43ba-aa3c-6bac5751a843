<section id="filter">
  <div class="row text-left">
    <div class="col-sm-12">
      <div class="card">
        <div class="card-header">
          <h4 class="card-title"> Liste des commandes à réserver </h4>
          <h6 style="color: red;">Date de réservation doit correspondre à la date d'immobilisation du camion vers ses destinations</h6>
          <br>
          <div class="row" style="align-content: flex-start; align-items: baseline;">
            <div class="col-md-3 mb-3">
              <select class="custom-select" required id="camion" [(ngModel)]="selectedCamionId" (change)="onCamionChange()">
                <option value="" disabled selected>Choisir un camion</option>
                <option *ngFor="let camionList of camionList" [value]="camionList.id">
                  {{ camionList.immatriculation }}
                </option>
              </select>
            </div>
            
            <div class="col-md-3 mb-3">
              <select class="custom-select" required id="conducteur" [(ngModel)]="selectedConducteur" (change)="onConducteurChange()">
                <option value="" disabled selected>Choisir un conducteur</option>
                <option *ngFor="let conducteurList of conducteurList" [ngValue]="conducteurList">
                  {{ conducteurList.nom + " " + conducteurList.prenom }}
                </option>
              </select>
            </div>
          
            <div class="col-md-3 mb-3">
              <input type="date" class="form-control" id="datePicker" [(ngModel)]="selectedDate" name="selectedDate" [min]="minDate" required>
            </div>
            
            <button *ngIf="selectedRows.length>0" class="btn btn-warning" style="color: white !important;" (click)="reserver()">RÉSERVER</button>
          </div>
          
          
          

        </div>
        <div class="card-content">
          <div class="card-body">
            <div class="table-responsive">
              <button class="btn btn-primary" style="background-color: green; color: white !important;margin-left: 15px;"
              (click)="downloadExcel()">
              <i class="fa fa-download"></i>&nbsp;
              <i class="fa fa-file-excel-o"></i>
            </button>

              <ng2-smart-table [settings]="settings" [source]="source" class="custom-smart-table"
                (userRowSelect)="onUserRowSelect($event)"  (custom)="onCustomAction($event)"  ></ng2-smart-table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<ng-template #popupTemplate let-modal>
  <div class="custom-modal-content">

  <div class="modal-header">
    <h4 class="modal-title">Total des Volumes</h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <p><b>Le total des volumes dans les lignes sélectionnées, y compris les réservations précédentes si existe, est de  {{ totalVolume }} m³</b></p>
  </div>
  <div class="modal-footer" style="display: flex; justify-content: space-between; flex-direction: row-reverse;">
    <!-- <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">Fermer</button> -->
    <!-- <button type="button" class="btn btn-primary" (click)="reserverSelectedRows()" style="color: white !important;">Réserver</button> -->
    <button type="button" class="btn btn-primary" (click)="next()" style="color: white !important;">Suivant</button>

  </div>

  </div>
</ng-template>

    
    <div style="max-height: 400px; overflow-y: auto;" *ngIf="ligneTable.length > 0">
      <table class="table">
        <thead>
          <tr>
            <th>ID</th>
            <th>Départ</th>
            <th>Arrivé</th>
            <th>Address Départ</th>
            <th>Address Arrivé</th>
            <th>Type Ligne</th>
            <th>Kilometrage</th>
            <th>Volume</th>
            <th>Telephone</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of ligneTable; let i = index" [class.selected]="i === selectedRowIndex" (click)="onRowClick(i)">
            <td>{{ item.id}}</td>

            <td>{{ item.from_destination.nom_locale }}</td>
            <td>{{ item.to_destination.nom_locale }}</td>
            <td>{{ item.from_destination.adresse }}</td>
            <td>{{ item.to_destination.adresse }}</td>
            <td>{{ item.type_ligne }}</td>
            <td>{{ item.kilometrage }}</td>
            <td >
              <ng-container *ngIf="i !== selectedRowIndex; else editMode">
                {{ item.volume }}
              </ng-container>
              <ng-template #editMode>
                <input type="number" class="form-group"  [(ngModel)]="item.volume" (blur)="selectedRowIndex = -1"  (keydown.enter)="updateVolume(item)" />
              </ng-template>
            </td>
            <td>{{ item.telephone }}</td>
          </tr>
        </tbody>
      </table>
    </div>
    
    



    <ng-template #details let-modal>
      <div class="custom-modal-content">
        <div class="modal-header">
          <h4 class="modal-title" id="modal-basic-title">
            Plus de détail :
          </h4>
          <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <div *ngIf="detailsLigne != 0" class="modal-body">
          <hr>
          <div class="row">
            <!-- Première colonne pour les adresses -->
            <div class="col-12">
              <ul class="no-list-style">
                <li class="mb-2">
                  <span class="text-bold-500 primary"><i class="fa fa-map-marker"></i>Adresse Départ:</span>
                  <span class="d-block overflow-hidden">
                    <h5>{{ detailsLigne.depart }}</h5>
                  </span>
                </li>
                <li class="mb-2">
                  <span class="text-bold-500 primary"><i class="fa fa-map-marker"></i>Adresse Arrivé:</span>
                  <span class="d-block overflow-hidden">
                    <h5>{{ detailsLigne.arrivee }}</h5>
                  </span>
                </li>
              </ul>
            </div>
            <!-- Deuxième colonne pour les autres détails -->
            <div class="col-12">
              <ul class="no-list-style" style="display: flex; justify-content: space-between;">
                <li class="mb-2">
                  <span class="text-bold-500 primary"><i class="fa fa-road"></i>Kilometrage:</span>
                  <span class="d-block overflow-hidden">
                    <h4>{{ detailsLigne.kilometrage }}</h4>
                  </span>
                </li>
                <li class="mb-2">
                  <span class="text-bold-500 primary"><i class="fa fa-cubes"></i>Volume:</span>
                  <span class="d-block overflow-hidden">
                    <h4>{{ detailsLigne.volume }}</h4>
                  </span>
                </li>
                <!-- Ajoutez d'autres détails ici -->
              </ul>
            </div>


            <div class="col-12">
              <ul class="no-list-style">
                <li class="mb-2">
                  <span class="text-bold-500 primary"><i class="fa fa-quora"></i>Réf:</span>
                  <span class="d-block overflow-hidden">
                    <h5 style="word-wrap: break-word;">{{ detailsLigne.sku }}</h5>
                  </span>
                </li>
              </ul>
            </div>
            


          </div>
          <hr>
        </div>
      </div>
    </ng-template>


    

    
    <ng-template #horaire let-modal>
      <div class="custom-modal-content">
        <div class="modal-header">
          <h4 class="modal-title">Horaire</h4>
          <button type="button" class="close" aria-label="Close" (click)="modal.dismiss()">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body" style="max-height: 400px; overflow-y: auto;">
          <table class="table">
            <thead>
              <tr>
                <th>Départ</th>
                <th>Heure Depart</th>
                <th>Arrivé</th>
                <th>Heure Arrivé</th>
                <th>Tolérance</th>

              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let destination of filteredRows">
                <td>{{ destination.nom_depart }}</td>
            
                <td>
                  <div class="d-flex">
                    <select 
                      class="form-control mr-1" 
                      [(ngModel)]="destination.plage_horaire_heure_dep" 
                      name="heure" 
                      style="width: 100px;" 
                      required>
                      <option *ngFor="let hour of hours" [value]="hour">{{ hour }}</option>
                    </select>
                  </div>
                </td>
            
                <td>{{ destination.nom_arrivee }}</td>
            
                <td>
                  <div class="d-flex " style="max-height: 100px; overflow-y: auto;">
                    <select 
                      class="form-control mr-1" 
                      [(ngModel)]="destination.plage_horaire_heure_arr" 
                      name="heure" 
                      style="width: 100px;" 
                      
                      required>
                      <option *ngFor="let hour of hours" [value]="hour" style="height: 200px;">{{ hour }}</option>
                    </select>
                  </div>
                </td>

                <td style="display: flex;"> <b style="font-size: x-large;"> ± </b>
                   <select ng build
                  class="form-control mr-1" 
                  [(ngModel)]="destination.tolerance" 
                  style="width: 100px;" 
                  
                  required>
                  <option [value]="0.5" style="height: 200px;" selected>30 min</option>
                  <option [value]="1" style="height: 200px;">1 Heure</option>
                  <option [value]="2" style="height: 200px;">2 Heure</option>

                </select>

                </td>

               
              </tr>
            </tbody>
            
          </table>
        </div>
        <div class="modal-footer" style="display: flex; justify-content: space-between; flex-direction: row-reverse;">
          <button type="button" class="btn btn-primary" (click)="res()" style="color: white !important;">Reserver</button>
        </div>
      </div>
    </ng-template>
    

