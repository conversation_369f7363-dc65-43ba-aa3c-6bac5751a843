<div class="card-body p-4">
  <form [formGroup]="form">
    <!-- Field Groups -->
    <div *ngFor="let group of config.fieldGroups" class="mb-4">
      <h6 *ngIf="group.groupLabel" class="form-group-label">{{ group.groupLabel }}</h6>
      <div class="row">
        <div class="col" *ngFor="let field of group.fields" [hidden]="!isFieldVisible(field)">
          <!-- Input Fields -->
          <div *ngIf="isInputField(field) && isFieldVisible(field)">
            <label [for]="field.name" class="form-label">{{ field.label }}</label>
            <input [type]="field.type" class="form-control" [id]="field.name" [formControlName]="field.name"
              [placeholder]="field.placeholder || ''" (input)="onFieldChange(field)" [disabled]="isFieldDisabled(field)" />
            <!-- Validation messages... -->
          </div>

          <!-- Textarea -->
          <div *ngIf="field.type === 'textarea' && isFieldVisible(field)">
            <label [for]="field.name" class="form-label">{{ field.label }}</label>
            <textarea class="form-control" [id]="field.name" [formControlName]="field.name"
              [placeholder]="field.placeholder || ''" rows="3" (input)="onFieldChange(field)" [disabled]="isFieldDisabled(field)"></textarea>
            <!-- Validation messages... -->
          </div>

          <!-- Select -->
          <div *ngIf="field.type === 'select' && field.options?.objectArray && isFieldVisible(field)">
            <label [for]="field.name" class="form-label">{{ field.label }}</label>
            <select class="form-select" [id]="field.name" [formControlName]="field.name" (change)="onFieldChange(field)" [disabled]="isFieldDisabled(field)">
              <option value="" disabled selected>{{ field.placeholder || 'Selectionner une option' }}</option>
              <option *ngFor="let option of field.options?.objectArray" [value]="getOptionValue(option, field)">
                {{ getOptionLabel(option, field) }}
              </option>
            </select>
          </div>

          <!-- Checkbox (single) -->
          <div *ngIf="field.type === 'checkbox' && !field.options?.objectArray && isFieldVisible(field)" class="form-check">
            <input class="form-check-input" type="checkbox" [id]="field.name" [formControlName]="field.name"
              (change)="onFieldChange(field)" [disabled]="isFieldDisabled(field)">
            <label class="form-check-label" [for]="field.name">{{ field.label }}</label>
          </div>

          <!-- Checkbox Group -->
          <div *ngIf="field.type === 'checkbox' && field.options?.objectArray && isFieldVisible(field)">
            <label class="form-label">{{ field.label }}</label>
            <div [class.d-flex]="field.options?.inline" [class.flex-wrap]="field.options?.inline">
              <div [class.form-check]="true" *ngFor="let option of field.options?.objectArray; let i = index">
                <input class="form-check-input" type="checkbox" 
                       [id]="field.name + '_' + i"
                       [value]="getOptionValue(option, field)"
                       [checked]="isOptionSelected(option, field)"
                       (change)="onMultiselectChange(option, field)" [disabled]="isFieldDisabled(field)">
                <label class="form-check-label" [for]="field.name + '_' + i">
                  {{ getOptionLabel(option, field) }}
                </label>
              </div>
            </div>
          </div>

          <!-- Radio Group -->
          <div *ngIf="field.type === 'radio' && field.options?.objectArray && isFieldVisible(field)">
            <label class="form-label">{{ field.label }}</label>
            <div [class.d-flex]="field.options?.inline" [class.flex-wrap]="field.options?.inline">
              <div [class.form-check]="true" [class.me-3]="field.options?.inline" 
                   *ngFor="let option of field.options?.objectArray; let i = index">
                <input class="form-check-input" type="radio" 
                       [id]="field.name + '_' + i"
                       [value]="getOptionValue(option, field)"
                       [formControlName]="field.name"
                       (change)="onFieldChange(field)" [disabled]="isFieldDisabled(field)">
                <label class="form-check-label" [for]="field.name + '_' + i">
                  {{ getOptionLabel(option, field) }}
                </label>
              </div>
            </div>
          </div>

          <!-- Multiselect -->
          <div *ngIf="field.type === 'multiselect' && field.options?.objectArray && isFieldVisible(field)">
            <label [for]="field.name" class="form-label">{{ field.label }}</label>
            <div class="dropdown" [class.show]="dropdownOpen[field.name]">
              <button class="form-select text-start dropdown-toggle" type="button" [id]="field.name + '_dropdown'"
                (click)="toggleDropdown(field.name)">
                {{ getSelectedLabels(field) || 'Select options' }}
              </button>
              <div class="dropdown-menu w-100" [class.show]="dropdownOpen[field.name]">
                <div class="px-2 py-1">
                  <div class="form-check" *ngFor="let option of field.options?.objectArray; let i = index">
                    <input class="form-check-input" type="checkbox" [id]="field.name + '_' + i"
                      [checked]="isOptionSelected(option, field)" (change)="onMultiselectChange(option, field)" [disabled]="isFieldDisabled(field)">
                    <label class="form-check-label w-100" [for]="field.name + '_' + i">
                      {{ getOptionLabel(option, field) }}
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Image Upload -->
          <div *ngIf="field.type === 'image' && isFieldVisible(field)">
            <label [for]="field.name" class="form-label">{{ field.label }}</label>
            <input type="file" class="form-control" [id]="field.name" accept="image/*"
              (change)="onFileChange($event, field.name); onFieldChange(field)" [disabled]="isFieldDisabled(field)" />
            <div *ngIf="imagePreview || (initialData && initialData[field.name])" class="mt-2">
              <img [src]="imagePreview || initialData[field.name]" alt="Preview" class="img-thumbnail"
                style="max-height: 200px;">
            </div>
          </div>

          <!-- Date -->
          <div *ngIf="field.type === 'date' && isFieldVisible(field)">
            <label [for]="field.name" class="form-label">{{ field.label }}</label>
            <input type="date" class="form-control" [id]="field.name" [formControlName]="field.name"
              (change)="onFieldChange(field)" [disabled]="isFieldDisabled(field)" />
          </div>
        </div>
      </div>
    </div>

    <!-- Render individual fields -->
    <div class="row">
      <div class="mb-3" [class.col-md-6]="shouldSplit(field)" *ngFor="let field of config.fields">
         <!-- Input Fields -->
         <div *ngIf="isInputField(field) && isFieldVisible(field)" >
          <label [for]="field.name" class="form-label">{{ field.label }}</label>
          <input [type]="field.type" class="form-control" [id]="field.name" [formControlName]="field.name"
            [placeholder]="field.placeholder || ''" (input)="onFieldChange(field)" [disabled]="isFieldDisabled(field)" />
          <!-- Validation messages... -->
        </div>

        <!-- Textarea -->
        <div *ngIf="field.type === 'textarea' && isFieldVisible(field)">
          <label [for]="field.name" class="form-label">{{ field.label }}</label>
          <textarea class="form-control" [id]="field.name" [formControlName]="field.name"
            [placeholder]="field.placeholder || ''" rows="3" (input)="onFieldChange(field)" [disabled]="isFieldDisabled(field)"></textarea>
          <!-- Validation messages... -->
        </div>

        <!-- Select -->
        <div *ngIf="field.type === 'select' && field.options?.objectArray && isFieldVisible(field)">
          <label [for]="field.name" class="form-label">{{ field.label }}</label>
          <select class="form-select" [id]="field.name" [formControlName]="field.name" (change)="onFieldChange(field)" [disabled]="isFieldDisabled(field)">
            <option value="" disabled selected>Select an option</option>
            <option *ngFor="let option of field.options?.objectArray" [value]="getOptionValue(option, field)">
              {{ getOptionLabel(option, field) }}
            </option>
          </select>
        </div>

        <!-- Checkbox (single) -->
        <div *ngIf="field.type === 'checkbox' && !field.options?.objectArray && isFieldVisible(field)" class="form-check">
          <input class="form-check-input" type="checkbox" [id]="field.name" [formControlName]="field.name"
            (change)="onFieldChange(field)" [disabled]="isFieldDisabled(field)">
          <label class="form-check-label" [for]="field.name">{{ field.label }}</label>
        </div>

        <!-- Checkbox Group -->
        <div *ngIf="field.type === 'checkbox' && field.options?.objectArray && isFieldVisible(field)">
          <label class="form-label">{{ field.label }}</label>
          <div [class.d-flex]="field.options?.inline" [class.flex-wrap]="field.options?.inline">
            <div [class.form-check]="true" *ngFor="let option of field.options?.objectArray; let i = index">
              <input class="form-check-input" type="checkbox" 
                     [id]="field.name + '_' + i"
                     [value]="getOptionValue(option, field)"
                     [checked]="isOptionSelected(option, field)"
                     (change)="onMultiselectChange(option, field)" [disabled]="isFieldDisabled(field)">
              <label class="form-check-label" [for]="field.name + '_' + i">
                {{ getOptionLabel(option, field) }}
              </label>
            </div>
          </div>
        </div>

        <!-- Radio Group -->
        <div *ngIf="field.type === 'radio' && field.options?.objectArray && isFieldVisible(field)">
          <label class="form-label">{{ field.label }}</label>
          <div [class.d-flex]="field.options?.inline" [class.flex-wrap]="field.options?.inline">
            <div [class.form-check]="true" [class.me-3]="field.options?.inline" 
                 *ngFor="let option of field.options?.objectArray; let i = index">
              <input class="form-check-input" type="radio" 
                     [id]="field.name + '_' + i"
                     [value]="getOptionValue(option, field)"
                     [formControlName]="field.name"
                     (change)="onFieldChange(field)" [disabled]="isFieldDisabled(field)">
              <label class="form-check-label" [for]="field.name + '_' + i">
                {{ getOptionLabel(option, field) }}
              </label>
            </div>
          </div>
        </div>

        <!-- Multiselect -->
        <div *ngIf="field.type === 'multiselect' && field.options?.objectArray && isFieldVisible(field)">
          <label [for]="field.name" class="form-label">{{ field.label }}</label>
          <div class="dropdown" [class.show]="dropdownOpen[field.name]">
            <button class="form-select text-start dropdown-toggle" type="button" [id]="field.name + '_dropdown'"
              (click)="toggleDropdown(field.name)">
              {{ getSelectedLabels(field) || 'Select options' }}
            </button>
            <div class="dropdown-menu w-100" [class.show]="dropdownOpen[field.name]">
              <div class="px-2 py-1">
                <div class="form-check" *ngFor="let option of field.options?.objectArray; let i = index">
                  <input class="form-check-input" type="checkbox" [id]="field.name + '_' + i"
                    [checked]="isOptionSelected(option, field)" (change)="onMultiselectChange(option, field)" [disabled]="isFieldDisabled(field)">
                  <label class="form-check-label w-100" [for]="field.name + '_' + i">
                    {{ getOptionLabel(option, field) }}
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Image Upload -->
        <div *ngIf="field.type === 'image' && isFieldVisible(field)">
          <label [for]="field.name" class="form-label">{{ field.label }}</label>
          <input type="file" class="form-control" [id]="field.name" accept="image/*"
            (change)="onFileChange($event, field.name); onFieldChange(field)" [disabled]="isFieldDisabled(field)" />
          <div *ngIf="imagePreview || (initialData && initialData[field.name])" class="mt-2">
            <img [src]="imagePreview || initialData[field.name]" alt="Preview" class="img-thumbnail"
              style="max-height: 200px;">
          </div>
        </div>

        <!-- Date -->
        <div *ngIf="field.type === 'date' && isFieldVisible(field)">
          <label [for]="field.name" class="form-label">{{ field.label }}</label>
          <input type="date" class="form-control" [id]="field.name" [formControlName]="field.name"
            (change)="onFieldChange(field)" [disabled]="isFieldDisabled(field)" />
        </div>
      </div>
    </div>

    <!-- Form buttons -->
    <div class="d-flex justify-content-between mt-3">
      <button *ngFor="let button of config.buttons"class="btn" [ngClass]="'btn-' + button.color"
        (click)="onButtonClick(button)">
        <i *ngIf="button.icon" [class]="button.icon"></i>
        {{ button.label }}
      </button>
    </div>
  </form>
</div>