<app-smart-container title="Type Marchandise">
    <div slot="content">
        <div class="d-flex flex-column w-100">
            <div class="d-flex flex-wrap align-items-center justify-content-between mb-3">
                <div class="d-flex flex-grow-1 align-items-center">
                    <input type="text" placeholder="Type Marchandise" class="form-control me-2" name="typeMarchandise">
                    <button type="button" class="btn btn-raised btn-primary me-3" (click)="addMarchandise()">
                        Ajouter
                    </button>
                </div>
            </div>
        </div>
        <div *ngIf="showTable">
            <app-smart-table [data]="marchandise" [columns]="tableColumns"
                [actionButtons]="tableaction" [isLoading]="loading"></app-smart-table>
        </div>
    </div>
    <div slot="actions">
        <app-smart-button [label]="'Détails'" [color]="'primary'" (onClick)="showTableMarchandise()"></app-smart-button>
    </div>
</app-smart-container>