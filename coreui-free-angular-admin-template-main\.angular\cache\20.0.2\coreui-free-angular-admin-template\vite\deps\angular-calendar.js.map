{"version": 3, "sources": ["../../../../../../node_modules/positioning/dist/positioning.js", "../../../../../../node_modules/calendar-utils/calendar-utils.js", "../../../../../../node_modules/angular-calendar/fesm2020/angular-calendar.mjs"], "sourcesContent": ["// previous version:\n// https://github.com/angular-ui/bootstrap/blob/07c31d0731f7cb068a1932b8e01d2312b796b4ec/src/position/position.js\nvar Positioning = /** @class */ (function () {\n    function Positioning() {\n    }\n    Positioning.prototype.getAllStyles = function (element) { return window.getComputedStyle(element); };\n    Positioning.prototype.getStyle = function (element, prop) { return this.getAllStyles(element)[prop]; };\n    Positioning.prototype.isStaticPositioned = function (element) {\n        return (this.getStyle(element, 'position') || 'static') === 'static';\n    };\n    Positioning.prototype.offsetParent = function (element) {\n        var offsetParentEl = element.offsetParent || document.documentElement;\n        while (offsetParentEl && offsetParentEl !== document.documentElement && this.isStaticPositioned(offsetParentEl)) {\n            offsetParentEl = offsetParentEl.offsetParent;\n        }\n        return offsetParentEl || document.documentElement;\n    };\n    Positioning.prototype.position = function (element, round) {\n        if (round === void 0) { round = true; }\n        var elPosition;\n        var parentOffset = { width: 0, height: 0, top: 0, bottom: 0, left: 0, right: 0 };\n        if (this.getStyle(element, 'position') === 'fixed') {\n            elPosition = element.getBoundingClientRect();\n            elPosition = {\n                top: elPosition.top,\n                bottom: elPosition.bottom,\n                left: elPosition.left,\n                right: elPosition.right,\n                height: elPosition.height,\n                width: elPosition.width\n            };\n        }\n        else {\n            var offsetParentEl = this.offsetParent(element);\n            elPosition = this.offset(element, false);\n            if (offsetParentEl !== document.documentElement) {\n                parentOffset = this.offset(offsetParentEl, false);\n            }\n            parentOffset.top += offsetParentEl.clientTop;\n            parentOffset.left += offsetParentEl.clientLeft;\n        }\n        elPosition.top -= parentOffset.top;\n        elPosition.bottom -= parentOffset.top;\n        elPosition.left -= parentOffset.left;\n        elPosition.right -= parentOffset.left;\n        if (round) {\n            elPosition.top = Math.round(elPosition.top);\n            elPosition.bottom = Math.round(elPosition.bottom);\n            elPosition.left = Math.round(elPosition.left);\n            elPosition.right = Math.round(elPosition.right);\n        }\n        return elPosition;\n    };\n    Positioning.prototype.offset = function (element, round) {\n        if (round === void 0) { round = true; }\n        var elBcr = element.getBoundingClientRect();\n        var viewportOffset = {\n            top: window.pageYOffset - document.documentElement.clientTop,\n            left: window.pageXOffset - document.documentElement.clientLeft\n        };\n        var elOffset = {\n            height: elBcr.height || element.offsetHeight,\n            width: elBcr.width || element.offsetWidth,\n            top: elBcr.top + viewportOffset.top,\n            bottom: elBcr.bottom + viewportOffset.top,\n            left: elBcr.left + viewportOffset.left,\n            right: elBcr.right + viewportOffset.left\n        };\n        if (round) {\n            elOffset.height = Math.round(elOffset.height);\n            elOffset.width = Math.round(elOffset.width);\n            elOffset.top = Math.round(elOffset.top);\n            elOffset.bottom = Math.round(elOffset.bottom);\n            elOffset.left = Math.round(elOffset.left);\n            elOffset.right = Math.round(elOffset.right);\n        }\n        return elOffset;\n    };\n    /*\n      Return false if the element to position is outside the viewport\n    */\n    Positioning.prototype.positionElements = function (hostElement, targetElement, placement, appendToBody) {\n        var _a = placement.split('-'), _b = _a[0], placementPrimary = _b === void 0 ? 'top' : _b, _c = _a[1], placementSecondary = _c === void 0 ? 'center' : _c;\n        var hostElPosition = appendToBody ? this.offset(hostElement, false) : this.position(hostElement, false);\n        var targetElStyles = this.getAllStyles(targetElement);\n        var marginTop = parseFloat(targetElStyles.marginTop);\n        var marginBottom = parseFloat(targetElStyles.marginBottom);\n        var marginLeft = parseFloat(targetElStyles.marginLeft);\n        var marginRight = parseFloat(targetElStyles.marginRight);\n        var topPosition = 0;\n        var leftPosition = 0;\n        switch (placementPrimary) {\n            case 'top':\n                topPosition = (hostElPosition.top - (targetElement.offsetHeight + marginTop + marginBottom));\n                break;\n            case 'bottom':\n                topPosition = (hostElPosition.top + hostElPosition.height);\n                break;\n            case 'left':\n                leftPosition = (hostElPosition.left - (targetElement.offsetWidth + marginLeft + marginRight));\n                break;\n            case 'right':\n                leftPosition = (hostElPosition.left + hostElPosition.width);\n                break;\n        }\n        switch (placementSecondary) {\n            case 'top':\n                topPosition = hostElPosition.top;\n                break;\n            case 'bottom':\n                topPosition = hostElPosition.top + hostElPosition.height - targetElement.offsetHeight;\n                break;\n            case 'left':\n                leftPosition = hostElPosition.left;\n                break;\n            case 'right':\n                leftPosition = hostElPosition.left + hostElPosition.width - targetElement.offsetWidth;\n                break;\n            case 'center':\n                if (placementPrimary === 'top' || placementPrimary === 'bottom') {\n                    leftPosition = (hostElPosition.left + hostElPosition.width / 2 - targetElement.offsetWidth / 2);\n                }\n                else {\n                    topPosition = (hostElPosition.top + hostElPosition.height / 2 - targetElement.offsetHeight / 2);\n                }\n                break;\n        }\n        /// The translate3d/gpu acceleration render a blurry text on chrome, the next line is commented until a browser fix\n        // targetElement.style.transform = `translate3d(${Math.round(leftPosition)}px, ${Math.floor(topPosition)}px, 0px)`;\n        targetElement.style.transform = \"translate(\" + Math.round(leftPosition) + \"px, \" + Math.round(topPosition) + \"px)\";\n        // Check if the targetElement is inside the viewport\n        var targetElBCR = targetElement.getBoundingClientRect();\n        var html = document.documentElement;\n        var windowHeight = window.innerHeight || html.clientHeight;\n        var windowWidth = window.innerWidth || html.clientWidth;\n        return targetElBCR.left >= 0 && targetElBCR.top >= 0 && targetElBCR.right <= windowWidth &&\n            targetElBCR.bottom <= windowHeight;\n    };\n    return Positioning;\n}());\nexport { Positioning };\nvar placementSeparator = /\\s+/;\nvar positionService = new Positioning();\n/*\n * Accept the placement array and applies the appropriate placement dependent on the viewport.\n * Returns the applied placement.\n * In case of auto placement, placements are selected in order\n *   'top', 'bottom', 'left', 'right',\n *   'top-left', 'top-right',\n *   'bottom-left', 'bottom-right',\n *   'left-top', 'left-bottom',\n *   'right-top', 'right-bottom'.\n * */\nexport function positionElements(hostElement, targetElement, placement, appendToBody, baseClass) {\n    var placementVals = Array.isArray(placement) ? placement : placement.split(placementSeparator);\n    var allowedPlacements = [\n        'top', 'bottom', 'left', 'right', 'top-left', 'top-right', 'bottom-left', 'bottom-right', 'left-top', 'left-bottom',\n        'right-top', 'right-bottom'\n    ];\n    var classList = targetElement.classList;\n    var addClassesToTarget = function (targetPlacement) {\n        var _a = targetPlacement.split('-'), primary = _a[0], secondary = _a[1];\n        var classes = [];\n        if (baseClass) {\n            classes.push(baseClass + \"-\" + primary);\n            if (secondary) {\n                classes.push(baseClass + \"-\" + primary + \"-\" + secondary);\n            }\n            classes.forEach(function (classname) { classList.add(classname); });\n        }\n        return classes;\n    };\n    // Remove old placement classes to avoid issues\n    if (baseClass) {\n        allowedPlacements.forEach(function (placementToRemove) { classList.remove(baseClass + \"-\" + placementToRemove); });\n    }\n    // replace auto placement with other placements\n    var hasAuto = placementVals.findIndex(function (val) { return val === 'auto'; });\n    if (hasAuto >= 0) {\n        allowedPlacements.forEach(function (obj) {\n            if (placementVals.find(function (val) { return val.search('^' + obj) !== -1; }) == null) {\n                placementVals.splice(hasAuto++, 1, obj);\n            }\n        });\n    }\n    // coordinates where to position\n    // Required for transform:\n    var style = targetElement.style;\n    style.position = 'absolute';\n    style.top = '0';\n    style.left = '0';\n    style['will-change'] = 'transform';\n    var testPlacement;\n    var isInViewport = false;\n    for (var _i = 0, placementVals_1 = placementVals; _i < placementVals_1.length; _i++) {\n        testPlacement = placementVals_1[_i];\n        var addedClasses = addClassesToTarget(testPlacement);\n        if (positionService.positionElements(hostElement, targetElement, testPlacement, appendToBody)) {\n            isInViewport = true;\n            break;\n        }\n        // Remove the baseClasses for further calculation\n        if (baseClass) {\n            addedClasses.forEach(function (classname) { classList.remove(classname); });\n        }\n    }\n    if (!isInViewport) {\n        // If nothing match, the first placement is the default one\n        testPlacement = placementVals[0];\n        addClassesToTarget(testPlacement);\n        positionService.positionElements(hostElement, targetElement, testPlacement, appendToBody);\n    }\n    return testPlacement;\n}\n", "var __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nexport var DAYS_OF_WEEK;\n(function (DAYS_OF_WEEK) {\n    DAYS_OF_WEEK[DAYS_OF_WEEK[\"SUNDAY\"] = 0] = \"SUNDAY\";\n    DAYS_OF_WEEK[DAYS_OF_WEEK[\"MONDAY\"] = 1] = \"MONDAY\";\n    DAYS_OF_WEEK[DAYS_OF_WEEK[\"TUESDAY\"] = 2] = \"TUESDAY\";\n    DAYS_OF_WEEK[DAYS_OF_WEEK[\"WEDNESDAY\"] = 3] = \"WEDNESDAY\";\n    DAYS_OF_WEEK[DAYS_OF_WEEK[\"THURSDAY\"] = 4] = \"THURSDAY\";\n    DAYS_OF_WEEK[DAYS_OF_WEEK[\"FRIDAY\"] = 5] = \"FRIDAY\";\n    DAYS_OF_WEEK[DAYS_OF_WEEK[\"SATURDAY\"] = 6] = \"SATURDAY\";\n})(DAYS_OF_WEEK || (DAYS_OF_WEEK = {}));\nvar DEFAULT_WEEKEND_DAYS = [\n    DAYS_OF_WEEK.SUNDAY,\n    DAYS_OF_WEEK.SATURDAY,\n];\nvar DAYS_IN_WEEK = 7;\nvar HOURS_IN_DAY = 24;\nvar MINUTES_IN_HOUR = 60;\nexport var SECONDS_IN_DAY = 60 * 60 * 24;\nfunction getExcludedSeconds(dateAdapter, _a) {\n    var startDate = _a.startDate, seconds = _a.seconds, excluded = _a.excluded, precision = _a.precision;\n    if (excluded.length < 1) {\n        return 0;\n    }\n    var addSeconds = dateAdapter.addSeconds, getDay = dateAdapter.getDay, addDays = dateAdapter.addDays;\n    var endDate = addSeconds(startDate, seconds - 1);\n    var dayStart = getDay(startDate);\n    var dayEnd = getDay(endDate);\n    var result = 0; // Calculated in seconds\n    var current = startDate;\n    var _loop_1 = function () {\n        var day = getDay(current);\n        if (excluded.some(function (excludedDay) { return excludedDay === day; })) {\n            result += calculateExcludedSeconds(dateAdapter, {\n                dayStart: dayStart,\n                dayEnd: dayEnd,\n                day: day,\n                precision: precision,\n                startDate: startDate,\n                endDate: endDate,\n            });\n        }\n        current = addDays(current, 1);\n    };\n    while (current < endDate) {\n        _loop_1();\n    }\n    return result;\n}\nfunction calculateExcludedSeconds(dateAdapter, _a) {\n    var precision = _a.precision, day = _a.day, dayStart = _a.dayStart, dayEnd = _a.dayEnd, startDate = _a.startDate, endDate = _a.endDate;\n    var differenceInSeconds = dateAdapter.differenceInSeconds, endOfDay = dateAdapter.endOfDay, startOfDay = dateAdapter.startOfDay;\n    if (precision === 'minutes') {\n        if (day === dayStart) {\n            return differenceInSeconds(endOfDay(startDate), startDate) + 1;\n        }\n        else if (day === dayEnd) {\n            return differenceInSeconds(endDate, startOfDay(endDate)) + 1;\n        }\n    }\n    return SECONDS_IN_DAY;\n}\nfunction getWeekViewEventSpan(dateAdapter, _a) {\n    var event = _a.event, offset = _a.offset, startOfWeekDate = _a.startOfWeekDate, excluded = _a.excluded, precision = _a.precision, totalDaysInView = _a.totalDaysInView;\n    var max = dateAdapter.max, differenceInSeconds = dateAdapter.differenceInSeconds, addDays = dateAdapter.addDays, endOfDay = dateAdapter.endOfDay, differenceInDays = dateAdapter.differenceInDays;\n    var span = SECONDS_IN_DAY;\n    var begin = max([event.start, startOfWeekDate]);\n    if (event.end) {\n        switch (precision) {\n            case 'minutes':\n                span = differenceInSeconds(event.end, begin);\n                break;\n            default:\n                span =\n                    differenceInDays(addDays(endOfDay(event.end), 1), begin) *\n                        SECONDS_IN_DAY;\n                break;\n        }\n    }\n    var offsetSeconds = offset * SECONDS_IN_DAY;\n    var totalLength = offsetSeconds + span;\n    // the best way to detect if an event is outside the week-view\n    // is to check if the total span beginning (from startOfWeekDay or event start) exceeds the total days in the view\n    var secondsInView = totalDaysInView * SECONDS_IN_DAY;\n    if (totalLength > secondsInView) {\n        span = secondsInView - offsetSeconds;\n    }\n    span -= getExcludedSeconds(dateAdapter, {\n        startDate: begin,\n        seconds: span,\n        excluded: excluded,\n        precision: precision,\n    });\n    return span / SECONDS_IN_DAY;\n}\nfunction getWeekViewEventOffset(dateAdapter, _a) {\n    var event = _a.event, startOfWeekDate = _a.startOfWeek, excluded = _a.excluded, precision = _a.precision;\n    var differenceInDays = dateAdapter.differenceInDays, startOfDay = dateAdapter.startOfDay, differenceInSeconds = dateAdapter.differenceInSeconds;\n    if (event.start < startOfWeekDate) {\n        return 0;\n    }\n    var offset = 0;\n    switch (precision) {\n        case 'days':\n            offset =\n                differenceInDays(startOfDay(event.start), startOfWeekDate) *\n                    SECONDS_IN_DAY;\n            break;\n        case 'minutes':\n            offset = differenceInSeconds(event.start, startOfWeekDate);\n            break;\n    }\n    offset -= getExcludedSeconds(dateAdapter, {\n        startDate: startOfWeekDate,\n        seconds: offset,\n        excluded: excluded,\n        precision: precision,\n    });\n    return Math.abs(offset / SECONDS_IN_DAY);\n}\nfunction isEventIsPeriod(dateAdapter, _a) {\n    var event = _a.event, periodStart = _a.periodStart, periodEnd = _a.periodEnd;\n    var isSameSecond = dateAdapter.isSameSecond;\n    var eventStart = event.start;\n    var eventEnd = event.end || event.start;\n    if (eventStart > periodStart && eventStart < periodEnd) {\n        return true;\n    }\n    if (eventEnd > periodStart && eventEnd < periodEnd) {\n        return true;\n    }\n    if (eventStart < periodStart && eventEnd > periodEnd) {\n        return true;\n    }\n    if (isSameSecond(eventStart, periodStart) ||\n        isSameSecond(eventStart, periodEnd)) {\n        return true;\n    }\n    if (isSameSecond(eventEnd, periodStart) ||\n        isSameSecond(eventEnd, periodEnd)) {\n        return true;\n    }\n    return false;\n}\nexport function getEventsInPeriod(dateAdapter, _a) {\n    var events = _a.events, periodStart = _a.periodStart, periodEnd = _a.periodEnd;\n    return events.filter(function (event) {\n        return isEventIsPeriod(dateAdapter, { event: event, periodStart: periodStart, periodEnd: periodEnd });\n    });\n}\nfunction getWeekDay(dateAdapter, _a) {\n    var date = _a.date, _b = _a.weekendDays, weekendDays = _b === void 0 ? DEFAULT_WEEKEND_DAYS : _b;\n    var startOfDay = dateAdapter.startOfDay, isSameDay = dateAdapter.isSameDay, getDay = dateAdapter.getDay;\n    var today = startOfDay(new Date());\n    var day = getDay(date);\n    return {\n        date: date,\n        day: day,\n        isPast: date < today,\n        isToday: isSameDay(date, today),\n        isFuture: date > today,\n        isWeekend: weekendDays.indexOf(day) > -1,\n    };\n}\nexport function getWeekViewHeader(dateAdapter, _a) {\n    var viewDate = _a.viewDate, weekStartsOn = _a.weekStartsOn, _b = _a.excluded, excluded = _b === void 0 ? [] : _b, weekendDays = _a.weekendDays, _c = _a.viewStart, viewStart = _c === void 0 ? dateAdapter.startOfWeek(viewDate, { weekStartsOn: weekStartsOn }) : _c, _d = _a.viewEnd, viewEnd = _d === void 0 ? dateAdapter.addDays(viewStart, DAYS_IN_WEEK) : _d;\n    var addDays = dateAdapter.addDays, getDay = dateAdapter.getDay;\n    var days = [];\n    var date = viewStart;\n    while (date < viewEnd) {\n        if (!excluded.some(function (e) { return getDay(date) === e; })) {\n            days.push(getWeekDay(dateAdapter, { date: date, weekendDays: weekendDays }));\n        }\n        date = addDays(date, 1);\n    }\n    return days;\n}\nexport function getDifferenceInDaysWithExclusions(dateAdapter, _a) {\n    var date1 = _a.date1, date2 = _a.date2, excluded = _a.excluded;\n    var date = date1;\n    var diff = 0;\n    while (date < date2) {\n        if (excluded.indexOf(dateAdapter.getDay(date)) === -1) {\n            diff++;\n        }\n        date = dateAdapter.addDays(date, 1);\n    }\n    return diff;\n}\nexport function getAllDayWeekEvents(dateAdapter, _a) {\n    var _b = _a.events, events = _b === void 0 ? [] : _b, _c = _a.excluded, excluded = _c === void 0 ? [] : _c, _d = _a.precision, precision = _d === void 0 ? 'days' : _d, _e = _a.absolutePositionedEvents, absolutePositionedEvents = _e === void 0 ? false : _e, viewStart = _a.viewStart, viewEnd = _a.viewEnd;\n    viewStart = dateAdapter.startOfDay(viewStart);\n    viewEnd = dateAdapter.endOfDay(viewEnd);\n    var differenceInSeconds = dateAdapter.differenceInSeconds, differenceInDays = dateAdapter.differenceInDays;\n    var maxRange = getDifferenceInDaysWithExclusions(dateAdapter, {\n        date1: viewStart,\n        date2: viewEnd,\n        excluded: excluded,\n    });\n    var totalDaysInView = differenceInDays(viewEnd, viewStart) + 1;\n    var eventsMapped = events\n        .filter(function (event) { return event.allDay; })\n        .map(function (event) {\n        var offset = getWeekViewEventOffset(dateAdapter, {\n            event: event,\n            startOfWeek: viewStart,\n            excluded: excluded,\n            precision: precision,\n        });\n        var span = getWeekViewEventSpan(dateAdapter, {\n            event: event,\n            offset: offset,\n            startOfWeekDate: viewStart,\n            excluded: excluded,\n            precision: precision,\n            totalDaysInView: totalDaysInView,\n        });\n        return { event: event, offset: offset, span: span };\n    })\n        .filter(function (e) { return e.offset < maxRange; })\n        .filter(function (e) { return e.span > 0; })\n        .map(function (entry) { return ({\n        event: entry.event,\n        offset: entry.offset,\n        span: entry.span,\n        startsBeforeWeek: entry.event.start < viewStart,\n        endsAfterWeek: (entry.event.end || entry.event.start) > viewEnd,\n    }); })\n        .sort(function (itemA, itemB) {\n        var startSecondsDiff = differenceInSeconds(itemA.event.start, itemB.event.start);\n        if (startSecondsDiff === 0) {\n            return differenceInSeconds(itemB.event.end || itemB.event.start, itemA.event.end || itemA.event.start);\n        }\n        return startSecondsDiff;\n    });\n    var allDayEventRows = [];\n    var allocatedEvents = [];\n    eventsMapped.forEach(function (event, index) {\n        if (allocatedEvents.indexOf(event) === -1) {\n            allocatedEvents.push(event);\n            var rowSpan_1 = event.span + event.offset;\n            var otherRowEvents = eventsMapped\n                .slice(index + 1)\n                .filter(function (nextEvent) {\n                if (nextEvent.offset >= rowSpan_1 &&\n                    rowSpan_1 + nextEvent.span <= totalDaysInView &&\n                    allocatedEvents.indexOf(nextEvent) === -1) {\n                    var nextEventOffset = nextEvent.offset - rowSpan_1;\n                    if (!absolutePositionedEvents) {\n                        nextEvent.offset = nextEventOffset;\n                    }\n                    rowSpan_1 += nextEvent.span + nextEventOffset;\n                    allocatedEvents.push(nextEvent);\n                    return true;\n                }\n            });\n            var weekEvents = __spreadArray([event], otherRowEvents, true);\n            var id = weekEvents\n                .filter(function (weekEvent) { return weekEvent.event.id; })\n                .map(function (weekEvent) { return weekEvent.event.id; })\n                .join('-');\n            allDayEventRows.push(__assign({ row: weekEvents }, (id ? { id: id } : {})));\n        }\n    });\n    return allDayEventRows;\n}\nfunction getWeekViewHourGrid(dateAdapter, _a) {\n    var events = _a.events, viewDate = _a.viewDate, hourSegments = _a.hourSegments, hourDuration = _a.hourDuration, dayStart = _a.dayStart, dayEnd = _a.dayEnd, weekStartsOn = _a.weekStartsOn, excluded = _a.excluded, weekendDays = _a.weekendDays, segmentHeight = _a.segmentHeight, viewStart = _a.viewStart, viewEnd = _a.viewEnd, minimumEventHeight = _a.minimumEventHeight;\n    var dayViewHourGrid = getDayViewHourGrid(dateAdapter, {\n        viewDate: viewDate,\n        hourSegments: hourSegments,\n        hourDuration: hourDuration,\n        dayStart: dayStart,\n        dayEnd: dayEnd,\n    });\n    var weekDays = getWeekViewHeader(dateAdapter, {\n        viewDate: viewDate,\n        weekStartsOn: weekStartsOn,\n        excluded: excluded,\n        weekendDays: weekendDays,\n        viewStart: viewStart,\n        viewEnd: viewEnd,\n    });\n    var setHours = dateAdapter.setHours, setMinutes = dateAdapter.setMinutes, getHours = dateAdapter.getHours, getMinutes = dateAdapter.getMinutes;\n    return weekDays.map(function (day) {\n        var dayView = getDayView(dateAdapter, {\n            events: events,\n            viewDate: day.date,\n            hourSegments: hourSegments,\n            dayStart: dayStart,\n            dayEnd: dayEnd,\n            segmentHeight: segmentHeight,\n            eventWidth: 1,\n            hourDuration: hourDuration,\n            minimumEventHeight: minimumEventHeight,\n        });\n        var hours = dayViewHourGrid.map(function (hour) {\n            var segments = hour.segments.map(function (segment) {\n                var date = setMinutes(setHours(day.date, getHours(segment.date)), getMinutes(segment.date));\n                return __assign(__assign({}, segment), { date: date });\n            });\n            return __assign(__assign({}, hour), { segments: segments });\n        });\n        function getColumnCount(allEvents, prevOverlappingEvents) {\n            var columnCount = Math.max.apply(Math, prevOverlappingEvents.map(function (iEvent) { return iEvent.left + 1; }));\n            var nextOverlappingEvents = allEvents\n                .filter(function (iEvent) { return iEvent.left >= columnCount; })\n                .filter(function (iEvent) {\n                return (getOverLappingWeekViewEvents(prevOverlappingEvents, iEvent.top, iEvent.top + iEvent.height).length > 0);\n            });\n            if (nextOverlappingEvents.length > 0) {\n                return getColumnCount(allEvents, nextOverlappingEvents);\n            }\n            else {\n                return columnCount;\n            }\n        }\n        var mappedEvents = dayView.events.map(function (event) {\n            var columnCount = getColumnCount(dayView.events, getOverLappingWeekViewEvents(dayView.events, event.top, event.top + event.height));\n            var width = 100 / columnCount;\n            return __assign(__assign({}, event), { left: event.left * width, width: width });\n        });\n        return {\n            hours: hours,\n            date: day.date,\n            events: mappedEvents.map(function (event) {\n                var overLappingEvents = getOverLappingWeekViewEvents(mappedEvents.filter(function (otherEvent) { return otherEvent.left > event.left; }), event.top, event.top + event.height);\n                if (overLappingEvents.length > 0) {\n                    return __assign(__assign({}, event), { width: Math.min.apply(Math, overLappingEvents.map(function (otherEvent) { return otherEvent.left; })) - event.left });\n                }\n                return event;\n            }),\n        };\n    });\n}\nexport function getWeekView(dateAdapter, _a) {\n    var _b = _a.events, events = _b === void 0 ? [] : _b, viewDate = _a.viewDate, weekStartsOn = _a.weekStartsOn, _c = _a.excluded, excluded = _c === void 0 ? [] : _c, _d = _a.precision, precision = _d === void 0 ? 'days' : _d, _e = _a.absolutePositionedEvents, absolutePositionedEvents = _e === void 0 ? false : _e, hourSegments = _a.hourSegments, hourDuration = _a.hourDuration, dayStart = _a.dayStart, dayEnd = _a.dayEnd, weekendDays = _a.weekendDays, segmentHeight = _a.segmentHeight, minimumEventHeight = _a.minimumEventHeight, _f = _a.viewStart, viewStart = _f === void 0 ? dateAdapter.startOfWeek(viewDate, { weekStartsOn: weekStartsOn }) : _f, _g = _a.viewEnd, viewEnd = _g === void 0 ? dateAdapter.endOfWeek(viewDate, { weekStartsOn: weekStartsOn }) : _g;\n    if (!events) {\n        events = [];\n    }\n    var startOfDay = dateAdapter.startOfDay, endOfDay = dateAdapter.endOfDay;\n    viewStart = startOfDay(viewStart);\n    viewEnd = endOfDay(viewEnd);\n    var eventsInPeriod = getEventsInPeriod(dateAdapter, {\n        events: events,\n        periodStart: viewStart,\n        periodEnd: viewEnd,\n    });\n    var header = getWeekViewHeader(dateAdapter, {\n        viewDate: viewDate,\n        weekStartsOn: weekStartsOn,\n        excluded: excluded,\n        weekendDays: weekendDays,\n        viewStart: viewStart,\n        viewEnd: viewEnd,\n    });\n    return {\n        allDayEventRows: getAllDayWeekEvents(dateAdapter, {\n            events: eventsInPeriod,\n            excluded: excluded,\n            precision: precision,\n            absolutePositionedEvents: absolutePositionedEvents,\n            viewStart: viewStart,\n            viewEnd: viewEnd,\n        }),\n        period: {\n            events: eventsInPeriod,\n            start: header[0].date,\n            end: endOfDay(header[header.length - 1].date),\n        },\n        hourColumns: getWeekViewHourGrid(dateAdapter, {\n            events: events,\n            viewDate: viewDate,\n            hourSegments: hourSegments,\n            hourDuration: hourDuration,\n            dayStart: dayStart,\n            dayEnd: dayEnd,\n            weekStartsOn: weekStartsOn,\n            excluded: excluded,\n            weekendDays: weekendDays,\n            segmentHeight: segmentHeight,\n            viewStart: viewStart,\n            viewEnd: viewEnd,\n            minimumEventHeight: minimumEventHeight,\n        }),\n    };\n}\nexport function getMonthView(dateAdapter, _a) {\n    var _b = _a.events, events = _b === void 0 ? [] : _b, viewDate = _a.viewDate, weekStartsOn = _a.weekStartsOn, _c = _a.excluded, excluded = _c === void 0 ? [] : _c, _d = _a.viewStart, viewStart = _d === void 0 ? dateAdapter.startOfMonth(viewDate) : _d, _e = _a.viewEnd, viewEnd = _e === void 0 ? dateAdapter.endOfMonth(viewDate) : _e, weekendDays = _a.weekendDays;\n    if (!events) {\n        events = [];\n    }\n    var startOfWeek = dateAdapter.startOfWeek, endOfWeek = dateAdapter.endOfWeek, differenceInDays = dateAdapter.differenceInDays, startOfDay = dateAdapter.startOfDay, addHours = dateAdapter.addHours, endOfDay = dateAdapter.endOfDay, isSameMonth = dateAdapter.isSameMonth, getDay = dateAdapter.getDay;\n    var start = startOfWeek(viewStart, { weekStartsOn: weekStartsOn });\n    var end = endOfWeek(viewEnd, { weekStartsOn: weekStartsOn });\n    var eventsInMonth = getEventsInPeriod(dateAdapter, {\n        events: events,\n        periodStart: start,\n        periodEnd: end,\n    });\n    var initialViewDays = [];\n    var previousDate;\n    var _loop_2 = function (i) {\n        // hacky fix for https://github.com/mattlewis92/angular-calendar/issues/173\n        var date;\n        if (previousDate) {\n            date = startOfDay(addHours(previousDate, HOURS_IN_DAY));\n            if (previousDate.getTime() === date.getTime()) {\n                // DST change, so need to add 25 hours\n                /* istanbul ignore next */\n                date = startOfDay(addHours(previousDate, HOURS_IN_DAY + 1));\n            }\n            previousDate = date;\n        }\n        else {\n            date = previousDate = start;\n        }\n        if (!excluded.some(function (e) { return getDay(date) === e; })) {\n            var day = getWeekDay(dateAdapter, {\n                date: date,\n                weekendDays: weekendDays,\n            });\n            var eventsInPeriod = getEventsInPeriod(dateAdapter, {\n                events: eventsInMonth,\n                periodStart: startOfDay(date),\n                periodEnd: endOfDay(date),\n            });\n            day.inMonth = isSameMonth(date, viewDate);\n            day.events = eventsInPeriod;\n            day.badgeTotal = eventsInPeriod.length;\n            initialViewDays.push(day);\n        }\n    };\n    for (var i = 0; i < differenceInDays(end, start) + 1; i++) {\n        _loop_2(i);\n    }\n    var days = [];\n    var totalDaysVisibleInWeek = DAYS_IN_WEEK - excluded.length;\n    if (totalDaysVisibleInWeek < DAYS_IN_WEEK) {\n        for (var i = 0; i < initialViewDays.length; i += totalDaysVisibleInWeek) {\n            var row = initialViewDays.slice(i, i + totalDaysVisibleInWeek);\n            var isRowInMonth = row.some(function (day) { return viewStart <= day.date && day.date < viewEnd; });\n            if (isRowInMonth) {\n                days = __spreadArray(__spreadArray([], days, true), row, true);\n            }\n        }\n    }\n    else {\n        days = initialViewDays;\n    }\n    var rows = Math.floor(days.length / totalDaysVisibleInWeek);\n    var rowOffsets = [];\n    for (var i = 0; i < rows; i++) {\n        rowOffsets.push(i * totalDaysVisibleInWeek);\n    }\n    return {\n        rowOffsets: rowOffsets,\n        totalDaysVisibleInWeek: totalDaysVisibleInWeek,\n        days: days,\n        period: {\n            start: days[0].date,\n            end: endOfDay(days[days.length - 1].date),\n            events: eventsInMonth,\n        },\n    };\n}\nfunction getOverLappingWeekViewEvents(events, top, bottom) {\n    return events.filter(function (previousEvent) {\n        var previousEventTop = previousEvent.top;\n        var previousEventBottom = previousEvent.top + previousEvent.height;\n        if (top < previousEventBottom && previousEventBottom < bottom) {\n            return true;\n        }\n        else if (top < previousEventTop && previousEventTop < bottom) {\n            return true;\n        }\n        else if (previousEventTop <= top && bottom <= previousEventBottom) {\n            return true;\n        }\n        return false;\n    });\n}\nfunction getDayView(dateAdapter, _a) {\n    var events = _a.events, viewDate = _a.viewDate, hourSegments = _a.hourSegments, dayStart = _a.dayStart, dayEnd = _a.dayEnd, eventWidth = _a.eventWidth, segmentHeight = _a.segmentHeight, hourDuration = _a.hourDuration, minimumEventHeight = _a.minimumEventHeight;\n    var setMinutes = dateAdapter.setMinutes, setHours = dateAdapter.setHours, startOfDay = dateAdapter.startOfDay, startOfMinute = dateAdapter.startOfMinute, endOfDay = dateAdapter.endOfDay, differenceInMinutes = dateAdapter.differenceInMinutes;\n    var startOfView = setMinutes(setHours(startOfDay(viewDate), sanitiseHours(dayStart.hour)), sanitiseMinutes(dayStart.minute));\n    var endOfView = setMinutes(setHours(startOfMinute(endOfDay(viewDate)), sanitiseHours(dayEnd.hour)), sanitiseMinutes(dayEnd.minute));\n    endOfView.setSeconds(59, 999);\n    var previousDayEvents = [];\n    var eventsInPeriod = getEventsInPeriod(dateAdapter, {\n        events: events.filter(function (event) { return !event.allDay; }),\n        periodStart: startOfView,\n        periodEnd: endOfView,\n    });\n    var dayViewEvents = eventsInPeriod\n        .sort(function (eventA, eventB) {\n        return eventA.start.valueOf() - eventB.start.valueOf();\n    })\n        .map(function (event) {\n        var eventStart = event.start;\n        var eventEnd = event.end || eventStart;\n        var startsBeforeDay = eventStart < startOfView;\n        var endsAfterDay = eventEnd > endOfView;\n        var hourHeightModifier = (hourSegments * segmentHeight) / (hourDuration || MINUTES_IN_HOUR);\n        var top = 0;\n        if (eventStart > startOfView) {\n            // adjust the difference in minutes if the user's offset is different between the start of the day and the event (e.g. when going to or from DST)\n            var eventOffset = dateAdapter.getTimezoneOffset(eventStart);\n            var startOffset = dateAdapter.getTimezoneOffset(startOfView);\n            var diff = startOffset - eventOffset;\n            top += differenceInMinutes(eventStart, startOfView) + diff;\n        }\n        top *= hourHeightModifier;\n        top = Math.floor(top);\n        var startDate = startsBeforeDay ? startOfView : eventStart;\n        var endDate = endsAfterDay ? endOfView : eventEnd;\n        var timezoneOffset = dateAdapter.getTimezoneOffset(startDate) -\n            dateAdapter.getTimezoneOffset(endDate);\n        var height = differenceInMinutes(endDate, startDate) + timezoneOffset;\n        if (!event.end) {\n            height = segmentHeight;\n        }\n        else {\n            height *= hourHeightModifier;\n        }\n        if (minimumEventHeight && height < minimumEventHeight) {\n            height = minimumEventHeight;\n        }\n        height = Math.floor(height);\n        var bottom = top + height;\n        var overlappingPreviousEvents = getOverLappingWeekViewEvents(previousDayEvents, top, bottom);\n        var left = 0;\n        while (overlappingPreviousEvents.some(function (previousEvent) { return previousEvent.left === left; })) {\n            left += eventWidth;\n        }\n        var dayEvent = {\n            event: event,\n            height: height,\n            width: eventWidth,\n            top: top,\n            left: left,\n            startsBeforeDay: startsBeforeDay,\n            endsAfterDay: endsAfterDay,\n        };\n        previousDayEvents.push(dayEvent);\n        return dayEvent;\n    });\n    var width = Math.max.apply(Math, dayViewEvents.map(function (event) { return event.left + event.width; }));\n    var allDayEvents = getEventsInPeriod(dateAdapter, {\n        events: events.filter(function (event) { return event.allDay; }),\n        periodStart: startOfDay(startOfView),\n        periodEnd: endOfDay(endOfView),\n    });\n    return {\n        events: dayViewEvents,\n        width: width,\n        allDayEvents: allDayEvents,\n        period: {\n            events: eventsInPeriod,\n            start: startOfView,\n            end: endOfView,\n        },\n    };\n}\nfunction sanitiseHours(hours) {\n    return Math.max(Math.min(23, hours), 0);\n}\nfunction sanitiseMinutes(minutes) {\n    return Math.max(Math.min(59, minutes), 0);\n}\nfunction getDayViewHourGrid(dateAdapter, _a) {\n    var viewDate = _a.viewDate, hourSegments = _a.hourSegments, hourDuration = _a.hourDuration, dayStart = _a.dayStart, dayEnd = _a.dayEnd;\n    var setMinutes = dateAdapter.setMinutes, setHours = dateAdapter.setHours, startOfDay = dateAdapter.startOfDay, startOfMinute = dateAdapter.startOfMinute, endOfDay = dateAdapter.endOfDay, addMinutes = dateAdapter.addMinutes, addDays = dateAdapter.addDays;\n    var hours = [];\n    var startOfView = setMinutes(setHours(startOfDay(viewDate), sanitiseHours(dayStart.hour)), sanitiseMinutes(dayStart.minute));\n    var endOfView = setMinutes(setHours(startOfMinute(endOfDay(viewDate)), sanitiseHours(dayEnd.hour)), sanitiseMinutes(dayEnd.minute));\n    var segmentDuration = (hourDuration || MINUTES_IN_HOUR) / hourSegments;\n    var startOfViewDay = startOfDay(viewDate);\n    var endOfViewDay = endOfDay(viewDate);\n    var dateAdjustment = function (d) { return d; };\n    // this means that we change from or to DST on this day and that's going to cause problems so we bump the date\n    if (dateAdapter.getTimezoneOffset(startOfViewDay) !==\n        dateAdapter.getTimezoneOffset(endOfViewDay)) {\n        startOfViewDay = addDays(startOfViewDay, 1);\n        startOfView = addDays(startOfView, 1);\n        endOfView = addDays(endOfView, 1);\n        dateAdjustment = function (d) { return addDays(d, -1); };\n    }\n    var dayDuration = hourDuration\n        ? (HOURS_IN_DAY * 60) / hourDuration\n        : MINUTES_IN_HOUR;\n    for (var i = 0; i < dayDuration; i++) {\n        var segments = [];\n        for (var j = 0; j < hourSegments; j++) {\n            var date = addMinutes(addMinutes(startOfView, i * (hourDuration || MINUTES_IN_HOUR)), j * segmentDuration);\n            if (date >= startOfView && date < endOfView) {\n                segments.push({\n                    date: dateAdjustment(date),\n                    displayDate: date,\n                    isStart: j === 0,\n                });\n            }\n        }\n        if (segments.length > 0) {\n            hours.push({ segments: segments });\n        }\n    }\n    return hours;\n}\nexport var EventValidationErrorMessage;\n(function (EventValidationErrorMessage) {\n    EventValidationErrorMessage[\"NotArray\"] = \"Events must be an array\";\n    EventValidationErrorMessage[\"StartPropertyMissing\"] = \"Event is missing the `start` property\";\n    EventValidationErrorMessage[\"StartPropertyNotDate\"] = \"Event `start` property should be a javascript date object. Do `new Date(event.start)` to fix it.\";\n    EventValidationErrorMessage[\"EndPropertyNotDate\"] = \"Event `end` property should be a javascript date object. Do `new Date(event.end)` to fix it.\";\n    EventValidationErrorMessage[\"EndsBeforeStart\"] = \"Event `start` property occurs after the `end`\";\n})(EventValidationErrorMessage || (EventValidationErrorMessage = {}));\nexport function validateEvents(events, log) {\n    var isValid = true;\n    function isError(msg, event) {\n        log(msg, event);\n        isValid = false;\n    }\n    if (!Array.isArray(events)) {\n        log(EventValidationErrorMessage.NotArray, events);\n        return false;\n    }\n    events.forEach(function (event) {\n        if (!event.start) {\n            isError(EventValidationErrorMessage.StartPropertyMissing, event);\n        }\n        else if (!(event.start instanceof Date)) {\n            isError(EventValidationErrorMessage.StartPropertyNotDate, event);\n        }\n        if (event.end) {\n            if (!(event.end instanceof Date)) {\n                isError(EventValidationErrorMessage.EndPropertyNotDate, event);\n            }\n            if (event.start > event.end) {\n                isError(EventValidationErrorMessage.EndsBeforeStart, event);\n            }\n        }\n    });\n    return isValid;\n}\n", "import * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Inject, Input, Output, Injectable, LOCALE_ID, Pipe, Component, HostListener, InjectionToken, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { DOCUMENT, formatDate, CommonModule, I18nPluralPipe } from '@angular/common';\nimport { Subject, Observable, of, timer, BehaviorSubject, interval } from 'rxjs';\nimport { takeUntil, switchMap, startWith, switchMapTo, map } from 'rxjs/operators';\nimport { positionElements } from 'positioning';\nimport { validateEvents as validateEvents$1, getMonthView, getWeekViewHeader, getWeekView } from 'calendar-utils';\nconst _c0 = (a0, a1) => ({\n  event: a0,\n  trackByActionId: a1\n});\nconst _c1 = a0 => ({\n  action: a0\n});\nfunction CalendarEventActionsComponent_ng_template_0_span_0_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 5);\n    i0.ɵɵpipe(1, \"calendarA11y\");\n    i0.ɵɵlistener(\"mwlClick\", function CalendarEventActionsComponent_ng_template_0_span_0_a_1_Template_a_mwlClick_0_listener($event) {\n      const action_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const event_r3 = i0.ɵɵnextContext(2).event;\n      return i0.ɵɵresetView(action_r2.onClick({\n        event: event_r3,\n        sourceEvent: $event\n      }));\n    })(\"mwlKeydownEnter\", function CalendarEventActionsComponent_ng_template_0_span_0_a_1_Template_a_mwlKeydownEnter_0_listener($event) {\n      const action_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const event_r3 = i0.ɵɵnextContext(2).event;\n      return i0.ɵɵresetView(action_r2.onClick({\n        event: event_r3,\n        sourceEvent: $event\n      }));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", action_r2.cssClass)(\"innerHtml\", action_r2.label, i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"aria-label\", i0.ɵɵpipeBind2(1, 3, i0.ɵɵpureFunction1(6, _c1, action_r2), \"actionButtonLabel\"));\n  }\n}\nfunction CalendarEventActionsComponent_ng_template_0_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 3);\n    i0.ɵɵtemplate(1, CalendarEventActionsComponent_ng_template_0_span_0_a_1_Template, 2, 8, \"a\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    const event_r3 = ctx_r3.event;\n    const trackByActionId_r5 = ctx_r3.trackByActionId;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", event_r3.actions)(\"ngForTrackBy\", trackByActionId_r5);\n  }\n}\nfunction CalendarEventActionsComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CalendarEventActionsComponent_ng_template_0_span_0_Template, 2, 2, \"span\", 2);\n  }\n  if (rf & 2) {\n    const event_r3 = ctx.event;\n    i0.ɵɵproperty(\"ngIf\", event_r3.actions);\n  }\n}\nfunction CalendarEventActionsComponent_ng_template_2_Template(rf, ctx) {}\nconst _c2 = (a0, a1) => ({\n  event: a0,\n  view: a1\n});\nconst _c3 = () => ({});\nfunction CalendarEventTitleComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 2);\n    i0.ɵɵpipe(1, \"calendarEventTitle\");\n    i0.ɵɵpipe(2, \"calendarA11y\");\n  }\n  if (rf & 2) {\n    const event_r1 = ctx.event;\n    const view_r2 = ctx.view;\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind3(1, 2, event_r1.title, view_r2, event_r1), i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"aria-hidden\", i0.ɵɵpipeBind2(2, 6, i0.ɵɵpureFunction0(9, _c3), \"hideEventTitle\"));\n  }\n}\nfunction CalendarEventTitleComponent_ng_template_2_Template(rf, ctx) {}\nconst _c4 = (a0, a1, a2) => ({\n  contents: a0,\n  placement: a1,\n  event: a2\n});\nfunction CalendarTooltipWindowComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵelement(1, \"div\", 3)(2, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contents_r1 = ctx.contents;\n    const placement_r2 = ctx.placement;\n    i0.ɵɵproperty(\"ngClass\", \"cal-tooltip-\" + placement_r2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHtml\", contents_r1, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction CalendarTooltipWindowComponent_ng_template_2_Template(rf, ctx) {}\nconst _c5 = (a0, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11) => ({\n  day: a0,\n  openDay: a1,\n  locale: a2,\n  tooltipPlacement: a3,\n  highlightDay: a4,\n  unhighlightDay: a5,\n  eventClicked: a6,\n  tooltipTemplate: a7,\n  tooltipAppendToBody: a8,\n  tooltipDelay: a9,\n  trackByEventId: a10,\n  validateDrag: a11\n});\nconst _c6 = (a0, a1) => ({\n  day: a0,\n  locale: a1\n});\nconst _c7 = a0 => ({\n  backgroundColor: a0\n});\nconst _c8 = (a0, a1) => ({\n  event: a0,\n  draggedFrom: a1\n});\nconst _c9 = (a0, a1) => ({\n  x: a0,\n  y: a1\n});\nconst _c10 = () => ({\n  delay: 300,\n  delta: 30\n});\nfunction CalendarMonthCellComponent_ng_template_0_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 7);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r1 = i0.ɵɵnextContext().day;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(day_r1.badgeTotal);\n  }\n}\nfunction CalendarMonthCellComponent_ng_template_0_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵpipe(1, \"calendarEventTitle\");\n    i0.ɵɵpipe(2, \"calendarA11y\");\n    i0.ɵɵlistener(\"mouseenter\", function CalendarMonthCellComponent_ng_template_0_div_7_div_1_Template_div_mouseenter_0_listener() {\n      const event_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const highlightDay_r4 = i0.ɵɵnextContext(2).highlightDay;\n      return i0.ɵɵresetView(highlightDay_r4.emit({\n        event: event_r3\n      }));\n    })(\"mouseleave\", function CalendarMonthCellComponent_ng_template_0_div_7_div_1_Template_div_mouseleave_0_listener() {\n      const event_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const unhighlightDay_r5 = i0.ɵɵnextContext(2).unhighlightDay;\n      return i0.ɵɵresetView(unhighlightDay_r5.emit({\n        event: event_r3\n      }));\n    })(\"mwlClick\", function CalendarMonthCellComponent_ng_template_0_div_7_div_1_Template_div_mwlClick_0_listener($event) {\n      const event_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const eventClicked_r6 = i0.ɵɵnextContext(2).eventClicked;\n      return i0.ɵɵresetView(eventClicked_r6.emit({\n        event: event_r3,\n        sourceEvent: $event\n      }));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const event_r3 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    const day_r1 = ctx_r6.day;\n    const tooltipPlacement_r8 = ctx_r6.tooltipPlacement;\n    const tooltipTemplate_r9 = ctx_r6.tooltipTemplate;\n    const tooltipAppendToBody_r10 = ctx_r6.tooltipAppendToBody;\n    const tooltipDelay_r11 = ctx_r6.tooltipDelay;\n    const validateDrag_r12 = ctx_r6.validateDrag;\n    i0.ɵɵclassProp(\"cal-draggable\", event_r3.draggable);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(22, _c7, event_r3.color == null ? null : event_r3.color.primary))(\"ngClass\", event_r3 == null ? null : event_r3.cssClass)(\"mwlCalendarTooltip\", i0.ɵɵpipeBind3(1, 15, event_r3.title, \"monthTooltip\", event_r3))(\"tooltipPlacement\", tooltipPlacement_r8)(\"tooltipEvent\", event_r3)(\"tooltipTemplate\", tooltipTemplate_r9)(\"tooltipAppendToBody\", tooltipAppendToBody_r10)(\"tooltipDelay\", tooltipDelay_r11)(\"dropData\", i0.ɵɵpureFunction2(24, _c8, event_r3, day_r1))(\"dragAxis\", i0.ɵɵpureFunction2(27, _c9, event_r3.draggable, event_r3.draggable))(\"validateDrag\", validateDrag_r12)(\"touchStartLongPress\", i0.ɵɵpureFunction0(30, _c10));\n    i0.ɵɵattribute(\"aria-hidden\", i0.ɵɵpipeBind2(2, 19, i0.ɵɵpureFunction0(31, _c3), \"hideMonthCellEvents\"));\n  }\n}\nfunction CalendarMonthCellComponent_ng_template_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtemplate(1, CalendarMonthCellComponent_ng_template_0_div_7_div_1_Template, 3, 32, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    const day_r1 = ctx_r6.day;\n    const trackByEventId_r13 = ctx_r6.trackByEventId;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", day_r1.events)(\"ngForTrackBy\", trackByEventId_r13);\n  }\n}\nfunction CalendarMonthCellComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵpipe(1, \"calendarA11y\");\n    i0.ɵɵelementStart(2, \"span\", 3);\n    i0.ɵɵtemplate(3, CalendarMonthCellComponent_ng_template_0_span_3_Template, 2, 1, \"span\", 4);\n    i0.ɵɵelementStart(4, \"span\", 5);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"calendarDate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(7, CalendarMonthCellComponent_ng_template_0_div_7_Template, 2, 2, \"div\", 6);\n  }\n  if (rf & 2) {\n    const day_r1 = ctx.day;\n    const locale_r14 = ctx.locale;\n    i0.ɵɵattribute(\"aria-label\", i0.ɵɵpipeBind2(1, 4, i0.ɵɵpureFunction2(11, _c6, day_r1, locale_r14), \"monthCell\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", day_r1.badgeTotal > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind3(6, 7, day_r1.date, \"monthViewDayNumber\", locale_r14));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", day_r1.events.length > 0);\n  }\n}\nfunction CalendarMonthCellComponent_ng_template_2_Template(rf, ctx) {}\nconst _c11 = (a0, a1, a2, a3, a4) => ({\n  events: a0,\n  eventClicked: a1,\n  isOpen: a2,\n  trackByEventId: a3,\n  validateDrag: a4\n});\nconst _c12 = (a0, a1) => ({\n  date: a0,\n  locale: a1\n});\nconst _c13 = a0 => ({\n  event: a0\n});\nconst _c14 = (a0, a1) => ({\n  event: a0,\n  locale: a1\n});\nfunction CalendarOpenDayEventsComponent_ng_template_0_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"span\", 8);\n    i0.ɵɵtext(2, \" \");\n    i0.ɵɵelementStart(3, \"mwl-calendar-event-title\", 9);\n    i0.ɵɵpipe(4, \"calendarA11y\");\n    i0.ɵɵlistener(\"mwlClick\", function CalendarOpenDayEventsComponent_ng_template_0_div_0_div_5_Template_mwl_calendar_event_title_mwlClick_3_listener($event) {\n      const event_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const eventClicked_r3 = i0.ɵɵnextContext(2).eventClicked;\n      return i0.ɵɵresetView(eventClicked_r3.emit({\n        event: event_r2,\n        sourceEvent: $event\n      }));\n    })(\"mwlKeydownEnter\", function CalendarOpenDayEventsComponent_ng_template_0_div_0_div_5_Template_mwl_calendar_event_title_mwlKeydownEnter_3_listener($event) {\n      const event_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const eventClicked_r3 = i0.ɵɵnextContext(2).eventClicked;\n      return i0.ɵɵresetView(eventClicked_r3.emit({\n        event: event_r2,\n        sourceEvent: $event\n      }));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \");\n    i0.ɵɵelement(6, \"mwl-calendar-event-actions\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const event_r2 = ctx.$implicit;\n    const validateDrag_r4 = i0.ɵɵnextContext(2).validateDrag;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"cal-draggable\", event_r2.draggable);\n    i0.ɵɵproperty(\"ngClass\", event_r2 == null ? null : event_r2.cssClass)(\"dropData\", i0.ɵɵpureFunction1(16, _c13, event_r2))(\"dragAxis\", i0.ɵɵpureFunction2(18, _c9, event_r2.draggable, event_r2.draggable))(\"validateDrag\", validateDrag_r4)(\"touchStartLongPress\", i0.ɵɵpureFunction0(21, _c10));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(22, _c7, event_r2.color == null ? null : event_r2.color.primary));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"event\", event_r2)(\"customTemplate\", ctx_r4.eventTitleTemplate);\n    i0.ɵɵattribute(\"aria-label\", i0.ɵɵpipeBind2(4, 13, i0.ɵɵpureFunction2(24, _c14, event_r2, ctx_r4.locale), \"eventDescription\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"event\", event_r2)(\"customTemplate\", ctx_r4.eventActionsTemplate);\n  }\n}\nfunction CalendarOpenDayEventsComponent_ng_template_0_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"span\", 4);\n    i0.ɵɵpipe(2, \"calendarA11y\");\n    i0.ɵɵelement(3, \"span\", 5);\n    i0.ɵɵpipe(4, \"calendarA11y\");\n    i0.ɵɵtemplate(5, CalendarOpenDayEventsComponent_ng_template_0_div_0_div_5_Template, 7, 27, \"div\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    const events_r7 = ctx_r5.events;\n    const trackByEventId_r8 = ctx_r5.trackByEventId;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@collapse\", undefined);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", i0.ɵɵpipeBind2(2, 5, i0.ɵɵpureFunction2(11, _c12, ctx_r4.date, ctx_r4.locale), \"openDayEventsAlert\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"aria-label\", i0.ɵɵpipeBind2(4, 8, i0.ɵɵpureFunction2(14, _c12, ctx_r4.date, ctx_r4.locale), \"openDayEventsLandmark\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", events_r7)(\"ngForTrackBy\", trackByEventId_r8);\n  }\n}\nfunction CalendarOpenDayEventsComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CalendarOpenDayEventsComponent_ng_template_0_div_0_Template, 6, 17, \"div\", 2);\n  }\n  if (rf & 2) {\n    const isOpen_r9 = ctx.isOpen;\n    i0.ɵɵproperty(\"ngIf\", isOpen_r9);\n  }\n}\nfunction CalendarOpenDayEventsComponent_ng_template_2_Template(rf, ctx) {}\nconst _c15 = (a0, a1, a2) => ({\n  days: a0,\n  locale: a1,\n  trackByWeekDayHeaderDate: a2\n});\nfunction CalendarMonthViewHeaderComponent_ng_template_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵlistener(\"click\", function CalendarMonthViewHeaderComponent_ng_template_0_div_1_Template_div_click_0_listener($event) {\n      const day_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.columnHeaderClicked.emit({\n        isoDayNumber: day_r2.day,\n        sourceEvent: $event\n      }));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"calendarDate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r2 = ctx.$implicit;\n    const locale_r4 = i0.ɵɵnextContext().locale;\n    i0.ɵɵclassProp(\"cal-past\", day_r2.isPast)(\"cal-today\", day_r2.isToday)(\"cal-future\", day_r2.isFuture)(\"cal-weekend\", day_r2.isWeekend);\n    i0.ɵɵproperty(\"ngClass\", day_r2.cssClass);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind3(2, 10, day_r2.date, \"monthViewColumnHeader\", locale_r4), \" \");\n  }\n}\nfunction CalendarMonthViewHeaderComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtemplate(1, CalendarMonthViewHeaderComponent_ng_template_0_div_1_Template, 3, 14, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const days_r5 = ctx.days;\n    const trackByWeekDayHeaderDate_r6 = ctx.trackByWeekDayHeaderDate;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", days_r5)(\"ngForTrackBy\", trackByWeekDayHeaderDate_r6);\n  }\n}\nfunction CalendarMonthViewHeaderComponent_ng_template_2_Template(rf, ctx) {}\nfunction CalendarMonthViewComponent_div_3_mwl_calendar_month_cell_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mwl-calendar-month-cell\", 7);\n    i0.ɵɵpipe(1, \"calendarA11y\");\n    i0.ɵɵlistener(\"mwlClick\", function CalendarMonthViewComponent_div_3_mwl_calendar_month_cell_2_Template_mwl_calendar_month_cell_mwlClick_0_listener($event) {\n      const day_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.dayClicked.emit({\n        day: day_r3,\n        sourceEvent: $event\n      }));\n    })(\"mwlKeydownEnter\", function CalendarMonthViewComponent_div_3_mwl_calendar_month_cell_2_Template_mwl_calendar_month_cell_mwlKeydownEnter_0_listener($event) {\n      const day_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.dayClicked.emit({\n        day: day_r3,\n        sourceEvent: $event\n      }));\n    })(\"highlightDay\", function CalendarMonthViewComponent_div_3_mwl_calendar_month_cell_2_Template_mwl_calendar_month_cell_highlightDay_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleDayHighlight($event.event, true));\n    })(\"unhighlightDay\", function CalendarMonthViewComponent_div_3_mwl_calendar_month_cell_2_Template_mwl_calendar_month_cell_unhighlightDay_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleDayHighlight($event.event, false));\n    })(\"drop\", function CalendarMonthViewComponent_div_3_mwl_calendar_month_cell_2_Template_mwl_calendar_month_cell_drop_0_listener($event) {\n      const day_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.eventDropped(day_r3, $event.dropData.event, $event.dropData.draggedFrom));\n    })(\"eventClicked\", function CalendarMonthViewComponent_div_3_mwl_calendar_month_cell_2_Template_mwl_calendar_month_cell_eventClicked_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.eventClicked.emit({\n        event: $event.event,\n        sourceEvent: $event.sourceEvent\n      }));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", day_r3 == null ? null : day_r3.cssClass)(\"day\", day_r3)(\"openDay\", ctx_r3.openDay)(\"locale\", ctx_r3.locale)(\"tooltipPlacement\", ctx_r3.tooltipPlacement)(\"tooltipAppendToBody\", ctx_r3.tooltipAppendToBody)(\"tooltipTemplate\", ctx_r3.tooltipTemplate)(\"tooltipDelay\", ctx_r3.tooltipDelay)(\"customTemplate\", ctx_r3.cellTemplate)(\"ngStyle\", i0.ɵɵpureFunction1(15, _c7, day_r3.backgroundColor))(\"clickListenerDisabled\", ctx_r3.dayClicked.observers.length === 0);\n    i0.ɵɵattribute(\"tabindex\", i0.ɵɵpipeBind2(1, 12, i0.ɵɵpureFunction0(17, _c3), \"monthCellTabIndex\"));\n  }\n}\nfunction CalendarMonthViewComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 4);\n    i0.ɵɵtemplate(2, CalendarMonthViewComponent_div_3_mwl_calendar_month_cell_2_Template, 2, 18, \"mwl-calendar-month-cell\", 5);\n    i0.ɵɵpipe(3, \"slice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mwl-calendar-open-day-events\", 6);\n    i0.ɵɵlistener(\"eventClicked\", function CalendarMonthViewComponent_div_3_Template_mwl_calendar_open_day_events_eventClicked_4_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.eventClicked.emit({\n        event: $event.event,\n        sourceEvent: $event.sourceEvent\n      }));\n    })(\"drop\", function CalendarMonthViewComponent_div_3_Template_mwl_calendar_open_day_events_drop_4_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.eventDropped(ctx_r3.openDay, $event.dropData.event, $event.dropData.draggedFrom));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const rowIndex_r5 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(3, 9, ctx_r3.view.days, rowIndex_r5, rowIndex_r5 + ctx_r3.view.totalDaysVisibleInWeek))(\"ngForTrackBy\", ctx_r3.trackByDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"locale\", ctx_r3.locale)(\"isOpen\", ctx_r3.openRowIndex === rowIndex_r5)(\"events\", ctx_r3.openDay == null ? null : ctx_r3.openDay.events)(\"date\", ctx_r3.openDay == null ? null : ctx_r3.openDay.date)(\"customTemplate\", ctx_r3.openDayEventsTemplate)(\"eventTitleTemplate\", ctx_r3.eventTitleTemplate)(\"eventActionsTemplate\", ctx_r3.eventActionsTemplate);\n  }\n}\nconst _c16 = (a0, a1, a2, a3, a4, a5) => ({\n  days: a0,\n  locale: a1,\n  dayHeaderClicked: a2,\n  eventDropped: a3,\n  dragEnter: a4,\n  trackByWeekDayHeaderDate: a5\n});\nfunction CalendarWeekViewHeaderComponent_ng_template_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵlistener(\"mwlClick\", function CalendarWeekViewHeaderComponent_ng_template_0_div_1_Template_div_mwlClick_0_listener($event) {\n      const day_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const dayHeaderClicked_r3 = i0.ɵɵnextContext().dayHeaderClicked;\n      return i0.ɵɵresetView(dayHeaderClicked_r3.emit({\n        day: day_r2,\n        sourceEvent: $event\n      }));\n    })(\"drop\", function CalendarWeekViewHeaderComponent_ng_template_0_div_1_Template_div_drop_0_listener($event) {\n      const day_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const eventDropped_r4 = i0.ɵɵnextContext().eventDropped;\n      return i0.ɵɵresetView(eventDropped_r4.emit({\n        event: $event.dropData.event,\n        newStart: day_r2.date\n      }));\n    })(\"dragEnter\", function CalendarWeekViewHeaderComponent_ng_template_0_div_1_Template_div_dragEnter_0_listener() {\n      const day_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const dragEnter_r5 = i0.ɵɵnextContext().dragEnter;\n      return i0.ɵɵresetView(dragEnter_r5.emit({\n        date: day_r2.date\n      }));\n    });\n    i0.ɵɵelementStart(1, \"b\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"calendarDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"br\");\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"calendarDate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const day_r2 = ctx.$implicit;\n    const locale_r6 = i0.ɵɵnextContext().locale;\n    i0.ɵɵclassProp(\"cal-past\", day_r2.isPast)(\"cal-today\", day_r2.isToday)(\"cal-future\", day_r2.isFuture)(\"cal-weekend\", day_r2.isWeekend);\n    i0.ɵɵproperty(\"ngClass\", day_r2.cssClass);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind3(3, 11, day_r2.date, \"weekViewColumnHeader\", locale_r6));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind3(7, 15, day_r2.date, \"weekViewColumnSubHeader\", locale_r6));\n  }\n}\nfunction CalendarWeekViewHeaderComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtemplate(1, CalendarWeekViewHeaderComponent_ng_template_0_div_1_Template, 8, 19, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const days_r7 = ctx.days;\n    const trackByWeekDayHeaderDate_r8 = ctx.trackByWeekDayHeaderDate;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", days_r7)(\"ngForTrackBy\", trackByWeekDayHeaderDate_r8);\n  }\n}\nfunction CalendarWeekViewHeaderComponent_ng_template_2_Template(rf, ctx) {}\nconst _c17 = (a0, a1, a2, a3, a4, a5, a6, a7, a8) => ({\n  weekEvent: a0,\n  tooltipPlacement: a1,\n  eventClicked: a2,\n  tooltipTemplate: a3,\n  tooltipAppendToBody: a4,\n  tooltipDisabled: a5,\n  tooltipDelay: a6,\n  column: a7,\n  daysInWeek: a8\n});\nconst _c18 = (a0, a1, a2) => ({\n  color: a0,\n  backgroundColor: a1,\n  borderColor: a2\n});\nfunction CalendarWeekViewEventComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵpipe(1, \"calendarEventTitle\");\n    i0.ɵɵpipe(2, \"calendarA11y\");\n    i0.ɵɵlistener(\"mwlClick\", function CalendarWeekViewEventComponent_ng_template_0_Template_div_mwlClick_0_listener($event) {\n      const eventClicked_r2 = i0.ɵɵrestoreView(_r1).eventClicked;\n      return i0.ɵɵresetView(eventClicked_r2.emit({\n        sourceEvent: $event\n      }));\n    })(\"mwlKeydownEnter\", function CalendarWeekViewEventComponent_ng_template_0_Template_div_mwlKeydownEnter_0_listener($event) {\n      const eventClicked_r2 = i0.ɵɵrestoreView(_r1).eventClicked;\n      return i0.ɵɵresetView(eventClicked_r2.emit({\n        sourceEvent: $event\n      }));\n    });\n    i0.ɵɵelement(3, \"mwl-calendar-event-actions\", 3);\n    i0.ɵɵtext(4, \" \");\n    i0.ɵɵelement(5, \"mwl-calendar-event-title\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const weekEvent_r3 = ctx.weekEvent;\n    const tooltipPlacement_r4 = ctx.tooltipPlacement;\n    const tooltipTemplate_r5 = ctx.tooltipTemplate;\n    const tooltipAppendToBody_r6 = ctx.tooltipAppendToBody;\n    const tooltipDisabled_r7 = ctx.tooltipDisabled;\n    const tooltipDelay_r8 = ctx.tooltipDelay;\n    const daysInWeek_r9 = ctx.daysInWeek;\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction3(20, _c18, weekEvent_r3.event.color == null ? null : weekEvent_r3.event.color.secondaryText, weekEvent_r3.event.color == null ? null : weekEvent_r3.event.color.secondary, weekEvent_r3.event.color == null ? null : weekEvent_r3.event.color.primary))(\"mwlCalendarTooltip\", !tooltipDisabled_r7 ? i0.ɵɵpipeBind3(1, 13, weekEvent_r3.event.title, daysInWeek_r9 === 1 ? \"dayTooltip\" : \"weekTooltip\", weekEvent_r3.tempEvent || weekEvent_r3.event) : \"\")(\"tooltipPlacement\", tooltipPlacement_r4)(\"tooltipEvent\", weekEvent_r3.tempEvent || weekEvent_r3.event)(\"tooltipTemplate\", tooltipTemplate_r5)(\"tooltipAppendToBody\", tooltipAppendToBody_r6)(\"tooltipDelay\", tooltipDelay_r8);\n    i0.ɵɵattribute(\"aria-label\", i0.ɵɵpipeBind2(2, 17, i0.ɵɵpureFunction2(24, _c14, weekEvent_r3.tempEvent || weekEvent_r3.event, ctx_r9.locale), \"eventDescription\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"event\", weekEvent_r3.tempEvent || weekEvent_r3.event)(\"customTemplate\", ctx_r9.eventActionsTemplate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"event\", weekEvent_r3.tempEvent || weekEvent_r3.event)(\"customTemplate\", ctx_r9.eventTitleTemplate)(\"view\", daysInWeek_r9 === 1 ? \"day\" : \"week\");\n  }\n}\nfunction CalendarWeekViewEventComponent_ng_template_2_Template(rf, ctx) {}\nconst _c19 = (a0, a1, a2, a3, a4) => ({\n  segment: a0,\n  locale: a1,\n  segmentHeight: a2,\n  isTimeLabel: a3,\n  daysInWeek: a4\n});\nfunction CalendarWeekViewHourSegmentComponent_ng_template_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"calendarDate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const segment_r2 = ctx_r0.segment;\n    const locale_r3 = ctx_r0.locale;\n    const daysInWeek_r4 = ctx_r0.daysInWeek;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind3(2, 1, segment_r2.displayDate, daysInWeek_r4 === 1 ? \"dayViewHour\" : \"weekViewHour\", locale_r3), \" \");\n  }\n}\nfunction CalendarWeekViewHourSegmentComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵpipe(1, \"calendarA11y\");\n    i0.ɵɵtemplate(2, CalendarWeekViewHourSegmentComponent_ng_template_0_div_2_Template, 3, 5, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const segment_r2 = ctx.segment;\n    const segmentHeight_r5 = ctx.segmentHeight;\n    const isTimeLabel_r6 = ctx.isTimeLabel;\n    const daysInWeek_r4 = ctx.daysInWeek;\n    i0.ɵɵstyleProp(\"height\", segmentHeight_r5, \"px\");\n    i0.ɵɵclassProp(\"cal-hour-start\", segment_r2.isStart)(\"cal-after-hour-start\", !segment_r2.isStart);\n    i0.ɵɵproperty(\"ngClass\", segment_r2.cssClass);\n    i0.ɵɵattribute(\"aria-hidden\", i0.ɵɵpipeBind2(1, 9, i0.ɵɵpureFunction0(12, _c3), daysInWeek_r4 === 1 ? \"hideDayHourSegment\" : \"hideWeekHourSegment\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", isTimeLabel_r6);\n  }\n}\nfunction CalendarWeekViewHourSegmentComponent_ng_template_2_Template(rf, ctx) {}\nconst _c20 = (a0, a1, a2, a3, a4, a5, a6) => ({\n  columnDate: a0,\n  dayStartHour: a1,\n  dayStartMinute: a2,\n  dayEndHour: a3,\n  dayEndMinute: a4,\n  isVisible: a5,\n  topPx: a6\n});\nfunction CalendarWeekViewCurrentTimeMarkerComponent_ng_template_0_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 3);\n  }\n  if (rf & 2) {\n    const topPx_r1 = i0.ɵɵnextContext().topPx;\n    i0.ɵɵstyleProp(\"top\", topPx_r1, \"px\");\n  }\n}\nfunction CalendarWeekViewCurrentTimeMarkerComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CalendarWeekViewCurrentTimeMarkerComponent_ng_template_0_div_0_Template, 1, 2, \"div\", 2);\n  }\n  if (rf & 2) {\n    const isVisible_r2 = ctx.isVisible;\n    i0.ɵɵproperty(\"ngIf\", isVisible_r2);\n  }\n}\nfunction CalendarWeekViewCurrentTimeMarkerComponent_ng_template_2_Template(rf, ctx) {}\nconst _c21 = (a0, a1) => ({\n  left: a0,\n  right: a1\n});\nconst _c22 = (a0, a1) => ({\n  event: a0,\n  calendarId: a1\n});\nconst _c23 = a0 => ({\n  x: a0\n});\nconst _c24 = () => ({\n  left: true\n});\nconst _c25 = () => ({\n  right: true\n});\nconst _c26 = (a0, a1, a2, a3) => ({\n  left: a0,\n  right: a1,\n  top: a2,\n  bottom: a3\n});\nconst _c27 = () => ({\n  left: true,\n  top: true\n});\nconst _c28 = () => ({\n  right: true,\n  bottom: true\n});\nfunction CalendarWeekViewComponent_div_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction CalendarWeekViewComponent_div_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵlistener(\"drop\", function CalendarWeekViewComponent_div_2_div_5_Template_div_drop_0_listener($event) {\n      const day_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.eventDropped($event, day_r5.date, true));\n    })(\"dragEnter\", function CalendarWeekViewComponent_div_2_div_5_Template_div_dragEnter_0_listener() {\n      const day_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.dateDragEnter(day_r5.date));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CalendarWeekViewComponent_div_2_div_6_div_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 24);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"resizeEdges\", i0.ɵɵpureFunction0(1, _c24));\n  }\n}\nfunction CalendarWeekViewComponent_div_2_div_6_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"resizeEdges\", i0.ɵɵpureFunction0(1, _c25));\n  }\n}\nfunction CalendarWeekViewComponent_div_2_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20, 3);\n    i0.ɵɵlistener(\"resizeStart\", function CalendarWeekViewComponent_div_2_div_6_div_2_Template_div_resizeStart_0_listener($event) {\n      const allDayEvent_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      i0.ɵɵnextContext();\n      const eventRowContainer_r8 = i0.ɵɵreference(1);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.allDayEventResizeStarted(eventRowContainer_r8, allDayEvent_r7, $event));\n    })(\"resizing\", function CalendarWeekViewComponent_div_2_div_6_div_2_Template_div_resizing_0_listener($event) {\n      const allDayEvent_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.allDayEventResizing(allDayEvent_r7, $event, ctx_r2.dayColumnWidth));\n    })(\"resizeEnd\", function CalendarWeekViewComponent_div_2_div_6_div_2_Template_div_resizeEnd_0_listener() {\n      const allDayEvent_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.allDayEventResizeEnded(allDayEvent_r7));\n    })(\"dragStart\", function CalendarWeekViewComponent_div_2_div_6_div_2_Template_div_dragStart_0_listener() {\n      const allDayEvent_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const event_r9 = i0.ɵɵreference(1);\n      i0.ɵɵnextContext();\n      const eventRowContainer_r8 = i0.ɵɵreference(1);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.dragStarted(eventRowContainer_r8, event_r9, allDayEvent_r7, false));\n    })(\"dragging\", function CalendarWeekViewComponent_div_2_div_6_div_2_Template_div_dragging_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.allDayEventDragMove());\n    })(\"dragEnd\", function CalendarWeekViewComponent_div_2_div_6_div_2_Template_div_dragEnd_0_listener($event) {\n      const allDayEvent_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.dragEnded(allDayEvent_r7, $event, ctx_r2.dayColumnWidth));\n    });\n    i0.ɵɵtemplate(2, CalendarWeekViewComponent_div_2_div_6_div_2_div_2_Template, 1, 2, \"div\", 21);\n    i0.ɵɵelementStart(3, \"mwl-calendar-week-view-event\", 22);\n    i0.ɵɵlistener(\"eventClicked\", function CalendarWeekViewComponent_div_2_div_6_div_2_Template_mwl_calendar_week_view_event_eventClicked_3_listener($event) {\n      const allDayEvent_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.eventClicked.emit({\n        event: allDayEvent_r7.event,\n        sourceEvent: $event.sourceEvent\n      }));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CalendarWeekViewComponent_div_2_div_6_div_2_div_4_Template, 1, 2, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const allDayEvent_r7 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"width\", 100 / ctx_r2.days.length * allDayEvent_r7.span, \"%\")(\"margin-left\", ctx_r2.rtl ? null : 100 / ctx_r2.days.length * allDayEvent_r7.offset, \"%\")(\"margin-right\", ctx_r2.rtl ? 100 / ctx_r2.days.length * allDayEvent_r7.offset : null, \"%\");\n    i0.ɵɵclassProp(\"cal-draggable\", allDayEvent_r7.event.draggable && ctx_r2.allDayEventResizes.size === 0)(\"cal-starts-within-week\", !allDayEvent_r7.startsBeforeWeek)(\"cal-ends-within-week\", !allDayEvent_r7.endsAfterWeek);\n    i0.ɵɵproperty(\"ngClass\", allDayEvent_r7.event == null ? null : allDayEvent_r7.event.cssClass)(\"resizeCursors\", ctx_r2.resizeCursors)(\"resizeSnapGrid\", i0.ɵɵpureFunction2(33, _c21, ctx_r2.dayColumnWidth, ctx_r2.dayColumnWidth))(\"validateResize\", ctx_r2.validateResize)(\"dropData\", i0.ɵɵpureFunction2(36, _c22, allDayEvent_r7.event, ctx_r2.calendarId))(\"dragAxis\", i0.ɵɵpureFunction2(39, _c9, allDayEvent_r7.event.draggable && ctx_r2.allDayEventResizes.size === 0, !ctx_r2.snapDraggedEvents && allDayEvent_r7.event.draggable && ctx_r2.allDayEventResizes.size === 0))(\"dragSnapGrid\", ctx_r2.snapDraggedEvents ? i0.ɵɵpureFunction1(42, _c23, ctx_r2.dayColumnWidth) : i0.ɵɵpureFunction0(44, _c3))(\"validateDrag\", ctx_r2.validateDrag)(\"touchStartLongPress\", i0.ɵɵpureFunction0(45, _c10));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (allDayEvent_r7.event == null ? null : allDayEvent_r7.event.resizable == null ? null : allDayEvent_r7.event.resizable.beforeStart) && !allDayEvent_r7.startsBeforeWeek);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"locale\", ctx_r2.locale)(\"weekEvent\", allDayEvent_r7)(\"tooltipPlacement\", ctx_r2.tooltipPlacement)(\"tooltipTemplate\", ctx_r2.tooltipTemplate)(\"tooltipAppendToBody\", ctx_r2.tooltipAppendToBody)(\"tooltipDelay\", ctx_r2.tooltipDelay)(\"customTemplate\", ctx_r2.eventTemplate)(\"eventTitleTemplate\", ctx_r2.eventTitleTemplate)(\"eventActionsTemplate\", ctx_r2.eventActionsTemplate)(\"daysInWeek\", ctx_r2.daysInWeek);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (allDayEvent_r7.event == null ? null : allDayEvent_r7.event.resizable == null ? null : allDayEvent_r7.event.resizable.afterEnd) && !allDayEvent_r7.endsAfterWeek);\n  }\n}\nfunction CalendarWeekViewComponent_div_2_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18, 2);\n    i0.ɵɵtemplate(2, CalendarWeekViewComponent_div_2_div_6_div_2_Template, 5, 46, \"div\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const eventRow_r10 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", eventRow_r10.row)(\"ngForTrackBy\", ctx_r2.trackByWeekAllDayEvent);\n  }\n}\nfunction CalendarWeekViewComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12, 1);\n    i0.ɵɵlistener(\"dragEnter\", function CalendarWeekViewComponent_div_2_Template_div_dragEnter_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.dragEnter(\"allDay\"));\n    })(\"dragLeave\", function CalendarWeekViewComponent_div_2_Template_div_dragLeave_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.dragLeave(\"allDay\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 10)(3, \"div\", 13);\n    i0.ɵɵtemplate(4, CalendarWeekViewComponent_div_2_ng_container_4_Template, 1, 0, \"ng-container\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, CalendarWeekViewComponent_div_2_div_5_Template, 1, 0, \"div\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, CalendarWeekViewComponent_div_2_div_6_Template, 3, 2, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.allDayEventsLabelTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.days)(\"ngForTrackBy\", ctx_r2.trackByWeekDayHeaderDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.view.allDayEventRows)(\"ngForTrackBy\", ctx_r2.trackById);\n  }\n}\nfunction CalendarWeekViewComponent_div_4_div_1_mwl_calendar_week_view_hour_segment_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mwl-calendar-week-view-hour-segment\", 29);\n  }\n  if (rf & 2) {\n    const segment_r11 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"height\", ctx_r2.hourSegmentHeight, \"px\");\n    i0.ɵɵproperty(\"segment\", segment_r11)(\"segmentHeight\", ctx_r2.hourSegmentHeight)(\"locale\", ctx_r2.locale)(\"customTemplate\", ctx_r2.hourSegmentTemplate)(\"isTimeLabel\", true)(\"daysInWeek\", ctx_r2.daysInWeek);\n  }\n}\nfunction CalendarWeekViewComponent_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, CalendarWeekViewComponent_div_4_div_1_mwl_calendar_week_view_hour_segment_1_Template, 1, 8, \"mwl-calendar-week-view-hour-segment\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const hour_r12 = ctx.$implicit;\n    const odd_r13 = ctx.odd;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"cal-hour-odd\", odd_r13);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", hour_r12.segments)(\"ngForTrackBy\", ctx_r2.trackByHourSegment);\n  }\n}\nfunction CalendarWeekViewComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵtemplate(1, CalendarWeekViewComponent_div_4_div_1_Template, 2, 4, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.view.hourColumns[0].hours)(\"ngForTrackBy\", ctx_r2.trackByHour);\n  }\n}\nfunction CalendarWeekViewComponent_div_7_div_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 24);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"resizeEdges\", i0.ɵɵpureFunction0(1, _c27));\n  }\n}\nfunction CalendarWeekViewComponent_div_7_div_3_ng_template_3_Template(rf, ctx) {}\nfunction CalendarWeekViewComponent_div_7_div_3_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mwl-calendar-week-view-event\", 36);\n    i0.ɵɵlistener(\"eventClicked\", function CalendarWeekViewComponent_div_7_div_3_ng_template_4_Template_mwl_calendar_week_view_event_eventClicked_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const timeEvent_r15 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.eventClicked.emit({\n        event: timeEvent_r15.event,\n        sourceEvent: $event.sourceEvent\n      }));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const timeEvent_r15 = i0.ɵɵnextContext().$implicit;\n    const column_r19 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"locale\", ctx_r2.locale)(\"weekEvent\", timeEvent_r15)(\"tooltipPlacement\", ctx_r2.tooltipPlacement)(\"tooltipTemplate\", ctx_r2.tooltipTemplate)(\"tooltipAppendToBody\", ctx_r2.tooltipAppendToBody)(\"tooltipDisabled\", ctx_r2.dragActive || ctx_r2.timeEventResizes.size > 0)(\"tooltipDelay\", ctx_r2.tooltipDelay)(\"customTemplate\", ctx_r2.eventTemplate)(\"eventTitleTemplate\", ctx_r2.eventTitleTemplate)(\"eventActionsTemplate\", ctx_r2.eventActionsTemplate)(\"column\", column_r19)(\"daysInWeek\", ctx_r2.daysInWeek);\n  }\n}\nfunction CalendarWeekViewComponent_div_7_div_3_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"resizeEdges\", i0.ɵɵpureFunction0(1, _c28));\n  }\n}\nfunction CalendarWeekViewComponent_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34, 3);\n    i0.ɵɵlistener(\"resizeStart\", function CalendarWeekViewComponent_div_7_div_3_Template_div_resizeStart_0_listener($event) {\n      const timeEvent_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      const dayColumns_r16 = i0.ɵɵreference(6);\n      return i0.ɵɵresetView(ctx_r2.timeEventResizeStarted(dayColumns_r16, timeEvent_r15, $event));\n    })(\"resizing\", function CalendarWeekViewComponent_div_7_div_3_Template_div_resizing_0_listener($event) {\n      const timeEvent_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.timeEventResizing(timeEvent_r15, $event));\n    })(\"resizeEnd\", function CalendarWeekViewComponent_div_7_div_3_Template_div_resizeEnd_0_listener() {\n      const timeEvent_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.timeEventResizeEnded(timeEvent_r15));\n    })(\"dragStart\", function CalendarWeekViewComponent_div_7_div_3_Template_div_dragStart_0_listener() {\n      const timeEvent_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const event_r17 = i0.ɵɵreference(1);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      const dayColumns_r16 = i0.ɵɵreference(6);\n      return i0.ɵɵresetView(ctx_r2.dragStarted(dayColumns_r16, event_r17, timeEvent_r15, true));\n    })(\"dragging\", function CalendarWeekViewComponent_div_7_div_3_Template_div_dragging_0_listener($event) {\n      const timeEvent_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.dragMove(timeEvent_r15, $event));\n    })(\"dragEnd\", function CalendarWeekViewComponent_div_7_div_3_Template_div_dragEnd_0_listener($event) {\n      const timeEvent_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.dragEnded(timeEvent_r15, $event, ctx_r2.dayColumnWidth, true));\n    });\n    i0.ɵɵtemplate(2, CalendarWeekViewComponent_div_7_div_3_div_2_Template, 1, 2, \"div\", 21)(3, CalendarWeekViewComponent_div_7_div_3_ng_template_3_Template, 0, 0, \"ng-template\", 35)(4, CalendarWeekViewComponent_div_7_div_3_ng_template_4_Template, 1, 12, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor)(6, CalendarWeekViewComponent_div_7_div_3_div_6_Template, 1, 2, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const timeEvent_r15 = ctx.$implicit;\n    const weekEventTemplate_r20 = i0.ɵɵreference(5);\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"top\", timeEvent_r15.top, \"px\")(\"height\", timeEvent_r15.height, \"px\")(\"left\", timeEvent_r15.left, \"%\")(\"width\", timeEvent_r15.width, \"%\");\n    i0.ɵɵclassProp(\"cal-draggable\", timeEvent_r15.event.draggable && ctx_r2.timeEventResizes.size === 0)(\"cal-starts-within-day\", !timeEvent_r15.startsBeforeDay)(\"cal-ends-within-day\", !timeEvent_r15.endsAfterDay);\n    i0.ɵɵproperty(\"ngClass\", timeEvent_r15.event.cssClass)(\"hidden\", timeEvent_r15.height === 0 && timeEvent_r15.width === 0)(\"resizeCursors\", ctx_r2.resizeCursors)(\"resizeSnapGrid\", i0.ɵɵpureFunction4(30, _c26, ctx_r2.dayColumnWidth, ctx_r2.dayColumnWidth, ctx_r2.eventSnapSize || ctx_r2.hourSegmentHeight, ctx_r2.eventSnapSize || ctx_r2.hourSegmentHeight))(\"validateResize\", ctx_r2.validateResize)(\"allowNegativeResizes\", true)(\"dropData\", i0.ɵɵpureFunction2(35, _c22, timeEvent_r15.event, ctx_r2.calendarId))(\"dragAxis\", i0.ɵɵpureFunction2(38, _c9, timeEvent_r15.event.draggable && ctx_r2.timeEventResizes.size === 0, timeEvent_r15.event.draggable && ctx_r2.timeEventResizes.size === 0))(\"dragSnapGrid\", ctx_r2.snapDraggedEvents ? i0.ɵɵpureFunction2(41, _c9, ctx_r2.dayColumnWidth, ctx_r2.eventSnapSize || ctx_r2.hourSegmentHeight) : i0.ɵɵpureFunction0(44, _c3))(\"touchStartLongPress\", i0.ɵɵpureFunction0(45, _c10))(\"ghostDragEnabled\", !ctx_r2.snapDraggedEvents)(\"ghostElementTemplate\", weekEventTemplate_r20)(\"validateDrag\", ctx_r2.validateDrag);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (timeEvent_r15.event == null ? null : timeEvent_r15.event.resizable == null ? null : timeEvent_r15.event.resizable.beforeStart) && !timeEvent_r15.startsBeforeDay);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", weekEventTemplate_r20);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (timeEvent_r15.event == null ? null : timeEvent_r15.event.resizable == null ? null : timeEvent_r15.event.resizable.afterEnd) && !timeEvent_r15.endsAfterDay);\n  }\n}\nfunction CalendarWeekViewComponent_div_7_div_4_mwl_calendar_week_view_hour_segment_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mwl-calendar-week-view-hour-segment\", 38);\n    i0.ɵɵlistener(\"mwlClick\", function CalendarWeekViewComponent_div_7_div_4_mwl_calendar_week_view_hour_segment_1_Template_mwl_calendar_week_view_hour_segment_mwlClick_0_listener($event) {\n      const segment_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.hourSegmentClicked.emit({\n        date: segment_r22.date,\n        sourceEvent: $event\n      }));\n    })(\"drop\", function CalendarWeekViewComponent_div_7_div_4_mwl_calendar_week_view_hour_segment_1_Template_mwl_calendar_week_view_hour_segment_drop_0_listener($event) {\n      const segment_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.eventDropped($event, segment_r22.date, false));\n    })(\"dragEnter\", function CalendarWeekViewComponent_div_7_div_4_mwl_calendar_week_view_hour_segment_1_Template_mwl_calendar_week_view_hour_segment_dragEnter_0_listener() {\n      const segment_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.dateDragEnter(segment_r22.date));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const segment_r22 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"height\", ctx_r2.hourSegmentHeight, \"px\");\n    i0.ɵɵproperty(\"segment\", segment_r22)(\"segmentHeight\", ctx_r2.hourSegmentHeight)(\"locale\", ctx_r2.locale)(\"customTemplate\", ctx_r2.hourSegmentTemplate)(\"daysInWeek\", ctx_r2.daysInWeek)(\"clickListenerDisabled\", ctx_r2.hourSegmentClicked.observers.length === 0)(\"dragOverClass\", !ctx_r2.dragActive || !ctx_r2.snapDraggedEvents ? \"cal-drag-over\" : null)(\"isTimeLabel\", ctx_r2.daysInWeek === 1);\n  }\n}\nfunction CalendarWeekViewComponent_div_7_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, CalendarWeekViewComponent_div_7_div_4_mwl_calendar_week_view_hour_segment_1_Template, 1, 10, \"mwl-calendar-week-view-hour-segment\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const hour_r23 = ctx.$implicit;\n    const odd_r24 = ctx.odd;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"cal-hour-odd\", odd_r24);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", hour_r23.segments)(\"ngForTrackBy\", ctx_r2.trackByHourSegment);\n  }\n}\nfunction CalendarWeekViewComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelement(1, \"mwl-calendar-week-view-current-time-marker\", 31);\n    i0.ɵɵelementStart(2, \"div\", 32);\n    i0.ɵɵtemplate(3, CalendarWeekViewComponent_div_7_div_3_Template, 7, 46, \"div\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CalendarWeekViewComponent_div_7_div_4_Template, 2, 4, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const column_r19 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"columnDate\", column_r19.date)(\"dayStartHour\", ctx_r2.dayStartHour)(\"dayStartMinute\", ctx_r2.dayStartMinute)(\"dayEndHour\", ctx_r2.dayEndHour)(\"dayEndMinute\", ctx_r2.dayEndMinute)(\"hourSegments\", ctx_r2.hourSegments)(\"hourDuration\", ctx_r2.hourDuration)(\"hourSegmentHeight\", ctx_r2.hourSegmentHeight)(\"customTemplate\", ctx_r2.currentTimeMarkerTemplate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", column_r19.events)(\"ngForTrackBy\", ctx_r2.trackByWeekTimeEvent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", column_r19.hours)(\"ngForTrackBy\", ctx_r2.trackByHour);\n  }\n}\nexport { DAYS_OF_WEEK } from 'calendar-utils';\nimport * as i2 from 'angular-draggable-droppable';\nimport { DragAndDropModule } from 'angular-draggable-droppable';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i4 from 'angular-resizable-element';\nimport { ResizableModule } from 'angular-resizable-element';\nclass ClickDirective {\n  constructor(renderer, elm, document) {\n    this.renderer = renderer;\n    this.elm = elm;\n    this.document = document;\n    this.clickListenerDisabled = false;\n    this.click = new EventEmitter(); // eslint-disable-line\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    if (!this.clickListenerDisabled) {\n      this.listen().pipe(takeUntil(this.destroy$)).subscribe(event => {\n        event.stopPropagation();\n        this.click.emit(event);\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n  }\n  listen() {\n    return new Observable(observer => {\n      return this.renderer.listen(this.elm.nativeElement, 'click', event => {\n        observer.next(event);\n      });\n    });\n  }\n}\nClickDirective.ɵfac = function ClickDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ClickDirective)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DOCUMENT));\n};\nClickDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: ClickDirective,\n  selectors: [[\"\", \"mwlClick\", \"\"]],\n  inputs: {\n    clickListenerDisabled: \"clickListenerDisabled\"\n  },\n  outputs: {\n    click: \"mwlClick\"\n  },\n  standalone: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ClickDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mwlClick]'\n    }]\n  }], function () {\n    return [{\n      type: i0.Renderer2\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, {\n    clickListenerDisabled: [{\n      type: Input\n    }],\n    click: [{\n      type: Output,\n      args: ['mwlClick']\n    }]\n  });\n})();\nclass KeydownEnterDirective {\n  constructor(host, ngZone, renderer) {\n    this.host = host;\n    this.ngZone = ngZone;\n    this.renderer = renderer;\n    this.keydown = new EventEmitter(); // eslint-disable-line\n    this.keydownListener = null;\n  }\n  ngOnInit() {\n    this.ngZone.runOutsideAngular(() => {\n      this.keydownListener = this.renderer.listen(this.host.nativeElement, 'keydown', event => {\n        if (event.keyCode === 13 || event.which === 13 || event.key === 'Enter') {\n          event.preventDefault();\n          event.stopPropagation();\n          this.ngZone.run(() => {\n            this.keydown.emit(event);\n          });\n        }\n      });\n    });\n  }\n  ngOnDestroy() {\n    if (this.keydownListener !== null) {\n      this.keydownListener();\n      this.keydownListener = null;\n    }\n  }\n}\nKeydownEnterDirective.ɵfac = function KeydownEnterDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || KeydownEnterDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.Renderer2));\n};\nKeydownEnterDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: KeydownEnterDirective,\n  selectors: [[\"\", \"mwlKeydownEnter\", \"\"]],\n  outputs: {\n    keydown: \"mwlKeydownEnter\"\n  },\n  standalone: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeydownEnterDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mwlKeydownEnter]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.Renderer2\n    }];\n  }, {\n    keydown: [{\n      type: Output,\n      args: ['mwlKeydownEnter']\n    }]\n  });\n})();\n\n/**\n * This class is responsible for adding accessibility to the calendar.\n * You may override any of its methods via angulars DI to suit your requirements.\n * For example:\n *\n * ```typescript\n * import { A11yParams, CalendarA11y } from 'angular-calendar';\n * import { formatDate, I18nPluralPipe } from '@angular/common';\n * import { Injectable } from '@angular/core';\n *\n * // adding your own a11y params\n * export interface CustomA11yParams extends A11yParams {\n *   isDrSuess?: boolean;\n * }\n *\n * @Injectable()\n * export class CustomCalendarA11y extends CalendarA11y {\n *   constructor(protected i18nPlural: I18nPluralPipe) {\n *     super(i18nPlural);\n *   }\n *\n *   // overriding a function\n *   public openDayEventsLandmark({ date, locale, isDrSuess }: CustomA11yParams): string {\n *     if (isDrSuess) {\n *       return `\n *         ${formatDate(date, 'EEEE MMMM d', locale)}\n *          Today you are you! That is truer than true! There is no one alive\n *          who is you-er than you!\n *       `;\n *     }\n *   }\n * }\n *\n * // in your component that uses the calendar\n * providers: [{\n *  provide: CalendarA11y,\n *  useClass: CustomCalendarA11y\n * }]\n * ```\n */\nclass CalendarA11y {\n  constructor(i18nPlural) {\n    this.i18nPlural = i18nPlural;\n  }\n  /**\n   * Aria label for the badges/date of a cell\n   * @example: `Saturday October 19 1 event click to expand`\n   */\n  monthCell({\n    day,\n    locale\n  }) {\n    if (day.badgeTotal > 0) {\n      return `\n        ${formatDate(day.date, 'EEEE MMMM d', locale)},\n        ${this.i18nPlural.transform(day.badgeTotal, {\n        '=0': 'No events',\n        '=1': 'One event',\n        other: '# events'\n      })},\n         click to expand\n      `;\n    } else {\n      return `${formatDate(day.date, 'EEEE MMMM d', locale)}`;\n    }\n  }\n  /**\n   * Aria label for the open day events start landmark\n   * @example: `Saturday October 19 expanded view`\n   */\n  openDayEventsLandmark({\n    date,\n    locale\n  }) {\n    return `\n      Beginning of expanded view for ${formatDate(date, 'EEEE MMMM dd', locale)}\n    `;\n  }\n  /**\n   * Aria label for alert that a day in the month view was expanded\n   * @example: `Saturday October 19 expanded`\n   */\n  openDayEventsAlert({\n    date,\n    locale\n  }) {\n    return `${formatDate(date, 'EEEE MMMM dd', locale)} expanded`;\n  }\n  /**\n   * Descriptive aria label for an event\n   * @example: `Saturday October 19th, Scott's Pizza Party, from 11:00am to 5:00pm`\n   */\n  eventDescription({\n    event,\n    locale\n  }) {\n    if (event.allDay === true) {\n      return this.allDayEventDescription({\n        event,\n        locale\n      });\n    }\n    const aria = `\n      ${formatDate(event.start, 'EEEE MMMM dd', locale)},\n      ${event.title}, from ${formatDate(event.start, 'hh:mm a', locale)}\n    `;\n    if (event.end) {\n      return aria + ` to ${formatDate(event.end, 'hh:mm a', locale)}`;\n    }\n    return aria;\n  }\n  /**\n   * Descriptive aria label for an all day event\n   * @example:\n   * `Scott's Party, event spans multiple days: start time October 19 5:00pm, no stop time`\n   */\n  allDayEventDescription({\n    event,\n    locale\n  }) {\n    const aria = `\n      ${event.title}, event spans multiple days:\n      start time ${formatDate(event.start, 'MMMM dd hh:mm a', locale)}\n    `;\n    if (event.end) {\n      return aria + `, stop time ${formatDate(event.end, 'MMMM d hh:mm a', locale)}`;\n    }\n    return aria + `, no stop time`;\n  }\n  /**\n   * Aria label for the calendar event actions icons\n   * @returns 'Edit' for fa-pencil icons, and 'Delete' for fa-times icons\n   */\n  actionButtonLabel({\n    action\n  }) {\n    return action.a11yLabel;\n  }\n  /**\n   * @returns {number} Tab index to be given to month cells\n   */\n  monthCellTabIndex() {\n    return 0;\n  }\n  /**\n   * @returns true if the events inside the month cell should be aria-hidden\n   */\n  hideMonthCellEvents() {\n    return true;\n  }\n  /**\n   * @returns true if event titles should be aria-hidden (global)\n   */\n  hideEventTitle() {\n    return true;\n  }\n  /**\n   * @returns true if hour segments in the week view should be aria-hidden\n   */\n  hideWeekHourSegment() {\n    return true;\n  }\n  /**\n   * @returns true if hour segments in the day view should be aria-hidden\n   */\n  hideDayHourSegment() {\n    return true;\n  }\n}\nCalendarA11y.ɵfac = function CalendarA11y_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarA11y)(i0.ɵɵinject(i1.I18nPluralPipe));\n};\nCalendarA11y.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: CalendarA11y,\n  factory: CalendarA11y.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarA11y, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i1.I18nPluralPipe\n    }];\n  }, null);\n})();\n\n/**\n * This pipe is primarily for rendering aria labels. Example usage:\n * ```typescript\n * // where `myEvent` is a `CalendarEvent` and myLocale is a locale identifier\n * {{ { event: myEvent, locale: myLocale } | calendarA11y: 'eventDescription' }}\n * ```\n */\nclass CalendarA11yPipe {\n  constructor(calendarA11y, locale) {\n    this.calendarA11y = calendarA11y;\n    this.locale = locale;\n  }\n  transform(a11yParams, method) {\n    a11yParams.locale = a11yParams.locale || this.locale;\n    if (typeof this.calendarA11y[method] === 'undefined') {\n      const allowedMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(CalendarA11y.prototype)).filter(iMethod => iMethod !== 'constructor');\n      throw new Error(`${method} is not a valid a11y method. Can only be one of ${allowedMethods.join(', ')}`);\n    }\n    return this.calendarA11y[method](a11yParams);\n  }\n}\nCalendarA11yPipe.ɵfac = function CalendarA11yPipe_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarA11yPipe)(i0.ɵɵdirectiveInject(CalendarA11y, 16), i0.ɵɵdirectiveInject(LOCALE_ID, 16));\n};\nCalendarA11yPipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n  name: \"calendarA11y\",\n  type: CalendarA11yPipe,\n  pure: true,\n  standalone: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarA11yPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'calendarA11y'\n    }]\n  }], function () {\n    return [{\n      type: CalendarA11y\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [LOCALE_ID]\n      }]\n    }];\n  }, null);\n})();\nclass CalendarEventActionsComponent {\n  constructor() {\n    this.trackByActionId = (index, action) => action.id ? action.id : action;\n  }\n}\nCalendarEventActionsComponent.ɵfac = function CalendarEventActionsComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarEventActionsComponent)();\n};\nCalendarEventActionsComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: CalendarEventActionsComponent,\n  selectors: [[\"mwl-calendar-event-actions\"]],\n  inputs: {\n    event: \"event\",\n    customTemplate: \"customTemplate\"\n  },\n  standalone: false,\n  decls: 3,\n  vars: 5,\n  consts: [[\"defaultTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"cal-event-actions\", 4, \"ngIf\"], [1, \"cal-event-actions\"], [\"class\", \"cal-event-action\", \"href\", \"javascript:;\", \"tabindex\", \"0\", \"role\", \"button\", 3, \"ngClass\", \"innerHtml\", \"mwlClick\", \"mwlKeydownEnter\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"href\", \"javascript:;\", \"tabindex\", \"0\", \"role\", \"button\", 1, \"cal-event-action\", 3, \"mwlClick\", \"mwlKeydownEnter\", \"ngClass\", \"innerHtml\"]],\n  template: function CalendarEventActionsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, CalendarEventActionsComponent_ng_template_0_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, CalendarEventActionsComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n    }\n    if (rf & 2) {\n      const defaultTemplate_r6 = i0.ɵɵreference(1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.customTemplate || defaultTemplate_r6)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c0, ctx.event, ctx.trackByActionId));\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, ClickDirective, KeydownEnterDirective, CalendarA11yPipe],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarEventActionsComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mwl-calendar-event-actions',\n      template: `\n    <ng-template\n      #defaultTemplate\n      let-event=\"event\"\n      let-trackByActionId=\"trackByActionId\"\n    >\n      <span *ngIf=\"event.actions\" class=\"cal-event-actions\">\n        <a\n          class=\"cal-event-action\"\n          href=\"javascript:;\"\n          *ngFor=\"let action of event.actions; trackBy: trackByActionId\"\n          (mwlClick)=\"action.onClick({ event: event, sourceEvent: $event })\"\n          (mwlKeydownEnter)=\"\n            action.onClick({ event: event, sourceEvent: $event })\n          \"\n          [ngClass]=\"action.cssClass\"\n          [innerHtml]=\"action.label\"\n          tabindex=\"0\"\n          role=\"button\"\n          [attr.aria-label]=\"\n            { action: action } | calendarA11y : 'actionButtonLabel'\n          \"\n        >\n        </a>\n      </span>\n    </ng-template>\n    <ng-template\n      [ngTemplateOutlet]=\"customTemplate || defaultTemplate\"\n      [ngTemplateOutletContext]=\"{\n        event: event,\n        trackByActionId: trackByActionId\n      }\"\n    >\n    </ng-template>\n  `\n    }]\n  }], null, {\n    event: [{\n      type: Input\n    }],\n    customTemplate: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * This class is responsible for displaying all event titles within the calendar. You may override any of its methods via angulars DI to suit your requirements. For example:\n *\n * ```typescript\n * import { Injectable } from '@angular/core';\n * import { CalendarEventTitleFormatter, CalendarEvent } from 'angular-calendar';\n *\n * @Injectable()\n * class CustomEventTitleFormatter extends CalendarEventTitleFormatter {\n *\n *   month(event: CalendarEvent): string {\n *     return `Custom prefix: ${event.title}`;\n *   }\n *\n * }\n *\n * // in your component\n * providers: [{\n *  provide: CalendarEventTitleFormatter,\n *  useClass: CustomEventTitleFormatter\n * }]\n * ```\n */\nclass CalendarEventTitleFormatter {\n  /**\n   * The month view event title.\n   */\n  month(event, title) {\n    return event.title;\n  }\n  /**\n   * The month view event tooltip. Return a falsey value from this to disable the tooltip.\n   */\n  monthTooltip(event, title) {\n    return event.title;\n  }\n  /**\n   * The week view event title.\n   */\n  week(event, title) {\n    return event.title;\n  }\n  /**\n   * The week view event tooltip. Return a falsey value from this to disable the tooltip.\n   */\n  weekTooltip(event, title) {\n    return event.title;\n  }\n  /**\n   * The day view event title.\n   */\n  day(event, title) {\n    return event.title;\n  }\n  /**\n   * The day view event tooltip. Return a falsey value from this to disable the tooltip.\n   */\n  dayTooltip(event, title) {\n    return event.title;\n  }\n}\nclass CalendarEventTitlePipe {\n  constructor(calendarEventTitle) {\n    this.calendarEventTitle = calendarEventTitle;\n  }\n  transform(title, titleType, event) {\n    return this.calendarEventTitle[titleType](event, title);\n  }\n}\nCalendarEventTitlePipe.ɵfac = function CalendarEventTitlePipe_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarEventTitlePipe)(i0.ɵɵdirectiveInject(CalendarEventTitleFormatter, 16));\n};\nCalendarEventTitlePipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n  name: \"calendarEventTitle\",\n  type: CalendarEventTitlePipe,\n  pure: true,\n  standalone: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarEventTitlePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'calendarEventTitle'\n    }]\n  }], function () {\n    return [{\n      type: CalendarEventTitleFormatter\n    }];\n  }, null);\n})();\nclass CalendarEventTitleComponent {}\nCalendarEventTitleComponent.ɵfac = function CalendarEventTitleComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarEventTitleComponent)();\n};\nCalendarEventTitleComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: CalendarEventTitleComponent,\n  selectors: [[\"mwl-calendar-event-title\"]],\n  inputs: {\n    event: \"event\",\n    customTemplate: \"customTemplate\",\n    view: \"view\"\n  },\n  standalone: false,\n  decls: 3,\n  vars: 5,\n  consts: [[\"defaultTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"cal-event-title\", 3, \"innerHTML\"]],\n  template: function CalendarEventTitleComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, CalendarEventTitleComponent_ng_template_0_Template, 3, 10, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, CalendarEventTitleComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n    }\n    if (rf & 2) {\n      const defaultTemplate_r3 = i0.ɵɵreference(1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.customTemplate || defaultTemplate_r3)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c2, ctx.event, ctx.view));\n    }\n  },\n  dependencies: [i1.NgTemplateOutlet, CalendarEventTitlePipe, CalendarA11yPipe],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarEventTitleComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mwl-calendar-event-title',\n      template: `\n    <ng-template #defaultTemplate let-event=\"event\" let-view=\"view\">\n      <span\n        class=\"cal-event-title\"\n        [innerHTML]=\"event.title | calendarEventTitle : view : event\"\n        [attr.aria-hidden]=\"{} | calendarA11y : 'hideEventTitle'\"\n      >\n      </span>\n    </ng-template>\n    <ng-template\n      [ngTemplateOutlet]=\"customTemplate || defaultTemplate\"\n      [ngTemplateOutletContext]=\"{\n        event: event,\n        view: view\n      }\"\n    >\n    </ng-template>\n  `\n    }]\n  }], null, {\n    event: [{\n      type: Input\n    }],\n    customTemplate: [{\n      type: Input\n    }],\n    view: [{\n      type: Input\n    }]\n  });\n})();\nclass CalendarTooltipWindowComponent {}\nCalendarTooltipWindowComponent.ɵfac = function CalendarTooltipWindowComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarTooltipWindowComponent)();\n};\nCalendarTooltipWindowComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: CalendarTooltipWindowComponent,\n  selectors: [[\"mwl-calendar-tooltip-window\"]],\n  inputs: {\n    contents: \"contents\",\n    placement: \"placement\",\n    event: \"event\",\n    customTemplate: \"customTemplate\"\n  },\n  standalone: false,\n  decls: 3,\n  vars: 6,\n  consts: [[\"defaultTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"cal-tooltip\", 3, \"ngClass\"], [1, \"cal-tooltip-arrow\"], [1, \"cal-tooltip-inner\", 3, \"innerHtml\"]],\n  template: function CalendarTooltipWindowComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, CalendarTooltipWindowComponent_ng_template_0_Template, 3, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, CalendarTooltipWindowComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n    }\n    if (rf & 2) {\n      const defaultTemplate_r3 = i0.ɵɵreference(1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.customTemplate || defaultTemplate_r3)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction3(2, _c4, ctx.contents, ctx.placement, ctx.event));\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgTemplateOutlet],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarTooltipWindowComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mwl-calendar-tooltip-window',\n      template: `\n    <ng-template\n      #defaultTemplate\n      let-contents=\"contents\"\n      let-placement=\"placement\"\n      let-event=\"event\"\n    >\n      <div class=\"cal-tooltip\" [ngClass]=\"'cal-tooltip-' + placement\">\n        <div class=\"cal-tooltip-arrow\"></div>\n        <div class=\"cal-tooltip-inner\" [innerHtml]=\"contents\"></div>\n      </div>\n    </ng-template>\n    <ng-template\n      [ngTemplateOutlet]=\"customTemplate || defaultTemplate\"\n      [ngTemplateOutletContext]=\"{\n        contents: contents,\n        placement: placement,\n        event: event\n      }\"\n    >\n    </ng-template>\n  `\n    }]\n  }], null, {\n    contents: [{\n      type: Input\n    }],\n    placement: [{\n      type: Input\n    }],\n    event: [{\n      type: Input\n    }],\n    customTemplate: [{\n      type: Input\n    }]\n  });\n})();\nclass CalendarTooltipDirective {\n  constructor(elementRef, injector, renderer, componentFactoryResolver, viewContainerRef, document // eslint-disable-line\n  ) {\n    this.elementRef = elementRef;\n    this.injector = injector;\n    this.renderer = renderer;\n    this.viewContainerRef = viewContainerRef;\n    this.document = document;\n    this.placement = 'auto'; // eslint-disable-line  @angular-eslint/no-input-rename\n    this.delay = null; // eslint-disable-line  @angular-eslint/no-input-rename\n    this.cancelTooltipDelay$ = new Subject();\n    this.tooltipFactory = componentFactoryResolver.resolveComponentFactory(CalendarTooltipWindowComponent);\n  }\n  ngOnChanges(changes) {\n    if (this.tooltipRef && (changes.contents || changes.customTemplate || changes.event)) {\n      this.tooltipRef.instance.contents = this.contents;\n      this.tooltipRef.instance.customTemplate = this.customTemplate;\n      this.tooltipRef.instance.event = this.event;\n      this.tooltipRef.changeDetectorRef.markForCheck();\n      if (!this.contents) {\n        this.hide();\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.hide();\n  }\n  onMouseOver() {\n    const delay$ = this.delay === null ? of('now') : timer(this.delay);\n    delay$.pipe(takeUntil(this.cancelTooltipDelay$)).subscribe(() => {\n      this.show();\n    });\n  }\n  onMouseOut() {\n    this.hide();\n  }\n  show() {\n    if (!this.tooltipRef && this.contents) {\n      this.tooltipRef = this.viewContainerRef.createComponent(this.tooltipFactory, 0, this.injector, []);\n      this.tooltipRef.instance.contents = this.contents;\n      this.tooltipRef.instance.customTemplate = this.customTemplate;\n      this.tooltipRef.instance.event = this.event;\n      if (this.appendToBody) {\n        this.document.body.appendChild(this.tooltipRef.location.nativeElement);\n      }\n      requestAnimationFrame(() => {\n        this.positionTooltip();\n      });\n    }\n  }\n  hide() {\n    if (this.tooltipRef) {\n      this.viewContainerRef.remove(this.viewContainerRef.indexOf(this.tooltipRef.hostView));\n      this.tooltipRef = null;\n    }\n    this.cancelTooltipDelay$.next();\n  }\n  positionTooltip(previousPositions = []) {\n    if (this.tooltipRef) {\n      this.tooltipRef.changeDetectorRef.detectChanges();\n      this.tooltipRef.instance.placement = positionElements(this.elementRef.nativeElement, this.tooltipRef.location.nativeElement.children[0], this.placement, this.appendToBody);\n      // keep re-positioning the tooltip until the arrow position doesn't make a difference\n      if (previousPositions.indexOf(this.tooltipRef.instance.placement) === -1) {\n        this.positionTooltip([...previousPositions, this.tooltipRef.instance.placement]);\n      }\n    }\n  }\n}\nCalendarTooltipDirective.ɵfac = function CalendarTooltipDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarTooltipDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(DOCUMENT));\n};\nCalendarTooltipDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CalendarTooltipDirective,\n  selectors: [[\"\", \"mwlCalendarTooltip\", \"\"]],\n  hostBindings: function CalendarTooltipDirective_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"mouseenter\", function CalendarTooltipDirective_mouseenter_HostBindingHandler() {\n        return ctx.onMouseOver();\n      })(\"mouseleave\", function CalendarTooltipDirective_mouseleave_HostBindingHandler() {\n        return ctx.onMouseOut();\n      });\n    }\n  },\n  inputs: {\n    contents: [0, \"mwlCalendarTooltip\", \"contents\"],\n    placement: [0, \"tooltipPlacement\", \"placement\"],\n    customTemplate: [0, \"tooltipTemplate\", \"customTemplate\"],\n    event: [0, \"tooltipEvent\", \"event\"],\n    appendToBody: [0, \"tooltipAppendToBody\", \"appendToBody\"],\n    delay: [0, \"tooltipDelay\", \"delay\"]\n  },\n  standalone: false,\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarTooltipDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mwlCalendarTooltip]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Injector\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ComponentFactoryResolver\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, {\n    contents: [{\n      type: Input,\n      args: ['mwlCalendarTooltip']\n    }],\n    placement: [{\n      type: Input,\n      args: ['tooltipPlacement']\n    }],\n    customTemplate: [{\n      type: Input,\n      args: ['tooltipTemplate']\n    }],\n    event: [{\n      type: Input,\n      args: ['tooltipEvent']\n    }],\n    appendToBody: [{\n      type: Input,\n      args: ['tooltipAppendToBody']\n    }],\n    delay: [{\n      type: Input,\n      args: ['tooltipDelay']\n    }],\n    onMouseOver: [{\n      type: HostListener,\n      args: ['mouseenter']\n    }],\n    onMouseOut: [{\n      type: HostListener,\n      args: ['mouseleave']\n    }]\n  });\n})();\nvar CalendarView;\n(function (CalendarView) {\n  CalendarView[\"Month\"] = \"month\";\n  CalendarView[\"Week\"] = \"week\";\n  CalendarView[\"Day\"] = \"day\";\n})(CalendarView || (CalendarView = {}));\nconst validateEvents = events => {\n  const warn = (...args) => console.warn('angular-calendar', ...args);\n  return validateEvents$1(events, warn);\n};\nfunction isInsideLeftAndRight(outer, inner) {\n  return Math.floor(outer.left) <= Math.ceil(inner.left) && Math.floor(inner.left) <= Math.ceil(outer.right) && Math.floor(outer.left) <= Math.ceil(inner.right) && Math.floor(inner.right) <= Math.ceil(outer.right);\n}\nfunction isInsideTopAndBottom(outer, inner) {\n  return Math.floor(outer.top) <= Math.ceil(inner.top) && Math.floor(inner.top) <= Math.ceil(outer.bottom) && Math.floor(outer.top) <= Math.ceil(inner.bottom) && Math.floor(inner.bottom) <= Math.ceil(outer.bottom);\n}\nfunction isInside(outer, inner) {\n  return isInsideLeftAndRight(outer, inner) && isInsideTopAndBottom(outer, inner);\n}\nfunction roundToNearest(amount, precision) {\n  return Math.round(amount / precision) * precision;\n}\nconst trackByEventId = (index, event) => event.id ? event.id : event;\nconst trackByWeekDayHeaderDate = (index, day) => day.date.toISOString();\nconst trackByHourSegment = (index, segment) => segment.date.toISOString();\nconst trackByHour = (index, hour) => hour.segments[0].date.toISOString();\nconst trackByWeekAllDayEvent = (index, weekEvent) => weekEvent.event.id ? weekEvent.event.id : weekEvent.event;\nconst trackByWeekTimeEvent = (index, weekEvent) => weekEvent.event.id ? weekEvent.event.id : weekEvent.event;\nconst MINUTES_IN_HOUR = 60;\nfunction getPixelAmountInMinutes(hourSegments, hourSegmentHeight, hourDuration) {\n  return (hourDuration || MINUTES_IN_HOUR) / (hourSegments * hourSegmentHeight);\n}\nfunction getMinutesMoved(movedY, hourSegments, hourSegmentHeight, eventSnapSize, hourDuration) {\n  const draggedInPixelsSnapSize = roundToNearest(movedY, eventSnapSize || hourSegmentHeight);\n  const pixelAmountInMinutes = getPixelAmountInMinutes(hourSegments, hourSegmentHeight, hourDuration);\n  return draggedInPixelsSnapSize * pixelAmountInMinutes;\n}\nfunction getDefaultEventEnd(dateAdapter, event, minimumMinutes) {\n  if (event.end) {\n    return event.end;\n  } else {\n    return dateAdapter.addMinutes(event.start, minimumMinutes);\n  }\n}\nfunction addDaysWithExclusions(dateAdapter, date, days, excluded) {\n  let daysCounter = 0;\n  let daysToAdd = 0;\n  const changeDays = days < 0 ? dateAdapter.subDays : dateAdapter.addDays;\n  let result = date;\n  while (daysToAdd <= Math.abs(days)) {\n    result = changeDays(date, daysCounter);\n    const day = dateAdapter.getDay(result);\n    if (excluded.indexOf(day) === -1) {\n      daysToAdd++;\n    }\n    daysCounter++;\n  }\n  return result;\n}\nfunction isDraggedWithinPeriod(newStart, newEnd, period) {\n  const end = newEnd || newStart;\n  return period.start <= newStart && newStart <= period.end || period.start <= end && end <= period.end;\n}\nfunction shouldFireDroppedEvent(dropEvent, date, allDay, calendarId) {\n  return dropEvent.dropData && dropEvent.dropData.event && (dropEvent.dropData.calendarId !== calendarId || dropEvent.dropData.event.allDay && !allDay || !dropEvent.dropData.event.allDay && allDay);\n}\nfunction getWeekViewPeriod(dateAdapter, viewDate, weekStartsOn, excluded = [], daysInWeek) {\n  let viewStart = daysInWeek ? dateAdapter.startOfDay(viewDate) : dateAdapter.startOfWeek(viewDate, {\n    weekStartsOn\n  });\n  const endOfWeek = dateAdapter.endOfWeek(viewDate, {\n    weekStartsOn\n  });\n  while (excluded.indexOf(dateAdapter.getDay(viewStart)) > -1 && viewStart < endOfWeek) {\n    viewStart = dateAdapter.addDays(viewStart, 1);\n  }\n  if (daysInWeek) {\n    const viewEnd = dateAdapter.endOfDay(addDaysWithExclusions(dateAdapter, viewStart, daysInWeek - 1, excluded));\n    return {\n      viewStart,\n      viewEnd\n    };\n  } else {\n    let viewEnd = endOfWeek;\n    while (excluded.indexOf(dateAdapter.getDay(viewEnd)) > -1 && viewEnd > viewStart) {\n      viewEnd = dateAdapter.subDays(viewEnd, 1);\n    }\n    return {\n      viewStart,\n      viewEnd\n    };\n  }\n}\nfunction isWithinThreshold({\n  x,\n  y\n}) {\n  const DRAG_THRESHOLD = 1;\n  return Math.abs(x) > DRAG_THRESHOLD || Math.abs(y) > DRAG_THRESHOLD;\n}\nclass DateAdapter {}\n\n/**\n * Change the view date to the previous view. For example:\n *\n * ```typescript\n * <button\n *  mwlCalendarPreviousView\n *  [(viewDate)]=\"viewDate\"\n *  [view]=\"view\">\n *  Previous\n * </button>\n * ```\n */\nclass CalendarPreviousViewDirective {\n  constructor(dateAdapter) {\n    this.dateAdapter = dateAdapter;\n    /**\n     * Days to skip when going back by 1 day\n     */\n    this.excludeDays = [];\n    /**\n     * Called when the view date is changed\n     */\n    this.viewDateChange = new EventEmitter();\n  }\n  /**\n   * @hidden\n   */\n  onClick() {\n    const subFn = {\n      day: this.dateAdapter.subDays,\n      week: this.dateAdapter.subWeeks,\n      month: this.dateAdapter.subMonths\n    }[this.view];\n    if (this.view === CalendarView.Day) {\n      this.viewDateChange.emit(addDaysWithExclusions(this.dateAdapter, this.viewDate, -1, this.excludeDays));\n    } else if (this.view === CalendarView.Week && this.daysInWeek) {\n      this.viewDateChange.emit(addDaysWithExclusions(this.dateAdapter, this.viewDate, -this.daysInWeek, this.excludeDays));\n    } else {\n      this.viewDateChange.emit(subFn(this.viewDate, 1));\n    }\n  }\n}\nCalendarPreviousViewDirective.ɵfac = function CalendarPreviousViewDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarPreviousViewDirective)(i0.ɵɵdirectiveInject(DateAdapter));\n};\nCalendarPreviousViewDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CalendarPreviousViewDirective,\n  selectors: [[\"\", \"mwlCalendarPreviousView\", \"\"]],\n  hostBindings: function CalendarPreviousViewDirective_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function CalendarPreviousViewDirective_click_HostBindingHandler() {\n        return ctx.onClick();\n      });\n    }\n  },\n  inputs: {\n    view: \"view\",\n    viewDate: \"viewDate\",\n    excludeDays: \"excludeDays\",\n    daysInWeek: \"daysInWeek\"\n  },\n  outputs: {\n    viewDateChange: \"viewDateChange\"\n  },\n  standalone: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarPreviousViewDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mwlCalendarPreviousView]'\n    }]\n  }], function () {\n    return [{\n      type: DateAdapter\n    }];\n  }, {\n    view: [{\n      type: Input\n    }],\n    viewDate: [{\n      type: Input\n    }],\n    excludeDays: [{\n      type: Input\n    }],\n    daysInWeek: [{\n      type: Input\n    }],\n    viewDateChange: [{\n      type: Output\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click']\n    }]\n  });\n})();\n\n/**\n * Change the view date to the next view. For example:\n *\n * ```typescript\n * <button\n *  mwlCalendarNextView\n *  [(viewDate)]=\"viewDate\"\n *  [view]=\"view\">\n *  Next\n * </button>\n * ```\n */\nclass CalendarNextViewDirective {\n  constructor(dateAdapter) {\n    this.dateAdapter = dateAdapter;\n    /**\n     * Days to skip when going forward by 1 day\n     */\n    this.excludeDays = [];\n    /**\n     * Called when the view date is changed\n     */\n    this.viewDateChange = new EventEmitter();\n  }\n  /**\n   * @hidden\n   */\n  onClick() {\n    const addFn = {\n      day: this.dateAdapter.addDays,\n      week: this.dateAdapter.addWeeks,\n      month: this.dateAdapter.addMonths\n    }[this.view];\n    if (this.view === CalendarView.Day) {\n      this.viewDateChange.emit(addDaysWithExclusions(this.dateAdapter, this.viewDate, 1, this.excludeDays));\n    } else if (this.view === CalendarView.Week && this.daysInWeek) {\n      this.viewDateChange.emit(addDaysWithExclusions(this.dateAdapter, this.viewDate, this.daysInWeek, this.excludeDays));\n    } else {\n      this.viewDateChange.emit(addFn(this.viewDate, 1));\n    }\n  }\n}\nCalendarNextViewDirective.ɵfac = function CalendarNextViewDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarNextViewDirective)(i0.ɵɵdirectiveInject(DateAdapter));\n};\nCalendarNextViewDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CalendarNextViewDirective,\n  selectors: [[\"\", \"mwlCalendarNextView\", \"\"]],\n  hostBindings: function CalendarNextViewDirective_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function CalendarNextViewDirective_click_HostBindingHandler() {\n        return ctx.onClick();\n      });\n    }\n  },\n  inputs: {\n    view: \"view\",\n    viewDate: \"viewDate\",\n    excludeDays: \"excludeDays\",\n    daysInWeek: \"daysInWeek\"\n  },\n  outputs: {\n    viewDateChange: \"viewDateChange\"\n  },\n  standalone: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarNextViewDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mwlCalendarNextView]'\n    }]\n  }], function () {\n    return [{\n      type: DateAdapter\n    }];\n  }, {\n    view: [{\n      type: Input\n    }],\n    viewDate: [{\n      type: Input\n    }],\n    excludeDays: [{\n      type: Input\n    }],\n    daysInWeek: [{\n      type: Input\n    }],\n    viewDateChange: [{\n      type: Output\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click']\n    }]\n  });\n})();\n\n/**\n * Change the view date to the current day. For example:\n *\n * ```typescript\n * <button\n *  mwlCalendarToday\n *  [(viewDate)]=\"viewDate\">\n *  Today\n * </button>\n * ```\n */\nclass CalendarTodayDirective {\n  constructor(dateAdapter) {\n    this.dateAdapter = dateAdapter;\n    /**\n     * Called when the view date is changed\n     */\n    this.viewDateChange = new EventEmitter();\n  }\n  /**\n   * @hidden\n   */\n  onClick() {\n    this.viewDateChange.emit(this.dateAdapter.startOfDay(new Date()));\n  }\n}\nCalendarTodayDirective.ɵfac = function CalendarTodayDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarTodayDirective)(i0.ɵɵdirectiveInject(DateAdapter));\n};\nCalendarTodayDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CalendarTodayDirective,\n  selectors: [[\"\", \"mwlCalendarToday\", \"\"]],\n  hostBindings: function CalendarTodayDirective_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function CalendarTodayDirective_click_HostBindingHandler() {\n        return ctx.onClick();\n      });\n    }\n  },\n  inputs: {\n    viewDate: \"viewDate\"\n  },\n  outputs: {\n    viewDateChange: \"viewDateChange\"\n  },\n  standalone: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarTodayDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mwlCalendarToday]'\n    }]\n  }], function () {\n    return [{\n      type: DateAdapter\n    }];\n  }, {\n    viewDate: [{\n      type: Input\n    }],\n    viewDateChange: [{\n      type: Output\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click']\n    }]\n  });\n})();\n\n/**\n * This will use the angular date pipe to do all date formatting. It is the default date formatter used by the calendar.\n */\nclass CalendarAngularDateFormatter {\n  constructor(dateAdapter) {\n    this.dateAdapter = dateAdapter;\n  }\n  /**\n   * The month view header week day labels\n   */\n  monthViewColumnHeader({\n    date,\n    locale\n  }) {\n    return formatDate(date, 'EEEE', locale);\n  }\n  /**\n   * The month view cell day number\n   */\n  monthViewDayNumber({\n    date,\n    locale\n  }) {\n    return formatDate(date, 'd', locale);\n  }\n  /**\n   * The month view title\n   */\n  monthViewTitle({\n    date,\n    locale\n  }) {\n    return formatDate(date, 'LLLL y', locale);\n  }\n  /**\n   * The week view header week day labels\n   */\n  weekViewColumnHeader({\n    date,\n    locale\n  }) {\n    return formatDate(date, 'EEEE', locale);\n  }\n  /**\n   * The week view sub header day and month labels\n   */\n  weekViewColumnSubHeader({\n    date,\n    locale\n  }) {\n    return formatDate(date, 'MMM d', locale);\n  }\n  /**\n   * The week view title\n   */\n  weekViewTitle({\n    date,\n    locale,\n    weekStartsOn,\n    excludeDays,\n    daysInWeek\n  }) {\n    const {\n      viewStart,\n      viewEnd\n    } = getWeekViewPeriod(this.dateAdapter, date, weekStartsOn, excludeDays, daysInWeek);\n    const format = (dateToFormat, showYear) => formatDate(dateToFormat, 'MMM d' + (showYear ? ', yyyy' : ''), locale);\n    return `${format(viewStart, viewStart.getUTCFullYear() !== viewEnd.getUTCFullYear())} - ${format(viewEnd, true)}`;\n  }\n  /**\n   * The time formatting down the left hand side of the week view\n   */\n  weekViewHour({\n    date,\n    locale\n  }) {\n    return formatDate(date, 'h a', locale);\n  }\n  /**\n   * The time formatting down the left hand side of the day view\n   */\n  dayViewHour({\n    date,\n    locale\n  }) {\n    return formatDate(date, 'h a', locale);\n  }\n  /**\n   * The day view title\n   */\n  dayViewTitle({\n    date,\n    locale\n  }) {\n    return formatDate(date, 'EEEE, MMMM d, y', locale);\n  }\n}\nCalendarAngularDateFormatter.ɵfac = function CalendarAngularDateFormatter_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarAngularDateFormatter)(i0.ɵɵinject(DateAdapter));\n};\nCalendarAngularDateFormatter.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: CalendarAngularDateFormatter,\n  factory: CalendarAngularDateFormatter.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarAngularDateFormatter, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: DateAdapter\n    }];\n  }, null);\n})();\n\n/**\n * This class is responsible for all formatting of dates. There are 3 implementations available, the `CalendarAngularDateFormatter` (default) which uses the angular date pipe to format dates, the `CalendarNativeDateFormatter` which will use the <a href=\"https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/Intl\" target=\"_blank\">Intl</a> API to format dates, or there is the `CalendarMomentDateFormatter` which uses <a href=\"http://momentjs.com/\" target=\"_blank\">moment</a>.\n *\n * If you wish, you may override any of the defaults via angulars DI. For example:\n *\n * ```typescript\n * import { CalendarDateFormatter, DateFormatterParams } from 'angular-calendar';\n * import { formatDate } from '@angular/common';\n * import { Injectable } from '@angular/core';\n *\n * @Injectable()\n * class CustomDateFormatter extends CalendarDateFormatter {\n *\n *   public monthViewColumnHeader({date, locale}: DateFormatterParams): string {\n *     return formatDate(date, 'EEE', locale); // use short week days\n *   }\n *\n * }\n *\n * // in your component that uses the calendar\n * providers: [{\n *   provide: CalendarDateFormatter,\n *   useClass: CustomDateFormatter\n * }]\n * ```\n */\nclass CalendarDateFormatter extends CalendarAngularDateFormatter {}\nCalendarDateFormatter.ɵfac = /* @__PURE__ */(() => {\n  let ɵCalendarDateFormatter_BaseFactory;\n  return function CalendarDateFormatter_Factory(__ngFactoryType__) {\n    return (ɵCalendarDateFormatter_BaseFactory || (ɵCalendarDateFormatter_BaseFactory = i0.ɵɵgetInheritedFactory(CalendarDateFormatter)))(__ngFactoryType__ || CalendarDateFormatter);\n  };\n})();\nCalendarDateFormatter.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: CalendarDateFormatter,\n  factory: CalendarDateFormatter.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarDateFormatter, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * This pipe is primarily for rendering the current view title. Example usage:\n * ```typescript\n * // where `viewDate` is a `Date` and view is `'month' | 'week' | 'day'`\n * {{ viewDate | calendarDate:(view + 'ViewTitle'):'en' }}\n * ```\n */\nclass CalendarDatePipe {\n  constructor(dateFormatter, locale) {\n    this.dateFormatter = dateFormatter;\n    this.locale = locale;\n  }\n  transform(date, method, locale = this.locale, weekStartsOn = 0, excludeDays = [], daysInWeek) {\n    if (typeof this.dateFormatter[method] === 'undefined') {\n      const allowedMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(CalendarDateFormatter.prototype)).filter(iMethod => iMethod !== 'constructor');\n      throw new Error(`${method} is not a valid date formatter. Can only be one of ${allowedMethods.join(', ')}`);\n    }\n    return this.dateFormatter[method]({\n      date,\n      locale,\n      weekStartsOn,\n      excludeDays,\n      daysInWeek\n    });\n  }\n}\nCalendarDatePipe.ɵfac = function CalendarDatePipe_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarDatePipe)(i0.ɵɵdirectiveInject(CalendarDateFormatter, 16), i0.ɵɵdirectiveInject(LOCALE_ID, 16));\n};\nCalendarDatePipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n  name: \"calendarDate\",\n  type: CalendarDatePipe,\n  pure: true,\n  standalone: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarDatePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'calendarDate'\n    }]\n  }], function () {\n    return [{\n      type: CalendarDateFormatter\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [LOCALE_ID]\n      }]\n    }];\n  }, null);\n})();\nclass CalendarUtils {\n  constructor(dateAdapter) {\n    this.dateAdapter = dateAdapter;\n  }\n  getMonthView(args) {\n    return getMonthView(this.dateAdapter, args);\n  }\n  getWeekViewHeader(args) {\n    return getWeekViewHeader(this.dateAdapter, args);\n  }\n  getWeekView(args) {\n    return getWeekView(this.dateAdapter, args);\n  }\n}\nCalendarUtils.ɵfac = function CalendarUtils_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarUtils)(i0.ɵɵinject(DateAdapter));\n};\nCalendarUtils.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: CalendarUtils,\n  factory: CalendarUtils.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarUtils, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: DateAdapter\n    }];\n  }, null);\n})();\nconst MOMENT = new InjectionToken('Moment');\n/**\n * This will use <a href=\"http://momentjs.com/\" target=\"_blank\">moment</a> to do all date formatting. To use this class:\n *\n * ```typescript\n * import { CalendarDateFormatter, CalendarMomentDateFormatter, MOMENT } from 'angular-calendar';\n * import moment from 'moment';\n *\n * // in your component\n * provide: [{\n *   provide: MOMENT, useValue: moment\n * }, {\n *   provide: CalendarDateFormatter, useClass: CalendarMomentDateFormatter\n * }]\n *\n * ```\n */\nclass CalendarMomentDateFormatter {\n  /**\n   * @hidden\n   */\n  constructor(moment, dateAdapter) {\n    this.moment = moment;\n    this.dateAdapter = dateAdapter;\n  }\n  /**\n   * The month view header week day labels\n   */\n  monthViewColumnHeader({\n    date,\n    locale\n  }) {\n    return this.moment(date).locale(locale).format('dddd');\n  }\n  /**\n   * The month view cell day number\n   */\n  monthViewDayNumber({\n    date,\n    locale\n  }) {\n    return this.moment(date).locale(locale).format('D');\n  }\n  /**\n   * The month view title\n   */\n  monthViewTitle({\n    date,\n    locale\n  }) {\n    return this.moment(date).locale(locale).format('MMMM YYYY');\n  }\n  /**\n   * The week view header week day labels\n   */\n  weekViewColumnHeader({\n    date,\n    locale\n  }) {\n    return this.moment(date).locale(locale).format('dddd');\n  }\n  /**\n   * The week view sub header day and month labels\n   */\n  weekViewColumnSubHeader({\n    date,\n    locale\n  }) {\n    return this.moment(date).locale(locale).format('MMM D');\n  }\n  /**\n   * The week view title\n   */\n  weekViewTitle({\n    date,\n    locale,\n    weekStartsOn,\n    excludeDays,\n    daysInWeek\n  }) {\n    const {\n      viewStart,\n      viewEnd\n    } = getWeekViewPeriod(this.dateAdapter, date, weekStartsOn, excludeDays, daysInWeek);\n    const format = (dateToFormat, showYear) => this.moment(dateToFormat).locale(locale).format('MMM D' + (showYear ? ', YYYY' : ''));\n    return `${format(viewStart, viewStart.getUTCFullYear() !== viewEnd.getUTCFullYear())} - ${format(viewEnd, true)}`;\n  }\n  /**\n   * The time formatting down the left hand side of the week view\n   */\n  weekViewHour({\n    date,\n    locale\n  }) {\n    return this.moment(date).locale(locale).format('ha');\n  }\n  /**\n   * The time formatting down the left hand side of the day view\n   */\n  dayViewHour({\n    date,\n    locale\n  }) {\n    return this.moment(date).locale(locale).format('ha');\n  }\n  /**\n   * The day view title\n   */\n  dayViewTitle({\n    date,\n    locale\n  }) {\n    return this.moment(date).locale(locale).format('dddd, LL'); // dddd = Thursday\n  } // LL = locale-dependent Month Day, Year\n}\nCalendarMomentDateFormatter.ɵfac = function CalendarMomentDateFormatter_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarMomentDateFormatter)(i0.ɵɵinject(MOMENT), i0.ɵɵinject(DateAdapter));\n};\nCalendarMomentDateFormatter.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: CalendarMomentDateFormatter,\n  factory: CalendarMomentDateFormatter.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarMomentDateFormatter, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MOMENT]\n      }]\n    }, {\n      type: DateAdapter\n    }];\n  }, null);\n})();\n\n/**\n * This will use <a href=\"https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/Intl\" target=\"_blank\">Intl</a> API to do all date formatting.\n *\n * You will need to include a <a href=\"https://github.com/andyearnshaw/Intl.js/\">polyfill</a> for older browsers.\n */\nclass CalendarNativeDateFormatter {\n  constructor(dateAdapter) {\n    this.dateAdapter = dateAdapter;\n  }\n  /**\n   * The month view header week day labels\n   */\n  monthViewColumnHeader({\n    date,\n    locale\n  }) {\n    return new Intl.DateTimeFormat(locale, {\n      weekday: 'long'\n    }).format(date);\n  }\n  /**\n   * The month view cell day number\n   */\n  monthViewDayNumber({\n    date,\n    locale\n  }) {\n    return new Intl.DateTimeFormat(locale, {\n      day: 'numeric'\n    }).format(date);\n  }\n  /**\n   * The month view title\n   */\n  monthViewTitle({\n    date,\n    locale\n  }) {\n    return new Intl.DateTimeFormat(locale, {\n      year: 'numeric',\n      month: 'long'\n    }).format(date);\n  }\n  /**\n   * The week view header week day labels\n   */\n  weekViewColumnHeader({\n    date,\n    locale\n  }) {\n    return new Intl.DateTimeFormat(locale, {\n      weekday: 'long'\n    }).format(date);\n  }\n  /**\n   * The week view sub header day and month labels\n   */\n  weekViewColumnSubHeader({\n    date,\n    locale\n  }) {\n    return new Intl.DateTimeFormat(locale, {\n      day: 'numeric',\n      month: 'short'\n    }).format(date);\n  }\n  /**\n   * The week view title\n   */\n  weekViewTitle({\n    date,\n    locale,\n    weekStartsOn,\n    excludeDays,\n    daysInWeek\n  }) {\n    const {\n      viewStart,\n      viewEnd\n    } = getWeekViewPeriod(this.dateAdapter, date, weekStartsOn, excludeDays, daysInWeek);\n    const format = (dateToFormat, showYear) => new Intl.DateTimeFormat(locale, {\n      day: 'numeric',\n      month: 'short',\n      year: showYear ? 'numeric' : undefined\n    }).format(dateToFormat);\n    return `${format(viewStart, viewStart.getUTCFullYear() !== viewEnd.getUTCFullYear())} - ${format(viewEnd, true)}`;\n  }\n  /**\n   * The time formatting down the left hand side of the week view\n   */\n  weekViewHour({\n    date,\n    locale\n  }) {\n    return new Intl.DateTimeFormat(locale, {\n      hour: 'numeric'\n    }).format(date);\n  }\n  /**\n   * The time formatting down the left hand side of the day view\n   */\n  dayViewHour({\n    date,\n    locale\n  }) {\n    return new Intl.DateTimeFormat(locale, {\n      hour: 'numeric'\n    }).format(date);\n  }\n  /**\n   * The day view title\n   */\n  dayViewTitle({\n    date,\n    locale\n  }) {\n    return new Intl.DateTimeFormat(locale, {\n      day: 'numeric',\n      month: 'long',\n      year: 'numeric',\n      weekday: 'long'\n    }).format(date);\n  }\n}\nCalendarNativeDateFormatter.ɵfac = function CalendarNativeDateFormatter_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarNativeDateFormatter)(i0.ɵɵinject(DateAdapter));\n};\nCalendarNativeDateFormatter.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: CalendarNativeDateFormatter,\n  factory: CalendarNativeDateFormatter.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarNativeDateFormatter, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: DateAdapter\n    }];\n  }, null);\n})();\nvar CalendarEventTimesChangedEventType;\n(function (CalendarEventTimesChangedEventType) {\n  CalendarEventTimesChangedEventType[\"Drag\"] = \"drag\";\n  CalendarEventTimesChangedEventType[\"Drop\"] = \"drop\";\n  CalendarEventTimesChangedEventType[\"Resize\"] = \"resize\";\n})(CalendarEventTimesChangedEventType || (CalendarEventTimesChangedEventType = {}));\n\n/**\n * Import this module to if you're just using a singular view and want to save on bundle size. Example usage:\n *\n * ```typescript\n * import { CalendarCommonModule, CalendarMonthModule } from 'angular-calendar';\n *\n * @NgModule({\n *   imports: [\n *     CalendarCommonModule.forRoot(),\n *     CalendarMonthModule\n *   ]\n * })\n * class MyModule {}\n * ```\n *\n */\nclass CalendarCommonModule {\n  static forRoot(dateAdapter, config = {}) {\n    return {\n      ngModule: CalendarCommonModule,\n      providers: [dateAdapter, config.eventTitleFormatter || CalendarEventTitleFormatter, config.dateFormatter || CalendarDateFormatter, config.utils || CalendarUtils, config.a11y || CalendarA11y]\n    };\n  }\n}\nCalendarCommonModule.ɵfac = function CalendarCommonModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarCommonModule)();\n};\nCalendarCommonModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: CalendarCommonModule,\n  declarations: [CalendarEventActionsComponent, CalendarEventTitleComponent, CalendarTooltipWindowComponent, CalendarTooltipDirective, CalendarPreviousViewDirective, CalendarNextViewDirective, CalendarTodayDirective, CalendarDatePipe, CalendarEventTitlePipe, CalendarA11yPipe, ClickDirective, KeydownEnterDirective],\n  imports: [CommonModule],\n  exports: [CalendarEventActionsComponent, CalendarEventTitleComponent, CalendarTooltipWindowComponent, CalendarTooltipDirective, CalendarPreviousViewDirective, CalendarNextViewDirective, CalendarTodayDirective, CalendarDatePipe, CalendarEventTitlePipe, CalendarA11yPipe, ClickDirective, KeydownEnterDirective]\n});\nCalendarCommonModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [I18nPluralPipe],\n  imports: [CommonModule]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarCommonModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [CalendarEventActionsComponent, CalendarEventTitleComponent, CalendarTooltipWindowComponent, CalendarTooltipDirective, CalendarPreviousViewDirective, CalendarNextViewDirective, CalendarTodayDirective, CalendarDatePipe, CalendarEventTitlePipe, CalendarA11yPipe, ClickDirective, KeydownEnterDirective],\n      imports: [CommonModule],\n      exports: [CalendarEventActionsComponent, CalendarEventTitleComponent, CalendarTooltipWindowComponent, CalendarTooltipDirective, CalendarPreviousViewDirective, CalendarNextViewDirective, CalendarTodayDirective, CalendarDatePipe, CalendarEventTitlePipe, CalendarA11yPipe, ClickDirective, KeydownEnterDirective],\n      providers: [I18nPluralPipe]\n    }]\n  }], null, null);\n})();\nclass CalendarMonthCellComponent {\n  constructor() {\n    this.highlightDay = new EventEmitter();\n    this.unhighlightDay = new EventEmitter();\n    this.eventClicked = new EventEmitter();\n    this.trackByEventId = trackByEventId;\n    this.validateDrag = isWithinThreshold;\n  }\n}\nCalendarMonthCellComponent.ɵfac = function CalendarMonthCellComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarMonthCellComponent)();\n};\nCalendarMonthCellComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: CalendarMonthCellComponent,\n  selectors: [[\"mwl-calendar-month-cell\"]],\n  hostAttrs: [1, \"cal-cell\", \"cal-day-cell\"],\n  hostVars: 18,\n  hostBindings: function CalendarMonthCellComponent_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"cal-past\", ctx.day.isPast)(\"cal-today\", ctx.day.isToday)(\"cal-future\", ctx.day.isFuture)(\"cal-weekend\", ctx.day.isWeekend)(\"cal-in-month\", ctx.day.inMonth)(\"cal-out-month\", !ctx.day.inMonth)(\"cal-has-events\", ctx.day.events.length > 0)(\"cal-open\", ctx.day === ctx.openDay)(\"cal-event-highlight\", !!ctx.day.backgroundColor);\n    }\n  },\n  inputs: {\n    day: \"day\",\n    openDay: \"openDay\",\n    locale: \"locale\",\n    tooltipPlacement: \"tooltipPlacement\",\n    tooltipAppendToBody: \"tooltipAppendToBody\",\n    customTemplate: \"customTemplate\",\n    tooltipTemplate: \"tooltipTemplate\",\n    tooltipDelay: \"tooltipDelay\"\n  },\n  outputs: {\n    highlightDay: \"highlightDay\",\n    unhighlightDay: \"unhighlightDay\",\n    eventClicked: \"eventClicked\"\n  },\n  standalone: false,\n  decls: 3,\n  vars: 15,\n  consts: [[\"defaultTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"cal-cell-top\"], [\"aria-hidden\", \"true\"], [\"class\", \"cal-day-badge\", 4, \"ngIf\"], [1, \"cal-day-number\"], [\"class\", \"cal-events\", 4, \"ngIf\"], [1, \"cal-day-badge\"], [1, \"cal-events\"], [\"class\", \"cal-event\", \"mwlDraggable\", \"\", \"dragActiveClass\", \"cal-drag-active\", 3, \"ngStyle\", \"ngClass\", \"mwlCalendarTooltip\", \"tooltipPlacement\", \"tooltipEvent\", \"tooltipTemplate\", \"tooltipAppendToBody\", \"tooltipDelay\", \"cal-draggable\", \"dropData\", \"dragAxis\", \"validateDrag\", \"touchStartLongPress\", \"mouseenter\", \"mouseleave\", \"mwlClick\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"mwlDraggable\", \"\", \"dragActiveClass\", \"cal-drag-active\", 1, \"cal-event\", 3, \"mouseenter\", \"mouseleave\", \"mwlClick\", \"ngStyle\", \"ngClass\", \"mwlCalendarTooltip\", \"tooltipPlacement\", \"tooltipEvent\", \"tooltipTemplate\", \"tooltipAppendToBody\", \"tooltipDelay\", \"dropData\", \"dragAxis\", \"validateDrag\", \"touchStartLongPress\"]],\n  template: function CalendarMonthCellComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, CalendarMonthCellComponent_ng_template_0_Template, 8, 14, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, CalendarMonthCellComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n    }\n    if (rf & 2) {\n      const defaultTemplate_r15 = i0.ɵɵreference(1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.customTemplate || defaultTemplate_r15)(\"ngTemplateOutletContext\", i0.ɵɵpureFunctionV(2, _c5, [ctx.day, ctx.openDay, ctx.locale, ctx.tooltipPlacement, ctx.highlightDay, ctx.unhighlightDay, ctx.eventClicked, ctx.tooltipTemplate, ctx.tooltipAppendToBody, ctx.tooltipDelay, ctx.trackByEventId, ctx.validateDrag]));\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.DraggableDirective, CalendarTooltipDirective, ClickDirective, CalendarDatePipe, CalendarEventTitlePipe, CalendarA11yPipe],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarMonthCellComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mwl-calendar-month-cell',\n      template: `\n    <ng-template\n      #defaultTemplate\n      let-day=\"day\"\n      let-openDay=\"openDay\"\n      let-locale=\"locale\"\n      let-tooltipPlacement=\"tooltipPlacement\"\n      let-highlightDay=\"highlightDay\"\n      let-unhighlightDay=\"unhighlightDay\"\n      let-eventClicked=\"eventClicked\"\n      let-tooltipTemplate=\"tooltipTemplate\"\n      let-tooltipAppendToBody=\"tooltipAppendToBody\"\n      let-tooltipDelay=\"tooltipDelay\"\n      let-trackByEventId=\"trackByEventId\"\n      let-validateDrag=\"validateDrag\"\n    >\n      <div\n        class=\"cal-cell-top\"\n        [attr.aria-label]=\"\n          { day: day, locale: locale } | calendarA11y : 'monthCell'\n        \"\n      >\n        <span aria-hidden=\"true\">\n          <span class=\"cal-day-badge\" *ngIf=\"day.badgeTotal > 0\">{{\n            day.badgeTotal\n          }}</span>\n          <span class=\"cal-day-number\">{{\n            day.date | calendarDate : 'monthViewDayNumber' : locale\n          }}</span>\n        </span>\n      </div>\n      <div class=\"cal-events\" *ngIf=\"day.events.length > 0\">\n        <div\n          class=\"cal-event\"\n          *ngFor=\"let event of day.events; trackBy: trackByEventId\"\n          [ngStyle]=\"{ backgroundColor: event.color?.primary }\"\n          [ngClass]=\"event?.cssClass\"\n          (mouseenter)=\"highlightDay.emit({ event: event })\"\n          (mouseleave)=\"unhighlightDay.emit({ event: event })\"\n          [mwlCalendarTooltip]=\"\n            event.title | calendarEventTitle : 'monthTooltip' : event\n          \"\n          [tooltipPlacement]=\"tooltipPlacement\"\n          [tooltipEvent]=\"event\"\n          [tooltipTemplate]=\"tooltipTemplate\"\n          [tooltipAppendToBody]=\"tooltipAppendToBody\"\n          [tooltipDelay]=\"tooltipDelay\"\n          mwlDraggable\n          [class.cal-draggable]=\"event.draggable\"\n          dragActiveClass=\"cal-drag-active\"\n          [dropData]=\"{ event: event, draggedFrom: day }\"\n          [dragAxis]=\"{ x: event.draggable, y: event.draggable }\"\n          [validateDrag]=\"validateDrag\"\n          [touchStartLongPress]=\"{ delay: 300, delta: 30 }\"\n          (mwlClick)=\"eventClicked.emit({ event: event, sourceEvent: $event })\"\n          [attr.aria-hidden]=\"{} | calendarA11y : 'hideMonthCellEvents'\"\n        ></div>\n      </div>\n    </ng-template>\n    <ng-template\n      [ngTemplateOutlet]=\"customTemplate || defaultTemplate\"\n      [ngTemplateOutletContext]=\"{\n        day: day,\n        openDay: openDay,\n        locale: locale,\n        tooltipPlacement: tooltipPlacement,\n        highlightDay: highlightDay,\n        unhighlightDay: unhighlightDay,\n        eventClicked: eventClicked,\n        tooltipTemplate: tooltipTemplate,\n        tooltipAppendToBody: tooltipAppendToBody,\n        tooltipDelay: tooltipDelay,\n        trackByEventId: trackByEventId,\n        validateDrag: validateDrag\n      }\"\n    >\n    </ng-template>\n  `,\n      // eslint-disable-next-line @angular-eslint/no-host-metadata-property\n      host: {\n        class: 'cal-cell cal-day-cell',\n        '[class.cal-past]': 'day.isPast',\n        '[class.cal-today]': 'day.isToday',\n        '[class.cal-future]': 'day.isFuture',\n        '[class.cal-weekend]': 'day.isWeekend',\n        '[class.cal-in-month]': 'day.inMonth',\n        '[class.cal-out-month]': '!day.inMonth',\n        '[class.cal-has-events]': 'day.events.length > 0',\n        '[class.cal-open]': 'day === openDay',\n        '[class.cal-event-highlight]': '!!day.backgroundColor'\n      }\n    }]\n  }], null, {\n    day: [{\n      type: Input\n    }],\n    openDay: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }],\n    tooltipPlacement: [{\n      type: Input\n    }],\n    tooltipAppendToBody: [{\n      type: Input\n    }],\n    customTemplate: [{\n      type: Input\n    }],\n    tooltipTemplate: [{\n      type: Input\n    }],\n    tooltipDelay: [{\n      type: Input\n    }],\n    highlightDay: [{\n      type: Output\n    }],\n    unhighlightDay: [{\n      type: Output\n    }],\n    eventClicked: [{\n      type: Output\n    }]\n  });\n})();\nconst collapseAnimation = trigger('collapse', [state('void', style({\n  height: 0,\n  overflow: 'hidden',\n  'padding-top': 0,\n  'padding-bottom': 0\n})), state('*', style({\n  height: '*',\n  overflow: 'hidden',\n  'padding-top': '*',\n  'padding-bottom': '*'\n})), transition('* => void', animate('150ms ease-out')), transition('void => *', animate('150ms ease-in'))]);\nclass CalendarOpenDayEventsComponent {\n  constructor() {\n    this.isOpen = false;\n    this.eventClicked = new EventEmitter();\n    this.trackByEventId = trackByEventId;\n    this.validateDrag = isWithinThreshold;\n  }\n}\nCalendarOpenDayEventsComponent.ɵfac = function CalendarOpenDayEventsComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarOpenDayEventsComponent)();\n};\nCalendarOpenDayEventsComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: CalendarOpenDayEventsComponent,\n  selectors: [[\"mwl-calendar-open-day-events\"]],\n  inputs: {\n    locale: \"locale\",\n    isOpen: \"isOpen\",\n    events: \"events\",\n    customTemplate: \"customTemplate\",\n    eventTitleTemplate: \"eventTitleTemplate\",\n    eventActionsTemplate: \"eventActionsTemplate\",\n    date: \"date\"\n  },\n  outputs: {\n    eventClicked: \"eventClicked\"\n  },\n  standalone: false,\n  decls: 3,\n  vars: 8,\n  consts: [[\"defaultTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"cal-open-day-events\", \"role\", \"application\", 4, \"ngIf\"], [\"role\", \"application\", 1, \"cal-open-day-events\"], [\"tabindex\", \"-1\", \"role\", \"alert\"], [\"tabindex\", \"0\", \"role\", \"landmark\"], [\"mwlDraggable\", \"\", \"dragActiveClass\", \"cal-drag-active\", 3, \"ngClass\", \"cal-draggable\", \"dropData\", \"dragAxis\", \"validateDrag\", \"touchStartLongPress\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"mwlDraggable\", \"\", \"dragActiveClass\", \"cal-drag-active\", 3, \"ngClass\", \"dropData\", \"dragAxis\", \"validateDrag\", \"touchStartLongPress\"], [1, \"cal-event\", 3, \"ngStyle\"], [\"view\", \"month\", \"tabindex\", \"0\", 3, \"mwlClick\", \"mwlKeydownEnter\", \"event\", \"customTemplate\"], [3, \"event\", \"customTemplate\"]],\n  template: function CalendarOpenDayEventsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, CalendarOpenDayEventsComponent_ng_template_0_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, CalendarOpenDayEventsComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n    }\n    if (rf & 2) {\n      const defaultTemplate_r10 = i0.ɵɵreference(1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.customTemplate || defaultTemplate_r10)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction5(2, _c11, ctx.events, ctx.eventClicked, ctx.isOpen, ctx.trackByEventId, ctx.validateDrag));\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.DraggableDirective, CalendarEventActionsComponent, CalendarEventTitleComponent, ClickDirective, KeydownEnterDirective, CalendarA11yPipe],\n  encapsulation: 2,\n  data: {\n    animation: [collapseAnimation]\n  }\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarOpenDayEventsComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mwl-calendar-open-day-events',\n      template: `\n    <ng-template\n      #defaultTemplate\n      let-events=\"events\"\n      let-eventClicked=\"eventClicked\"\n      let-isOpen=\"isOpen\"\n      let-trackByEventId=\"trackByEventId\"\n      let-validateDrag=\"validateDrag\"\n    >\n      <div\n        class=\"cal-open-day-events\"\n        [@collapse]\n        *ngIf=\"isOpen\"\n        role=\"application\"\n      >\n        <span\n          tabindex=\"-1\"\n          role=\"alert\"\n          [attr.aria-label]=\"\n            { date: date, locale: locale } | calendarA11y : 'openDayEventsAlert'\n          \"\n        ></span>\n        <span\n          tabindex=\"0\"\n          role=\"landmark\"\n          [attr.aria-label]=\"\n            { date: date, locale: locale }\n              | calendarA11y : 'openDayEventsLandmark'\n          \"\n        ></span>\n        <div\n          *ngFor=\"let event of events; trackBy: trackByEventId\"\n          [ngClass]=\"event?.cssClass\"\n          mwlDraggable\n          [class.cal-draggable]=\"event.draggable\"\n          dragActiveClass=\"cal-drag-active\"\n          [dropData]=\"{ event: event }\"\n          [dragAxis]=\"{ x: event.draggable, y: event.draggable }\"\n          [validateDrag]=\"validateDrag\"\n          [touchStartLongPress]=\"{ delay: 300, delta: 30 }\"\n        >\n          <span\n            class=\"cal-event\"\n            [ngStyle]=\"{ backgroundColor: event.color?.primary }\"\n          >\n          </span>\n          &ngsp;\n          <mwl-calendar-event-title\n            [event]=\"event\"\n            [customTemplate]=\"eventTitleTemplate\"\n            view=\"month\"\n            (mwlClick)=\"\n              eventClicked.emit({ event: event, sourceEvent: $event })\n            \"\n            (mwlKeydownEnter)=\"\n              eventClicked.emit({ event: event, sourceEvent: $event })\n            \"\n            tabindex=\"0\"\n            [attr.aria-label]=\"\n              { event: event, locale: locale }\n                | calendarA11y : 'eventDescription'\n            \"\n          >\n          </mwl-calendar-event-title>\n          &ngsp;\n          <mwl-calendar-event-actions\n            [event]=\"event\"\n            [customTemplate]=\"eventActionsTemplate\"\n          >\n          </mwl-calendar-event-actions>\n        </div>\n      </div>\n    </ng-template>\n    <ng-template\n      [ngTemplateOutlet]=\"customTemplate || defaultTemplate\"\n      [ngTemplateOutletContext]=\"{\n        events: events,\n        eventClicked: eventClicked,\n        isOpen: isOpen,\n        trackByEventId: trackByEventId,\n        validateDrag: validateDrag\n      }\"\n    >\n    </ng-template>\n  `,\n      animations: [collapseAnimation]\n    }]\n  }], null, {\n    locale: [{\n      type: Input\n    }],\n    isOpen: [{\n      type: Input\n    }],\n    events: [{\n      type: Input\n    }],\n    customTemplate: [{\n      type: Input\n    }],\n    eventTitleTemplate: [{\n      type: Input\n    }],\n    eventActionsTemplate: [{\n      type: Input\n    }],\n    date: [{\n      type: Input\n    }],\n    eventClicked: [{\n      type: Output\n    }]\n  });\n})();\nclass CalendarMonthViewHeaderComponent {\n  constructor() {\n    this.columnHeaderClicked = new EventEmitter();\n    this.trackByWeekDayHeaderDate = trackByWeekDayHeaderDate;\n  }\n}\nCalendarMonthViewHeaderComponent.ɵfac = function CalendarMonthViewHeaderComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarMonthViewHeaderComponent)();\n};\nCalendarMonthViewHeaderComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: CalendarMonthViewHeaderComponent,\n  selectors: [[\"mwl-calendar-month-view-header\"]],\n  inputs: {\n    days: \"days\",\n    locale: \"locale\",\n    customTemplate: \"customTemplate\"\n  },\n  outputs: {\n    columnHeaderClicked: \"columnHeaderClicked\"\n  },\n  standalone: false,\n  decls: 3,\n  vars: 6,\n  consts: [[\"defaultTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"role\", \"row\", 1, \"cal-cell-row\", \"cal-header\"], [\"class\", \"cal-cell\", \"tabindex\", \"0\", \"role\", \"columnheader\", 3, \"cal-past\", \"cal-today\", \"cal-future\", \"cal-weekend\", \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"tabindex\", \"0\", \"role\", \"columnheader\", 1, \"cal-cell\", 3, \"click\", \"ngClass\"]],\n  template: function CalendarMonthViewHeaderComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, CalendarMonthViewHeaderComponent_ng_template_0_Template, 2, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, CalendarMonthViewHeaderComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n    }\n    if (rf & 2) {\n      const defaultTemplate_r7 = i0.ɵɵreference(1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.customTemplate || defaultTemplate_r7)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction3(2, _c15, ctx.days, ctx.locale, ctx.trackByWeekDayHeaderDate));\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgForOf, i1.NgTemplateOutlet, CalendarDatePipe],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarMonthViewHeaderComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mwl-calendar-month-view-header',\n      template: `\n    <ng-template\n      #defaultTemplate\n      let-days=\"days\"\n      let-locale=\"locale\"\n      let-trackByWeekDayHeaderDate=\"trackByWeekDayHeaderDate\"\n    >\n      <div class=\"cal-cell-row cal-header\" role=\"row\">\n        <div\n          class=\"cal-cell\"\n          *ngFor=\"let day of days; trackBy: trackByWeekDayHeaderDate\"\n          [class.cal-past]=\"day.isPast\"\n          [class.cal-today]=\"day.isToday\"\n          [class.cal-future]=\"day.isFuture\"\n          [class.cal-weekend]=\"day.isWeekend\"\n          (click)=\"\n            columnHeaderClicked.emit({\n              isoDayNumber: day.day,\n              sourceEvent: $event\n            })\n          \"\n          [ngClass]=\"day.cssClass\"\n          tabindex=\"0\"\n          role=\"columnheader\"\n        >\n          {{ day.date | calendarDate : 'monthViewColumnHeader' : locale }}\n        </div>\n      </div>\n    </ng-template>\n    <ng-template\n      [ngTemplateOutlet]=\"customTemplate || defaultTemplate\"\n      [ngTemplateOutletContext]=\"{\n        days: days,\n        locale: locale,\n        trackByWeekDayHeaderDate: trackByWeekDayHeaderDate\n      }\"\n    >\n    </ng-template>\n  `\n    }]\n  }], null, {\n    days: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }],\n    customTemplate: [{\n      type: Input\n    }],\n    columnHeaderClicked: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Shows all events on a given month. Example usage:\n *\n * ```typescript\n * <mwl-calendar-month-view\n *  [viewDate]=\"viewDate\"\n *  [events]=\"events\">\n * </mwl-calendar-month-view>\n * ```\n */\nclass CalendarMonthViewComponent {\n  /**\n   * @hidden\n   */\n  constructor(cdr, utils, locale, dateAdapter) {\n    this.cdr = cdr;\n    this.utils = utils;\n    this.dateAdapter = dateAdapter;\n    /**\n     * An array of events to display on view.\n     * The schema is available here: https://github.com/mattlewis92/calendar-utils/blob/c51689985f59a271940e30bc4e2c4e1fee3fcb5c/src/calendarUtils.ts#L49-L63\n     */\n    this.events = [];\n    /**\n     * An array of day indexes (0 = sunday, 1 = monday etc) that will be hidden on the view\n     */\n    this.excludeDays = [];\n    /**\n     * Whether the events list for the day of the `viewDate` option is visible or not\n     */\n    this.activeDayIsOpen = false;\n    /**\n     * The placement of the event tooltip\n     */\n    this.tooltipPlacement = 'auto';\n    /**\n     * Whether to append tooltips to the body or next to the trigger element\n     */\n    this.tooltipAppendToBody = true;\n    /**\n     * The delay in milliseconds before the tooltip should be displayed. If not provided the tooltip\n     * will be displayed immediately.\n     */\n    this.tooltipDelay = null;\n    /**\n     * An output that will be called before the view is rendered for the current month.\n     * If you add the `cssClass` property to a day in the body it will add that class to the cell element in the template\n     */\n    this.beforeViewRender = new EventEmitter();\n    /**\n     * Called when the day cell is clicked\n     */\n    this.dayClicked = new EventEmitter();\n    /**\n     * Called when the event title is clicked\n     */\n    this.eventClicked = new EventEmitter();\n    /**\n     * Called when a header week day is clicked. Returns ISO day number.\n     */\n    this.columnHeaderClicked = new EventEmitter();\n    /**\n     * Called when an event is dragged and dropped\n     */\n    this.eventTimesChanged = new EventEmitter();\n    /**\n     * @hidden\n     */\n    this.trackByRowOffset = (index, offset) => this.view.days.slice(offset, this.view.totalDaysVisibleInWeek).map(day => day.date.toISOString()).join('-');\n    /**\n     * @hidden\n     */\n    this.trackByDate = (index, day) => day.date.toISOString();\n    this.locale = locale;\n  }\n  /**\n   * @hidden\n   */\n  ngOnInit() {\n    if (this.refresh) {\n      this.refreshSubscription = this.refresh.subscribe(() => {\n        this.refreshAll();\n        this.cdr.markForCheck();\n      });\n    }\n  }\n  /**\n   * @hidden\n   */\n  ngOnChanges(changes) {\n    const refreshHeader = changes.viewDate || changes.excludeDays || changes.weekendDays;\n    const refreshBody = changes.viewDate || changes.events || changes.excludeDays || changes.weekendDays;\n    if (refreshHeader) {\n      this.refreshHeader();\n    }\n    if (changes.events) {\n      validateEvents(this.events);\n    }\n    if (refreshBody) {\n      this.refreshBody();\n    }\n    if (refreshHeader || refreshBody) {\n      this.emitBeforeViewRender();\n    }\n    if (changes.activeDayIsOpen || changes.viewDate || changes.events || changes.excludeDays || changes.activeDay) {\n      this.checkActiveDayIsOpen();\n    }\n  }\n  /**\n   * @hidden\n   */\n  ngOnDestroy() {\n    if (this.refreshSubscription) {\n      this.refreshSubscription.unsubscribe();\n    }\n  }\n  /**\n   * @hidden\n   */\n  toggleDayHighlight(event, isHighlighted) {\n    this.view.days.forEach(day => {\n      if (isHighlighted && day.events.indexOf(event) > -1) {\n        day.backgroundColor = event.color && event.color.secondary || '#D1E8FF';\n      } else {\n        delete day.backgroundColor;\n      }\n    });\n  }\n  /**\n   * @hidden\n   */\n  eventDropped(droppedOn, event, draggedFrom) {\n    if (droppedOn !== draggedFrom) {\n      const year = this.dateAdapter.getYear(droppedOn.date);\n      const month = this.dateAdapter.getMonth(droppedOn.date);\n      const date = this.dateAdapter.getDate(droppedOn.date);\n      const newStart = this.dateAdapter.setDate(this.dateAdapter.setMonth(this.dateAdapter.setYear(event.start, year), month), date);\n      let newEnd;\n      if (event.end) {\n        const secondsDiff = this.dateAdapter.differenceInSeconds(newStart, event.start);\n        newEnd = this.dateAdapter.addSeconds(event.end, secondsDiff);\n      }\n      this.eventTimesChanged.emit({\n        event,\n        newStart,\n        newEnd,\n        day: droppedOn,\n        type: CalendarEventTimesChangedEventType.Drop\n      });\n    }\n  }\n  refreshHeader() {\n    this.columnHeaders = this.utils.getWeekViewHeader({\n      viewDate: this.viewDate,\n      weekStartsOn: this.weekStartsOn,\n      excluded: this.excludeDays,\n      weekendDays: this.weekendDays\n    });\n  }\n  refreshBody() {\n    this.view = this.utils.getMonthView({\n      events: this.events,\n      viewDate: this.viewDate,\n      weekStartsOn: this.weekStartsOn,\n      excluded: this.excludeDays,\n      weekendDays: this.weekendDays\n    });\n  }\n  checkActiveDayIsOpen() {\n    if (this.activeDayIsOpen === true) {\n      const activeDay = this.activeDay || this.viewDate;\n      this.openDay = this.view.days.find(day => this.dateAdapter.isSameDay(day.date, activeDay));\n      const index = this.view.days.indexOf(this.openDay);\n      this.openRowIndex = Math.floor(index / this.view.totalDaysVisibleInWeek) * this.view.totalDaysVisibleInWeek;\n    } else {\n      this.openRowIndex = null;\n      this.openDay = null;\n    }\n  }\n  refreshAll() {\n    this.refreshHeader();\n    this.refreshBody();\n    this.emitBeforeViewRender();\n    this.checkActiveDayIsOpen();\n  }\n  emitBeforeViewRender() {\n    if (this.columnHeaders && this.view) {\n      this.beforeViewRender.emit({\n        header: this.columnHeaders,\n        body: this.view.days,\n        period: this.view.period\n      });\n    }\n  }\n}\nCalendarMonthViewComponent.ɵfac = function CalendarMonthViewComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarMonthViewComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(CalendarUtils), i0.ɵɵdirectiveInject(LOCALE_ID), i0.ɵɵdirectiveInject(DateAdapter));\n};\nCalendarMonthViewComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: CalendarMonthViewComponent,\n  selectors: [[\"mwl-calendar-month-view\"]],\n  inputs: {\n    viewDate: \"viewDate\",\n    events: \"events\",\n    excludeDays: \"excludeDays\",\n    activeDayIsOpen: \"activeDayIsOpen\",\n    activeDay: \"activeDay\",\n    refresh: \"refresh\",\n    locale: \"locale\",\n    tooltipPlacement: \"tooltipPlacement\",\n    tooltipTemplate: \"tooltipTemplate\",\n    tooltipAppendToBody: \"tooltipAppendToBody\",\n    tooltipDelay: \"tooltipDelay\",\n    weekStartsOn: \"weekStartsOn\",\n    headerTemplate: \"headerTemplate\",\n    cellTemplate: \"cellTemplate\",\n    openDayEventsTemplate: \"openDayEventsTemplate\",\n    eventTitleTemplate: \"eventTitleTemplate\",\n    eventActionsTemplate: \"eventActionsTemplate\",\n    weekendDays: \"weekendDays\"\n  },\n  outputs: {\n    beforeViewRender: \"beforeViewRender\",\n    dayClicked: \"dayClicked\",\n    eventClicked: \"eventClicked\",\n    columnHeaderClicked: \"columnHeaderClicked\",\n    eventTimesChanged: \"eventTimesChanged\"\n  },\n  standalone: false,\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 4,\n  vars: 5,\n  consts: [[\"role\", \"grid\", 1, \"cal-month-view\"], [3, \"columnHeaderClicked\", \"days\", \"locale\", \"customTemplate\"], [1, \"cal-days\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"role\", \"row\", 1, \"cal-cell-row\"], [\"role\", \"gridcell\", \"mwlDroppable\", \"\", \"dragOverClass\", \"cal-drag-over\", 3, \"ngClass\", \"day\", \"openDay\", \"locale\", \"tooltipPlacement\", \"tooltipAppendToBody\", \"tooltipTemplate\", \"tooltipDelay\", \"customTemplate\", \"ngStyle\", \"clickListenerDisabled\", \"mwlClick\", \"mwlKeydownEnter\", \"highlightDay\", \"unhighlightDay\", \"drop\", \"eventClicked\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"mwlDroppable\", \"\", \"dragOverClass\", \"cal-drag-over\", 3, \"eventClicked\", \"drop\", \"locale\", \"isOpen\", \"events\", \"date\", \"customTemplate\", \"eventTitleTemplate\", \"eventActionsTemplate\"], [\"role\", \"gridcell\", \"mwlDroppable\", \"\", \"dragOverClass\", \"cal-drag-over\", 3, \"mwlClick\", \"mwlKeydownEnter\", \"highlightDay\", \"unhighlightDay\", \"drop\", \"eventClicked\", \"ngClass\", \"day\", \"openDay\", \"locale\", \"tooltipPlacement\", \"tooltipAppendToBody\", \"tooltipTemplate\", \"tooltipDelay\", \"customTemplate\", \"ngStyle\", \"clickListenerDisabled\"]],\n  template: function CalendarMonthViewComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"mwl-calendar-month-view-header\", 1);\n      i0.ɵɵlistener(\"columnHeaderClicked\", function CalendarMonthViewComponent_Template_mwl_calendar_month_view_header_columnHeaderClicked_1_listener($event) {\n        return ctx.columnHeaderClicked.emit($event);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(2, \"div\", 2);\n      i0.ɵɵtemplate(3, CalendarMonthViewComponent_div_3_Template, 5, 13, \"div\", 3);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"days\", ctx.columnHeaders)(\"locale\", ctx.locale)(\"customTemplate\", ctx.headerTemplate);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx.view.rowOffsets)(\"ngForTrackBy\", ctx.trackByRowOffset);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgForOf, i1.NgStyle, i2.DroppableDirective, ClickDirective, KeydownEnterDirective, CalendarMonthCellComponent, CalendarOpenDayEventsComponent, CalendarMonthViewHeaderComponent, i1.SlicePipe, CalendarA11yPipe],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarMonthViewComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mwl-calendar-month-view',\n      template: `\n    <div class=\"cal-month-view\" role=\"grid\">\n      <mwl-calendar-month-view-header\n        [days]=\"columnHeaders\"\n        [locale]=\"locale\"\n        (columnHeaderClicked)=\"columnHeaderClicked.emit($event)\"\n        [customTemplate]=\"headerTemplate\"\n      >\n      </mwl-calendar-month-view-header>\n      <div class=\"cal-days\">\n        <div\n          *ngFor=\"let rowIndex of view.rowOffsets; trackBy: trackByRowOffset\"\n        >\n          <div role=\"row\" class=\"cal-cell-row\">\n            <mwl-calendar-month-cell\n              role=\"gridcell\"\n              *ngFor=\"\n                let day of view.days\n                  | slice : rowIndex : rowIndex + view.totalDaysVisibleInWeek;\n                trackBy: trackByDate\n              \"\n              [ngClass]=\"day?.cssClass\"\n              [day]=\"day\"\n              [openDay]=\"openDay\"\n              [locale]=\"locale\"\n              [tooltipPlacement]=\"tooltipPlacement\"\n              [tooltipAppendToBody]=\"tooltipAppendToBody\"\n              [tooltipTemplate]=\"tooltipTemplate\"\n              [tooltipDelay]=\"tooltipDelay\"\n              [customTemplate]=\"cellTemplate\"\n              [ngStyle]=\"{ backgroundColor: day.backgroundColor }\"\n              (mwlClick)=\"dayClicked.emit({ day: day, sourceEvent: $event })\"\n              [clickListenerDisabled]=\"dayClicked.observers.length === 0\"\n              (mwlKeydownEnter)=\"\n                dayClicked.emit({ day: day, sourceEvent: $event })\n              \"\n              (highlightDay)=\"toggleDayHighlight($event.event, true)\"\n              (unhighlightDay)=\"toggleDayHighlight($event.event, false)\"\n              mwlDroppable\n              dragOverClass=\"cal-drag-over\"\n              (drop)=\"\n                eventDropped(\n                  day,\n                  $event.dropData.event,\n                  $event.dropData.draggedFrom\n                )\n              \"\n              (eventClicked)=\"\n                eventClicked.emit({\n                  event: $event.event,\n                  sourceEvent: $event.sourceEvent\n                })\n              \"\n              [attr.tabindex]=\"{} | calendarA11y : 'monthCellTabIndex'\"\n            >\n            </mwl-calendar-month-cell>\n          </div>\n          <mwl-calendar-open-day-events\n            [locale]=\"locale\"\n            [isOpen]=\"openRowIndex === rowIndex\"\n            [events]=\"openDay?.events\"\n            [date]=\"openDay?.date\"\n            [customTemplate]=\"openDayEventsTemplate\"\n            [eventTitleTemplate]=\"eventTitleTemplate\"\n            [eventActionsTemplate]=\"eventActionsTemplate\"\n            (eventClicked)=\"\n              eventClicked.emit({\n                event: $event.event,\n                sourceEvent: $event.sourceEvent\n              })\n            \"\n            mwlDroppable\n            dragOverClass=\"cal-drag-over\"\n            (drop)=\"\n              eventDropped(\n                openDay,\n                $event.dropData.event,\n                $event.dropData.draggedFrom\n              )\n            \"\n          >\n          </mwl-calendar-open-day-events>\n        </div>\n      </div>\n    </div>\n  `\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: CalendarUtils\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [LOCALE_ID]\n      }]\n    }, {\n      type: DateAdapter\n    }];\n  }, {\n    viewDate: [{\n      type: Input\n    }],\n    events: [{\n      type: Input\n    }],\n    excludeDays: [{\n      type: Input\n    }],\n    activeDayIsOpen: [{\n      type: Input\n    }],\n    activeDay: [{\n      type: Input\n    }],\n    refresh: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }],\n    tooltipPlacement: [{\n      type: Input\n    }],\n    tooltipTemplate: [{\n      type: Input\n    }],\n    tooltipAppendToBody: [{\n      type: Input\n    }],\n    tooltipDelay: [{\n      type: Input\n    }],\n    weekStartsOn: [{\n      type: Input\n    }],\n    headerTemplate: [{\n      type: Input\n    }],\n    cellTemplate: [{\n      type: Input\n    }],\n    openDayEventsTemplate: [{\n      type: Input\n    }],\n    eventTitleTemplate: [{\n      type: Input\n    }],\n    eventActionsTemplate: [{\n      type: Input\n    }],\n    weekendDays: [{\n      type: Input\n    }],\n    beforeViewRender: [{\n      type: Output\n    }],\n    dayClicked: [{\n      type: Output\n    }],\n    eventClicked: [{\n      type: Output\n    }],\n    columnHeaderClicked: [{\n      type: Output\n    }],\n    eventTimesChanged: [{\n      type: Output\n    }]\n  });\n})();\nclass CalendarMonthModule {}\nCalendarMonthModule.ɵfac = function CalendarMonthModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarMonthModule)();\n};\nCalendarMonthModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: CalendarMonthModule,\n  declarations: [CalendarMonthViewComponent, CalendarMonthCellComponent, CalendarOpenDayEventsComponent, CalendarMonthViewHeaderComponent],\n  imports: [CommonModule, DragAndDropModule, CalendarCommonModule],\n  exports: [DragAndDropModule, CalendarMonthViewComponent, CalendarMonthCellComponent, CalendarOpenDayEventsComponent, CalendarMonthViewHeaderComponent]\n});\nCalendarMonthModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, DragAndDropModule, CalendarCommonModule, DragAndDropModule]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarMonthModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, DragAndDropModule, CalendarCommonModule],\n      declarations: [CalendarMonthViewComponent, CalendarMonthCellComponent, CalendarOpenDayEventsComponent, CalendarMonthViewHeaderComponent],\n      exports: [DragAndDropModule, CalendarMonthViewComponent, CalendarMonthCellComponent, CalendarOpenDayEventsComponent, CalendarMonthViewHeaderComponent]\n    }]\n  }], null, null);\n})();\nclass CalendarDragHelper {\n  constructor(dragContainerElement, draggableElement) {\n    this.dragContainerElement = dragContainerElement;\n    this.startPosition = draggableElement.getBoundingClientRect();\n  }\n  validateDrag({\n    x,\n    y,\n    snapDraggedEvents,\n    dragAlreadyMoved,\n    transform\n  }) {\n    const isDraggedWithinThreshold = isWithinThreshold({\n      x,\n      y\n    }) || dragAlreadyMoved;\n    if (snapDraggedEvents) {\n      const inner = Object.assign({}, this.startPosition, {\n        left: this.startPosition.left + transform.x,\n        right: this.startPosition.right + transform.x,\n        top: this.startPosition.top + transform.y,\n        bottom: this.startPosition.bottom + transform.y\n      });\n      if (isDraggedWithinThreshold) {\n        const outer = this.dragContainerElement.getBoundingClientRect();\n        const isTopInside = outer.top < inner.top && inner.top < outer.bottom;\n        const isBottomInside = outer.top < inner.bottom && inner.bottom < outer.bottom;\n        return isInsideLeftAndRight(outer, inner) && (isTopInside || isBottomInside);\n      }\n      /* istanbul ignore next */\n      return false;\n    } else {\n      return isDraggedWithinThreshold;\n    }\n  }\n}\nclass CalendarResizeHelper {\n  constructor(resizeContainerElement, minWidth, rtl) {\n    this.resizeContainerElement = resizeContainerElement;\n    this.minWidth = minWidth;\n    this.rtl = rtl;\n  }\n  validateResize({\n    rectangle,\n    edges\n  }) {\n    if (this.rtl) {\n      // TODO - find a way of testing this, for some reason the tests always fail but it does actually work\n      /* istanbul ignore next */\n      if (typeof edges.left !== 'undefined') {\n        rectangle.left -= edges.left;\n        rectangle.right += edges.left;\n      } else if (typeof edges.right !== 'undefined') {\n        rectangle.left += edges.right;\n        rectangle.right -= edges.right;\n      }\n      rectangle.width = rectangle.right - rectangle.left;\n    }\n    if (this.minWidth && Math.ceil(rectangle.width) < Math.ceil(this.minWidth)) {\n      return false;\n    }\n    return isInside(this.resizeContainerElement.getBoundingClientRect(), rectangle);\n  }\n}\nclass CalendarWeekViewHeaderComponent {\n  constructor() {\n    this.dayHeaderClicked = new EventEmitter();\n    this.eventDropped = new EventEmitter();\n    this.dragEnter = new EventEmitter();\n    this.trackByWeekDayHeaderDate = trackByWeekDayHeaderDate;\n  }\n}\nCalendarWeekViewHeaderComponent.ɵfac = function CalendarWeekViewHeaderComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarWeekViewHeaderComponent)();\n};\nCalendarWeekViewHeaderComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: CalendarWeekViewHeaderComponent,\n  selectors: [[\"mwl-calendar-week-view-header\"]],\n  inputs: {\n    days: \"days\",\n    locale: \"locale\",\n    customTemplate: \"customTemplate\"\n  },\n  outputs: {\n    dayHeaderClicked: \"dayHeaderClicked\",\n    eventDropped: \"eventDropped\",\n    dragEnter: \"dragEnter\"\n  },\n  standalone: false,\n  decls: 3,\n  vars: 9,\n  consts: [[\"defaultTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"role\", \"row\", 1, \"cal-day-headers\"], [\"class\", \"cal-header\", \"mwlDroppable\", \"\", \"dragOverClass\", \"cal-drag-over\", \"tabindex\", \"0\", \"role\", \"columnheader\", 3, \"cal-past\", \"cal-today\", \"cal-future\", \"cal-weekend\", \"ngClass\", \"mwlClick\", \"drop\", \"dragEnter\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"mwlDroppable\", \"\", \"dragOverClass\", \"cal-drag-over\", \"tabindex\", \"0\", \"role\", \"columnheader\", 1, \"cal-header\", 3, \"mwlClick\", \"drop\", \"dragEnter\", \"ngClass\"]],\n  template: function CalendarWeekViewHeaderComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, CalendarWeekViewHeaderComponent_ng_template_0_Template, 2, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, CalendarWeekViewHeaderComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n    }\n    if (rf & 2) {\n      const defaultTemplate_r9 = i0.ɵɵreference(1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.customTemplate || defaultTemplate_r9)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction6(2, _c16, ctx.days, ctx.locale, ctx.dayHeaderClicked, ctx.eventDropped, ctx.dragEnter, ctx.trackByWeekDayHeaderDate));\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgForOf, i1.NgTemplateOutlet, i2.DroppableDirective, ClickDirective, CalendarDatePipe],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarWeekViewHeaderComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mwl-calendar-week-view-header',\n      template: `\n    <ng-template\n      #defaultTemplate\n      let-days=\"days\"\n      let-locale=\"locale\"\n      let-dayHeaderClicked=\"dayHeaderClicked\"\n      let-eventDropped=\"eventDropped\"\n      let-trackByWeekDayHeaderDate=\"trackByWeekDayHeaderDate\"\n      let-dragEnter=\"dragEnter\"\n    >\n      <div class=\"cal-day-headers\" role=\"row\">\n        <div\n          class=\"cal-header\"\n          *ngFor=\"let day of days; trackBy: trackByWeekDayHeaderDate\"\n          [class.cal-past]=\"day.isPast\"\n          [class.cal-today]=\"day.isToday\"\n          [class.cal-future]=\"day.isFuture\"\n          [class.cal-weekend]=\"day.isWeekend\"\n          [ngClass]=\"day.cssClass\"\n          (mwlClick)=\"dayHeaderClicked.emit({ day: day, sourceEvent: $event })\"\n          mwlDroppable\n          dragOverClass=\"cal-drag-over\"\n          (drop)=\"\n            eventDropped.emit({\n              event: $event.dropData.event,\n              newStart: day.date\n            })\n          \"\n          (dragEnter)=\"dragEnter.emit({ date: day.date })\"\n          tabindex=\"0\"\n          role=\"columnheader\"\n        >\n          <b>{{ day.date | calendarDate : 'weekViewColumnHeader' : locale }}</b\n          ><br />\n          <span>{{\n            day.date | calendarDate : 'weekViewColumnSubHeader' : locale\n          }}</span>\n        </div>\n      </div>\n    </ng-template>\n    <ng-template\n      [ngTemplateOutlet]=\"customTemplate || defaultTemplate\"\n      [ngTemplateOutletContext]=\"{\n        days: days,\n        locale: locale,\n        dayHeaderClicked: dayHeaderClicked,\n        eventDropped: eventDropped,\n        dragEnter: dragEnter,\n        trackByWeekDayHeaderDate: trackByWeekDayHeaderDate\n      }\"\n    >\n    </ng-template>\n  `\n    }]\n  }], null, {\n    days: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }],\n    customTemplate: [{\n      type: Input\n    }],\n    dayHeaderClicked: [{\n      type: Output\n    }],\n    eventDropped: [{\n      type: Output\n    }],\n    dragEnter: [{\n      type: Output\n    }]\n  });\n})();\nclass CalendarWeekViewEventComponent {\n  constructor() {\n    this.eventClicked = new EventEmitter();\n  }\n}\nCalendarWeekViewEventComponent.ɵfac = function CalendarWeekViewEventComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarWeekViewEventComponent)();\n};\nCalendarWeekViewEventComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: CalendarWeekViewEventComponent,\n  selectors: [[\"mwl-calendar-week-view-event\"]],\n  inputs: {\n    locale: \"locale\",\n    weekEvent: \"weekEvent\",\n    tooltipPlacement: \"tooltipPlacement\",\n    tooltipAppendToBody: \"tooltipAppendToBody\",\n    tooltipDisabled: \"tooltipDisabled\",\n    tooltipDelay: \"tooltipDelay\",\n    customTemplate: \"customTemplate\",\n    eventTitleTemplate: \"eventTitleTemplate\",\n    eventActionsTemplate: \"eventActionsTemplate\",\n    tooltipTemplate: \"tooltipTemplate\",\n    column: \"column\",\n    daysInWeek: \"daysInWeek\"\n  },\n  outputs: {\n    eventClicked: \"eventClicked\"\n  },\n  standalone: false,\n  decls: 3,\n  vars: 12,\n  consts: [[\"defaultTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"tabindex\", \"0\", \"role\", \"application\", 1, \"cal-event\", 3, \"mwlClick\", \"mwlKeydownEnter\", \"ngStyle\", \"mwlCalendarTooltip\", \"tooltipPlacement\", \"tooltipEvent\", \"tooltipTemplate\", \"tooltipAppendToBody\", \"tooltipDelay\"], [3, \"event\", \"customTemplate\"], [3, \"event\", \"customTemplate\", \"view\"]],\n  template: function CalendarWeekViewEventComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, CalendarWeekViewEventComponent_ng_template_0_Template, 6, 27, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, CalendarWeekViewEventComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n    }\n    if (rf & 2) {\n      const defaultTemplate_r11 = i0.ɵɵreference(1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.customTemplate || defaultTemplate_r11)(\"ngTemplateOutletContext\", i0.ɵɵpureFunctionV(2, _c17, [ctx.weekEvent, ctx.tooltipPlacement, ctx.eventClicked, ctx.tooltipTemplate, ctx.tooltipAppendToBody, ctx.tooltipDisabled, ctx.tooltipDelay, ctx.column, ctx.daysInWeek]));\n    }\n  },\n  dependencies: [i1.NgTemplateOutlet, i1.NgStyle, CalendarEventActionsComponent, CalendarEventTitleComponent, CalendarTooltipDirective, ClickDirective, KeydownEnterDirective, CalendarEventTitlePipe, CalendarA11yPipe],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarWeekViewEventComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mwl-calendar-week-view-event',\n      template: `\n    <ng-template\n      #defaultTemplate\n      let-weekEvent=\"weekEvent\"\n      let-tooltipPlacement=\"tooltipPlacement\"\n      let-eventClicked=\"eventClicked\"\n      let-tooltipTemplate=\"tooltipTemplate\"\n      let-tooltipAppendToBody=\"tooltipAppendToBody\"\n      let-tooltipDisabled=\"tooltipDisabled\"\n      let-tooltipDelay=\"tooltipDelay\"\n      let-column=\"column\"\n      let-daysInWeek=\"daysInWeek\"\n    >\n      <div\n        class=\"cal-event\"\n        [ngStyle]=\"{\n          color: weekEvent.event.color?.secondaryText,\n          backgroundColor: weekEvent.event.color?.secondary,\n          borderColor: weekEvent.event.color?.primary\n        }\"\n        [mwlCalendarTooltip]=\"\n          !tooltipDisabled\n            ? (weekEvent.event.title\n              | calendarEventTitle\n                : (daysInWeek === 1 ? 'dayTooltip' : 'weekTooltip')\n                : weekEvent.tempEvent || weekEvent.event)\n            : ''\n        \"\n        [tooltipPlacement]=\"tooltipPlacement\"\n        [tooltipEvent]=\"weekEvent.tempEvent || weekEvent.event\"\n        [tooltipTemplate]=\"tooltipTemplate\"\n        [tooltipAppendToBody]=\"tooltipAppendToBody\"\n        [tooltipDelay]=\"tooltipDelay\"\n        (mwlClick)=\"eventClicked.emit({ sourceEvent: $event })\"\n        (mwlKeydownEnter)=\"eventClicked.emit({ sourceEvent: $event })\"\n        tabindex=\"0\"\n        role=\"application\"\n        [attr.aria-label]=\"\n          { event: weekEvent.tempEvent || weekEvent.event, locale: locale }\n            | calendarA11y : 'eventDescription'\n        \"\n      >\n        <mwl-calendar-event-actions\n          [event]=\"weekEvent.tempEvent || weekEvent.event\"\n          [customTemplate]=\"eventActionsTemplate\"\n        >\n        </mwl-calendar-event-actions>\n        &ngsp;\n        <mwl-calendar-event-title\n          [event]=\"weekEvent.tempEvent || weekEvent.event\"\n          [customTemplate]=\"eventTitleTemplate\"\n          [view]=\"daysInWeek === 1 ? 'day' : 'week'\"\n        >\n        </mwl-calendar-event-title>\n      </div>\n    </ng-template>\n    <ng-template\n      [ngTemplateOutlet]=\"customTemplate || defaultTemplate\"\n      [ngTemplateOutletContext]=\"{\n        weekEvent: weekEvent,\n        tooltipPlacement: tooltipPlacement,\n        eventClicked: eventClicked,\n        tooltipTemplate: tooltipTemplate,\n        tooltipAppendToBody: tooltipAppendToBody,\n        tooltipDisabled: tooltipDisabled,\n        tooltipDelay: tooltipDelay,\n        column: column,\n        daysInWeek: daysInWeek\n      }\"\n    >\n    </ng-template>\n  `\n    }]\n  }], null, {\n    locale: [{\n      type: Input\n    }],\n    weekEvent: [{\n      type: Input\n    }],\n    tooltipPlacement: [{\n      type: Input\n    }],\n    tooltipAppendToBody: [{\n      type: Input\n    }],\n    tooltipDisabled: [{\n      type: Input\n    }],\n    tooltipDelay: [{\n      type: Input\n    }],\n    customTemplate: [{\n      type: Input\n    }],\n    eventTitleTemplate: [{\n      type: Input\n    }],\n    eventActionsTemplate: [{\n      type: Input\n    }],\n    tooltipTemplate: [{\n      type: Input\n    }],\n    column: [{\n      type: Input\n    }],\n    daysInWeek: [{\n      type: Input\n    }],\n    eventClicked: [{\n      type: Output\n    }]\n  });\n})();\nclass CalendarWeekViewHourSegmentComponent {}\nCalendarWeekViewHourSegmentComponent.ɵfac = function CalendarWeekViewHourSegmentComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarWeekViewHourSegmentComponent)();\n};\nCalendarWeekViewHourSegmentComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: CalendarWeekViewHourSegmentComponent,\n  selectors: [[\"mwl-calendar-week-view-hour-segment\"]],\n  inputs: {\n    segment: \"segment\",\n    segmentHeight: \"segmentHeight\",\n    locale: \"locale\",\n    isTimeLabel: \"isTimeLabel\",\n    daysInWeek: \"daysInWeek\",\n    customTemplate: \"customTemplate\"\n  },\n  standalone: false,\n  decls: 3,\n  vars: 8,\n  consts: [[\"defaultTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"cal-hour-segment\", 3, \"ngClass\"], [\"class\", \"cal-time\", 4, \"ngIf\"], [1, \"cal-time\"]],\n  template: function CalendarWeekViewHourSegmentComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, CalendarWeekViewHourSegmentComponent_ng_template_0_Template, 3, 13, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, CalendarWeekViewHourSegmentComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n    }\n    if (rf & 2) {\n      const defaultTemplate_r7 = i0.ɵɵreference(1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.customTemplate || defaultTemplate_r7)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction5(2, _c19, ctx.segment, ctx.locale, ctx.segmentHeight, ctx.isTimeLabel, ctx.daysInWeek));\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, CalendarDatePipe, CalendarA11yPipe],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarWeekViewHourSegmentComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mwl-calendar-week-view-hour-segment',\n      template: `\n    <ng-template\n      #defaultTemplate\n      let-segment=\"segment\"\n      let-locale=\"locale\"\n      let-segmentHeight=\"segmentHeight\"\n      let-isTimeLabel=\"isTimeLabel\"\n      let-daysInWeek=\"daysInWeek\"\n    >\n      <div\n        [attr.aria-hidden]=\"\n          {}\n            | calendarA11y\n              : (daysInWeek === 1\n                  ? 'hideDayHourSegment'\n                  : 'hideWeekHourSegment')\n        \"\n        class=\"cal-hour-segment\"\n        [style.height.px]=\"segmentHeight\"\n        [class.cal-hour-start]=\"segment.isStart\"\n        [class.cal-after-hour-start]=\"!segment.isStart\"\n        [ngClass]=\"segment.cssClass\"\n      >\n        <div class=\"cal-time\" *ngIf=\"isTimeLabel\">\n          {{\n            segment.displayDate\n              | calendarDate\n                : (daysInWeek === 1 ? 'dayViewHour' : 'weekViewHour')\n                : locale\n          }}\n        </div>\n      </div>\n    </ng-template>\n    <ng-template\n      [ngTemplateOutlet]=\"customTemplate || defaultTemplate\"\n      [ngTemplateOutletContext]=\"{\n        segment: segment,\n        locale: locale,\n        segmentHeight: segmentHeight,\n        isTimeLabel: isTimeLabel,\n        daysInWeek: daysInWeek\n      }\"\n    >\n    </ng-template>\n  `\n    }]\n  }], null, {\n    segment: [{\n      type: Input\n    }],\n    segmentHeight: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }],\n    isTimeLabel: [{\n      type: Input\n    }],\n    daysInWeek: [{\n      type: Input\n    }],\n    customTemplate: [{\n      type: Input\n    }]\n  });\n})();\nclass CalendarWeekViewCurrentTimeMarkerComponent {\n  constructor(dateAdapter, zone) {\n    this.dateAdapter = dateAdapter;\n    this.zone = zone;\n    this.columnDate$ = new BehaviorSubject(undefined);\n    this.marker$ = this.zone.onStable.pipe(switchMap(() => interval(60 * 1000)), startWith(0), switchMapTo(this.columnDate$), map(columnDate => {\n      const startOfDay = this.dateAdapter.setMinutes(this.dateAdapter.setHours(columnDate, this.dayStartHour), this.dayStartMinute);\n      const endOfDay = this.dateAdapter.setMinutes(this.dateAdapter.setHours(columnDate, this.dayEndHour), this.dayEndMinute);\n      const hourHeightModifier = this.hourSegments * this.hourSegmentHeight / (this.hourDuration || 60);\n      const now = new Date();\n      return {\n        isVisible: this.dateAdapter.isSameDay(columnDate, now) && now >= startOfDay && now <= endOfDay,\n        top: this.dateAdapter.differenceInMinutes(now, startOfDay) * hourHeightModifier\n      };\n    }));\n  }\n  ngOnChanges(changes) {\n    if (changes.columnDate) {\n      this.columnDate$.next(changes.columnDate.currentValue);\n    }\n  }\n}\nCalendarWeekViewCurrentTimeMarkerComponent.ɵfac = function CalendarWeekViewCurrentTimeMarkerComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarWeekViewCurrentTimeMarkerComponent)(i0.ɵɵdirectiveInject(DateAdapter), i0.ɵɵdirectiveInject(i0.NgZone));\n};\nCalendarWeekViewCurrentTimeMarkerComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: CalendarWeekViewCurrentTimeMarkerComponent,\n  selectors: [[\"mwl-calendar-week-view-current-time-marker\"]],\n  inputs: {\n    columnDate: \"columnDate\",\n    dayStartHour: \"dayStartHour\",\n    dayStartMinute: \"dayStartMinute\",\n    dayEndHour: \"dayEndHour\",\n    dayEndMinute: \"dayEndMinute\",\n    hourSegments: \"hourSegments\",\n    hourDuration: \"hourDuration\",\n    hourSegmentHeight: \"hourSegmentHeight\",\n    customTemplate: \"customTemplate\"\n  },\n  standalone: false,\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 5,\n  vars: 14,\n  consts: [[\"defaultTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"cal-current-time-marker\", 3, \"top\", 4, \"ngIf\"], [1, \"cal-current-time-marker\"]],\n  template: function CalendarWeekViewCurrentTimeMarkerComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, CalendarWeekViewCurrentTimeMarkerComponent_ng_template_0_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, CalendarWeekViewCurrentTimeMarkerComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n      i0.ɵɵpipe(3, \"async\");\n      i0.ɵɵpipe(4, \"async\");\n    }\n    if (rf & 2) {\n      let tmp_2_0;\n      const defaultTemplate_r3 = i0.ɵɵreference(1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.customTemplate || defaultTemplate_r3)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction7(6, _c20, ctx.columnDate, ctx.dayStartHour, ctx.dayStartMinute, ctx.dayEndHour, ctx.dayEndMinute, (tmp_2_0 = i0.ɵɵpipeBind1(3, 2, ctx.marker$)) == null ? null : tmp_2_0.isVisible, (tmp_2_0 = i0.ɵɵpipeBind1(4, 4, ctx.marker$)) == null ? null : tmp_2_0.top));\n    }\n  },\n  dependencies: [i1.NgIf, i1.NgTemplateOutlet, i1.AsyncPipe],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarWeekViewCurrentTimeMarkerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mwl-calendar-week-view-current-time-marker',\n      template: `\n    <ng-template\n      #defaultTemplate\n      let-columnDate=\"columnDate\"\n      let-dayStartHour=\"dayStartHour\"\n      let-dayStartMinute=\"dayStartMinute\"\n      let-dayEndHour=\"dayEndHour\"\n      let-dayEndMinute=\"dayEndMinute\"\n      let-isVisible=\"isVisible\"\n      let-topPx=\"topPx\"\n    >\n      <div\n        class=\"cal-current-time-marker\"\n        *ngIf=\"isVisible\"\n        [style.top.px]=\"topPx\"\n      ></div>\n    </ng-template>\n    <ng-template\n      [ngTemplateOutlet]=\"customTemplate || defaultTemplate\"\n      [ngTemplateOutletContext]=\"{\n        columnDate: columnDate,\n        dayStartHour: dayStartHour,\n        dayStartMinute: dayStartMinute,\n        dayEndHour: dayEndHour,\n        dayEndMinute: dayEndMinute,\n        isVisible: (marker$ | async)?.isVisible,\n        topPx: (marker$ | async)?.top\n      }\"\n    >\n    </ng-template>\n  `\n    }]\n  }], function () {\n    return [{\n      type: DateAdapter\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    columnDate: [{\n      type: Input\n    }],\n    dayStartHour: [{\n      type: Input\n    }],\n    dayStartMinute: [{\n      type: Input\n    }],\n    dayEndHour: [{\n      type: Input\n    }],\n    dayEndMinute: [{\n      type: Input\n    }],\n    hourSegments: [{\n      type: Input\n    }],\n    hourDuration: [{\n      type: Input\n    }],\n    hourSegmentHeight: [{\n      type: Input\n    }],\n    customTemplate: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Shows all events on a given week. Example usage:\n *\n * ```typescript\n * <mwl-calendar-week-view\n *  [viewDate]=\"viewDate\"\n *  [events]=\"events\">\n * </mwl-calendar-week-view>\n * ```\n */\nclass CalendarWeekViewComponent {\n  /**\n   * @hidden\n   */\n  constructor(cdr, utils, locale, dateAdapter, element) {\n    this.cdr = cdr;\n    this.utils = utils;\n    this.dateAdapter = dateAdapter;\n    this.element = element;\n    /**\n     * An array of events to display on view\n     * The schema is available here: https://github.com/mattlewis92/calendar-utils/blob/c51689985f59a271940e30bc4e2c4e1fee3fcb5c/src/calendarUtils.ts#L49-L63\n     */\n    this.events = [];\n    /**\n     * An array of day indexes (0 = sunday, 1 = monday etc) that will be hidden on the view\n     */\n    this.excludeDays = [];\n    /**\n     * The placement of the event tooltip\n     */\n    this.tooltipPlacement = 'auto';\n    /**\n     * Whether to append tooltips to the body or next to the trigger element\n     */\n    this.tooltipAppendToBody = true;\n    /**\n     * The delay in milliseconds before the tooltip should be displayed. If not provided the tooltip\n     * will be displayed immediately.\n     */\n    this.tooltipDelay = null;\n    /**\n     * The precision to display events.\n     * `days` will round event start and end dates to the nearest day and `minutes` will not do this rounding\n     */\n    this.precision = 'days';\n    /**\n     * Whether to snap events to a grid when dragging\n     */\n    this.snapDraggedEvents = true;\n    /**\n     * The number of segments in an hour. Must divide equally into 60.\n     */\n    this.hourSegments = 2;\n    /**\n     * The height in pixels of each hour segment\n     */\n    this.hourSegmentHeight = 30;\n    /**\n     * The minimum height in pixels of each event\n     */\n    this.minimumEventHeight = 30;\n    /**\n     * The day start hours in 24 hour time. Must be 0-23\n     */\n    this.dayStartHour = 0;\n    /**\n     * The day start minutes. Must be 0-59\n     */\n    this.dayStartMinute = 0;\n    /**\n     * The day end hours in 24 hour time. Must be 0-23\n     */\n    this.dayEndHour = 23;\n    /**\n     * The day end minutes. Must be 0-59\n     */\n    this.dayEndMinute = 59;\n    /**\n     * Called when a header week day is clicked. Adding a `cssClass` property on `$event.day` will add that class to the header element\n     */\n    this.dayHeaderClicked = new EventEmitter();\n    /**\n     * Called when an event title is clicked\n     */\n    this.eventClicked = new EventEmitter();\n    /**\n     * Called when an event is resized or dragged and dropped\n     */\n    this.eventTimesChanged = new EventEmitter();\n    /**\n     * An output that will be called before the view is rendered for the current week.\n     * If you add the `cssClass` property to a day in the header it will add that class to the cell element in the template\n     */\n    this.beforeViewRender = new EventEmitter();\n    /**\n     * Called when an hour segment is clicked\n     */\n    this.hourSegmentClicked = new EventEmitter();\n    /**\n     * @hidden\n     */\n    this.allDayEventResizes = new Map();\n    /**\n     * @hidden\n     */\n    this.timeEventResizes = new Map();\n    /**\n     * @hidden\n     */\n    this.eventDragEnterByType = {\n      allDay: 0,\n      time: 0\n    };\n    /**\n     * @hidden\n     */\n    this.dragActive = false;\n    /**\n     * @hidden\n     */\n    this.dragAlreadyMoved = false;\n    /**\n     * @hidden\n     */\n    this.calendarId = Symbol('angular calendar week view id');\n    /**\n     * @hidden\n     */\n    this.rtl = false;\n    /**\n     * @hidden\n     */\n    this.trackByWeekDayHeaderDate = trackByWeekDayHeaderDate;\n    /**\n     * @hidden\n     */\n    this.trackByHourSegment = trackByHourSegment;\n    /**\n     * @hidden\n     */\n    this.trackByHour = trackByHour;\n    /**\n     * @hidden\n     */\n    this.trackByWeekAllDayEvent = trackByWeekAllDayEvent;\n    /**\n     * @hidden\n     */\n    this.trackByWeekTimeEvent = trackByWeekTimeEvent;\n    /**\n     * @hidden\n     */\n    this.trackByHourColumn = (index, column) => column.hours[0] ? column.hours[0].segments[0].date.toISOString() : column;\n    /**\n     * @hidden\n     */\n    this.trackById = (index, row) => row.id;\n    this.locale = locale;\n  }\n  /**\n   * @hidden\n   */\n  ngOnInit() {\n    if (this.refresh) {\n      this.refreshSubscription = this.refresh.subscribe(() => {\n        this.refreshAll();\n        this.cdr.markForCheck();\n      });\n    }\n  }\n  /**\n   * @hidden\n   */\n  ngOnChanges(changes) {\n    const refreshHeader = changes.viewDate || changes.excludeDays || changes.weekendDays || changes.daysInWeek || changes.weekStartsOn;\n    const refreshBody = changes.viewDate || changes.dayStartHour || changes.dayStartMinute || changes.dayEndHour || changes.dayEndMinute || changes.hourSegments || changes.hourDuration || changes.weekStartsOn || changes.weekendDays || changes.excludeDays || changes.hourSegmentHeight || changes.events || changes.daysInWeek || changes.minimumEventHeight;\n    if (refreshHeader) {\n      this.refreshHeader();\n    }\n    if (changes.events) {\n      validateEvents(this.events);\n    }\n    if (refreshBody) {\n      this.refreshBody();\n    }\n    if (refreshHeader || refreshBody) {\n      this.emitBeforeViewRender();\n    }\n  }\n  /**\n   * @hidden\n   */\n  ngOnDestroy() {\n    if (this.refreshSubscription) {\n      this.refreshSubscription.unsubscribe();\n    }\n  }\n  /**\n   * @hidden\n   */\n  ngAfterViewInit() {\n    this.rtl = typeof window !== 'undefined' && getComputedStyle(this.element.nativeElement).direction === 'rtl';\n    this.cdr.detectChanges();\n  }\n  /**\n   * @hidden\n   */\n  timeEventResizeStarted(eventsContainer, timeEvent, resizeEvent) {\n    this.timeEventResizes.set(timeEvent.event, resizeEvent);\n    this.resizeStarted(eventsContainer, timeEvent);\n  }\n  /**\n   * @hidden\n   */\n  timeEventResizing(timeEvent, resizeEvent) {\n    this.timeEventResizes.set(timeEvent.event, resizeEvent);\n    const adjustedEvents = new Map();\n    const tempEvents = [...this.events];\n    this.timeEventResizes.forEach((lastResizeEvent, event) => {\n      const newEventDates = this.getTimeEventResizedDates(event, lastResizeEvent);\n      const adjustedEvent = {\n        ...event,\n        ...newEventDates\n      };\n      adjustedEvents.set(adjustedEvent, event);\n      const eventIndex = tempEvents.indexOf(event);\n      tempEvents[eventIndex] = adjustedEvent;\n    });\n    this.restoreOriginalEvents(tempEvents, adjustedEvents, true);\n  }\n  /**\n   * @hidden\n   */\n  timeEventResizeEnded(timeEvent) {\n    this.view = this.getWeekView(this.events);\n    const lastResizeEvent = this.timeEventResizes.get(timeEvent.event);\n    if (lastResizeEvent) {\n      this.timeEventResizes.delete(timeEvent.event);\n      const newEventDates = this.getTimeEventResizedDates(timeEvent.event, lastResizeEvent);\n      this.eventTimesChanged.emit({\n        newStart: newEventDates.start,\n        newEnd: newEventDates.end,\n        event: timeEvent.event,\n        type: CalendarEventTimesChangedEventType.Resize\n      });\n    }\n  }\n  /**\n   * @hidden\n   */\n  allDayEventResizeStarted(allDayEventsContainer, allDayEvent, resizeEvent) {\n    this.allDayEventResizes.set(allDayEvent, {\n      originalOffset: allDayEvent.offset,\n      originalSpan: allDayEvent.span,\n      edge: typeof resizeEvent.edges.left !== 'undefined' ? 'left' : 'right'\n    });\n    this.resizeStarted(allDayEventsContainer, allDayEvent, this.getDayColumnWidth(allDayEventsContainer));\n  }\n  /**\n   * @hidden\n   */\n  allDayEventResizing(allDayEvent, resizeEvent, dayWidth) {\n    const currentResize = this.allDayEventResizes.get(allDayEvent);\n    const modifier = this.rtl ? -1 : 1;\n    if (typeof resizeEvent.edges.left !== 'undefined') {\n      const diff = Math.round(+resizeEvent.edges.left / dayWidth) * modifier;\n      allDayEvent.offset = currentResize.originalOffset + diff;\n      allDayEvent.span = currentResize.originalSpan - diff;\n    } else if (typeof resizeEvent.edges.right !== 'undefined') {\n      const diff = Math.round(+resizeEvent.edges.right / dayWidth) * modifier;\n      allDayEvent.span = currentResize.originalSpan + diff;\n    }\n  }\n  /**\n   * @hidden\n   */\n  allDayEventResizeEnded(allDayEvent) {\n    const currentResize = this.allDayEventResizes.get(allDayEvent);\n    if (currentResize) {\n      const allDayEventResizingBeforeStart = currentResize.edge === 'left';\n      let daysDiff;\n      if (allDayEventResizingBeforeStart) {\n        daysDiff = allDayEvent.offset - currentResize.originalOffset;\n      } else {\n        daysDiff = allDayEvent.span - currentResize.originalSpan;\n      }\n      allDayEvent.offset = currentResize.originalOffset;\n      allDayEvent.span = currentResize.originalSpan;\n      const newDates = this.getAllDayEventResizedDates(allDayEvent.event, daysDiff, allDayEventResizingBeforeStart);\n      this.eventTimesChanged.emit({\n        newStart: newDates.start,\n        newEnd: newDates.end,\n        event: allDayEvent.event,\n        type: CalendarEventTimesChangedEventType.Resize\n      });\n      this.allDayEventResizes.delete(allDayEvent);\n    }\n  }\n  /**\n   * @hidden\n   */\n  getDayColumnWidth(eventRowContainer) {\n    return Math.floor(eventRowContainer.offsetWidth / this.days.length);\n  }\n  /**\n   * @hidden\n   */\n  dateDragEnter(date) {\n    this.lastDragEnterDate = date;\n  }\n  /**\n   * @hidden\n   */\n  eventDropped(dropEvent, date, allDay) {\n    if (shouldFireDroppedEvent(dropEvent, date, allDay, this.calendarId) && this.lastDragEnterDate.getTime() === date.getTime() && (!this.snapDraggedEvents || dropEvent.dropData.event !== this.lastDraggedEvent)) {\n      this.eventTimesChanged.emit({\n        type: CalendarEventTimesChangedEventType.Drop,\n        event: dropEvent.dropData.event,\n        newStart: date,\n        allDay\n      });\n    }\n    this.lastDraggedEvent = null;\n  }\n  /**\n   * @hidden\n   */\n  dragEnter(type) {\n    this.eventDragEnterByType[type]++;\n  }\n  /**\n   * @hidden\n   */\n  dragLeave(type) {\n    this.eventDragEnterByType[type]--;\n  }\n  /**\n   * @hidden\n   */\n  dragStarted(eventsContainerElement, eventElement, event, useY) {\n    this.dayColumnWidth = this.getDayColumnWidth(eventsContainerElement);\n    const dragHelper = new CalendarDragHelper(eventsContainerElement, eventElement);\n    this.validateDrag = ({\n      x,\n      y,\n      transform\n    }) => {\n      const isAllowed = this.allDayEventResizes.size === 0 && this.timeEventResizes.size === 0 && dragHelper.validateDrag({\n        x,\n        y,\n        snapDraggedEvents: this.snapDraggedEvents,\n        dragAlreadyMoved: this.dragAlreadyMoved,\n        transform\n      });\n      if (isAllowed && this.validateEventTimesChanged) {\n        const newEventTimes = this.getDragMovedEventTimes(event, {\n          x,\n          y\n        }, this.dayColumnWidth, useY);\n        return this.validateEventTimesChanged({\n          type: CalendarEventTimesChangedEventType.Drag,\n          event: event.event,\n          newStart: newEventTimes.start,\n          newEnd: newEventTimes.end\n        });\n      }\n      return isAllowed;\n    };\n    this.dragActive = true;\n    this.dragAlreadyMoved = false;\n    this.lastDraggedEvent = null;\n    this.eventDragEnterByType = {\n      allDay: 0,\n      time: 0\n    };\n    if (!this.snapDraggedEvents && useY) {\n      this.view.hourColumns.forEach(column => {\n        const linkedEvent = column.events.find(columnEvent => columnEvent.event === event.event && columnEvent !== event);\n        // hide any linked events while dragging\n        if (linkedEvent) {\n          linkedEvent.width = 0;\n          linkedEvent.height = 0;\n        }\n      });\n    }\n    this.cdr.markForCheck();\n  }\n  /**\n   * @hidden\n   */\n  dragMove(dayEvent, dragEvent) {\n    const newEventTimes = this.getDragMovedEventTimes(dayEvent, dragEvent, this.dayColumnWidth, true);\n    const originalEvent = dayEvent.event;\n    const adjustedEvent = {\n      ...originalEvent,\n      ...newEventTimes\n    };\n    const tempEvents = this.events.map(event => {\n      if (event === originalEvent) {\n        return adjustedEvent;\n      }\n      return event;\n    });\n    this.restoreOriginalEvents(tempEvents, new Map([[adjustedEvent, originalEvent]]), this.snapDraggedEvents);\n    this.dragAlreadyMoved = true;\n  }\n  /**\n   * @hidden\n   */\n  allDayEventDragMove() {\n    this.dragAlreadyMoved = true;\n  }\n  /**\n   * @hidden\n   */\n  dragEnded(weekEvent, dragEndEvent, dayWidth, useY = false) {\n    this.view = this.getWeekView(this.events);\n    this.dragActive = false;\n    this.validateDrag = null;\n    const {\n      start,\n      end\n    } = this.getDragMovedEventTimes(weekEvent, dragEndEvent, dayWidth, useY);\n    if ((this.snapDraggedEvents || this.eventDragEnterByType[useY ? 'time' : 'allDay'] > 0) && isDraggedWithinPeriod(start, end, this.view.period)) {\n      this.lastDraggedEvent = weekEvent.event;\n      this.eventTimesChanged.emit({\n        newStart: start,\n        newEnd: end,\n        event: weekEvent.event,\n        type: CalendarEventTimesChangedEventType.Drag,\n        allDay: !useY\n      });\n    }\n  }\n  refreshHeader() {\n    this.days = this.utils.getWeekViewHeader({\n      viewDate: this.viewDate,\n      weekStartsOn: this.weekStartsOn,\n      excluded: this.excludeDays,\n      weekendDays: this.weekendDays,\n      ...getWeekViewPeriod(this.dateAdapter, this.viewDate, this.weekStartsOn, this.excludeDays, this.daysInWeek)\n    });\n  }\n  refreshBody() {\n    this.view = this.getWeekView(this.events);\n  }\n  refreshAll() {\n    this.refreshHeader();\n    this.refreshBody();\n    this.emitBeforeViewRender();\n  }\n  emitBeforeViewRender() {\n    if (this.days && this.view) {\n      this.beforeViewRender.emit({\n        header: this.days,\n        ...this.view\n      });\n    }\n  }\n  getWeekView(events) {\n    return this.utils.getWeekView({\n      events,\n      viewDate: this.viewDate,\n      weekStartsOn: this.weekStartsOn,\n      excluded: this.excludeDays,\n      precision: this.precision,\n      absolutePositionedEvents: true,\n      hourSegments: this.hourSegments,\n      hourDuration: this.hourDuration,\n      dayStart: {\n        hour: this.dayStartHour,\n        minute: this.dayStartMinute\n      },\n      dayEnd: {\n        hour: this.dayEndHour,\n        minute: this.dayEndMinute\n      },\n      segmentHeight: this.hourSegmentHeight,\n      weekendDays: this.weekendDays,\n      minimumEventHeight: this.minimumEventHeight,\n      ...getWeekViewPeriod(this.dateAdapter, this.viewDate, this.weekStartsOn, this.excludeDays, this.daysInWeek)\n    });\n  }\n  getDragMovedEventTimes(weekEvent, dragEndEvent, dayWidth, useY) {\n    const daysDragged = roundToNearest(dragEndEvent.x, dayWidth) / dayWidth * (this.rtl ? -1 : 1);\n    const minutesMoved = useY ? getMinutesMoved(dragEndEvent.y, this.hourSegments, this.hourSegmentHeight, this.eventSnapSize, this.hourDuration) : 0;\n    const start = this.dateAdapter.addMinutes(addDaysWithExclusions(this.dateAdapter, weekEvent.event.start, daysDragged, this.excludeDays), minutesMoved);\n    let end;\n    if (weekEvent.event.end) {\n      end = this.dateAdapter.addMinutes(addDaysWithExclusions(this.dateAdapter, weekEvent.event.end, daysDragged, this.excludeDays), minutesMoved);\n    }\n    return {\n      start,\n      end\n    };\n  }\n  restoreOriginalEvents(tempEvents, adjustedEvents, snapDraggedEvents = true) {\n    const previousView = this.view;\n    if (snapDraggedEvents) {\n      this.view = this.getWeekView(tempEvents);\n    }\n    const adjustedEventsArray = tempEvents.filter(event => adjustedEvents.has(event));\n    this.view.hourColumns.forEach((column, columnIndex) => {\n      previousView.hourColumns[columnIndex].hours.forEach((hour, hourIndex) => {\n        hour.segments.forEach((segment, segmentIndex) => {\n          column.hours[hourIndex].segments[segmentIndex].cssClass = segment.cssClass;\n        });\n      });\n      adjustedEventsArray.forEach(adjustedEvent => {\n        const originalEvent = adjustedEvents.get(adjustedEvent);\n        const existingColumnEvent = column.events.find(columnEvent => columnEvent.event === (snapDraggedEvents ? adjustedEvent : originalEvent));\n        if (existingColumnEvent) {\n          // restore the original event so trackBy kicks in and the dom isn't changed\n          existingColumnEvent.event = originalEvent;\n          existingColumnEvent['tempEvent'] = adjustedEvent;\n          if (!snapDraggedEvents) {\n            existingColumnEvent.height = 0;\n            existingColumnEvent.width = 0;\n          }\n        } else {\n          // add a dummy event to the drop so if the event was removed from the original column the drag doesn't end early\n          const event = {\n            event: originalEvent,\n            left: 0,\n            top: 0,\n            height: 0,\n            width: 0,\n            startsBeforeDay: false,\n            endsAfterDay: false,\n            tempEvent: adjustedEvent\n          };\n          column.events.push(event);\n        }\n      });\n    });\n    adjustedEvents.clear();\n  }\n  getTimeEventResizedDates(calendarEvent, resizeEvent) {\n    const newEventDates = {\n      start: calendarEvent.start,\n      end: getDefaultEventEnd(this.dateAdapter, calendarEvent, this.minimumEventHeight)\n    };\n    const {\n      end,\n      ...eventWithoutEnd\n    } = calendarEvent;\n    const smallestResizes = {\n      start: this.dateAdapter.addMinutes(newEventDates.end, this.minimumEventHeight * -1),\n      end: getDefaultEventEnd(this.dateAdapter, eventWithoutEnd, this.minimumEventHeight)\n    };\n    const modifier = this.rtl ? -1 : 1;\n    if (typeof resizeEvent.edges.left !== 'undefined') {\n      const daysDiff = Math.round(+resizeEvent.edges.left / this.dayColumnWidth) * modifier;\n      const newStart = addDaysWithExclusions(this.dateAdapter, newEventDates.start, daysDiff, this.excludeDays);\n      if (newStart < smallestResizes.start) {\n        newEventDates.start = newStart;\n      } else {\n        newEventDates.start = smallestResizes.start;\n      }\n    } else if (typeof resizeEvent.edges.right !== 'undefined') {\n      const daysDiff = Math.round(+resizeEvent.edges.right / this.dayColumnWidth) * modifier;\n      const newEnd = addDaysWithExclusions(this.dateAdapter, newEventDates.end, daysDiff, this.excludeDays);\n      if (newEnd > smallestResizes.end) {\n        newEventDates.end = newEnd;\n      } else {\n        newEventDates.end = smallestResizes.end;\n      }\n    }\n    if (typeof resizeEvent.edges.top !== 'undefined') {\n      const minutesMoved = getMinutesMoved(resizeEvent.edges.top, this.hourSegments, this.hourSegmentHeight, this.eventSnapSize, this.hourDuration);\n      const newStart = this.dateAdapter.addMinutes(newEventDates.start, minutesMoved);\n      if (newStart < smallestResizes.start) {\n        newEventDates.start = newStart;\n      } else {\n        newEventDates.start = smallestResizes.start;\n      }\n    } else if (typeof resizeEvent.edges.bottom !== 'undefined') {\n      const minutesMoved = getMinutesMoved(resizeEvent.edges.bottom, this.hourSegments, this.hourSegmentHeight, this.eventSnapSize, this.hourDuration);\n      const newEnd = this.dateAdapter.addMinutes(newEventDates.end, minutesMoved);\n      if (newEnd > smallestResizes.end) {\n        newEventDates.end = newEnd;\n      } else {\n        newEventDates.end = smallestResizes.end;\n      }\n    }\n    return newEventDates;\n  }\n  resizeStarted(eventsContainer, event, dayWidth) {\n    this.dayColumnWidth = this.getDayColumnWidth(eventsContainer);\n    const resizeHelper = new CalendarResizeHelper(eventsContainer, dayWidth, this.rtl);\n    this.validateResize = ({\n      rectangle,\n      edges\n    }) => {\n      const isWithinBoundary = resizeHelper.validateResize({\n        rectangle: {\n          ...rectangle\n        },\n        edges\n      });\n      if (isWithinBoundary && this.validateEventTimesChanged) {\n        let newEventDates;\n        if (!dayWidth) {\n          newEventDates = this.getTimeEventResizedDates(event.event, {\n            rectangle,\n            edges\n          });\n        } else {\n          const modifier = this.rtl ? -1 : 1;\n          if (typeof edges.left !== 'undefined') {\n            const diff = Math.round(+edges.left / dayWidth) * modifier;\n            newEventDates = this.getAllDayEventResizedDates(event.event, diff, !this.rtl);\n          } else {\n            const diff = Math.round(+edges.right / dayWidth) * modifier;\n            newEventDates = this.getAllDayEventResizedDates(event.event, diff, this.rtl);\n          }\n        }\n        return this.validateEventTimesChanged({\n          type: CalendarEventTimesChangedEventType.Resize,\n          event: event.event,\n          newStart: newEventDates.start,\n          newEnd: newEventDates.end\n        });\n      }\n      return isWithinBoundary;\n    };\n    this.cdr.markForCheck();\n  }\n  /**\n   * @hidden\n   */\n  getAllDayEventResizedDates(event, daysDiff, beforeStart) {\n    let start = event.start;\n    let end = event.end || event.start;\n    if (beforeStart) {\n      start = addDaysWithExclusions(this.dateAdapter, start, daysDiff, this.excludeDays);\n    } else {\n      end = addDaysWithExclusions(this.dateAdapter, end, daysDiff, this.excludeDays);\n    }\n    return {\n      start,\n      end\n    };\n  }\n}\nCalendarWeekViewComponent.ɵfac = function CalendarWeekViewComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarWeekViewComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(CalendarUtils), i0.ɵɵdirectiveInject(LOCALE_ID), i0.ɵɵdirectiveInject(DateAdapter), i0.ɵɵdirectiveInject(i0.ElementRef));\n};\nCalendarWeekViewComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: CalendarWeekViewComponent,\n  selectors: [[\"mwl-calendar-week-view\"]],\n  inputs: {\n    viewDate: \"viewDate\",\n    events: \"events\",\n    excludeDays: \"excludeDays\",\n    refresh: \"refresh\",\n    locale: \"locale\",\n    tooltipPlacement: \"tooltipPlacement\",\n    tooltipTemplate: \"tooltipTemplate\",\n    tooltipAppendToBody: \"tooltipAppendToBody\",\n    tooltipDelay: \"tooltipDelay\",\n    weekStartsOn: \"weekStartsOn\",\n    headerTemplate: \"headerTemplate\",\n    eventTemplate: \"eventTemplate\",\n    eventTitleTemplate: \"eventTitleTemplate\",\n    eventActionsTemplate: \"eventActionsTemplate\",\n    precision: \"precision\",\n    weekendDays: \"weekendDays\",\n    snapDraggedEvents: \"snapDraggedEvents\",\n    hourSegments: \"hourSegments\",\n    hourDuration: \"hourDuration\",\n    hourSegmentHeight: \"hourSegmentHeight\",\n    minimumEventHeight: \"minimumEventHeight\",\n    dayStartHour: \"dayStartHour\",\n    dayStartMinute: \"dayStartMinute\",\n    dayEndHour: \"dayEndHour\",\n    dayEndMinute: \"dayEndMinute\",\n    hourSegmentTemplate: \"hourSegmentTemplate\",\n    eventSnapSize: \"eventSnapSize\",\n    allDayEventsLabelTemplate: \"allDayEventsLabelTemplate\",\n    daysInWeek: \"daysInWeek\",\n    currentTimeMarkerTemplate: \"currentTimeMarkerTemplate\",\n    validateEventTimesChanged: \"validateEventTimesChanged\",\n    resizeCursors: \"resizeCursors\"\n  },\n  outputs: {\n    dayHeaderClicked: \"dayHeaderClicked\",\n    eventClicked: \"eventClicked\",\n    eventTimesChanged: \"eventTimesChanged\",\n    beforeViewRender: \"beforeViewRender\",\n    hourSegmentClicked: \"hourSegmentClicked\"\n  },\n  standalone: false,\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 8,\n  vars: 9,\n  consts: [[\"dayColumns\", \"\"], [\"allDayEventsContainer\", \"\"], [\"eventRowContainer\", \"\"], [\"event\", \"\"], [\"weekEventTemplate\", \"\"], [\"role\", \"grid\", 1, \"cal-week-view\"], [3, \"dayHeaderClicked\", \"eventDropped\", \"dragEnter\", \"days\", \"locale\", \"customTemplate\"], [\"class\", \"cal-all-day-events\", \"mwlDroppable\", \"\", 3, \"dragEnter\", \"dragLeave\", 4, \"ngIf\"], [\"mwlDroppable\", \"\", 1, \"cal-time-events\", 3, \"dragEnter\", \"dragLeave\"], [\"class\", \"cal-time-label-column\", 4, \"ngIf\"], [1, \"cal-day-columns\"], [\"class\", \"cal-day-column\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"mwlDroppable\", \"\", 1, \"cal-all-day-events\", 3, \"dragEnter\", \"dragLeave\"], [1, \"cal-time-label-column\"], [4, \"ngTemplateOutlet\"], [\"class\", \"cal-day-column\", \"mwlDroppable\", \"\", \"dragOverClass\", \"cal-drag-over\", 3, \"drop\", \"dragEnter\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"cal-events-row\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"mwlDroppable\", \"\", \"dragOverClass\", \"cal-drag-over\", 1, \"cal-day-column\", 3, \"drop\", \"dragEnter\"], [1, \"cal-events-row\"], [\"class\", \"cal-event-container\", \"mwlResizable\", \"\", \"mwlDraggable\", \"\", \"dragActiveClass\", \"cal-drag-active\", 3, \"cal-draggable\", \"cal-starts-within-week\", \"cal-ends-within-week\", \"ngClass\", \"width\", \"marginLeft\", \"marginRight\", \"resizeCursors\", \"resizeSnapGrid\", \"validateResize\", \"dropData\", \"dragAxis\", \"dragSnapGrid\", \"validateDrag\", \"touchStartLongPress\", \"resizeStart\", \"resizing\", \"resizeEnd\", \"dragStart\", \"dragging\", \"dragEnd\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"mwlResizable\", \"\", \"mwlDraggable\", \"\", \"dragActiveClass\", \"cal-drag-active\", 1, \"cal-event-container\", 3, \"resizeStart\", \"resizing\", \"resizeEnd\", \"dragStart\", \"dragging\", \"dragEnd\", \"ngClass\", \"resizeCursors\", \"resizeSnapGrid\", \"validateResize\", \"dropData\", \"dragAxis\", \"dragSnapGrid\", \"validateDrag\", \"touchStartLongPress\"], [\"class\", \"cal-resize-handle cal-resize-handle-before-start\", \"mwlResizeHandle\", \"\", 3, \"resizeEdges\", 4, \"ngIf\"], [3, \"eventClicked\", \"locale\", \"weekEvent\", \"tooltipPlacement\", \"tooltipTemplate\", \"tooltipAppendToBody\", \"tooltipDelay\", \"customTemplate\", \"eventTitleTemplate\", \"eventActionsTemplate\", \"daysInWeek\"], [\"class\", \"cal-resize-handle cal-resize-handle-after-end\", \"mwlResizeHandle\", \"\", 3, \"resizeEdges\", 4, \"ngIf\"], [\"mwlResizeHandle\", \"\", 1, \"cal-resize-handle\", \"cal-resize-handle-before-start\", 3, \"resizeEdges\"], [\"mwlResizeHandle\", \"\", 1, \"cal-resize-handle\", \"cal-resize-handle-after-end\", 3, \"resizeEdges\"], [\"class\", \"cal-hour\", 3, \"cal-hour-odd\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"cal-hour\"], [3, \"height\", \"segment\", \"segmentHeight\", \"locale\", \"customTemplate\", \"isTimeLabel\", \"daysInWeek\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [3, \"segment\", \"segmentHeight\", \"locale\", \"customTemplate\", \"isTimeLabel\", \"daysInWeek\"], [1, \"cal-day-column\"], [3, \"columnDate\", \"dayStartHour\", \"dayStartMinute\", \"dayEndHour\", \"dayEndMinute\", \"hourSegments\", \"hourDuration\", \"hourSegmentHeight\", \"customTemplate\"], [1, \"cal-events-container\"], [\"class\", \"cal-event-container\", \"mwlResizable\", \"\", \"mwlDraggable\", \"\", \"dragActiveClass\", \"cal-drag-active\", 3, \"cal-draggable\", \"cal-starts-within-day\", \"cal-ends-within-day\", \"ngClass\", \"hidden\", \"top\", \"height\", \"left\", \"width\", \"resizeCursors\", \"resizeSnapGrid\", \"validateResize\", \"allowNegativeResizes\", \"dropData\", \"dragAxis\", \"dragSnapGrid\", \"touchStartLongPress\", \"ghostDragEnabled\", \"ghostElementTemplate\", \"validateDrag\", \"resizeStart\", \"resizing\", \"resizeEnd\", \"dragStart\", \"dragging\", \"dragEnd\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"mwlResizable\", \"\", \"mwlDraggable\", \"\", \"dragActiveClass\", \"cal-drag-active\", 1, \"cal-event-container\", 3, \"resizeStart\", \"resizing\", \"resizeEnd\", \"dragStart\", \"dragging\", \"dragEnd\", \"ngClass\", \"hidden\", \"resizeCursors\", \"resizeSnapGrid\", \"validateResize\", \"allowNegativeResizes\", \"dropData\", \"dragAxis\", \"dragSnapGrid\", \"touchStartLongPress\", \"ghostDragEnabled\", \"ghostElementTemplate\", \"validateDrag\"], [3, \"ngTemplateOutlet\"], [3, \"eventClicked\", \"locale\", \"weekEvent\", \"tooltipPlacement\", \"tooltipTemplate\", \"tooltipAppendToBody\", \"tooltipDisabled\", \"tooltipDelay\", \"customTemplate\", \"eventTitleTemplate\", \"eventActionsTemplate\", \"column\", \"daysInWeek\"], [\"mwlDroppable\", \"\", \"dragActiveClass\", \"cal-drag-active\", 3, \"height\", \"segment\", \"segmentHeight\", \"locale\", \"customTemplate\", \"daysInWeek\", \"clickListenerDisabled\", \"dragOverClass\", \"isTimeLabel\", \"mwlClick\", \"drop\", \"dragEnter\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"mwlDroppable\", \"\", \"dragActiveClass\", \"cal-drag-active\", 3, \"mwlClick\", \"drop\", \"dragEnter\", \"segment\", \"segmentHeight\", \"locale\", \"customTemplate\", \"daysInWeek\", \"clickListenerDisabled\", \"dragOverClass\", \"isTimeLabel\"]],\n  template: function CalendarWeekViewComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r1 = i0.ɵɵgetCurrentView();\n      i0.ɵɵelementStart(0, \"div\", 5)(1, \"mwl-calendar-week-view-header\", 6);\n      i0.ɵɵlistener(\"dayHeaderClicked\", function CalendarWeekViewComponent_Template_mwl_calendar_week_view_header_dayHeaderClicked_1_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.dayHeaderClicked.emit($event));\n      })(\"eventDropped\", function CalendarWeekViewComponent_Template_mwl_calendar_week_view_header_eventDropped_1_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.eventDropped({\n          dropData: $event\n        }, $event.newStart, true));\n      })(\"dragEnter\", function CalendarWeekViewComponent_Template_mwl_calendar_week_view_header_dragEnter_1_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.dateDragEnter($event.date));\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(2, CalendarWeekViewComponent_div_2_Template, 7, 5, \"div\", 7);\n      i0.ɵɵelementStart(3, \"div\", 8);\n      i0.ɵɵlistener(\"dragEnter\", function CalendarWeekViewComponent_Template_div_dragEnter_3_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.dragEnter(\"time\"));\n      })(\"dragLeave\", function CalendarWeekViewComponent_Template_div_dragLeave_3_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.dragLeave(\"time\"));\n      });\n      i0.ɵɵtemplate(4, CalendarWeekViewComponent_div_4_Template, 2, 2, \"div\", 9);\n      i0.ɵɵelementStart(5, \"div\", 10, 0);\n      i0.ɵɵtemplate(7, CalendarWeekViewComponent_div_7_Template, 5, 13, \"div\", 11);\n      i0.ɵɵelementEnd()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"days\", ctx.days)(\"locale\", ctx.locale)(\"customTemplate\", ctx.headerTemplate);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.view.allDayEventRows.length > 0);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.view.hourColumns.length > 0 && ctx.daysInWeek !== 1);\n      i0.ɵɵadvance();\n      i0.ɵɵclassProp(\"cal-resize-active\", ctx.timeEventResizes.size > 0);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx.view.hourColumns)(\"ngForTrackBy\", ctx.trackByHourColumn);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i4.ResizableDirective, i4.ResizeHandleDirective, i2.DraggableDirective, i2.DroppableDirective, ClickDirective, CalendarWeekViewHeaderComponent, CalendarWeekViewEventComponent, CalendarWeekViewHourSegmentComponent, CalendarWeekViewCurrentTimeMarkerComponent],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarWeekViewComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mwl-calendar-week-view',\n      template: `\n    <div class=\"cal-week-view\" role=\"grid\">\n      <mwl-calendar-week-view-header\n        [days]=\"days\"\n        [locale]=\"locale\"\n        [customTemplate]=\"headerTemplate\"\n        (dayHeaderClicked)=\"dayHeaderClicked.emit($event)\"\n        (eventDropped)=\"\n          eventDropped({ dropData: $event }, $event.newStart, true)\n        \"\n        (dragEnter)=\"dateDragEnter($event.date)\"\n      >\n      </mwl-calendar-week-view-header>\n      <div\n        class=\"cal-all-day-events\"\n        #allDayEventsContainer\n        *ngIf=\"view.allDayEventRows.length > 0\"\n        mwlDroppable\n        (dragEnter)=\"dragEnter('allDay')\"\n        (dragLeave)=\"dragLeave('allDay')\"\n      >\n        <div class=\"cal-day-columns\">\n          <div class=\"cal-time-label-column\">\n            <ng-container\n              *ngTemplateOutlet=\"allDayEventsLabelTemplate\"\n            ></ng-container>\n          </div>\n          <div\n            class=\"cal-day-column\"\n            *ngFor=\"let day of days; trackBy: trackByWeekDayHeaderDate\"\n            mwlDroppable\n            dragOverClass=\"cal-drag-over\"\n            (drop)=\"eventDropped($event, day.date, true)\"\n            (dragEnter)=\"dateDragEnter(day.date)\"\n          ></div>\n        </div>\n        <div\n          *ngFor=\"let eventRow of view.allDayEventRows; trackBy: trackById\"\n          #eventRowContainer\n          class=\"cal-events-row\"\n        >\n          <div\n            *ngFor=\"\n              let allDayEvent of eventRow.row;\n              trackBy: trackByWeekAllDayEvent\n            \"\n            #event\n            class=\"cal-event-container\"\n            [class.cal-draggable]=\"\n              allDayEvent.event.draggable && allDayEventResizes.size === 0\n            \"\n            [class.cal-starts-within-week]=\"!allDayEvent.startsBeforeWeek\"\n            [class.cal-ends-within-week]=\"!allDayEvent.endsAfterWeek\"\n            [ngClass]=\"allDayEvent.event?.cssClass\"\n            [style.width.%]=\"(100 / days.length) * allDayEvent.span\"\n            [style.marginLeft.%]=\"\n              rtl ? null : (100 / days.length) * allDayEvent.offset\n            \"\n            [style.marginRight.%]=\"\n              rtl ? (100 / days.length) * allDayEvent.offset : null\n            \"\n            mwlResizable\n            [resizeCursors]=\"resizeCursors\"\n            [resizeSnapGrid]=\"{ left: dayColumnWidth, right: dayColumnWidth }\"\n            [validateResize]=\"validateResize\"\n            (resizeStart)=\"\n              allDayEventResizeStarted(eventRowContainer, allDayEvent, $event)\n            \"\n            (resizing)=\"\n              allDayEventResizing(allDayEvent, $event, dayColumnWidth)\n            \"\n            (resizeEnd)=\"allDayEventResizeEnded(allDayEvent)\"\n            mwlDraggable\n            dragActiveClass=\"cal-drag-active\"\n            [dropData]=\"{ event: allDayEvent.event, calendarId: calendarId }\"\n            [dragAxis]=\"{\n              x: allDayEvent.event.draggable && allDayEventResizes.size === 0,\n              y:\n                !snapDraggedEvents &&\n                allDayEvent.event.draggable &&\n                allDayEventResizes.size === 0\n            }\"\n            [dragSnapGrid]=\"snapDraggedEvents ? { x: dayColumnWidth } : {}\"\n            [validateDrag]=\"validateDrag\"\n            [touchStartLongPress]=\"{ delay: 300, delta: 30 }\"\n            (dragStart)=\"\n              dragStarted(eventRowContainer, event, allDayEvent, false)\n            \"\n            (dragging)=\"allDayEventDragMove()\"\n            (dragEnd)=\"dragEnded(allDayEvent, $event, dayColumnWidth)\"\n          >\n            <div\n              class=\"cal-resize-handle cal-resize-handle-before-start\"\n              *ngIf=\"\n                allDayEvent.event?.resizable?.beforeStart &&\n                !allDayEvent.startsBeforeWeek\n              \"\n              mwlResizeHandle\n              [resizeEdges]=\"{ left: true }\"\n            ></div>\n            <mwl-calendar-week-view-event\n              [locale]=\"locale\"\n              [weekEvent]=\"allDayEvent\"\n              [tooltipPlacement]=\"tooltipPlacement\"\n              [tooltipTemplate]=\"tooltipTemplate\"\n              [tooltipAppendToBody]=\"tooltipAppendToBody\"\n              [tooltipDelay]=\"tooltipDelay\"\n              [customTemplate]=\"eventTemplate\"\n              [eventTitleTemplate]=\"eventTitleTemplate\"\n              [eventActionsTemplate]=\"eventActionsTemplate\"\n              [daysInWeek]=\"daysInWeek\"\n              (eventClicked)=\"\n                eventClicked.emit({\n                  event: allDayEvent.event,\n                  sourceEvent: $event.sourceEvent\n                })\n              \"\n            >\n            </mwl-calendar-week-view-event>\n            <div\n              class=\"cal-resize-handle cal-resize-handle-after-end\"\n              *ngIf=\"\n                allDayEvent.event?.resizable?.afterEnd &&\n                !allDayEvent.endsAfterWeek\n              \"\n              mwlResizeHandle\n              [resizeEdges]=\"{ right: true }\"\n            ></div>\n          </div>\n        </div>\n      </div>\n      <div\n        class=\"cal-time-events\"\n        mwlDroppable\n        (dragEnter)=\"dragEnter('time')\"\n        (dragLeave)=\"dragLeave('time')\"\n      >\n        <div\n          class=\"cal-time-label-column\"\n          *ngIf=\"view.hourColumns.length > 0 && daysInWeek !== 1\"\n        >\n          <div\n            *ngFor=\"\n              let hour of view.hourColumns[0].hours;\n              trackBy: trackByHour;\n              let odd = odd\n            \"\n            class=\"cal-hour\"\n            [class.cal-hour-odd]=\"odd\"\n          >\n            <mwl-calendar-week-view-hour-segment\n              *ngFor=\"let segment of hour.segments; trackBy: trackByHourSegment\"\n              [style.height.px]=\"hourSegmentHeight\"\n              [segment]=\"segment\"\n              [segmentHeight]=\"hourSegmentHeight\"\n              [locale]=\"locale\"\n              [customTemplate]=\"hourSegmentTemplate\"\n              [isTimeLabel]=\"true\"\n              [daysInWeek]=\"daysInWeek\"\n            >\n            </mwl-calendar-week-view-hour-segment>\n          </div>\n        </div>\n        <div\n          class=\"cal-day-columns\"\n          [class.cal-resize-active]=\"timeEventResizes.size > 0\"\n          #dayColumns\n        >\n          <div\n            class=\"cal-day-column\"\n            *ngFor=\"let column of view.hourColumns; trackBy: trackByHourColumn\"\n          >\n            <mwl-calendar-week-view-current-time-marker\n              [columnDate]=\"column.date\"\n              [dayStartHour]=\"dayStartHour\"\n              [dayStartMinute]=\"dayStartMinute\"\n              [dayEndHour]=\"dayEndHour\"\n              [dayEndMinute]=\"dayEndMinute\"\n              [hourSegments]=\"hourSegments\"\n              [hourDuration]=\"hourDuration\"\n              [hourSegmentHeight]=\"hourSegmentHeight\"\n              [customTemplate]=\"currentTimeMarkerTemplate\"\n            ></mwl-calendar-week-view-current-time-marker>\n            <div class=\"cal-events-container\">\n              <div\n                *ngFor=\"\n                  let timeEvent of column.events;\n                  trackBy: trackByWeekTimeEvent\n                \"\n                #event\n                class=\"cal-event-container\"\n                [class.cal-draggable]=\"\n                  timeEvent.event.draggable && timeEventResizes.size === 0\n                \"\n                [class.cal-starts-within-day]=\"!timeEvent.startsBeforeDay\"\n                [class.cal-ends-within-day]=\"!timeEvent.endsAfterDay\"\n                [ngClass]=\"timeEvent.event.cssClass\"\n                [hidden]=\"timeEvent.height === 0 && timeEvent.width === 0\"\n                [style.top.px]=\"timeEvent.top\"\n                [style.height.px]=\"timeEvent.height\"\n                [style.left.%]=\"timeEvent.left\"\n                [style.width.%]=\"timeEvent.width\"\n                mwlResizable\n                [resizeCursors]=\"resizeCursors\"\n                [resizeSnapGrid]=\"{\n                  left: dayColumnWidth,\n                  right: dayColumnWidth,\n                  top: eventSnapSize || hourSegmentHeight,\n                  bottom: eventSnapSize || hourSegmentHeight\n                }\"\n                [validateResize]=\"validateResize\"\n                [allowNegativeResizes]=\"true\"\n                (resizeStart)=\"\n                  timeEventResizeStarted(dayColumns, timeEvent, $event)\n                \"\n                (resizing)=\"timeEventResizing(timeEvent, $event)\"\n                (resizeEnd)=\"timeEventResizeEnded(timeEvent)\"\n                mwlDraggable\n                dragActiveClass=\"cal-drag-active\"\n                [dropData]=\"{ event: timeEvent.event, calendarId: calendarId }\"\n                [dragAxis]=\"{\n                  x: timeEvent.event.draggable && timeEventResizes.size === 0,\n                  y: timeEvent.event.draggable && timeEventResizes.size === 0\n                }\"\n                [dragSnapGrid]=\"\n                  snapDraggedEvents\n                    ? {\n                        x: dayColumnWidth,\n                        y: eventSnapSize || hourSegmentHeight\n                      }\n                    : {}\n                \"\n                [touchStartLongPress]=\"{ delay: 300, delta: 30 }\"\n                [ghostDragEnabled]=\"!snapDraggedEvents\"\n                [ghostElementTemplate]=\"weekEventTemplate\"\n                [validateDrag]=\"validateDrag\"\n                (dragStart)=\"dragStarted(dayColumns, event, timeEvent, true)\"\n                (dragging)=\"dragMove(timeEvent, $event)\"\n                (dragEnd)=\"dragEnded(timeEvent, $event, dayColumnWidth, true)\"\n              >\n                <div\n                  class=\"cal-resize-handle cal-resize-handle-before-start\"\n                  *ngIf=\"\n                    timeEvent.event?.resizable?.beforeStart &&\n                    !timeEvent.startsBeforeDay\n                  \"\n                  mwlResizeHandle\n                  [resizeEdges]=\"{\n                    left: true,\n                    top: true\n                  }\"\n                ></div>\n                <ng-template\n                  [ngTemplateOutlet]=\"weekEventTemplate\"\n                ></ng-template>\n                <ng-template #weekEventTemplate>\n                  <mwl-calendar-week-view-event\n                    [locale]=\"locale\"\n                    [weekEvent]=\"timeEvent\"\n                    [tooltipPlacement]=\"tooltipPlacement\"\n                    [tooltipTemplate]=\"tooltipTemplate\"\n                    [tooltipAppendToBody]=\"tooltipAppendToBody\"\n                    [tooltipDisabled]=\"dragActive || timeEventResizes.size > 0\"\n                    [tooltipDelay]=\"tooltipDelay\"\n                    [customTemplate]=\"eventTemplate\"\n                    [eventTitleTemplate]=\"eventTitleTemplate\"\n                    [eventActionsTemplate]=\"eventActionsTemplate\"\n                    [column]=\"column\"\n                    [daysInWeek]=\"daysInWeek\"\n                    (eventClicked)=\"\n                      eventClicked.emit({\n                        event: timeEvent.event,\n                        sourceEvent: $event.sourceEvent\n                      })\n                    \"\n                  >\n                  </mwl-calendar-week-view-event>\n                </ng-template>\n                <div\n                  class=\"cal-resize-handle cal-resize-handle-after-end\"\n                  *ngIf=\"\n                    timeEvent.event?.resizable?.afterEnd &&\n                    !timeEvent.endsAfterDay\n                  \"\n                  mwlResizeHandle\n                  [resizeEdges]=\"{\n                    right: true,\n                    bottom: true\n                  }\"\n                ></div>\n              </div>\n            </div>\n\n            <div\n              *ngFor=\"\n                let hour of column.hours;\n                trackBy: trackByHour;\n                let odd = odd\n              \"\n              class=\"cal-hour\"\n              [class.cal-hour-odd]=\"odd\"\n            >\n              <mwl-calendar-week-view-hour-segment\n                *ngFor=\"\n                  let segment of hour.segments;\n                  trackBy: trackByHourSegment\n                \"\n                [style.height.px]=\"hourSegmentHeight\"\n                [segment]=\"segment\"\n                [segmentHeight]=\"hourSegmentHeight\"\n                [locale]=\"locale\"\n                [customTemplate]=\"hourSegmentTemplate\"\n                [daysInWeek]=\"daysInWeek\"\n                (mwlClick)=\"\n                  hourSegmentClicked.emit({\n                    date: segment.date,\n                    sourceEvent: $event\n                  })\n                \"\n                [clickListenerDisabled]=\"\n                  hourSegmentClicked.observers.length === 0\n                \"\n                mwlDroppable\n                [dragOverClass]=\"\n                  !dragActive || !snapDraggedEvents ? 'cal-drag-over' : null\n                \"\n                dragActiveClass=\"cal-drag-active\"\n                (drop)=\"eventDropped($event, segment.date, false)\"\n                (dragEnter)=\"dateDragEnter(segment.date)\"\n                [isTimeLabel]=\"daysInWeek === 1\"\n              >\n              </mwl-calendar-week-view-hour-segment>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  `\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: CalendarUtils\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [LOCALE_ID]\n      }]\n    }, {\n      type: DateAdapter\n    }, {\n      type: i0.ElementRef\n    }];\n  }, {\n    viewDate: [{\n      type: Input\n    }],\n    events: [{\n      type: Input\n    }],\n    excludeDays: [{\n      type: Input\n    }],\n    refresh: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }],\n    tooltipPlacement: [{\n      type: Input\n    }],\n    tooltipTemplate: [{\n      type: Input\n    }],\n    tooltipAppendToBody: [{\n      type: Input\n    }],\n    tooltipDelay: [{\n      type: Input\n    }],\n    weekStartsOn: [{\n      type: Input\n    }],\n    headerTemplate: [{\n      type: Input\n    }],\n    eventTemplate: [{\n      type: Input\n    }],\n    eventTitleTemplate: [{\n      type: Input\n    }],\n    eventActionsTemplate: [{\n      type: Input\n    }],\n    precision: [{\n      type: Input\n    }],\n    weekendDays: [{\n      type: Input\n    }],\n    snapDraggedEvents: [{\n      type: Input\n    }],\n    hourSegments: [{\n      type: Input\n    }],\n    hourDuration: [{\n      type: Input\n    }],\n    hourSegmentHeight: [{\n      type: Input\n    }],\n    minimumEventHeight: [{\n      type: Input\n    }],\n    dayStartHour: [{\n      type: Input\n    }],\n    dayStartMinute: [{\n      type: Input\n    }],\n    dayEndHour: [{\n      type: Input\n    }],\n    dayEndMinute: [{\n      type: Input\n    }],\n    hourSegmentTemplate: [{\n      type: Input\n    }],\n    eventSnapSize: [{\n      type: Input\n    }],\n    allDayEventsLabelTemplate: [{\n      type: Input\n    }],\n    daysInWeek: [{\n      type: Input\n    }],\n    currentTimeMarkerTemplate: [{\n      type: Input\n    }],\n    validateEventTimesChanged: [{\n      type: Input\n    }],\n    resizeCursors: [{\n      type: Input\n    }],\n    dayHeaderClicked: [{\n      type: Output\n    }],\n    eventClicked: [{\n      type: Output\n    }],\n    eventTimesChanged: [{\n      type: Output\n    }],\n    beforeViewRender: [{\n      type: Output\n    }],\n    hourSegmentClicked: [{\n      type: Output\n    }]\n  });\n})();\nclass CalendarWeekModule {}\nCalendarWeekModule.ɵfac = function CalendarWeekModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarWeekModule)();\n};\nCalendarWeekModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: CalendarWeekModule,\n  declarations: [CalendarWeekViewComponent, CalendarWeekViewHeaderComponent, CalendarWeekViewEventComponent, CalendarWeekViewHourSegmentComponent, CalendarWeekViewCurrentTimeMarkerComponent],\n  imports: [CommonModule, ResizableModule, DragAndDropModule, CalendarCommonModule],\n  exports: [ResizableModule, DragAndDropModule, CalendarWeekViewComponent, CalendarWeekViewHeaderComponent, CalendarWeekViewEventComponent, CalendarWeekViewHourSegmentComponent, CalendarWeekViewCurrentTimeMarkerComponent]\n});\nCalendarWeekModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, ResizableModule, DragAndDropModule, CalendarCommonModule, ResizableModule, DragAndDropModule]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarWeekModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, ResizableModule, DragAndDropModule, CalendarCommonModule],\n      declarations: [CalendarWeekViewComponent, CalendarWeekViewHeaderComponent, CalendarWeekViewEventComponent, CalendarWeekViewHourSegmentComponent, CalendarWeekViewCurrentTimeMarkerComponent],\n      exports: [ResizableModule, DragAndDropModule, CalendarWeekViewComponent, CalendarWeekViewHeaderComponent, CalendarWeekViewEventComponent, CalendarWeekViewHourSegmentComponent, CalendarWeekViewCurrentTimeMarkerComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Shows all events on a given day. Example usage:\n *\n * ```typescript\n * <mwl-calendar-day-view\n *  [viewDate]=\"viewDate\"\n *  [events]=\"events\">\n * </mwl-calendar-day-view>\n * ```\n */\nclass CalendarDayViewComponent {\n  constructor() {\n    /**\n     * An array of events to display on view\n     * The schema is available here: https://github.com/mattlewis92/calendar-utils/blob/c51689985f59a271940e30bc4e2c4e1fee3fcb5c/src/calendarUtils.ts#L49-L63\n     */\n    this.events = [];\n    /**\n     * The number of segments in an hour. Must divide equally into 60.\n     */\n    this.hourSegments = 2;\n    /**\n     * The height in pixels of each hour segment\n     */\n    this.hourSegmentHeight = 30;\n    /**\n     * The minimum height in pixels of each event\n     */\n    this.minimumEventHeight = 30;\n    /**\n     * The day start hours in 24 hour time. Must be 0-23\n     */\n    this.dayStartHour = 0;\n    /**\n     * The day start minutes. Must be 0-59\n     */\n    this.dayStartMinute = 0;\n    /**\n     * The day end hours in 24 hour time. Must be 0-23\n     */\n    this.dayEndHour = 23;\n    /**\n     * The day end minutes. Must be 0-59\n     */\n    this.dayEndMinute = 59;\n    /**\n     * The placement of the event tooltip\n     */\n    this.tooltipPlacement = 'auto';\n    /**\n     * Whether to append tooltips to the body or next to the trigger element\n     */\n    this.tooltipAppendToBody = true;\n    /**\n     * The delay in milliseconds before the tooltip should be displayed. If not provided the tooltip\n     * will be displayed immediately.\n     */\n    this.tooltipDelay = null;\n    /**\n     * Whether to snap events to a grid when dragging\n     */\n    this.snapDraggedEvents = true;\n    /**\n     * Called when an event title is clicked\n     */\n    this.eventClicked = new EventEmitter();\n    /**\n     * Called when an hour segment is clicked\n     */\n    this.hourSegmentClicked = new EventEmitter();\n    /**\n     * Called when an event is resized or dragged and dropped\n     */\n    this.eventTimesChanged = new EventEmitter();\n    /**\n     * An output that will be called before the view is rendered for the current day.\n     * If you add the `cssClass` property to an hour grid segment it will add that class to the hour segment in the template\n     */\n    this.beforeViewRender = new EventEmitter();\n  }\n}\nCalendarDayViewComponent.ɵfac = function CalendarDayViewComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarDayViewComponent)();\n};\nCalendarDayViewComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: CalendarDayViewComponent,\n  selectors: [[\"mwl-calendar-day-view\"]],\n  inputs: {\n    viewDate: \"viewDate\",\n    events: \"events\",\n    hourSegments: \"hourSegments\",\n    hourSegmentHeight: \"hourSegmentHeight\",\n    hourDuration: \"hourDuration\",\n    minimumEventHeight: \"minimumEventHeight\",\n    dayStartHour: \"dayStartHour\",\n    dayStartMinute: \"dayStartMinute\",\n    dayEndHour: \"dayEndHour\",\n    dayEndMinute: \"dayEndMinute\",\n    refresh: \"refresh\",\n    locale: \"locale\",\n    eventSnapSize: \"eventSnapSize\",\n    tooltipPlacement: \"tooltipPlacement\",\n    tooltipTemplate: \"tooltipTemplate\",\n    tooltipAppendToBody: \"tooltipAppendToBody\",\n    tooltipDelay: \"tooltipDelay\",\n    hourSegmentTemplate: \"hourSegmentTemplate\",\n    eventTemplate: \"eventTemplate\",\n    eventTitleTemplate: \"eventTitleTemplate\",\n    eventActionsTemplate: \"eventActionsTemplate\",\n    snapDraggedEvents: \"snapDraggedEvents\",\n    allDayEventsLabelTemplate: \"allDayEventsLabelTemplate\",\n    currentTimeMarkerTemplate: \"currentTimeMarkerTemplate\",\n    validateEventTimesChanged: \"validateEventTimesChanged\",\n    resizeCursors: \"resizeCursors\"\n  },\n  outputs: {\n    eventClicked: \"eventClicked\",\n    hourSegmentClicked: \"hourSegmentClicked\",\n    eventTimesChanged: \"eventTimesChanged\",\n    beforeViewRender: \"beforeViewRender\"\n  },\n  standalone: false,\n  decls: 1,\n  vars: 27,\n  consts: [[1, \"cal-day-view\", 3, \"eventClicked\", \"hourSegmentClicked\", \"eventTimesChanged\", \"beforeViewRender\", \"daysInWeek\", \"viewDate\", \"events\", \"hourSegments\", \"hourDuration\", \"hourSegmentHeight\", \"minimumEventHeight\", \"dayStartHour\", \"dayStartMinute\", \"dayEndHour\", \"dayEndMinute\", \"refresh\", \"locale\", \"eventSnapSize\", \"tooltipPlacement\", \"tooltipTemplate\", \"tooltipAppendToBody\", \"tooltipDelay\", \"resizeCursors\", \"hourSegmentTemplate\", \"eventTemplate\", \"eventTitleTemplate\", \"eventActionsTemplate\", \"snapDraggedEvents\", \"allDayEventsLabelTemplate\", \"currentTimeMarkerTemplate\", \"validateEventTimesChanged\"]],\n  template: function CalendarDayViewComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"mwl-calendar-week-view\", 0);\n      i0.ɵɵlistener(\"eventClicked\", function CalendarDayViewComponent_Template_mwl_calendar_week_view_eventClicked_0_listener($event) {\n        return ctx.eventClicked.emit($event);\n      })(\"hourSegmentClicked\", function CalendarDayViewComponent_Template_mwl_calendar_week_view_hourSegmentClicked_0_listener($event) {\n        return ctx.hourSegmentClicked.emit($event);\n      })(\"eventTimesChanged\", function CalendarDayViewComponent_Template_mwl_calendar_week_view_eventTimesChanged_0_listener($event) {\n        return ctx.eventTimesChanged.emit($event);\n      })(\"beforeViewRender\", function CalendarDayViewComponent_Template_mwl_calendar_week_view_beforeViewRender_0_listener($event) {\n        return ctx.beforeViewRender.emit($event);\n      });\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"daysInWeek\", 1)(\"viewDate\", ctx.viewDate)(\"events\", ctx.events)(\"hourSegments\", ctx.hourSegments)(\"hourDuration\", ctx.hourDuration)(\"hourSegmentHeight\", ctx.hourSegmentHeight)(\"minimumEventHeight\", ctx.minimumEventHeight)(\"dayStartHour\", ctx.dayStartHour)(\"dayStartMinute\", ctx.dayStartMinute)(\"dayEndHour\", ctx.dayEndHour)(\"dayEndMinute\", ctx.dayEndMinute)(\"refresh\", ctx.refresh)(\"locale\", ctx.locale)(\"eventSnapSize\", ctx.eventSnapSize)(\"tooltipPlacement\", ctx.tooltipPlacement)(\"tooltipTemplate\", ctx.tooltipTemplate)(\"tooltipAppendToBody\", ctx.tooltipAppendToBody)(\"tooltipDelay\", ctx.tooltipDelay)(\"resizeCursors\", ctx.resizeCursors)(\"hourSegmentTemplate\", ctx.hourSegmentTemplate)(\"eventTemplate\", ctx.eventTemplate)(\"eventTitleTemplate\", ctx.eventTitleTemplate)(\"eventActionsTemplate\", ctx.eventActionsTemplate)(\"snapDraggedEvents\", ctx.snapDraggedEvents)(\"allDayEventsLabelTemplate\", ctx.allDayEventsLabelTemplate)(\"currentTimeMarkerTemplate\", ctx.currentTimeMarkerTemplate)(\"validateEventTimesChanged\", ctx.validateEventTimesChanged);\n    }\n  },\n  dependencies: [CalendarWeekViewComponent],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarDayViewComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mwl-calendar-day-view',\n      template: `\n    <mwl-calendar-week-view\n      class=\"cal-day-view\"\n      [daysInWeek]=\"1\"\n      [viewDate]=\"viewDate\"\n      [events]=\"events\"\n      [hourSegments]=\"hourSegments\"\n      [hourDuration]=\"hourDuration\"\n      [hourSegmentHeight]=\"hourSegmentHeight\"\n      [minimumEventHeight]=\"minimumEventHeight\"\n      [dayStartHour]=\"dayStartHour\"\n      [dayStartMinute]=\"dayStartMinute\"\n      [dayEndHour]=\"dayEndHour\"\n      [dayEndMinute]=\"dayEndMinute\"\n      [refresh]=\"refresh\"\n      [locale]=\"locale\"\n      [eventSnapSize]=\"eventSnapSize\"\n      [tooltipPlacement]=\"tooltipPlacement\"\n      [tooltipTemplate]=\"tooltipTemplate\"\n      [tooltipAppendToBody]=\"tooltipAppendToBody\"\n      [tooltipDelay]=\"tooltipDelay\"\n      [resizeCursors]=\"resizeCursors\"\n      [hourSegmentTemplate]=\"hourSegmentTemplate\"\n      [eventTemplate]=\"eventTemplate\"\n      [eventTitleTemplate]=\"eventTitleTemplate\"\n      [eventActionsTemplate]=\"eventActionsTemplate\"\n      [snapDraggedEvents]=\"snapDraggedEvents\"\n      [allDayEventsLabelTemplate]=\"allDayEventsLabelTemplate\"\n      [currentTimeMarkerTemplate]=\"currentTimeMarkerTemplate\"\n      [validateEventTimesChanged]=\"validateEventTimesChanged\"\n      (eventClicked)=\"eventClicked.emit($event)\"\n      (hourSegmentClicked)=\"hourSegmentClicked.emit($event)\"\n      (eventTimesChanged)=\"eventTimesChanged.emit($event)\"\n      (beforeViewRender)=\"beforeViewRender.emit($event)\"\n    ></mwl-calendar-week-view>\n  `\n    }]\n  }], null, {\n    viewDate: [{\n      type: Input\n    }],\n    events: [{\n      type: Input\n    }],\n    hourSegments: [{\n      type: Input\n    }],\n    hourSegmentHeight: [{\n      type: Input\n    }],\n    hourDuration: [{\n      type: Input\n    }],\n    minimumEventHeight: [{\n      type: Input\n    }],\n    dayStartHour: [{\n      type: Input\n    }],\n    dayStartMinute: [{\n      type: Input\n    }],\n    dayEndHour: [{\n      type: Input\n    }],\n    dayEndMinute: [{\n      type: Input\n    }],\n    refresh: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }],\n    eventSnapSize: [{\n      type: Input\n    }],\n    tooltipPlacement: [{\n      type: Input\n    }],\n    tooltipTemplate: [{\n      type: Input\n    }],\n    tooltipAppendToBody: [{\n      type: Input\n    }],\n    tooltipDelay: [{\n      type: Input\n    }],\n    hourSegmentTemplate: [{\n      type: Input\n    }],\n    eventTemplate: [{\n      type: Input\n    }],\n    eventTitleTemplate: [{\n      type: Input\n    }],\n    eventActionsTemplate: [{\n      type: Input\n    }],\n    snapDraggedEvents: [{\n      type: Input\n    }],\n    allDayEventsLabelTemplate: [{\n      type: Input\n    }],\n    currentTimeMarkerTemplate: [{\n      type: Input\n    }],\n    validateEventTimesChanged: [{\n      type: Input\n    }],\n    resizeCursors: [{\n      type: Input\n    }],\n    eventClicked: [{\n      type: Output\n    }],\n    hourSegmentClicked: [{\n      type: Output\n    }],\n    eventTimesChanged: [{\n      type: Output\n    }],\n    beforeViewRender: [{\n      type: Output\n    }]\n  });\n})();\nclass CalendarDayModule {}\nCalendarDayModule.ɵfac = function CalendarDayModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarDayModule)();\n};\nCalendarDayModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: CalendarDayModule,\n  declarations: [CalendarDayViewComponent],\n  imports: [CommonModule, CalendarCommonModule, CalendarWeekModule],\n  exports: [CalendarDayViewComponent]\n});\nCalendarDayModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, CalendarCommonModule, CalendarWeekModule]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarDayModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, CalendarCommonModule, CalendarWeekModule],\n      declarations: [CalendarDayViewComponent],\n      exports: [CalendarDayViewComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * The main module of this library. Example usage:\n *\n * ```typescript\n * import { CalenderModule } from 'angular-calendar';\n *\n * @NgModule({\n *   imports: [\n *     CalenderModule.forRoot()\n *   ]\n * })\n * class MyModule {}\n * ```\n *\n */\nclass CalendarModule {\n  static forRoot(dateAdapter, config = {}) {\n    return {\n      ngModule: CalendarModule,\n      providers: [dateAdapter, config.eventTitleFormatter || CalendarEventTitleFormatter, config.dateFormatter || CalendarDateFormatter, config.utils || CalendarUtils, config.a11y || CalendarA11y]\n    };\n  }\n}\nCalendarModule.ɵfac = function CalendarModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || CalendarModule)();\n};\nCalendarModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: CalendarModule,\n  imports: [CalendarCommonModule, CalendarMonthModule, CalendarWeekModule, CalendarDayModule],\n  exports: [CalendarCommonModule, CalendarMonthModule, CalendarWeekModule, CalendarDayModule]\n});\nCalendarModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CalendarCommonModule, CalendarMonthModule, CalendarWeekModule, CalendarDayModule, CalendarCommonModule, CalendarMonthModule, CalendarWeekModule, CalendarDayModule]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CalendarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CalendarCommonModule, CalendarMonthModule, CalendarWeekModule, CalendarDayModule],\n      exports: [CalendarCommonModule, CalendarMonthModule, CalendarWeekModule, CalendarDayModule]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of angular-calendar\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CalendarA11y, CalendarAngularDateFormatter, CalendarCommonModule, CalendarDateFormatter, CalendarDayModule, CalendarDayViewComponent, CalendarEventTimesChangedEventType, CalendarEventTitleFormatter, CalendarModule, CalendarMomentDateFormatter, CalendarMonthModule, CalendarMonthViewComponent, CalendarNativeDateFormatter, CalendarUtils, CalendarView, CalendarWeekModule, CalendarWeekViewComponent, DateAdapter, MOMENT, collapseAnimation, getWeekViewPeriod, CalendarA11yPipe as ɵCalendarA11yPipe, CalendarDatePipe as ɵCalendarDatePipe, CalendarEventActionsComponent as ɵCalendarEventActionsComponent, CalendarEventTitleComponent as ɵCalendarEventTitleComponent, CalendarEventTitlePipe as ɵCalendarEventTitlePipe, CalendarMonthCellComponent as ɵCalendarMonthCellComponent, CalendarMonthViewHeaderComponent as ɵCalendarMonthViewHeaderComponent, CalendarNextViewDirective as ɵCalendarNextViewDirective, CalendarOpenDayEventsComponent as ɵCalendarOpenDayEventsComponent, CalendarPreviousViewDirective as ɵCalendarPreviousViewDirective, CalendarTodayDirective as ɵCalendarTodayDirective, CalendarTooltipDirective as ɵCalendarTooltipDirective, CalendarTooltipWindowComponent as ɵCalendarTooltipWindowComponent, CalendarWeekViewCurrentTimeMarkerComponent as ɵCalendarWeekViewCurrentTimeMarkerComponent, CalendarWeekViewEventComponent as ɵCalendarWeekViewEventComponent, CalendarWeekViewHeaderComponent as ɵCalendarWeekViewHeaderComponent, CalendarWeekViewHourSegmentComponent as ɵCalendarWeekViewHourSegmentComponent, ClickDirective as ɵClickDirective, KeydownEnterDirective as ɵKeydownEnterDirective };\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAI;AAAA;AAAA,EAA6B,WAAY;AACzC,aAASA,eAAc;AAAA,IACvB;AACA,IAAAA,aAAY,UAAU,eAAe,SAAU,SAAS;AAAE,aAAO,OAAO,iBAAiB,OAAO;AAAA,IAAG;AACnG,IAAAA,aAAY,UAAU,WAAW,SAAU,SAAS,MAAM;AAAE,aAAO,KAAK,aAAa,OAAO,EAAE,IAAI;AAAA,IAAG;AACrG,IAAAA,aAAY,UAAU,qBAAqB,SAAU,SAAS;AAC1D,cAAQ,KAAK,SAAS,SAAS,UAAU,KAAK,cAAc;AAAA,IAChE;AACA,IAAAA,aAAY,UAAU,eAAe,SAAU,SAAS;AACpD,UAAI,iBAAiB,QAAQ,gBAAgB,SAAS;AACtD,aAAO,kBAAkB,mBAAmB,SAAS,mBAAmB,KAAK,mBAAmB,cAAc,GAAG;AAC7G,yBAAiB,eAAe;AAAA,MACpC;AACA,aAAO,kBAAkB,SAAS;AAAA,IACtC;AACA,IAAAA,aAAY,UAAU,WAAW,SAAU,SAAS,OAAO;AACvD,UAAI,UAAU,QAAQ;AAAE,gBAAQ;AAAA,MAAM;AACtC,UAAI;AACJ,UAAI,eAAe,EAAE,OAAO,GAAG,QAAQ,GAAG,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,OAAO,EAAE;AAC/E,UAAI,KAAK,SAAS,SAAS,UAAU,MAAM,SAAS;AAChD,qBAAa,QAAQ,sBAAsB;AAC3C,qBAAa;AAAA,UACT,KAAK,WAAW;AAAA,UAChB,QAAQ,WAAW;AAAA,UACnB,MAAM,WAAW;AAAA,UACjB,OAAO,WAAW;AAAA,UAClB,QAAQ,WAAW;AAAA,UACnB,OAAO,WAAW;AAAA,QACtB;AAAA,MACJ,OACK;AACD,YAAI,iBAAiB,KAAK,aAAa,OAAO;AAC9C,qBAAa,KAAK,OAAO,SAAS,KAAK;AACvC,YAAI,mBAAmB,SAAS,iBAAiB;AAC7C,yBAAe,KAAK,OAAO,gBAAgB,KAAK;AAAA,QACpD;AACA,qBAAa,OAAO,eAAe;AACnC,qBAAa,QAAQ,eAAe;AAAA,MACxC;AACA,iBAAW,OAAO,aAAa;AAC/B,iBAAW,UAAU,aAAa;AAClC,iBAAW,QAAQ,aAAa;AAChC,iBAAW,SAAS,aAAa;AACjC,UAAI,OAAO;AACP,mBAAW,MAAM,KAAK,MAAM,WAAW,GAAG;AAC1C,mBAAW,SAAS,KAAK,MAAM,WAAW,MAAM;AAChD,mBAAW,OAAO,KAAK,MAAM,WAAW,IAAI;AAC5C,mBAAW,QAAQ,KAAK,MAAM,WAAW,KAAK;AAAA,MAClD;AACA,aAAO;AAAA,IACX;AACA,IAAAA,aAAY,UAAU,SAAS,SAAU,SAAS,OAAO;AACrD,UAAI,UAAU,QAAQ;AAAE,gBAAQ;AAAA,MAAM;AACtC,UAAI,QAAQ,QAAQ,sBAAsB;AAC1C,UAAI,iBAAiB;AAAA,QACjB,KAAK,OAAO,cAAc,SAAS,gBAAgB;AAAA,QACnD,MAAM,OAAO,cAAc,SAAS,gBAAgB;AAAA,MACxD;AACA,UAAI,WAAW;AAAA,QACX,QAAQ,MAAM,UAAU,QAAQ;AAAA,QAChC,OAAO,MAAM,SAAS,QAAQ;AAAA,QAC9B,KAAK,MAAM,MAAM,eAAe;AAAA,QAChC,QAAQ,MAAM,SAAS,eAAe;AAAA,QACtC,MAAM,MAAM,OAAO,eAAe;AAAA,QAClC,OAAO,MAAM,QAAQ,eAAe;AAAA,MACxC;AACA,UAAI,OAAO;AACP,iBAAS,SAAS,KAAK,MAAM,SAAS,MAAM;AAC5C,iBAAS,QAAQ,KAAK,MAAM,SAAS,KAAK;AAC1C,iBAAS,MAAM,KAAK,MAAM,SAAS,GAAG;AACtC,iBAAS,SAAS,KAAK,MAAM,SAAS,MAAM;AAC5C,iBAAS,OAAO,KAAK,MAAM,SAAS,IAAI;AACxC,iBAAS,QAAQ,KAAK,MAAM,SAAS,KAAK;AAAA,MAC9C;AACA,aAAO;AAAA,IACX;AAIA,IAAAA,aAAY,UAAU,mBAAmB,SAAU,aAAa,eAAe,WAAW,cAAc;AACpG,UAAI,KAAK,UAAU,MAAM,GAAG,GAAG,KAAK,GAAG,CAAC,GAAG,mBAAmB,OAAO,SAAS,QAAQ,IAAI,KAAK,GAAG,CAAC,GAAG,qBAAqB,OAAO,SAAS,WAAW;AACtJ,UAAI,iBAAiB,eAAe,KAAK,OAAO,aAAa,KAAK,IAAI,KAAK,SAAS,aAAa,KAAK;AACtG,UAAI,iBAAiB,KAAK,aAAa,aAAa;AACpD,UAAI,YAAY,WAAW,eAAe,SAAS;AACnD,UAAI,eAAe,WAAW,eAAe,YAAY;AACzD,UAAI,aAAa,WAAW,eAAe,UAAU;AACrD,UAAI,cAAc,WAAW,eAAe,WAAW;AACvD,UAAI,cAAc;AAClB,UAAI,eAAe;AACnB,cAAQ,kBAAkB;AAAA,QACtB,KAAK;AACD,wBAAe,eAAe,OAAO,cAAc,eAAe,YAAY;AAC9E;AAAA,QACJ,KAAK;AACD,wBAAe,eAAe,MAAM,eAAe;AACnD;AAAA,QACJ,KAAK;AACD,yBAAgB,eAAe,QAAQ,cAAc,cAAc,aAAa;AAChF;AAAA,QACJ,KAAK;AACD,yBAAgB,eAAe,OAAO,eAAe;AACrD;AAAA,MACR;AACA,cAAQ,oBAAoB;AAAA,QACxB,KAAK;AACD,wBAAc,eAAe;AAC7B;AAAA,QACJ,KAAK;AACD,wBAAc,eAAe,MAAM,eAAe,SAAS,cAAc;AACzE;AAAA,QACJ,KAAK;AACD,yBAAe,eAAe;AAC9B;AAAA,QACJ,KAAK;AACD,yBAAe,eAAe,OAAO,eAAe,QAAQ,cAAc;AAC1E;AAAA,QACJ,KAAK;AACD,cAAI,qBAAqB,SAAS,qBAAqB,UAAU;AAC7D,2BAAgB,eAAe,OAAO,eAAe,QAAQ,IAAI,cAAc,cAAc;AAAA,UACjG,OACK;AACD,0BAAe,eAAe,MAAM,eAAe,SAAS,IAAI,cAAc,eAAe;AAAA,UACjG;AACA;AAAA,MACR;AAGA,oBAAc,MAAM,YAAY,eAAe,KAAK,MAAM,YAAY,IAAI,SAAS,KAAK,MAAM,WAAW,IAAI;AAE7G,UAAI,cAAc,cAAc,sBAAsB;AACtD,UAAI,OAAO,SAAS;AACpB,UAAI,eAAe,OAAO,eAAe,KAAK;AAC9C,UAAI,cAAc,OAAO,cAAc,KAAK;AAC5C,aAAO,YAAY,QAAQ,KAAK,YAAY,OAAO,KAAK,YAAY,SAAS,eACzE,YAAY,UAAU;AAAA,IAC9B;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,IAAI,qBAAqB;AACzB,IAAI,kBAAkB,IAAI,YAAY;AAW/B,SAAS,iBAAiB,aAAa,eAAe,WAAW,cAAc,WAAW;AAC7F,MAAI,gBAAgB,MAAM,QAAQ,SAAS,IAAI,YAAY,UAAU,MAAM,kBAAkB;AAC7F,MAAI,oBAAoB;AAAA,IACpB;AAAA,IAAO;AAAA,IAAU;AAAA,IAAQ;AAAA,IAAS;AAAA,IAAY;AAAA,IAAa;AAAA,IAAe;AAAA,IAAgB;AAAA,IAAY;AAAA,IACtG;AAAA,IAAa;AAAA,EACjB;AACA,MAAI,YAAY,cAAc;AAC9B,MAAI,qBAAqB,SAAU,iBAAiB;AAChD,QAAI,KAAK,gBAAgB,MAAM,GAAG,GAAG,UAAU,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC;AACtE,QAAI,UAAU,CAAC;AACf,QAAI,WAAW;AACX,cAAQ,KAAK,YAAY,MAAM,OAAO;AACtC,UAAI,WAAW;AACX,gBAAQ,KAAK,YAAY,MAAM,UAAU,MAAM,SAAS;AAAA,MAC5D;AACA,cAAQ,QAAQ,SAAU,WAAW;AAAE,kBAAU,IAAI,SAAS;AAAA,MAAG,CAAC;AAAA,IACtE;AACA,WAAO;AAAA,EACX;AAEA,MAAI,WAAW;AACX,sBAAkB,QAAQ,SAAU,mBAAmB;AAAE,gBAAU,OAAO,YAAY,MAAM,iBAAiB;AAAA,IAAG,CAAC;AAAA,EACrH;AAEA,MAAI,UAAU,cAAc,UAAU,SAAU,KAAK;AAAE,WAAO,QAAQ;AAAA,EAAQ,CAAC;AAC/E,MAAI,WAAW,GAAG;AACd,sBAAkB,QAAQ,SAAU,KAAK;AACrC,UAAI,cAAc,KAAK,SAAU,KAAK;AAAE,eAAO,IAAI,OAAO,MAAM,GAAG,MAAM;AAAA,MAAI,CAAC,KAAK,MAAM;AACrF,sBAAc,OAAO,WAAW,GAAG,GAAG;AAAA,MAC1C;AAAA,IACJ,CAAC;AAAA,EACL;AAGA,MAAIC,SAAQ,cAAc;AAC1B,EAAAA,OAAM,WAAW;AACjB,EAAAA,OAAM,MAAM;AACZ,EAAAA,OAAM,OAAO;AACb,EAAAA,OAAM,aAAa,IAAI;AACvB,MAAI;AACJ,MAAI,eAAe;AACnB,WAAS,KAAK,GAAG,kBAAkB,eAAe,KAAK,gBAAgB,QAAQ,MAAM;AACjF,oBAAgB,gBAAgB,EAAE;AAClC,QAAI,eAAe,mBAAmB,aAAa;AACnD,QAAI,gBAAgB,iBAAiB,aAAa,eAAe,eAAe,YAAY,GAAG;AAC3F,qBAAe;AACf;AAAA,IACJ;AAEA,QAAI,WAAW;AACX,mBAAa,QAAQ,SAAU,WAAW;AAAE,kBAAU,OAAO,SAAS;AAAA,MAAG,CAAC;AAAA,IAC9E;AAAA,EACJ;AACA,MAAI,CAAC,cAAc;AAEf,oBAAgB,cAAc,CAAC;AAC/B,uBAAmB,aAAa;AAChC,oBAAgB,iBAAiB,aAAa,eAAe,eAAe,YAAY;AAAA,EAC5F;AACA,SAAO;AACX;;;ACrNA,IAAI,WAAsC,WAAY;AAClD,aAAW,OAAO,UAAU,SAAS,GAAG;AACpC,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,UAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;AACA,IAAI,gBAAgD,SAAU,IAAI,MAAM,MAAM;AAC1E,MAAI,QAAQ,UAAU,WAAW,EAAG,UAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACjF,QAAI,MAAM,EAAE,KAAK,OAAO;AACpB,UAAI,CAAC,GAAI,MAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,CAAC;AACnD,SAAG,CAAC,IAAI,KAAK,CAAC;AAAA,IAClB;AAAA,EACJ;AACA,SAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AAC3D;AACO,IAAI;AAAA,CACV,SAAUC,eAAc;AACrB,EAAAA,cAAaA,cAAa,QAAQ,IAAI,CAAC,IAAI;AAC3C,EAAAA,cAAaA,cAAa,QAAQ,IAAI,CAAC,IAAI;AAC3C,EAAAA,cAAaA,cAAa,SAAS,IAAI,CAAC,IAAI;AAC5C,EAAAA,cAAaA,cAAa,WAAW,IAAI,CAAC,IAAI;AAC9C,EAAAA,cAAaA,cAAa,UAAU,IAAI,CAAC,IAAI;AAC7C,EAAAA,cAAaA,cAAa,QAAQ,IAAI,CAAC,IAAI;AAC3C,EAAAA,cAAaA,cAAa,UAAU,IAAI,CAAC,IAAI;AACjD,GAAG,iBAAiB,eAAe,CAAC,EAAE;AACtC,IAAI,uBAAuB;AAAA,EACvB,aAAa;AAAA,EACb,aAAa;AACjB;AACA,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAI,kBAAkB;AACf,IAAI,iBAAiB,KAAK,KAAK;AACtC,SAAS,mBAAmB,aAAa,IAAI;AACzC,MAAI,YAAY,GAAG,WAAW,UAAU,GAAG,SAAS,WAAW,GAAG,UAAU,YAAY,GAAG;AAC3F,MAAI,SAAS,SAAS,GAAG;AACrB,WAAO;AAAA,EACX;AACA,MAAI,aAAa,YAAY,YAAY,SAAS,YAAY,QAAQ,UAAU,YAAY;AAC5F,MAAI,UAAU,WAAW,WAAW,UAAU,CAAC;AAC/C,MAAI,WAAW,OAAO,SAAS;AAC/B,MAAI,SAAS,OAAO,OAAO;AAC3B,MAAI,SAAS;AACb,MAAI,UAAU;AACd,MAAI,UAAU,WAAY;AACtB,QAAI,MAAM,OAAO,OAAO;AACxB,QAAI,SAAS,KAAK,SAAU,aAAa;AAAE,aAAO,gBAAgB;AAAA,IAAK,CAAC,GAAG;AACvE,gBAAU,yBAAyB,aAAa;AAAA,QAC5C;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,IACL;AACA,cAAU,QAAQ,SAAS,CAAC;AAAA,EAChC;AACA,SAAO,UAAU,SAAS;AACtB,YAAQ;AAAA,EACZ;AACA,SAAO;AACX;AACA,SAAS,yBAAyB,aAAa,IAAI;AAC/C,MAAI,YAAY,GAAG,WAAW,MAAM,GAAG,KAAK,WAAW,GAAG,UAAU,SAAS,GAAG,QAAQ,YAAY,GAAG,WAAW,UAAU,GAAG;AAC/H,MAAI,sBAAsB,YAAY,qBAAqB,WAAW,YAAY,UAAU,aAAa,YAAY;AACrH,MAAI,cAAc,WAAW;AACzB,QAAI,QAAQ,UAAU;AAClB,aAAO,oBAAoB,SAAS,SAAS,GAAG,SAAS,IAAI;AAAA,IACjE,WACS,QAAQ,QAAQ;AACrB,aAAO,oBAAoB,SAAS,WAAW,OAAO,CAAC,IAAI;AAAA,IAC/D;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,qBAAqB,aAAa,IAAI;AAC3C,MAAI,QAAQ,GAAG,OAAO,SAAS,GAAG,QAAQ,kBAAkB,GAAG,iBAAiB,WAAW,GAAG,UAAU,YAAY,GAAG,WAAW,kBAAkB,GAAG;AACvJ,MAAI,MAAM,YAAY,KAAK,sBAAsB,YAAY,qBAAqB,UAAU,YAAY,SAAS,WAAW,YAAY,UAAU,mBAAmB,YAAY;AACjL,MAAI,OAAO;AACX,MAAI,QAAQ,IAAI,CAAC,MAAM,OAAO,eAAe,CAAC;AAC9C,MAAI,MAAM,KAAK;AACX,YAAQ,WAAW;AAAA,MACf,KAAK;AACD,eAAO,oBAAoB,MAAM,KAAK,KAAK;AAC3C;AAAA,MACJ;AACI,eACI,iBAAiB,QAAQ,SAAS,MAAM,GAAG,GAAG,CAAC,GAAG,KAAK,IACnD;AACR;AAAA,IACR;AAAA,EACJ;AACA,MAAI,gBAAgB,SAAS;AAC7B,MAAI,cAAc,gBAAgB;AAGlC,MAAI,gBAAgB,kBAAkB;AACtC,MAAI,cAAc,eAAe;AAC7B,WAAO,gBAAgB;AAAA,EAC3B;AACA,UAAQ,mBAAmB,aAAa;AAAA,IACpC,WAAW;AAAA,IACX,SAAS;AAAA,IACT;AAAA,IACA;AAAA,EACJ,CAAC;AACD,SAAO,OAAO;AAClB;AACA,SAAS,uBAAuB,aAAa,IAAI;AAC7C,MAAI,QAAQ,GAAG,OAAO,kBAAkB,GAAG,aAAa,WAAW,GAAG,UAAU,YAAY,GAAG;AAC/F,MAAI,mBAAmB,YAAY,kBAAkB,aAAa,YAAY,YAAY,sBAAsB,YAAY;AAC5H,MAAI,MAAM,QAAQ,iBAAiB;AAC/B,WAAO;AAAA,EACX;AACA,MAAI,SAAS;AACb,UAAQ,WAAW;AAAA,IACf,KAAK;AACD,eACI,iBAAiB,WAAW,MAAM,KAAK,GAAG,eAAe,IACrD;AACR;AAAA,IACJ,KAAK;AACD,eAAS,oBAAoB,MAAM,OAAO,eAAe;AACzD;AAAA,EACR;AACA,YAAU,mBAAmB,aAAa;AAAA,IACtC,WAAW;AAAA,IACX,SAAS;AAAA,IACT;AAAA,IACA;AAAA,EACJ,CAAC;AACD,SAAO,KAAK,IAAI,SAAS,cAAc;AAC3C;AACA,SAAS,gBAAgB,aAAa,IAAI;AACtC,MAAI,QAAQ,GAAG,OAAO,cAAc,GAAG,aAAa,YAAY,GAAG;AACnE,MAAI,eAAe,YAAY;AAC/B,MAAI,aAAa,MAAM;AACvB,MAAI,WAAW,MAAM,OAAO,MAAM;AAClC,MAAI,aAAa,eAAe,aAAa,WAAW;AACpD,WAAO;AAAA,EACX;AACA,MAAI,WAAW,eAAe,WAAW,WAAW;AAChD,WAAO;AAAA,EACX;AACA,MAAI,aAAa,eAAe,WAAW,WAAW;AAClD,WAAO;AAAA,EACX;AACA,MAAI,aAAa,YAAY,WAAW,KACpC,aAAa,YAAY,SAAS,GAAG;AACrC,WAAO;AAAA,EACX;AACA,MAAI,aAAa,UAAU,WAAW,KAClC,aAAa,UAAU,SAAS,GAAG;AACnC,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACO,SAAS,kBAAkB,aAAa,IAAI;AAC/C,MAAI,SAAS,GAAG,QAAQ,cAAc,GAAG,aAAa,YAAY,GAAG;AACrE,SAAO,OAAO,OAAO,SAAU,OAAO;AAClC,WAAO,gBAAgB,aAAa,EAAE,OAAc,aAA0B,UAAqB,CAAC;AAAA,EACxG,CAAC;AACL;AACA,SAAS,WAAW,aAAa,IAAI;AACjC,MAAI,OAAO,GAAG,MAAM,KAAK,GAAG,aAAa,cAAc,OAAO,SAAS,uBAAuB;AAC9F,MAAI,aAAa,YAAY,YAAY,YAAY,YAAY,WAAW,SAAS,YAAY;AACjG,MAAI,QAAQ,WAAW,oBAAI,KAAK,CAAC;AACjC,MAAI,MAAM,OAAO,IAAI;AACrB,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA,QAAQ,OAAO;AAAA,IACf,SAAS,UAAU,MAAM,KAAK;AAAA,IAC9B,UAAU,OAAO;AAAA,IACjB,WAAW,YAAY,QAAQ,GAAG,IAAI;AAAA,EAC1C;AACJ;AACO,SAAS,kBAAkB,aAAa,IAAI;AAC/C,MAAI,WAAW,GAAG,UAAU,eAAe,GAAG,cAAc,KAAK,GAAG,UAAU,WAAW,OAAO,SAAS,CAAC,IAAI,IAAI,cAAc,GAAG,aAAa,KAAK,GAAG,WAAW,YAAY,OAAO,SAAS,YAAY,YAAY,UAAU,EAAE,aAA2B,CAAC,IAAI,IAAI,KAAK,GAAG,SAAS,UAAU,OAAO,SAAS,YAAY,QAAQ,WAAW,YAAY,IAAI;AACjW,MAAI,UAAU,YAAY,SAAS,SAAS,YAAY;AACxD,MAAI,OAAO,CAAC;AACZ,MAAI,OAAO;AACX,SAAO,OAAO,SAAS;AACnB,QAAI,CAAC,SAAS,KAAK,SAAU,GAAG;AAAE,aAAO,OAAO,IAAI,MAAM;AAAA,IAAG,CAAC,GAAG;AAC7D,WAAK,KAAK,WAAW,aAAa,EAAE,MAAY,YAAyB,CAAC,CAAC;AAAA,IAC/E;AACA,WAAO,QAAQ,MAAM,CAAC;AAAA,EAC1B;AACA,SAAO;AACX;AACO,SAAS,kCAAkC,aAAa,IAAI;AAC/D,MAAI,QAAQ,GAAG,OAAO,QAAQ,GAAG,OAAO,WAAW,GAAG;AACtD,MAAI,OAAO;AACX,MAAI,OAAO;AACX,SAAO,OAAO,OAAO;AACjB,QAAI,SAAS,QAAQ,YAAY,OAAO,IAAI,CAAC,MAAM,IAAI;AACnD;AAAA,IACJ;AACA,WAAO,YAAY,QAAQ,MAAM,CAAC;AAAA,EACtC;AACA,SAAO;AACX;AACO,SAAS,oBAAoB,aAAa,IAAI;AACjD,MAAI,KAAK,GAAG,QAAQ,SAAS,OAAO,SAAS,CAAC,IAAI,IAAI,KAAK,GAAG,UAAU,WAAW,OAAO,SAAS,CAAC,IAAI,IAAI,KAAK,GAAG,WAAW,YAAY,OAAO,SAAS,SAAS,IAAI,KAAK,GAAG,0BAA0B,2BAA2B,OAAO,SAAS,QAAQ,IAAI,YAAY,GAAG,WAAW,UAAU,GAAG;AACxS,cAAY,YAAY,WAAW,SAAS;AAC5C,YAAU,YAAY,SAAS,OAAO;AACtC,MAAI,sBAAsB,YAAY,qBAAqB,mBAAmB,YAAY;AAC1F,MAAI,WAAW,kCAAkC,aAAa;AAAA,IAC1D,OAAO;AAAA,IACP,OAAO;AAAA,IACP;AAAA,EACJ,CAAC;AACD,MAAI,kBAAkB,iBAAiB,SAAS,SAAS,IAAI;AAC7D,MAAI,eAAe,OACd,OAAO,SAAU,OAAO;AAAE,WAAO,MAAM;AAAA,EAAQ,CAAC,EAChD,IAAI,SAAU,OAAO;AACtB,QAAI,SAAS,uBAAuB,aAAa;AAAA,MAC7C;AAAA,MACA,aAAa;AAAA,MACb;AAAA,MACA;AAAA,IACJ,CAAC;AACD,QAAI,OAAO,qBAAqB,aAAa;AAAA,MACzC;AAAA,MACA;AAAA,MACA,iBAAiB;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AACD,WAAO,EAAE,OAAc,QAAgB,KAAW;AAAA,EACtD,CAAC,EACI,OAAO,SAAU,GAAG;AAAE,WAAO,EAAE,SAAS;AAAA,EAAU,CAAC,EACnD,OAAO,SAAU,GAAG;AAAE,WAAO,EAAE,OAAO;AAAA,EAAG,CAAC,EAC1C,IAAI,SAAU,OAAO;AAAE,WAAQ;AAAA,MAChC,OAAO,MAAM;AAAA,MACb,QAAQ,MAAM;AAAA,MACd,MAAM,MAAM;AAAA,MACZ,kBAAkB,MAAM,MAAM,QAAQ;AAAA,MACtC,gBAAgB,MAAM,MAAM,OAAO,MAAM,MAAM,SAAS;AAAA,IAC5D;AAAA,EAAI,CAAC,EACA,KAAK,SAAU,OAAO,OAAO;AAC9B,QAAI,mBAAmB,oBAAoB,MAAM,MAAM,OAAO,MAAM,MAAM,KAAK;AAC/E,QAAI,qBAAqB,GAAG;AACxB,aAAO,oBAAoB,MAAM,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM,MAAM,KAAK;AAAA,IACzG;AACA,WAAO;AAAA,EACX,CAAC;AACD,MAAI,kBAAkB,CAAC;AACvB,MAAI,kBAAkB,CAAC;AACvB,eAAa,QAAQ,SAAU,OAAO,OAAO;AACzC,QAAI,gBAAgB,QAAQ,KAAK,MAAM,IAAI;AACvC,sBAAgB,KAAK,KAAK;AAC1B,UAAI,YAAY,MAAM,OAAO,MAAM;AACnC,UAAI,iBAAiB,aAChB,MAAM,QAAQ,CAAC,EACf,OAAO,SAAU,WAAW;AAC7B,YAAI,UAAU,UAAU,aACpB,YAAY,UAAU,QAAQ,mBAC9B,gBAAgB,QAAQ,SAAS,MAAM,IAAI;AAC3C,cAAI,kBAAkB,UAAU,SAAS;AACzC,cAAI,CAAC,0BAA0B;AAC3B,sBAAU,SAAS;AAAA,UACvB;AACA,uBAAa,UAAU,OAAO;AAC9B,0BAAgB,KAAK,SAAS;AAC9B,iBAAO;AAAA,QACX;AAAA,MACJ,CAAC;AACD,UAAI,aAAa,cAAc,CAAC,KAAK,GAAG,gBAAgB,IAAI;AAC5D,UAAI,KAAK,WACJ,OAAO,SAAU,WAAW;AAAE,eAAO,UAAU,MAAM;AAAA,MAAI,CAAC,EAC1D,IAAI,SAAU,WAAW;AAAE,eAAO,UAAU,MAAM;AAAA,MAAI,CAAC,EACvD,KAAK,GAAG;AACb,sBAAgB,KAAK,SAAS,EAAE,KAAK,WAAW,GAAI,KAAK,EAAE,GAAO,IAAI,CAAC,CAAE,CAAC;AAAA,IAC9E;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AACA,SAAS,oBAAoB,aAAa,IAAI;AAC1C,MAAI,SAAS,GAAG,QAAQ,WAAW,GAAG,UAAU,eAAe,GAAG,cAAc,eAAe,GAAG,cAAc,WAAW,GAAG,UAAU,SAAS,GAAG,QAAQ,eAAe,GAAG,cAAc,WAAW,GAAG,UAAU,cAAc,GAAG,aAAa,gBAAgB,GAAG,eAAe,YAAY,GAAG,WAAW,UAAU,GAAG,SAAS,qBAAqB,GAAG;AAC5V,MAAI,kBAAkB,mBAAmB,aAAa;AAAA,IAClD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,MAAI,WAAW,kBAAkB,aAAa;AAAA,IAC1C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,MAAI,WAAW,YAAY,UAAU,aAAa,YAAY,YAAY,WAAW,YAAY,UAAU,aAAa,YAAY;AACpI,SAAO,SAAS,IAAI,SAAU,KAAK;AAC/B,QAAI,UAAU,WAAW,aAAa;AAAA,MAClC;AAAA,MACA,UAAU,IAAI;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,IACJ,CAAC;AACD,QAAI,QAAQ,gBAAgB,IAAI,SAAU,MAAM;AAC5C,UAAI,WAAW,KAAK,SAAS,IAAI,SAAU,SAAS;AAChD,YAAI,OAAO,WAAW,SAAS,IAAI,MAAM,SAAS,QAAQ,IAAI,CAAC,GAAG,WAAW,QAAQ,IAAI,CAAC;AAC1F,eAAO,SAAS,SAAS,CAAC,GAAG,OAAO,GAAG,EAAE,KAAW,CAAC;AAAA,MACzD,CAAC;AACD,aAAO,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,EAAE,SAAmB,CAAC;AAAA,IAC9D,CAAC;AACD,aAAS,eAAe,WAAW,uBAAuB;AACtD,UAAI,cAAc,KAAK,IAAI,MAAM,MAAM,sBAAsB,IAAI,SAAU,QAAQ;AAAE,eAAO,OAAO,OAAO;AAAA,MAAG,CAAC,CAAC;AAC/G,UAAI,wBAAwB,UACvB,OAAO,SAAU,QAAQ;AAAE,eAAO,OAAO,QAAQ;AAAA,MAAa,CAAC,EAC/D,OAAO,SAAU,QAAQ;AAC1B,eAAQ,6BAA6B,uBAAuB,OAAO,KAAK,OAAO,MAAM,OAAO,MAAM,EAAE,SAAS;AAAA,MACjH,CAAC;AACD,UAAI,sBAAsB,SAAS,GAAG;AAClC,eAAO,eAAe,WAAW,qBAAqB;AAAA,MAC1D,OACK;AACD,eAAO;AAAA,MACX;AAAA,IACJ;AACA,QAAI,eAAe,QAAQ,OAAO,IAAI,SAAU,OAAO;AACnD,UAAI,cAAc,eAAe,QAAQ,QAAQ,6BAA6B,QAAQ,QAAQ,MAAM,KAAK,MAAM,MAAM,MAAM,MAAM,CAAC;AAClI,UAAI,QAAQ,MAAM;AAClB,aAAO,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG,EAAE,MAAM,MAAM,OAAO,OAAO,MAAa,CAAC;AAAA,IACnF,CAAC;AACD,WAAO;AAAA,MACH;AAAA,MACA,MAAM,IAAI;AAAA,MACV,QAAQ,aAAa,IAAI,SAAU,OAAO;AACtC,YAAI,oBAAoB,6BAA6B,aAAa,OAAO,SAAU,YAAY;AAAE,iBAAO,WAAW,OAAO,MAAM;AAAA,QAAM,CAAC,GAAG,MAAM,KAAK,MAAM,MAAM,MAAM,MAAM;AAC7K,YAAI,kBAAkB,SAAS,GAAG;AAC9B,iBAAO,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG,EAAE,OAAO,KAAK,IAAI,MAAM,MAAM,kBAAkB,IAAI,SAAU,YAAY;AAAE,mBAAO,WAAW;AAAA,UAAM,CAAC,CAAC,IAAI,MAAM,KAAK,CAAC;AAAA,QAC/J;AACA,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ,CAAC;AACL;AACO,SAAS,YAAY,aAAa,IAAI;AACzC,MAAI,KAAK,GAAG,QAAQ,SAAS,OAAO,SAAS,CAAC,IAAI,IAAI,WAAW,GAAG,UAAU,eAAe,GAAG,cAAc,KAAK,GAAG,UAAU,WAAW,OAAO,SAAS,CAAC,IAAI,IAAI,KAAK,GAAG,WAAW,YAAY,OAAO,SAAS,SAAS,IAAI,KAAK,GAAG,0BAA0B,2BAA2B,OAAO,SAAS,QAAQ,IAAI,eAAe,GAAG,cAAc,eAAe,GAAG,cAAc,WAAW,GAAG,UAAU,SAAS,GAAG,QAAQ,cAAc,GAAG,aAAa,gBAAgB,GAAG,eAAe,qBAAqB,GAAG,oBAAoB,KAAK,GAAG,WAAW,YAAY,OAAO,SAAS,YAAY,YAAY,UAAU,EAAE,aAA2B,CAAC,IAAI,IAAI,KAAK,GAAG,SAAS,UAAU,OAAO,SAAS,YAAY,UAAU,UAAU,EAAE,aAA2B,CAAC,IAAI;AACrvB,MAAI,CAAC,QAAQ;AACT,aAAS,CAAC;AAAA,EACd;AACA,MAAI,aAAa,YAAY,YAAY,WAAW,YAAY;AAChE,cAAY,WAAW,SAAS;AAChC,YAAU,SAAS,OAAO;AAC1B,MAAI,iBAAiB,kBAAkB,aAAa;AAAA,IAChD;AAAA,IACA,aAAa;AAAA,IACb,WAAW;AAAA,EACf,CAAC;AACD,MAAI,SAAS,kBAAkB,aAAa;AAAA,IACxC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,SAAO;AAAA,IACH,iBAAiB,oBAAoB,aAAa;AAAA,MAC9C,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,IACD,QAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,OAAO,OAAO,CAAC,EAAE;AAAA,MACjB,KAAK,SAAS,OAAO,OAAO,SAAS,CAAC,EAAE,IAAI;AAAA,IAChD;AAAA,IACA,aAAa,oBAAoB,aAAa;AAAA,MAC1C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AACO,SAAS,aAAa,aAAa,IAAI;AAC1C,MAAI,KAAK,GAAG,QAAQ,SAAS,OAAO,SAAS,CAAC,IAAI,IAAI,WAAW,GAAG,UAAU,eAAe,GAAG,cAAc,KAAK,GAAG,UAAU,WAAW,OAAO,SAAS,CAAC,IAAI,IAAI,KAAK,GAAG,WAAW,YAAY,OAAO,SAAS,YAAY,aAAa,QAAQ,IAAI,IAAI,KAAK,GAAG,SAAS,UAAU,OAAO,SAAS,YAAY,WAAW,QAAQ,IAAI,IAAI,cAAc,GAAG;AAC/V,MAAI,CAAC,QAAQ;AACT,aAAS,CAAC;AAAA,EACd;AACA,MAAI,cAAc,YAAY,aAAa,YAAY,YAAY,WAAW,mBAAmB,YAAY,kBAAkB,aAAa,YAAY,YAAY,WAAW,YAAY,UAAU,WAAW,YAAY,UAAU,cAAc,YAAY,aAAa,SAAS,YAAY;AAClS,MAAI,QAAQ,YAAY,WAAW,EAAE,aAA2B,CAAC;AACjE,MAAI,MAAM,UAAU,SAAS,EAAE,aAA2B,CAAC;AAC3D,MAAI,gBAAgB,kBAAkB,aAAa;AAAA,IAC/C;AAAA,IACA,aAAa;AAAA,IACb,WAAW;AAAA,EACf,CAAC;AACD,MAAI,kBAAkB,CAAC;AACvB,MAAI;AACJ,MAAI,UAAU,SAAUC,IAAG;AAEvB,QAAI;AACJ,QAAI,cAAc;AACd,aAAO,WAAW,SAAS,cAAc,YAAY,CAAC;AACtD,UAAI,aAAa,QAAQ,MAAM,KAAK,QAAQ,GAAG;AAG3C,eAAO,WAAW,SAAS,cAAc,eAAe,CAAC,CAAC;AAAA,MAC9D;AACA,qBAAe;AAAA,IACnB,OACK;AACD,aAAO,eAAe;AAAA,IAC1B;AACA,QAAI,CAAC,SAAS,KAAK,SAAU,GAAG;AAAE,aAAO,OAAO,IAAI,MAAM;AAAA,IAAG,CAAC,GAAG;AAC7D,UAAI,MAAM,WAAW,aAAa;AAAA,QAC9B;AAAA,QACA;AAAA,MACJ,CAAC;AACD,UAAI,iBAAiB,kBAAkB,aAAa;AAAA,QAChD,QAAQ;AAAA,QACR,aAAa,WAAW,IAAI;AAAA,QAC5B,WAAW,SAAS,IAAI;AAAA,MAC5B,CAAC;AACD,UAAI,UAAU,YAAY,MAAM,QAAQ;AACxC,UAAI,SAAS;AACb,UAAI,aAAa,eAAe;AAChC,sBAAgB,KAAK,GAAG;AAAA,IAC5B;AAAA,EACJ;AACA,WAAS,IAAI,GAAG,IAAI,iBAAiB,KAAK,KAAK,IAAI,GAAG,KAAK;AACvD,YAAQ,CAAC;AAAA,EACb;AACA,MAAI,OAAO,CAAC;AACZ,MAAI,yBAAyB,eAAe,SAAS;AACrD,MAAI,yBAAyB,cAAc;AACvC,aAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK,wBAAwB;AACrE,UAAI,MAAM,gBAAgB,MAAM,GAAG,IAAI,sBAAsB;AAC7D,UAAI,eAAe,IAAI,KAAK,SAAU,KAAK;AAAE,eAAO,aAAa,IAAI,QAAQ,IAAI,OAAO;AAAA,MAAS,CAAC;AAClG,UAAI,cAAc;AACd,eAAO,cAAc,cAAc,CAAC,GAAG,MAAM,IAAI,GAAG,KAAK,IAAI;AAAA,MACjE;AAAA,IACJ;AAAA,EACJ,OACK;AACD,WAAO;AAAA,EACX;AACA,MAAI,OAAO,KAAK,MAAM,KAAK,SAAS,sBAAsB;AAC1D,MAAI,aAAa,CAAC;AAClB,WAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC3B,eAAW,KAAK,IAAI,sBAAsB;AAAA,EAC9C;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,MACJ,OAAO,KAAK,CAAC,EAAE;AAAA,MACf,KAAK,SAAS,KAAK,KAAK,SAAS,CAAC,EAAE,IAAI;AAAA,MACxC,QAAQ;AAAA,IACZ;AAAA,EACJ;AACJ;AACA,SAAS,6BAA6B,QAAQ,KAAK,QAAQ;AACvD,SAAO,OAAO,OAAO,SAAU,eAAe;AAC1C,QAAI,mBAAmB,cAAc;AACrC,QAAI,sBAAsB,cAAc,MAAM,cAAc;AAC5D,QAAI,MAAM,uBAAuB,sBAAsB,QAAQ;AAC3D,aAAO;AAAA,IACX,WACS,MAAM,oBAAoB,mBAAmB,QAAQ;AAC1D,aAAO;AAAA,IACX,WACS,oBAAoB,OAAO,UAAU,qBAAqB;AAC/D,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX,CAAC;AACL;AACA,SAAS,WAAW,aAAa,IAAI;AACjC,MAAI,SAAS,GAAG,QAAQ,WAAW,GAAG,UAAU,eAAe,GAAG,cAAc,WAAW,GAAG,UAAU,SAAS,GAAG,QAAQ,aAAa,GAAG,YAAY,gBAAgB,GAAG,eAAe,eAAe,GAAG,cAAc,qBAAqB,GAAG;AAClP,MAAI,aAAa,YAAY,YAAY,WAAW,YAAY,UAAU,aAAa,YAAY,YAAY,gBAAgB,YAAY,eAAe,WAAW,YAAY,UAAU,sBAAsB,YAAY;AAC7N,MAAI,cAAc,WAAW,SAAS,WAAW,QAAQ,GAAG,cAAc,SAAS,IAAI,CAAC,GAAG,gBAAgB,SAAS,MAAM,CAAC;AAC3H,MAAI,YAAY,WAAW,SAAS,cAAc,SAAS,QAAQ,CAAC,GAAG,cAAc,OAAO,IAAI,CAAC,GAAG,gBAAgB,OAAO,MAAM,CAAC;AAClI,YAAU,WAAW,IAAI,GAAG;AAC5B,MAAI,oBAAoB,CAAC;AACzB,MAAI,iBAAiB,kBAAkB,aAAa;AAAA,IAChD,QAAQ,OAAO,OAAO,SAAU,OAAO;AAAE,aAAO,CAAC,MAAM;AAAA,IAAQ,CAAC;AAAA,IAChE,aAAa;AAAA,IACb,WAAW;AAAA,EACf,CAAC;AACD,MAAI,gBAAgB,eACf,KAAK,SAAU,QAAQ,QAAQ;AAChC,WAAO,OAAO,MAAM,QAAQ,IAAI,OAAO,MAAM,QAAQ;AAAA,EACzD,CAAC,EACI,IAAI,SAAU,OAAO;AACtB,QAAI,aAAa,MAAM;AACvB,QAAI,WAAW,MAAM,OAAO;AAC5B,QAAI,kBAAkB,aAAa;AACnC,QAAI,eAAe,WAAW;AAC9B,QAAI,qBAAsB,eAAe,iBAAkB,gBAAgB;AAC3E,QAAI,MAAM;AACV,QAAI,aAAa,aAAa;AAE1B,UAAI,cAAc,YAAY,kBAAkB,UAAU;AAC1D,UAAI,cAAc,YAAY,kBAAkB,WAAW;AAC3D,UAAI,OAAO,cAAc;AACzB,aAAO,oBAAoB,YAAY,WAAW,IAAI;AAAA,IAC1D;AACA,WAAO;AACP,UAAM,KAAK,MAAM,GAAG;AACpB,QAAI,YAAY,kBAAkB,cAAc;AAChD,QAAI,UAAU,eAAe,YAAY;AACzC,QAAI,iBAAiB,YAAY,kBAAkB,SAAS,IACxD,YAAY,kBAAkB,OAAO;AACzC,QAAI,SAAS,oBAAoB,SAAS,SAAS,IAAI;AACvD,QAAI,CAAC,MAAM,KAAK;AACZ,eAAS;AAAA,IACb,OACK;AACD,gBAAU;AAAA,IACd;AACA,QAAI,sBAAsB,SAAS,oBAAoB;AACnD,eAAS;AAAA,IACb;AACA,aAAS,KAAK,MAAM,MAAM;AAC1B,QAAI,SAAS,MAAM;AACnB,QAAI,4BAA4B,6BAA6B,mBAAmB,KAAK,MAAM;AAC3F,QAAI,OAAO;AACX,WAAO,0BAA0B,KAAK,SAAU,eAAe;AAAE,aAAO,cAAc,SAAS;AAAA,IAAM,CAAC,GAAG;AACrG,cAAQ;AAAA,IACZ;AACA,QAAI,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,sBAAkB,KAAK,QAAQ;AAC/B,WAAO;AAAA,EACX,CAAC;AACD,MAAI,QAAQ,KAAK,IAAI,MAAM,MAAM,cAAc,IAAI,SAAU,OAAO;AAAE,WAAO,MAAM,OAAO,MAAM;AAAA,EAAO,CAAC,CAAC;AACzG,MAAI,eAAe,kBAAkB,aAAa;AAAA,IAC9C,QAAQ,OAAO,OAAO,SAAU,OAAO;AAAE,aAAO,MAAM;AAAA,IAAQ,CAAC;AAAA,IAC/D,aAAa,WAAW,WAAW;AAAA,IACnC,WAAW,SAAS,SAAS;AAAA,EACjC,CAAC;AACD,SAAO;AAAA,IACH,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,IACT;AAAA,EACJ;AACJ;AACA,SAAS,cAAc,OAAO;AAC1B,SAAO,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG,CAAC;AAC1C;AACA,SAAS,gBAAgB,SAAS;AAC9B,SAAO,KAAK,IAAI,KAAK,IAAI,IAAI,OAAO,GAAG,CAAC;AAC5C;AACA,SAAS,mBAAmB,aAAa,IAAI;AACzC,MAAI,WAAW,GAAG,UAAU,eAAe,GAAG,cAAc,eAAe,GAAG,cAAc,WAAW,GAAG,UAAU,SAAS,GAAG;AAChI,MAAI,aAAa,YAAY,YAAY,WAAW,YAAY,UAAU,aAAa,YAAY,YAAY,gBAAgB,YAAY,eAAe,WAAW,YAAY,UAAU,aAAa,YAAY,YAAY,UAAU,YAAY;AACtP,MAAI,QAAQ,CAAC;AACb,MAAI,cAAc,WAAW,SAAS,WAAW,QAAQ,GAAG,cAAc,SAAS,IAAI,CAAC,GAAG,gBAAgB,SAAS,MAAM,CAAC;AAC3H,MAAI,YAAY,WAAW,SAAS,cAAc,SAAS,QAAQ,CAAC,GAAG,cAAc,OAAO,IAAI,CAAC,GAAG,gBAAgB,OAAO,MAAM,CAAC;AAClI,MAAI,mBAAmB,gBAAgB,mBAAmB;AAC1D,MAAI,iBAAiB,WAAW,QAAQ;AACxC,MAAI,eAAe,SAAS,QAAQ;AACpC,MAAI,iBAAiB,SAAU,GAAG;AAAE,WAAO;AAAA,EAAG;AAE9C,MAAI,YAAY,kBAAkB,cAAc,MAC5C,YAAY,kBAAkB,YAAY,GAAG;AAC7C,qBAAiB,QAAQ,gBAAgB,CAAC;AAC1C,kBAAc,QAAQ,aAAa,CAAC;AACpC,gBAAY,QAAQ,WAAW,CAAC;AAChC,qBAAiB,SAAU,GAAG;AAAE,aAAO,QAAQ,GAAG,EAAE;AAAA,IAAG;AAAA,EAC3D;AACA,MAAI,cAAc,eACX,eAAe,KAAM,eACtB;AACN,WAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AAClC,QAAI,WAAW,CAAC;AAChB,aAAS,IAAI,GAAG,IAAI,cAAc,KAAK;AACnC,UAAI,OAAO,WAAW,WAAW,aAAa,KAAK,gBAAgB,gBAAgB,GAAG,IAAI,eAAe;AACzG,UAAI,QAAQ,eAAe,OAAO,WAAW;AACzC,iBAAS,KAAK;AAAA,UACV,MAAM,eAAe,IAAI;AAAA,UACzB,aAAa;AAAA,UACb,SAAS,MAAM;AAAA,QACnB,CAAC;AAAA,MACL;AAAA,IACJ;AACA,QAAI,SAAS,SAAS,GAAG;AACrB,YAAM,KAAK,EAAE,SAAmB,CAAC;AAAA,IACrC;AAAA,EACJ;AACA,SAAO;AACX;AACO,IAAI;AAAA,CACV,SAAUC,8BAA6B;AACpC,EAAAA,6BAA4B,UAAU,IAAI;AAC1C,EAAAA,6BAA4B,sBAAsB,IAAI;AACtD,EAAAA,6BAA4B,sBAAsB,IAAI;AACtD,EAAAA,6BAA4B,oBAAoB,IAAI;AACpD,EAAAA,6BAA4B,iBAAiB,IAAI;AACrD,GAAG,gCAAgC,8BAA8B,CAAC,EAAE;AAC7D,SAAS,eAAe,QAAQ,KAAK;AACxC,MAAI,UAAU;AACd,WAAS,QAAQ,KAAK,OAAO;AACzB,QAAI,KAAK,KAAK;AACd,cAAU;AAAA,EACd;AACA,MAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AACxB,QAAI,4BAA4B,UAAU,MAAM;AAChD,WAAO;AAAA,EACX;AACA,SAAO,QAAQ,SAAU,OAAO;AAC5B,QAAI,CAAC,MAAM,OAAO;AACd,cAAQ,4BAA4B,sBAAsB,KAAK;AAAA,IACnE,WACS,EAAE,MAAM,iBAAiB,OAAO;AACrC,cAAQ,4BAA4B,sBAAsB,KAAK;AAAA,IACnE;AACA,QAAI,MAAM,KAAK;AACX,UAAI,EAAE,MAAM,eAAe,OAAO;AAC9B,gBAAQ,4BAA4B,oBAAoB,KAAK;AAAA,MACjE;AACA,UAAI,MAAM,QAAQ,MAAM,KAAK;AACzB,gBAAQ,4BAA4B,iBAAiB,KAAK;AAAA,MAC9D;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,SAAO;AACX;;;AC/oBA,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,OAAO;AAAA,EACP,iBAAiB;AACnB;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,QAAQ;AACV;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,CAAC;AAC3B,IAAG,OAAO,GAAG,cAAc;AAC3B,IAAG,WAAW,YAAY,SAAS,sFAAsF,QAAQ;AAC/H,YAAM,YAAe,cAAc,GAAG,EAAE;AACxC,YAAM,WAAc,cAAc,CAAC,EAAE;AACrC,aAAU,YAAY,UAAU,QAAQ;AAAA,QACtC,OAAO;AAAA,QACP,aAAa;AAAA,MACf,CAAC,CAAC;AAAA,IACJ,CAAC,EAAE,mBAAmB,SAAS,6FAA6F,QAAQ;AAClI,YAAM,YAAe,cAAc,GAAG,EAAE;AACxC,YAAM,WAAc,cAAc,CAAC,EAAE;AACrC,aAAU,YAAY,UAAU,QAAQ;AAAA,QACtC,OAAO;AAAA,QACP,aAAa;AAAA,MACf,CAAC,CAAC;AAAA,IACJ,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,IAAG,WAAW,WAAW,UAAU,QAAQ,EAAE,aAAa,UAAU,OAAU,cAAc;AAC5F,IAAG,YAAY,cAAiB,YAAY,GAAG,GAAM,gBAAgB,GAAG,KAAK,SAAS,GAAG,mBAAmB,CAAC;AAAA,EAC/G;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,iEAAiE,GAAG,GAAG,KAAK,CAAC;AAC9F,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,WAAW,OAAO;AACxB,UAAM,qBAAqB,OAAO;AAClC,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,SAAS,OAAO,EAAE,gBAAgB,kBAAkB;AAAA,EAC/E;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,QAAQ,CAAC;AAAA,EAC/F;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,IAAG,WAAW,QAAQ,SAAS,OAAO;AAAA,EACxC;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AAAC;AACxE,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,OAAO;AAAA,EACP,MAAM;AACR;AACA,IAAM,MAAM,OAAO,CAAC;AACpB,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,OAAO,GAAG,oBAAoB;AACjC,IAAG,OAAO,GAAG,cAAc;AAAA,EAC7B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,UAAM,UAAU,IAAI;AACpB,IAAG,WAAW,aAAgB,YAAY,GAAG,GAAG,SAAS,OAAO,SAAS,QAAQ,GAAM,cAAc;AACrG,IAAG,YAAY,eAAkB,YAAY,GAAG,GAAM,gBAAgB,GAAG,GAAG,GAAG,gBAAgB,CAAC;AAAA,EAClG;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AAAC;AACtE,IAAM,MAAM,CAAC,IAAI,IAAI,QAAQ;AAAA,EAC3B,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AACT;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,UAAU,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AACrC,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,UAAM,eAAe,IAAI;AACzB,IAAG,WAAW,WAAW,iBAAiB,YAAY;AACtD,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,aAAa,aAAgB,cAAc;AAAA,EAC3D;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AAAC;AACzE,IAAM,MAAM,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,SAAS;AAAA,EACjE,KAAK;AAAA,EACL,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,kBAAkB;AAAA,EAClB,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,qBAAqB;AAAA,EACrB,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,cAAc;AAChB;AACA,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,KAAK;AAAA,EACL,QAAQ;AACV;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,iBAAiB;AACnB;AACA,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,OAAO;AAAA,EACP,aAAa;AACf;AACA,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,GAAG;AAAA,EACH,GAAG;AACL;AACA,IAAM,OAAO,OAAO;AAAA,EAClB,OAAO;AAAA,EACP,OAAO;AACT;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,EAAE;AAClC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,UAAU;AAAA,EACxC;AACF;AACA,SAAS,8DAA8D,IAAI,KAAK;AAC9E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,OAAO,GAAG,oBAAoB;AACjC,IAAG,OAAO,GAAG,cAAc;AAC3B,IAAG,WAAW,cAAc,SAAS,0FAA0F;AAC7H,YAAM,WAAc,cAAc,GAAG,EAAE;AACvC,YAAM,kBAAqB,cAAc,CAAC,EAAE;AAC5C,aAAU,YAAY,gBAAgB,KAAK;AAAA,QACzC,OAAO;AAAA,MACT,CAAC,CAAC;AAAA,IACJ,CAAC,EAAE,cAAc,SAAS,0FAA0F;AAClH,YAAM,WAAc,cAAc,GAAG,EAAE;AACvC,YAAM,oBAAuB,cAAc,CAAC,EAAE;AAC9C,aAAU,YAAY,kBAAkB,KAAK;AAAA,QAC3C,OAAO;AAAA,MACT,CAAC,CAAC;AAAA,IACJ,CAAC,EAAE,YAAY,SAAS,sFAAsF,QAAQ;AACpH,YAAM,WAAc,cAAc,GAAG,EAAE;AACvC,YAAM,kBAAqB,cAAc,CAAC,EAAE;AAC5C,aAAU,YAAY,gBAAgB,KAAK;AAAA,QACzC,OAAO;AAAA,QACP,aAAa;AAAA,MACf,CAAC,CAAC;AAAA,IACJ,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,UAAM,SAAY,cAAc,CAAC;AACjC,UAAM,SAAS,OAAO;AACtB,UAAM,sBAAsB,OAAO;AACnC,UAAM,qBAAqB,OAAO;AAClC,UAAM,0BAA0B,OAAO;AACvC,UAAM,mBAAmB,OAAO;AAChC,UAAM,mBAAmB,OAAO;AAChC,IAAG,YAAY,iBAAiB,SAAS,SAAS;AAClD,IAAG,WAAW,WAAc,gBAAgB,IAAI,KAAK,SAAS,SAAS,OAAO,OAAO,SAAS,MAAM,OAAO,CAAC,EAAE,WAAW,YAAY,OAAO,OAAO,SAAS,QAAQ,EAAE,sBAAyB,YAAY,GAAG,IAAI,SAAS,OAAO,gBAAgB,QAAQ,CAAC,EAAE,oBAAoB,mBAAmB,EAAE,gBAAgB,QAAQ,EAAE,mBAAmB,kBAAkB,EAAE,uBAAuB,uBAAuB,EAAE,gBAAgB,gBAAgB,EAAE,YAAe,gBAAgB,IAAI,KAAK,UAAU,MAAM,CAAC,EAAE,YAAe,gBAAgB,IAAI,KAAK,SAAS,WAAW,SAAS,SAAS,CAAC,EAAE,gBAAgB,gBAAgB,EAAE,uBAA0B,gBAAgB,IAAI,IAAI,CAAC;AAC1pB,IAAG,YAAY,eAAkB,YAAY,GAAG,IAAO,gBAAgB,IAAI,GAAG,GAAG,qBAAqB,CAAC;AAAA,EACzG;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,+DAA+D,GAAG,IAAI,OAAO,CAAC;AAC/F,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,SAAS,OAAO;AACtB,UAAM,qBAAqB,OAAO;AAClC,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,MAAM,EAAE,gBAAgB,kBAAkB;AAAA,EAC5E;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,OAAO,GAAG,cAAc;AAC3B,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,QAAQ,CAAC;AAC1F,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,cAAc;AAC3B,IAAG,aAAa,EAAE,EAAE;AACpB,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,OAAO,CAAC;AAAA,EAC1F;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,UAAM,aAAa,IAAI;AACvB,IAAG,YAAY,cAAiB,YAAY,GAAG,GAAM,gBAAgB,IAAI,KAAK,QAAQ,UAAU,GAAG,WAAW,CAAC;AAC/G,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,aAAa,CAAC;AAC3C,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,GAAG,GAAG,OAAO,MAAM,sBAAsB,UAAU,CAAC;AACxF,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,OAAO,SAAS,CAAC;AAAA,EAChD;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAAC;AACrE,IAAM,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,QAAQ;AAAA,EACpC,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,cAAc;AAChB;AACA,IAAM,OAAO,CAAC,IAAI,QAAQ;AAAA,EACxB,MAAM;AAAA,EACN,QAAQ;AACV;AACA,IAAM,OAAO,SAAO;AAAA,EAClB,OAAO;AACT;AACA,IAAM,OAAO,CAAC,IAAI,QAAQ;AAAA,EACxB,OAAO;AAAA,EACP,QAAQ;AACV;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,OAAO,GAAG,GAAG;AAChB,IAAG,eAAe,GAAG,4BAA4B,CAAC;AAClD,IAAG,OAAO,GAAG,cAAc;AAC3B,IAAG,WAAW,YAAY,SAAS,+GAA+G,QAAQ;AACxJ,YAAM,WAAc,cAAc,GAAG,EAAE;AACvC,YAAM,kBAAqB,cAAc,CAAC,EAAE;AAC5C,aAAU,YAAY,gBAAgB,KAAK;AAAA,QACzC,OAAO;AAAA,QACP,aAAa;AAAA,MACf,CAAC,CAAC;AAAA,IACJ,CAAC,EAAE,mBAAmB,SAAS,sHAAsH,QAAQ;AAC3J,YAAM,WAAc,cAAc,GAAG,EAAE;AACvC,YAAM,kBAAqB,cAAc,CAAC,EAAE;AAC5C,aAAU,YAAY,gBAAgB,KAAK;AAAA,QACzC,OAAO;AAAA,QACP,aAAa;AAAA,MACf,CAAC,CAAC;AAAA,IACJ,CAAC;AACD,IAAG,aAAa;AAChB,IAAG,OAAO,GAAG,GAAG;AAChB,IAAG,UAAU,GAAG,8BAA8B,EAAE;AAChD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,UAAM,kBAAqB,cAAc,CAAC,EAAE;AAC5C,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,iBAAiB,SAAS,SAAS;AAClD,IAAG,WAAW,WAAW,YAAY,OAAO,OAAO,SAAS,QAAQ,EAAE,YAAe,gBAAgB,IAAI,MAAM,QAAQ,CAAC,EAAE,YAAe,gBAAgB,IAAI,KAAK,SAAS,WAAW,SAAS,SAAS,CAAC,EAAE,gBAAgB,eAAe,EAAE,uBAA0B,gBAAgB,IAAI,IAAI,CAAC;AAC/R,IAAG,UAAU;AACb,IAAG,WAAW,WAAc,gBAAgB,IAAI,KAAK,SAAS,SAAS,OAAO,OAAO,SAAS,MAAM,OAAO,CAAC;AAC5G,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,SAAS,QAAQ,EAAE,kBAAkB,OAAO,kBAAkB;AAC5E,IAAG,YAAY,cAAiB,YAAY,GAAG,IAAO,gBAAgB,IAAI,MAAM,UAAU,OAAO,MAAM,GAAG,kBAAkB,CAAC;AAC7H,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,SAAS,QAAQ,EAAE,kBAAkB,OAAO,oBAAoB;AAAA,EAChF;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,OAAO,GAAG,cAAc;AAC3B,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,OAAO,GAAG,cAAc;AAC3B,IAAG,WAAW,GAAG,mEAAmE,GAAG,IAAI,OAAO,CAAC;AACnG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,YAAY,OAAO;AACzB,UAAM,oBAAoB,OAAO;AACjC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,aAAa,MAAS;AACpC,IAAG,UAAU;AACb,IAAG,YAAY,cAAiB,YAAY,GAAG,GAAM,gBAAgB,IAAI,MAAM,OAAO,MAAM,OAAO,MAAM,GAAG,oBAAoB,CAAC;AACjI,IAAG,UAAU,CAAC;AACd,IAAG,YAAY,cAAiB,YAAY,GAAG,GAAM,gBAAgB,IAAI,MAAM,OAAO,MAAM,OAAO,MAAM,GAAG,uBAAuB,CAAC;AACpI,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,SAAS,EAAE,gBAAgB,iBAAiB;AAAA,EACvE;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,6DAA6D,GAAG,IAAI,OAAO,CAAC;AAAA,EAC/F;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,IAAG,WAAW,QAAQ,SAAS;AAAA,EACjC;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AAAC;AACzE,IAAM,OAAO,CAAC,IAAI,IAAI,QAAQ;AAAA,EAC5B,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,0BAA0B;AAC5B;AACA,SAAS,8DAA8D,IAAI,KAAK;AAC9E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,SAAS,SAAS,mFAAmF,QAAQ;AACzH,YAAM,SAAY,cAAc,GAAG,EAAE;AACrC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,oBAAoB,KAAK;AAAA,QACpD,cAAc,OAAO;AAAA,QACrB,aAAa;AAAA,MACf,CAAC,CAAC;AAAA,IACJ,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,cAAc;AAC3B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,UAAM,YAAe,cAAc,EAAE;AACrC,IAAG,YAAY,YAAY,OAAO,MAAM,EAAE,aAAa,OAAO,OAAO,EAAE,cAAc,OAAO,QAAQ,EAAE,eAAe,OAAO,SAAS;AACrI,IAAG,WAAW,WAAW,OAAO,QAAQ;AACxC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,IAAI,OAAO,MAAM,yBAAyB,SAAS,GAAG,GAAG;AAAA,EACxG;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,+DAA+D,GAAG,IAAI,OAAO,CAAC;AAC/F,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,8BAA8B,IAAI;AACxC,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,EAAE,gBAAgB,2BAA2B;AAAA,EAC/E;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AAAC;AAC3E,SAAS,oEAAoE,IAAI,KAAK;AACpF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,2BAA2B,CAAC;AACjD,IAAG,OAAO,GAAG,cAAc;AAC3B,IAAG,WAAW,YAAY,SAAS,gHAAgH,QAAQ;AACzJ,YAAM,SAAY,cAAc,GAAG,EAAE;AACrC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,WAAW,KAAK;AAAA,QAC3C,KAAK;AAAA,QACL,aAAa;AAAA,MACf,CAAC,CAAC;AAAA,IACJ,CAAC,EAAE,mBAAmB,SAAS,uHAAuH,QAAQ;AAC5J,YAAM,SAAY,cAAc,GAAG,EAAE;AACrC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,WAAW,KAAK;AAAA,QAC3C,KAAK;AAAA,QACL,aAAa;AAAA,MACf,CAAC,CAAC;AAAA,IACJ,CAAC,EAAE,gBAAgB,SAAS,oHAAoH,QAAQ;AACtJ,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,mBAAmB,OAAO,OAAO,IAAI,CAAC;AAAA,IACrE,CAAC,EAAE,kBAAkB,SAAS,sHAAsH,QAAQ;AAC1J,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,mBAAmB,OAAO,OAAO,KAAK,CAAC;AAAA,IACtE,CAAC,EAAE,QAAQ,SAAS,4GAA4G,QAAQ;AACtI,YAAM,SAAY,cAAc,GAAG,EAAE;AACrC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,QAAQ,OAAO,SAAS,OAAO,OAAO,SAAS,WAAW,CAAC;AAAA,IACvG,CAAC,EAAE,gBAAgB,SAAS,oHAAoH,QAAQ;AACtJ,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,KAAK;AAAA,QAC7C,OAAO,OAAO;AAAA,QACd,aAAa,OAAO;AAAA,MACtB,CAAC,CAAC;AAAA,IACJ,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,UAAU,OAAO,OAAO,OAAO,QAAQ,EAAE,OAAO,MAAM,EAAE,WAAW,OAAO,OAAO,EAAE,UAAU,OAAO,MAAM,EAAE,oBAAoB,OAAO,gBAAgB,EAAE,uBAAuB,OAAO,mBAAmB,EAAE,mBAAmB,OAAO,eAAe,EAAE,gBAAgB,OAAO,YAAY,EAAE,kBAAkB,OAAO,YAAY,EAAE,WAAc,gBAAgB,IAAI,KAAK,OAAO,eAAe,CAAC,EAAE,yBAAyB,OAAO,WAAW,UAAU,WAAW,CAAC;AAC7d,IAAG,YAAY,YAAe,YAAY,GAAG,IAAO,gBAAgB,IAAI,GAAG,GAAG,mBAAmB,CAAC;AAAA,EACpG;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,IAAG,WAAW,GAAG,qEAAqE,GAAG,IAAI,2BAA2B,CAAC;AACzH,IAAG,OAAO,GAAG,OAAO;AACpB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,gCAAgC,CAAC;AACtD,IAAG,WAAW,gBAAgB,SAAS,+FAA+F,QAAQ;AAC5I,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,aAAa,KAAK;AAAA,QAC7C,OAAO,OAAO;AAAA,QACd,aAAa,OAAO;AAAA,MACtB,CAAC,CAAC;AAAA,IACJ,CAAC,EAAE,QAAQ,SAAS,uFAAuF,QAAQ;AACjH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,aAAa,OAAO,SAAS,OAAO,SAAS,OAAO,OAAO,SAAS,WAAW,CAAC;AAAA,IAC/G,CAAC;AACD,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAc,YAAY,GAAG,GAAG,OAAO,KAAK,MAAM,aAAa,cAAc,OAAO,KAAK,sBAAsB,CAAC,EAAE,gBAAgB,OAAO,WAAW;AAClK,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,UAAU,OAAO,MAAM,EAAE,UAAU,OAAO,iBAAiB,WAAW,EAAE,UAAU,OAAO,WAAW,OAAO,OAAO,OAAO,QAAQ,MAAM,EAAE,QAAQ,OAAO,WAAW,OAAO,OAAO,OAAO,QAAQ,IAAI,EAAE,kBAAkB,OAAO,qBAAqB,EAAE,sBAAsB,OAAO,kBAAkB,EAAE,wBAAwB,OAAO,oBAAoB;AAAA,EAC1W;AACF;AACA,IAAM,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,QAAQ;AAAA,EACxC,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,kBAAkB;AAAA,EAClB,cAAc;AAAA,EACd,WAAW;AAAA,EACX,0BAA0B;AAC5B;AACA,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,YAAY,SAAS,qFAAqF,QAAQ;AAC9H,YAAM,SAAY,cAAc,GAAG,EAAE;AACrC,YAAM,sBAAyB,cAAc,EAAE;AAC/C,aAAU,YAAY,oBAAoB,KAAK;AAAA,QAC7C,KAAK;AAAA,QACL,aAAa;AAAA,MACf,CAAC,CAAC;AAAA,IACJ,CAAC,EAAE,QAAQ,SAAS,iFAAiF,QAAQ;AAC3G,YAAM,SAAY,cAAc,GAAG,EAAE;AACrC,YAAM,kBAAqB,cAAc,EAAE;AAC3C,aAAU,YAAY,gBAAgB,KAAK;AAAA,QACzC,OAAO,OAAO,SAAS;AAAA,QACvB,UAAU,OAAO;AAAA,MACnB,CAAC,CAAC;AAAA,IACJ,CAAC,EAAE,aAAa,SAAS,wFAAwF;AAC/G,YAAM,SAAY,cAAc,GAAG,EAAE;AACrC,YAAM,eAAkB,cAAc,EAAE;AACxC,aAAU,YAAY,aAAa,KAAK;AAAA,QACtC,MAAM,OAAO;AAAA,MACf,CAAC,CAAC;AAAA,IACJ,CAAC;AACD,IAAG,eAAe,GAAG,GAAG;AACxB,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,cAAc;AAC3B,IAAG,aAAa;AAChB,IAAG,UAAU,GAAG,IAAI;AACpB,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,cAAc;AAC3B,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,UAAM,YAAe,cAAc,EAAE;AACrC,IAAG,YAAY,YAAY,OAAO,MAAM,EAAE,aAAa,OAAO,OAAO,EAAE,cAAc,OAAO,QAAQ,EAAE,eAAe,OAAO,SAAS;AACrI,IAAG,WAAW,WAAW,OAAO,QAAQ;AACxC,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,GAAG,IAAI,OAAO,MAAM,wBAAwB,SAAS,CAAC;AAC1F,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,GAAG,IAAI,OAAO,MAAM,2BAA2B,SAAS,CAAC;AAAA,EAC/F;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,8DAA8D,GAAG,IAAI,OAAO,CAAC;AAC9F,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,8BAA8B,IAAI;AACxC,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,EAAE,gBAAgB,2BAA2B;AAAA,EAC/E;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AAAC;AAC1E,IAAM,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,QAAQ;AAAA,EACpD,WAAW;AAAA,EACX,kBAAkB;AAAA,EAClB,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,qBAAqB;AAAA,EACrB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,YAAY;AACd;AACA,IAAM,OAAO,CAAC,IAAI,IAAI,QAAQ;AAAA,EAC5B,OAAO;AAAA,EACP,iBAAiB;AAAA,EACjB,aAAa;AACf;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,OAAO,GAAG,oBAAoB;AACjC,IAAG,OAAO,GAAG,cAAc;AAC3B,IAAG,WAAW,YAAY,SAAS,8EAA8E,QAAQ;AACvH,YAAM,kBAAqB,cAAc,GAAG,EAAE;AAC9C,aAAU,YAAY,gBAAgB,KAAK;AAAA,QACzC,aAAa;AAAA,MACf,CAAC,CAAC;AAAA,IACJ,CAAC,EAAE,mBAAmB,SAAS,qFAAqF,QAAQ;AAC1H,YAAM,kBAAqB,cAAc,GAAG,EAAE;AAC9C,aAAU,YAAY,gBAAgB,KAAK;AAAA,QACzC,aAAa;AAAA,MACf,CAAC,CAAC;AAAA,IACJ,CAAC;AACD,IAAG,UAAU,GAAG,8BAA8B,CAAC;AAC/C,IAAG,OAAO,GAAG,GAAG;AAChB,IAAG,UAAU,GAAG,4BAA4B,CAAC;AAC7C,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,UAAM,sBAAsB,IAAI;AAChC,UAAM,qBAAqB,IAAI;AAC/B,UAAM,yBAAyB,IAAI;AACnC,UAAM,qBAAqB,IAAI;AAC/B,UAAM,kBAAkB,IAAI;AAC5B,UAAM,gBAAgB,IAAI;AAC1B,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAc,gBAAgB,IAAI,MAAM,aAAa,MAAM,SAAS,OAAO,OAAO,aAAa,MAAM,MAAM,eAAe,aAAa,MAAM,SAAS,OAAO,OAAO,aAAa,MAAM,MAAM,WAAW,aAAa,MAAM,SAAS,OAAO,OAAO,aAAa,MAAM,MAAM,OAAO,CAAC,EAAE,sBAAsB,CAAC,qBAAwB,YAAY,GAAG,IAAI,aAAa,MAAM,OAAO,kBAAkB,IAAI,eAAe,eAAe,aAAa,aAAa,aAAa,KAAK,IAAI,EAAE,EAAE,oBAAoB,mBAAmB,EAAE,gBAAgB,aAAa,aAAa,aAAa,KAAK,EAAE,mBAAmB,kBAAkB,EAAE,uBAAuB,sBAAsB,EAAE,gBAAgB,eAAe;AACnsB,IAAG,YAAY,cAAiB,YAAY,GAAG,IAAO,gBAAgB,IAAI,MAAM,aAAa,aAAa,aAAa,OAAO,OAAO,MAAM,GAAG,kBAAkB,CAAC;AACjK,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,SAAS,aAAa,aAAa,aAAa,KAAK,EAAE,kBAAkB,OAAO,oBAAoB;AAClH,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,SAAS,aAAa,aAAa,aAAa,KAAK,EAAE,kBAAkB,OAAO,kBAAkB,EAAE,QAAQ,kBAAkB,IAAI,QAAQ,MAAM;AAAA,EAChK;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AAAC;AACzE,IAAM,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,QAAQ;AAAA,EACpC,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,aAAa;AAAA,EACb,YAAY;AACd;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,cAAc;AAC3B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,aAAa,OAAO;AAC1B,UAAM,YAAY,OAAO;AACzB,UAAM,gBAAgB,OAAO;AAC7B,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,WAAW,aAAa,kBAAkB,IAAI,gBAAgB,gBAAgB,SAAS,GAAG,GAAG;AAAA,EAC/I;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,OAAO,GAAG,cAAc;AAC3B,IAAG,WAAW,GAAG,mEAAmE,GAAG,GAAG,OAAO,CAAC;AAClG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAa,IAAI;AACvB,UAAM,mBAAmB,IAAI;AAC7B,UAAM,iBAAiB,IAAI;AAC3B,UAAM,gBAAgB,IAAI;AAC1B,IAAG,YAAY,UAAU,kBAAkB,IAAI;AAC/C,IAAG,YAAY,kBAAkB,WAAW,OAAO,EAAE,wBAAwB,CAAC,WAAW,OAAO;AAChG,IAAG,WAAW,WAAW,WAAW,QAAQ;AAC5C,IAAG,YAAY,eAAkB,YAAY,GAAG,GAAM,gBAAgB,IAAI,GAAG,GAAG,kBAAkB,IAAI,uBAAuB,qBAAqB,CAAC;AACnJ,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,cAAc;AAAA,EACtC;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAAC;AAC/E,IAAM,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,QAAQ;AAAA,EAC5C,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,WAAW;AAAA,EACX,OAAO;AACT;AACA,SAAS,wEAAwE,IAAI,KAAK;AACxF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,EAAE;AACpC,IAAG,YAAY,OAAO,UAAU,IAAI;AAAA,EACtC;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,yEAAyE,GAAG,GAAG,OAAO,CAAC;AAAA,EAC1G;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,IAAG,WAAW,QAAQ,YAAY;AAAA,EACpC;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAAC;AACrF,IAAM,OAAO,CAAC,IAAI,QAAQ;AAAA,EACxB,MAAM;AAAA,EACN,OAAO;AACT;AACA,IAAM,OAAO,CAAC,IAAI,QAAQ;AAAA,EACxB,OAAO;AAAA,EACP,YAAY;AACd;AACA,IAAM,OAAO,SAAO;AAAA,EAClB,GAAG;AACL;AACA,IAAM,OAAO,OAAO;AAAA,EAClB,MAAM;AACR;AACA,IAAM,OAAO,OAAO;AAAA,EAClB,OAAO;AACT;AACA,IAAM,OAAO,CAAC,IAAI,IAAI,IAAI,QAAQ;AAAA,EAChC,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,QAAQ;AACV;AACA,IAAM,OAAO,OAAO;AAAA,EAClB,MAAM;AAAA,EACN,KAAK;AACP;AACA,IAAM,OAAO,OAAO;AAAA,EAClB,OAAO;AAAA,EACP,QAAQ;AACV;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,QAAQ,SAAS,mEAAmE,QAAQ;AACxG,YAAM,SAAY,cAAc,GAAG,EAAE;AACrC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,QAAQ,OAAO,MAAM,IAAI,CAAC;AAAA,IACtE,CAAC,EAAE,aAAa,SAAS,0EAA0E;AACjG,YAAM,SAAY,cAAc,GAAG,EAAE;AACrC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,cAAc,OAAO,IAAI,CAAC;AAAA,IACzD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,EAAE;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,eAAkB,gBAAgB,GAAG,IAAI,CAAC;AAAA,EAC1D;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,EAAE;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,eAAkB,gBAAgB,GAAG,IAAI,CAAC;AAAA,EAC1D;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,IAAI,CAAC;AACjC,IAAG,WAAW,eAAe,SAAS,gFAAgF,QAAQ;AAC5H,YAAM,iBAAoB,cAAc,GAAG,EAAE;AAC7C,MAAG,cAAc;AACjB,YAAM,uBAA0B,YAAY,CAAC;AAC7C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,yBAAyB,sBAAsB,gBAAgB,MAAM,CAAC;AAAA,IACrG,CAAC,EAAE,YAAY,SAAS,6EAA6E,QAAQ;AAC3G,YAAM,iBAAoB,cAAc,GAAG,EAAE;AAC7C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,oBAAoB,gBAAgB,QAAQ,OAAO,cAAc,CAAC;AAAA,IACjG,CAAC,EAAE,aAAa,SAAS,gFAAgF;AACvG,YAAM,iBAAoB,cAAc,GAAG,EAAE;AAC7C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,uBAAuB,cAAc,CAAC;AAAA,IACrE,CAAC,EAAE,aAAa,SAAS,gFAAgF;AACvG,YAAM,iBAAoB,cAAc,GAAG,EAAE;AAC7C,YAAM,WAAc,YAAY,CAAC;AACjC,MAAG,cAAc;AACjB,YAAM,uBAA0B,YAAY,CAAC;AAC7C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,YAAY,sBAAsB,UAAU,gBAAgB,KAAK,CAAC;AAAA,IACjG,CAAC,EAAE,YAAY,SAAS,+EAA+E;AACrG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,oBAAoB,CAAC;AAAA,IACpD,CAAC,EAAE,WAAW,SAAS,4EAA4E,QAAQ;AACzG,YAAM,iBAAoB,cAAc,GAAG,EAAE;AAC7C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,UAAU,gBAAgB,QAAQ,OAAO,cAAc,CAAC;AAAA,IACvF,CAAC;AACD,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,OAAO,EAAE;AAC5F,IAAG,eAAe,GAAG,gCAAgC,EAAE;AACvD,IAAG,WAAW,gBAAgB,SAAS,0GAA0G,QAAQ;AACvJ,YAAM,iBAAoB,cAAc,GAAG,EAAE;AAC7C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,KAAK;AAAA,QAC7C,OAAO,eAAe;AAAA,QACtB,aAAa,OAAO;AAAA,MACtB,CAAC,CAAC;AAAA,IACJ,CAAC;AACD,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,OAAO,EAAE;AAC5F,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,iBAAiB,IAAI;AAC3B,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,SAAS,MAAM,OAAO,KAAK,SAAS,eAAe,MAAM,GAAG,EAAE,eAAe,OAAO,MAAM,OAAO,MAAM,OAAO,KAAK,SAAS,eAAe,QAAQ,GAAG,EAAE,gBAAgB,OAAO,MAAM,MAAM,OAAO,KAAK,SAAS,eAAe,SAAS,MAAM,GAAG;AAChQ,IAAG,YAAY,iBAAiB,eAAe,MAAM,aAAa,OAAO,mBAAmB,SAAS,CAAC,EAAE,0BAA0B,CAAC,eAAe,gBAAgB,EAAE,wBAAwB,CAAC,eAAe,aAAa;AACzN,IAAG,WAAW,WAAW,eAAe,SAAS,OAAO,OAAO,eAAe,MAAM,QAAQ,EAAE,iBAAiB,OAAO,aAAa,EAAE,kBAAqB,gBAAgB,IAAI,MAAM,OAAO,gBAAgB,OAAO,cAAc,CAAC,EAAE,kBAAkB,OAAO,cAAc,EAAE,YAAe,gBAAgB,IAAI,MAAM,eAAe,OAAO,OAAO,UAAU,CAAC,EAAE,YAAe,gBAAgB,IAAI,KAAK,eAAe,MAAM,aAAa,OAAO,mBAAmB,SAAS,GAAG,CAAC,OAAO,qBAAqB,eAAe,MAAM,aAAa,OAAO,mBAAmB,SAAS,CAAC,CAAC,EAAE,gBAAgB,OAAO,oBAAuB,gBAAgB,IAAI,MAAM,OAAO,cAAc,IAAO,gBAAgB,IAAI,GAAG,CAAC,EAAE,gBAAgB,OAAO,YAAY,EAAE,uBAA0B,gBAAgB,IAAI,IAAI,CAAC;AAC3wB,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,SAAS,eAAe,SAAS,OAAO,OAAO,eAAe,MAAM,aAAa,OAAO,OAAO,eAAe,MAAM,UAAU,gBAAgB,CAAC,eAAe,gBAAgB;AAC5L,IAAG,UAAU;AACb,IAAG,WAAW,UAAU,OAAO,MAAM,EAAE,aAAa,cAAc,EAAE,oBAAoB,OAAO,gBAAgB,EAAE,mBAAmB,OAAO,eAAe,EAAE,uBAAuB,OAAO,mBAAmB,EAAE,gBAAgB,OAAO,YAAY,EAAE,kBAAkB,OAAO,aAAa,EAAE,sBAAsB,OAAO,kBAAkB,EAAE,wBAAwB,OAAO,oBAAoB,EAAE,cAAc,OAAO,UAAU;AACja,IAAG,UAAU;AACb,IAAG,WAAW,SAAS,eAAe,SAAS,OAAO,OAAO,eAAe,MAAM,aAAa,OAAO,OAAO,eAAe,MAAM,UAAU,aAAa,CAAC,eAAe,aAAa;AAAA,EACxL;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,IAAI,CAAC;AACjC,IAAG,WAAW,GAAG,sDAAsD,GAAG,IAAI,OAAO,EAAE;AACvF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,aAAa,GAAG,EAAE,gBAAgB,OAAO,sBAAsB;AAAA,EAC1F;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,IAAI,CAAC;AACjC,IAAG,WAAW,aAAa,SAAS,oEAAoE;AACtG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,UAAU,QAAQ,CAAC;AAAA,IAClD,CAAC,EAAE,aAAa,SAAS,oEAAoE;AAC3F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,UAAU,QAAQ,CAAC;AAAA,IAClD,CAAC;AACD,IAAG,eAAe,GAAG,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE;AAC5C,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,gBAAgB,EAAE;AAClG,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,OAAO,EAAE;AAChF,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,OAAO,EAAE;AAChF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,yBAAyB;AAClE,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,IAAI,EAAE,gBAAgB,OAAO,wBAAwB;AACrF,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,KAAK,eAAe,EAAE,gBAAgB,OAAO,SAAS;AAAA,EACxF;AACF;AACA,SAAS,qFAAqF,IAAI,KAAK;AACrG,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,uCAAuC,EAAE;AAAA,EAC3D;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,UAAU,OAAO,mBAAmB,IAAI;AACvD,IAAG,WAAW,WAAW,WAAW,EAAE,iBAAiB,OAAO,iBAAiB,EAAE,UAAU,OAAO,MAAM,EAAE,kBAAkB,OAAO,mBAAmB,EAAE,eAAe,IAAI,EAAE,cAAc,OAAO,UAAU;AAAA,EAC9M;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,sFAAsF,GAAG,GAAG,uCAAuC,EAAE;AACtJ,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,UAAM,UAAU,IAAI;AACpB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,gBAAgB,OAAO;AACtC,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,SAAS,QAAQ,EAAE,gBAAgB,OAAO,kBAAkB;AAAA,EACvF;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,OAAO,EAAE;AAChF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,KAAK,YAAY,CAAC,EAAE,KAAK,EAAE,gBAAgB,OAAO,WAAW;AAAA,EAC/F;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,EAAE;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,eAAkB,gBAAgB,GAAG,IAAI,CAAC;AAAA,EAC1D;AACF;AACA,SAAS,6DAA6D,IAAI,KAAK;AAAC;AAChF,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,gCAAgC,EAAE;AACvD,IAAG,WAAW,gBAAgB,SAAS,kHAAkH,QAAQ;AAC/J,MAAG,cAAc,IAAI;AACrB,YAAM,gBAAmB,cAAc,EAAE;AACzC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,KAAK;AAAA,QAC7C,OAAO,cAAc;AAAA,QACrB,aAAa,OAAO;AAAA,MACtB,CAAC,CAAC;AAAA,IACJ,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,gBAAmB,cAAc,EAAE;AACzC,UAAM,aAAgB,cAAc,EAAE;AACtC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,UAAU,OAAO,MAAM,EAAE,aAAa,aAAa,EAAE,oBAAoB,OAAO,gBAAgB,EAAE,mBAAmB,OAAO,eAAe,EAAE,uBAAuB,OAAO,mBAAmB,EAAE,mBAAmB,OAAO,cAAc,OAAO,iBAAiB,OAAO,CAAC,EAAE,gBAAgB,OAAO,YAAY,EAAE,kBAAkB,OAAO,aAAa,EAAE,sBAAsB,OAAO,kBAAkB,EAAE,wBAAwB,OAAO,oBAAoB,EAAE,UAAU,UAAU,EAAE,cAAc,OAAO,UAAU;AAAA,EAClgB;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,EAAE;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,eAAkB,gBAAgB,GAAG,IAAI,CAAC;AAAA,EAC1D;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,OAAO,IAAI,CAAC;AACjC,IAAG,WAAW,eAAe,SAAS,0EAA0E,QAAQ;AACtH,YAAM,gBAAmB,cAAc,IAAI,EAAE;AAC7C,YAAM,SAAY,cAAc,CAAC;AACjC,YAAM,iBAAoB,YAAY,CAAC;AACvC,aAAU,YAAY,OAAO,uBAAuB,gBAAgB,eAAe,MAAM,CAAC;AAAA,IAC5F,CAAC,EAAE,YAAY,SAAS,uEAAuE,QAAQ;AACrG,YAAM,gBAAmB,cAAc,IAAI,EAAE;AAC7C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,kBAAkB,eAAe,MAAM,CAAC;AAAA,IACvE,CAAC,EAAE,aAAa,SAAS,0EAA0E;AACjG,YAAM,gBAAmB,cAAc,IAAI,EAAE;AAC7C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,qBAAqB,aAAa,CAAC;AAAA,IAClE,CAAC,EAAE,aAAa,SAAS,0EAA0E;AACjG,YAAM,gBAAmB,cAAc,IAAI,EAAE;AAC7C,YAAM,YAAe,YAAY,CAAC;AAClC,YAAM,SAAY,cAAc,CAAC;AACjC,YAAM,iBAAoB,YAAY,CAAC;AACvC,aAAU,YAAY,OAAO,YAAY,gBAAgB,WAAW,eAAe,IAAI,CAAC;AAAA,IAC1F,CAAC,EAAE,YAAY,SAAS,uEAAuE,QAAQ;AACrG,YAAM,gBAAmB,cAAc,IAAI,EAAE;AAC7C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,SAAS,eAAe,MAAM,CAAC;AAAA,IAC9D,CAAC,EAAE,WAAW,SAAS,sEAAsE,QAAQ;AACnG,YAAM,gBAAmB,cAAc,IAAI,EAAE;AAC7C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,UAAU,eAAe,QAAQ,OAAO,gBAAgB,IAAI,CAAC;AAAA,IAC5F,CAAC;AACD,IAAG,WAAW,GAAG,sDAAsD,GAAG,GAAG,OAAO,EAAE,EAAE,GAAG,8DAA8D,GAAG,GAAG,eAAe,EAAE,EAAE,GAAG,8DAA8D,GAAG,IAAI,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,sDAAsD,GAAG,GAAG,OAAO,EAAE;AACrX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,gBAAgB,IAAI;AAC1B,UAAM,wBAA2B,YAAY,CAAC;AAC9C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,OAAO,cAAc,KAAK,IAAI,EAAE,UAAU,cAAc,QAAQ,IAAI,EAAE,QAAQ,cAAc,MAAM,GAAG,EAAE,SAAS,cAAc,OAAO,GAAG;AACvJ,IAAG,YAAY,iBAAiB,cAAc,MAAM,aAAa,OAAO,iBAAiB,SAAS,CAAC,EAAE,yBAAyB,CAAC,cAAc,eAAe,EAAE,uBAAuB,CAAC,cAAc,YAAY;AAChN,IAAG,WAAW,WAAW,cAAc,MAAM,QAAQ,EAAE,UAAU,cAAc,WAAW,KAAK,cAAc,UAAU,CAAC,EAAE,iBAAiB,OAAO,aAAa,EAAE,kBAAqB,gBAAgB,IAAI,MAAM,OAAO,gBAAgB,OAAO,gBAAgB,OAAO,iBAAiB,OAAO,mBAAmB,OAAO,iBAAiB,OAAO,iBAAiB,CAAC,EAAE,kBAAkB,OAAO,cAAc,EAAE,wBAAwB,IAAI,EAAE,YAAe,gBAAgB,IAAI,MAAM,cAAc,OAAO,OAAO,UAAU,CAAC,EAAE,YAAe,gBAAgB,IAAI,KAAK,cAAc,MAAM,aAAa,OAAO,iBAAiB,SAAS,GAAG,cAAc,MAAM,aAAa,OAAO,iBAAiB,SAAS,CAAC,CAAC,EAAE,gBAAgB,OAAO,oBAAuB,gBAAgB,IAAI,KAAK,OAAO,gBAAgB,OAAO,iBAAiB,OAAO,iBAAiB,IAAO,gBAAgB,IAAI,GAAG,CAAC,EAAE,uBAA0B,gBAAgB,IAAI,IAAI,CAAC,EAAE,oBAAoB,CAAC,OAAO,iBAAiB,EAAE,wBAAwB,qBAAqB,EAAE,gBAAgB,OAAO,YAAY;AACphC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,SAAS,cAAc,SAAS,OAAO,OAAO,cAAc,MAAM,aAAa,OAAO,OAAO,cAAc,MAAM,UAAU,gBAAgB,CAAC,cAAc,eAAe;AACvL,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,qBAAqB;AACvD,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,SAAS,cAAc,SAAS,OAAO,OAAO,cAAc,MAAM,aAAa,OAAO,OAAO,cAAc,MAAM,UAAU,aAAa,CAAC,cAAc,YAAY;AAAA,EACnL;AACF;AACA,SAAS,qFAAqF,IAAI,KAAK;AACrG,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,uCAAuC,EAAE;AAC9D,IAAG,WAAW,YAAY,SAAS,6IAA6I,QAAQ;AACtL,YAAM,cAAiB,cAAc,IAAI,EAAE;AAC3C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,mBAAmB,KAAK;AAAA,QACnD,MAAM,YAAY;AAAA,QAClB,aAAa;AAAA,MACf,CAAC,CAAC;AAAA,IACJ,CAAC,EAAE,QAAQ,SAAS,yIAAyI,QAAQ;AACnK,YAAM,cAAiB,cAAc,IAAI,EAAE;AAC3C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,QAAQ,YAAY,MAAM,KAAK,CAAC;AAAA,IAC5E,CAAC,EAAE,aAAa,SAAS,gJAAgJ;AACvK,YAAM,cAAiB,cAAc,IAAI,EAAE;AAC3C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,cAAc,YAAY,IAAI,CAAC;AAAA,IAC9D,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,UAAU,OAAO,mBAAmB,IAAI;AACvD,IAAG,WAAW,WAAW,WAAW,EAAE,iBAAiB,OAAO,iBAAiB,EAAE,UAAU,OAAO,MAAM,EAAE,kBAAkB,OAAO,mBAAmB,EAAE,cAAc,OAAO,UAAU,EAAE,yBAAyB,OAAO,mBAAmB,UAAU,WAAW,CAAC,EAAE,iBAAiB,CAAC,OAAO,cAAc,CAAC,OAAO,oBAAoB,kBAAkB,IAAI,EAAE,eAAe,OAAO,eAAe,CAAC;AAAA,EACvY;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,sFAAsF,GAAG,IAAI,uCAAuC,EAAE;AACvJ,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,UAAM,UAAU,IAAI;AACpB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,gBAAgB,OAAO;AACtC,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,SAAS,QAAQ,EAAE,gBAAgB,OAAO,kBAAkB;AAAA,EACvF;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,UAAU,GAAG,8CAA8C,EAAE;AAChE,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,gDAAgD,GAAG,IAAI,OAAO,EAAE;AACjF,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,OAAO,EAAE;AAChF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAa,IAAI;AACvB,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,cAAc,WAAW,IAAI,EAAE,gBAAgB,OAAO,YAAY,EAAE,kBAAkB,OAAO,cAAc,EAAE,cAAc,OAAO,UAAU,EAAE,gBAAgB,OAAO,YAAY,EAAE,gBAAgB,OAAO,YAAY,EAAE,gBAAgB,OAAO,YAAY,EAAE,qBAAqB,OAAO,iBAAiB,EAAE,kBAAkB,OAAO,yBAAyB;AAC5W,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,WAAW,MAAM,EAAE,gBAAgB,OAAO,oBAAoB;AACvF,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,WAAW,KAAK,EAAE,gBAAgB,OAAO,WAAW;AAAA,EAC/E;AACF;AAOA,IAAM,iBAAN,MAAqB;AAAA,EACnB,YAAY,UAAU,KAAKC,WAAU;AACnC,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,WAAWA;AAChB,SAAK,wBAAwB;AAC7B,SAAK,QAAQ,IAAI,aAAa;AAC9B,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,QAAI,CAAC,KAAK,uBAAuB;AAC/B,WAAK,OAAO,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAC9D,cAAM,gBAAgB;AACtB,aAAK,MAAM,KAAK,KAAK;AAAA,MACvB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AAAA,EACrB;AAAA,EACA,SAAS;AACP,WAAO,IAAI,WAAW,cAAY;AAChC,aAAO,KAAK,SAAS,OAAO,KAAK,IAAI,eAAe,SAAS,WAAS;AACpE,iBAAS,KAAK,KAAK;AAAA,MACrB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,eAAe,OAAO,SAAS,uBAAuB,mBAAmB;AACvE,SAAO,KAAK,qBAAqB,gBAAmB,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,GAAM,kBAAkB,QAAQ,CAAC;AAC1J;AACA,eAAe,OAAyB,kBAAkB;AAAA,EACxD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,YAAY,EAAE,CAAC;AAAA,EAChC,QAAQ;AAAA,IACN,uBAAuB;AAAA,EACzB;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,QAAQ;AAAA,MACjB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAN,MAA4B;AAAA,EAC1B,YAAY,MAAM,QAAQ,UAAU;AAClC,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,UAAU,IAAI,aAAa;AAChC,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,WAAW;AACT,SAAK,OAAO,kBAAkB,MAAM;AAClC,WAAK,kBAAkB,KAAK,SAAS,OAAO,KAAK,KAAK,eAAe,WAAW,WAAS;AACvF,YAAI,MAAM,YAAY,MAAM,MAAM,UAAU,MAAM,MAAM,QAAQ,SAAS;AACvE,gBAAM,eAAe;AACrB,gBAAM,gBAAgB;AACtB,eAAK,OAAO,IAAI,MAAM;AACpB,iBAAK,QAAQ,KAAK,KAAK;AAAA,UACzB,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,oBAAoB,MAAM;AACjC,WAAK,gBAAgB;AACrB,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AACF;AACA,sBAAsB,OAAO,SAAS,8BAA8B,mBAAmB;AACrF,SAAO,KAAK,qBAAqB,uBAA0B,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,SAAS,CAAC;AAClK;AACA,sBAAsB,OAAyB,kBAAkB;AAAA,EAC/D,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,EACvC,SAAS;AAAA,IACP,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AA0CH,IAAM,eAAN,MAAmB;AAAA,EACjB,YAAY,YAAY;AACtB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,EACF,GAAG;AACD,QAAI,IAAI,aAAa,GAAG;AACtB,aAAO;AAAA,UACH,WAAW,IAAI,MAAM,eAAe,MAAM,CAAC;AAAA,UAC3C,KAAK,WAAW,UAAU,IAAI,YAAY;AAAA,QAC5C,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC,CAAC;AAAA;AAAA;AAAA,IAGJ,OAAO;AACL,aAAO,GAAG,WAAW,IAAI,MAAM,eAAe,MAAM,CAAC;AAAA,IACvD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB;AAAA,IACpB;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO;AAAA,uCAC4B,WAAW,MAAM,gBAAgB,MAAM,CAAC;AAAA;AAAA,EAE7E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AAAA,IACjB;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,GAAG,WAAW,MAAM,gBAAgB,MAAM,CAAC;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AAAA,IACf;AAAA,IACA;AAAA,EACF,GAAG;AACD,QAAI,MAAM,WAAW,MAAM;AACzB,aAAO,KAAK,uBAAuB;AAAA,QACjC;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,OAAO;AAAA,QACT,WAAW,MAAM,OAAO,gBAAgB,MAAM,CAAC;AAAA,QAC/C,MAAM,KAAK,UAAU,WAAW,MAAM,OAAO,WAAW,MAAM,CAAC;AAAA;AAEnE,QAAI,MAAM,KAAK;AACb,aAAO,OAAO,OAAO,WAAW,MAAM,KAAK,WAAW,MAAM,CAAC;AAAA,IAC/D;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB;AAAA,IACrB;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM,OAAO;AAAA,QACT,MAAM,KAAK;AAAA,mBACA,WAAW,MAAM,OAAO,mBAAmB,MAAM,CAAC;AAAA;AAEjE,QAAI,MAAM,KAAK;AACb,aAAO,OAAO,eAAe,WAAW,MAAM,KAAK,kBAAkB,MAAM,CAAC;AAAA,IAC9E;AACA,WAAO,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAAA,IAChB;AAAA,EACF,GAAG;AACD,WAAO,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAClB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AACpB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AACpB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,qBAAqB;AACnB,WAAO;AAAA,EACT;AACF;AACA,aAAa,OAAO,SAAS,qBAAqB,mBAAmB;AACnE,SAAO,KAAK,qBAAqB,cAAiB,SAAY,cAAc,CAAC;AAC/E;AACA,aAAa,QAA0B,mBAAmB;AAAA,EACxD,OAAO;AAAA,EACP,SAAS,aAAa;AACxB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AASH,IAAM,mBAAN,MAAuB;AAAA,EACrB,YAAY,cAAc,QAAQ;AAChC,SAAK,eAAe;AACpB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,UAAU,YAAY,QAAQ;AAC5B,eAAW,SAAS,WAAW,UAAU,KAAK;AAC9C,QAAI,OAAO,KAAK,aAAa,MAAM,MAAM,aAAa;AACpD,YAAM,iBAAiB,OAAO,oBAAoB,OAAO,eAAe,aAAa,SAAS,CAAC,EAAE,OAAO,aAAW,YAAY,aAAa;AAC5I,YAAM,IAAI,MAAM,GAAG,MAAM,mDAAmD,eAAe,KAAK,IAAI,CAAC,EAAE;AAAA,IACzG;AACA,WAAO,KAAK,aAAa,MAAM,EAAE,UAAU;AAAA,EAC7C;AACF;AACA,iBAAiB,OAAO,SAAS,yBAAyB,mBAAmB;AAC3E,SAAO,KAAK,qBAAqB,kBAAqB,kBAAkB,cAAc,EAAE,GAAM,kBAAkB,WAAW,EAAE,CAAC;AAChI;AACA,iBAAiB,QAA0B,aAAa;AAAA,EACtD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,SAAS;AAAA,MAClB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,gCAAN,MAAoC;AAAA,EAClC,cAAc;AACZ,SAAK,kBAAkB,CAAC,OAAO,WAAW,OAAO,KAAK,OAAO,KAAK;AAAA,EACpE;AACF;AACA,8BAA8B,OAAO,SAAS,sCAAsC,mBAAmB;AACrG,SAAO,KAAK,qBAAqB,+BAA+B;AAClE;AACA,8BAA8B,OAAyB,kBAAkB;AAAA,EACvE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,4BAA4B,CAAC;AAAA,EAC1C,QAAQ;AAAA,IACN,OAAO;AAAA,IACP,gBAAgB;AAAA,EAClB;AAAA,EACA,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,SAAS,qBAAqB,GAAG,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,SAAS,oBAAoB,QAAQ,gBAAgB,YAAY,KAAK,QAAQ,UAAU,GAAG,WAAW,aAAa,YAAY,mBAAmB,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,QAAQ,gBAAgB,YAAY,KAAK,QAAQ,UAAU,GAAG,oBAAoB,GAAG,YAAY,mBAAmB,WAAW,WAAW,CAAC;AAAA,EACle,UAAU,SAAS,uCAAuC,IAAI,KAAK;AACjE,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,GAAG,sDAAsD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,sDAAsD,GAAG,GAAG,eAAe,CAAC;AAAA,IACjN;AACA,QAAI,KAAK,GAAG;AACV,YAAM,qBAAwB,YAAY,CAAC;AAC3C,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,oBAAoB,IAAI,kBAAkB,kBAAkB,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,IAAI,OAAO,IAAI,eAAe,CAAC;AAAA,IACnK;AAAA,EACF;AAAA,EACA,cAAc,CAAI,SAAY,SAAY,MAAS,kBAAkB,gBAAgB,uBAAuB,gBAAgB;AAAA,EAC5H,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAmCZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAyBH,IAAM,8BAAN,MAAkC;AAAA;AAAA;AAAA;AAAA,EAIhC,MAAM,OAAO,OAAO;AAClB,WAAO,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,OAAO,OAAO;AACzB,WAAO,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAIA,KAAK,OAAO,OAAO;AACjB,WAAO,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,OAAO,OAAO;AACxB,WAAO,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO,OAAO;AAChB,WAAO,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,OAAO,OAAO;AACvB,WAAO,MAAM;AAAA,EACf;AACF;AACA,IAAM,yBAAN,MAA6B;AAAA,EAC3B,YAAY,oBAAoB;AAC9B,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,UAAU,OAAO,WAAW,OAAO;AACjC,WAAO,KAAK,mBAAmB,SAAS,EAAE,OAAO,KAAK;AAAA,EACxD;AACF;AACA,uBAAuB,OAAO,SAAS,+BAA+B,mBAAmB;AACvF,SAAO,KAAK,qBAAqB,wBAA2B,kBAAkB,6BAA6B,EAAE,CAAC;AAChH;AACA,uBAAuB,QAA0B,aAAa;AAAA,EAC5D,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,8BAAN,MAAkC;AAAC;AACnC,4BAA4B,OAAO,SAAS,oCAAoC,mBAAmB;AACjG,SAAO,KAAK,qBAAqB,6BAA6B;AAChE;AACA,4BAA4B,OAAyB,kBAAkB;AAAA,EACrE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,0BAA0B,CAAC;AAAA,EACxC,QAAQ;AAAA,IACN,OAAO;AAAA,IACP,gBAAgB;AAAA,IAChB,MAAM;AAAA,EACR;AAAA,EACA,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,mBAAmB,GAAG,WAAW,CAAC;AAAA,EAC5H,UAAU,SAAS,qCAAqC,IAAI,KAAK;AAC/D,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,GAAG,oDAAoD,GAAG,IAAI,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,oDAAoD,GAAG,GAAG,eAAe,CAAC;AAAA,IAC9M;AACA,QAAI,KAAK,GAAG;AACV,YAAM,qBAAwB,YAAY,CAAC;AAC3C,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,oBAAoB,IAAI,kBAAkB,kBAAkB,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,IAAI,OAAO,IAAI,IAAI,CAAC;AAAA,IACxJ;AAAA,EACF;AAAA,EACA,cAAc,CAAI,kBAAkB,wBAAwB,gBAAgB;AAAA,EAC5E,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAkBZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iCAAN,MAAqC;AAAC;AACtC,+BAA+B,OAAO,SAAS,uCAAuC,mBAAmB;AACvG,SAAO,KAAK,qBAAqB,gCAAgC;AACnE;AACA,+BAA+B,OAAyB,kBAAkB;AAAA,EACxE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,6BAA6B,CAAC;AAAA,EAC3C,QAAQ;AAAA,IACN,UAAU;AAAA,IACV,WAAW;AAAA,IACX,OAAO;AAAA,IACP,gBAAgB;AAAA,EAClB;AAAA,EACA,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,eAAe,GAAG,SAAS,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,qBAAqB,GAAG,WAAW,CAAC;AAAA,EAC1L,UAAU,SAAS,wCAAwC,IAAI,KAAK;AAClE,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,GAAG,uDAAuD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,uDAAuD,GAAG,GAAG,eAAe,CAAC;AAAA,IACnN;AACA,QAAI,KAAK,GAAG;AACV,YAAM,qBAAwB,YAAY,CAAC;AAC3C,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,oBAAoB,IAAI,kBAAkB,kBAAkB,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,IAAI,UAAU,IAAI,WAAW,IAAI,KAAK,CAAC;AAAA,IAC3K;AAAA,EACF;AAAA,EACA,cAAc,CAAI,SAAY,gBAAgB;AAAA,EAC9C,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gCAAgC,CAAC;AAAA,IACvG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAsBZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,2BAAN,MAA+B;AAAA,EAC7B,YAAY,YAAY,UAAU,UAAU,0BAA0B,kBAAkBA,WACtF;AACA,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,mBAAmB;AACxB,SAAK,WAAWA;AAChB,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,sBAAsB,IAAI,QAAQ;AACvC,SAAK,iBAAiB,yBAAyB,wBAAwB,8BAA8B;AAAA,EACvG;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,KAAK,eAAe,QAAQ,YAAY,QAAQ,kBAAkB,QAAQ,QAAQ;AACpF,WAAK,WAAW,SAAS,WAAW,KAAK;AACzC,WAAK,WAAW,SAAS,iBAAiB,KAAK;AAC/C,WAAK,WAAW,SAAS,QAAQ,KAAK;AACtC,WAAK,WAAW,kBAAkB,aAAa;AAC/C,UAAI,CAAC,KAAK,UAAU;AAClB,aAAK,KAAK;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,cAAc;AACZ,UAAM,SAAS,KAAK,UAAU,OAAO,GAAG,KAAK,IAAI,MAAM,KAAK,KAAK;AACjE,WAAO,KAAK,UAAU,KAAK,mBAAmB,CAAC,EAAE,UAAU,MAAM;AAC/D,WAAK,KAAK;AAAA,IACZ,CAAC;AAAA,EACH;AAAA,EACA,aAAa;AACX,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,OAAO;AACL,QAAI,CAAC,KAAK,cAAc,KAAK,UAAU;AACrC,WAAK,aAAa,KAAK,iBAAiB,gBAAgB,KAAK,gBAAgB,GAAG,KAAK,UAAU,CAAC,CAAC;AACjG,WAAK,WAAW,SAAS,WAAW,KAAK;AACzC,WAAK,WAAW,SAAS,iBAAiB,KAAK;AAC/C,WAAK,WAAW,SAAS,QAAQ,KAAK;AACtC,UAAI,KAAK,cAAc;AACrB,aAAK,SAAS,KAAK,YAAY,KAAK,WAAW,SAAS,aAAa;AAAA,MACvE;AACA,4BAAsB,MAAM;AAC1B,aAAK,gBAAgB;AAAA,MACvB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO;AACL,QAAI,KAAK,YAAY;AACnB,WAAK,iBAAiB,OAAO,KAAK,iBAAiB,QAAQ,KAAK,WAAW,QAAQ,CAAC;AACpF,WAAK,aAAa;AAAA,IACpB;AACA,SAAK,oBAAoB,KAAK;AAAA,EAChC;AAAA,EACA,gBAAgB,oBAAoB,CAAC,GAAG;AACtC,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,kBAAkB,cAAc;AAChD,WAAK,WAAW,SAAS,YAAY,iBAAiB,KAAK,WAAW,eAAe,KAAK,WAAW,SAAS,cAAc,SAAS,CAAC,GAAG,KAAK,WAAW,KAAK,YAAY;AAE1K,UAAI,kBAAkB,QAAQ,KAAK,WAAW,SAAS,SAAS,MAAM,IAAI;AACxE,aAAK,gBAAgB,CAAC,GAAG,mBAAmB,KAAK,WAAW,SAAS,SAAS,CAAC;AAAA,MACjF;AAAA,IACF;AAAA,EACF;AACF;AACA,yBAAyB,OAAO,SAAS,iCAAiC,mBAAmB;AAC3F,SAAO,KAAK,qBAAqB,0BAA6B,kBAAqB,UAAU,GAAM,kBAAqB,QAAQ,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,0BAAwB,GAAM,kBAAqB,gBAAgB,GAAM,kBAAkB,QAAQ,CAAC;AACrS;AACA,yBAAyB,OAAyB,kBAAkB;AAAA,EAClE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC;AAAA,EAC1C,cAAc,SAAS,sCAAsC,IAAI,KAAK;AACpE,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,cAAc,SAAS,yDAAyD;AAC5F,eAAO,IAAI,YAAY;AAAA,MACzB,CAAC,EAAE,cAAc,SAAS,yDAAyD;AACjF,eAAO,IAAI,WAAW;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,UAAU,CAAC,GAAG,sBAAsB,UAAU;AAAA,IAC9C,WAAW,CAAC,GAAG,oBAAoB,WAAW;AAAA,IAC9C,gBAAgB,CAAC,GAAG,mBAAmB,gBAAgB;AAAA,IACvD,OAAO,CAAC,GAAG,gBAAgB,OAAO;AAAA,IAClC,cAAc,CAAC,GAAG,uBAAuB,cAAc;AAAA,IACvD,OAAO,CAAC,GAAG,gBAAgB,OAAO;AAAA,EACpC;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,oBAAoB;AACpC,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,QAAQ;AAAA,MACjB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAI;AAAA,CACH,SAAUC,eAAc;AACvB,EAAAA,cAAa,OAAO,IAAI;AACxB,EAAAA,cAAa,MAAM,IAAI;AACvB,EAAAA,cAAa,KAAK,IAAI;AACxB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AACtC,IAAMC,kBAAiB,YAAU;AAC/B,QAAM,OAAO,IAAI,SAAS,QAAQ,KAAK,oBAAoB,GAAG,IAAI;AAClE,SAAO,eAAiB,QAAQ,IAAI;AACtC;AACA,SAAS,qBAAqB,OAAO,OAAO;AAC1C,SAAO,KAAK,MAAM,MAAM,IAAI,KAAK,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,MAAM,IAAI,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM,IAAI,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK;AACpN;AACA,SAAS,qBAAqB,OAAO,OAAO;AAC1C,SAAO,KAAK,MAAM,MAAM,GAAG,KAAK,KAAK,KAAK,MAAM,GAAG,KAAK,KAAK,MAAM,MAAM,GAAG,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,GAAG,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM;AACpN;AACA,SAAS,SAAS,OAAO,OAAO;AAC9B,SAAO,qBAAqB,OAAO,KAAK,KAAK,qBAAqB,OAAO,KAAK;AAChF;AACA,SAAS,eAAe,QAAQ,WAAW;AACzC,SAAO,KAAK,MAAM,SAAS,SAAS,IAAI;AAC1C;AACA,IAAM,iBAAiB,CAAC,OAAO,UAAU,MAAM,KAAK,MAAM,KAAK;AAC/D,IAAM,2BAA2B,CAAC,OAAO,QAAQ,IAAI,KAAK,YAAY;AACtE,IAAM,qBAAqB,CAAC,OAAO,YAAY,QAAQ,KAAK,YAAY;AACxE,IAAM,cAAc,CAAC,OAAO,SAAS,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY;AACvE,IAAM,yBAAyB,CAAC,OAAO,cAAc,UAAU,MAAM,KAAK,UAAU,MAAM,KAAK,UAAU;AACzG,IAAM,uBAAuB,CAAC,OAAO,cAAc,UAAU,MAAM,KAAK,UAAU,MAAM,KAAK,UAAU;AACvG,IAAMC,mBAAkB;AACxB,SAAS,wBAAwB,cAAc,mBAAmB,cAAc;AAC9E,UAAQ,gBAAgBA,qBAAoB,eAAe;AAC7D;AACA,SAAS,gBAAgB,QAAQ,cAAc,mBAAmB,eAAe,cAAc;AAC7F,QAAM,0BAA0B,eAAe,QAAQ,iBAAiB,iBAAiB;AACzF,QAAM,uBAAuB,wBAAwB,cAAc,mBAAmB,YAAY;AAClG,SAAO,0BAA0B;AACnC;AACA,SAAS,mBAAmB,aAAa,OAAO,gBAAgB;AAC9D,MAAI,MAAM,KAAK;AACb,WAAO,MAAM;AAAA,EACf,OAAO;AACL,WAAO,YAAY,WAAW,MAAM,OAAO,cAAc;AAAA,EAC3D;AACF;AACA,SAAS,sBAAsB,aAAa,MAAM,MAAM,UAAU;AAChE,MAAI,cAAc;AAClB,MAAI,YAAY;AAChB,QAAM,aAAa,OAAO,IAAI,YAAY,UAAU,YAAY;AAChE,MAAI,SAAS;AACb,SAAO,aAAa,KAAK,IAAI,IAAI,GAAG;AAClC,aAAS,WAAW,MAAM,WAAW;AACrC,UAAM,MAAM,YAAY,OAAO,MAAM;AACrC,QAAI,SAAS,QAAQ,GAAG,MAAM,IAAI;AAChC;AAAA,IACF;AACA;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,sBAAsB,UAAU,QAAQ,QAAQ;AACvD,QAAM,MAAM,UAAU;AACtB,SAAO,OAAO,SAAS,YAAY,YAAY,OAAO,OAAO,OAAO,SAAS,OAAO,OAAO,OAAO;AACpG;AACA,SAAS,uBAAuB,WAAW,MAAM,QAAQ,YAAY;AACnE,SAAO,UAAU,YAAY,UAAU,SAAS,UAAU,UAAU,SAAS,eAAe,cAAc,UAAU,SAAS,MAAM,UAAU,CAAC,UAAU,CAAC,UAAU,SAAS,MAAM,UAAU;AAC9L;AACA,SAAS,kBAAkB,aAAa,UAAU,cAAc,WAAW,CAAC,GAAG,YAAY;AACzF,MAAI,YAAY,aAAa,YAAY,WAAW,QAAQ,IAAI,YAAY,YAAY,UAAU;AAAA,IAChG;AAAA,EACF,CAAC;AACD,QAAM,YAAY,YAAY,UAAU,UAAU;AAAA,IAChD;AAAA,EACF,CAAC;AACD,SAAO,SAAS,QAAQ,YAAY,OAAO,SAAS,CAAC,IAAI,MAAM,YAAY,WAAW;AACpF,gBAAY,YAAY,QAAQ,WAAW,CAAC;AAAA,EAC9C;AACA,MAAI,YAAY;AACd,UAAM,UAAU,YAAY,SAAS,sBAAsB,aAAa,WAAW,aAAa,GAAG,QAAQ,CAAC;AAC5G,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,UAAU;AACd,WAAO,SAAS,QAAQ,YAAY,OAAO,OAAO,CAAC,IAAI,MAAM,UAAU,WAAW;AAChF,gBAAU,YAAY,QAAQ,SAAS,CAAC;AAAA,IAC1C;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,kBAAkB;AAAA,EACzB;AAAA,EACA;AACF,GAAG;AACD,QAAM,iBAAiB;AACvB,SAAO,KAAK,IAAI,CAAC,IAAI,kBAAkB,KAAK,IAAI,CAAC,IAAI;AACvD;AACA,IAAM,cAAN,MAAkB;AAAC;AAcnB,IAAM,gCAAN,MAAoC;AAAA,EAClC,YAAY,aAAa;AACvB,SAAK,cAAc;AAInB,SAAK,cAAc,CAAC;AAIpB,SAAK,iBAAiB,IAAI,aAAa;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,UAAM,QAAQ;AAAA,MACZ,KAAK,KAAK,YAAY;AAAA,MACtB,MAAM,KAAK,YAAY;AAAA,MACvB,OAAO,KAAK,YAAY;AAAA,IAC1B,EAAE,KAAK,IAAI;AACX,QAAI,KAAK,SAAS,aAAa,KAAK;AAClC,WAAK,eAAe,KAAK,sBAAsB,KAAK,aAAa,KAAK,UAAU,IAAI,KAAK,WAAW,CAAC;AAAA,IACvG,WAAW,KAAK,SAAS,aAAa,QAAQ,KAAK,YAAY;AAC7D,WAAK,eAAe,KAAK,sBAAsB,KAAK,aAAa,KAAK,UAAU,CAAC,KAAK,YAAY,KAAK,WAAW,CAAC;AAAA,IACrH,OAAO;AACL,WAAK,eAAe,KAAK,MAAM,KAAK,UAAU,CAAC,CAAC;AAAA,IAClD;AAAA,EACF;AACF;AACA,8BAA8B,OAAO,SAAS,sCAAsC,mBAAmB;AACrG,SAAO,KAAK,qBAAqB,+BAAkC,kBAAkB,WAAW,CAAC;AACnG;AACA,8BAA8B,OAAyB,kBAAkB;AAAA,EACvE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,2BAA2B,EAAE,CAAC;AAAA,EAC/C,cAAc,SAAS,2CAA2C,IAAI,KAAK;AACzE,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,SAAS,SAAS,yDAAyD;AACvF,eAAO,IAAI,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,EACd;AAAA,EACA,SAAS;AAAA,IACP,gBAAgB;AAAA,EAClB;AAAA,EACA,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAcH,IAAM,4BAAN,MAAgC;AAAA,EAC9B,YAAY,aAAa;AACvB,SAAK,cAAc;AAInB,SAAK,cAAc,CAAC;AAIpB,SAAK,iBAAiB,IAAI,aAAa;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,UAAM,QAAQ;AAAA,MACZ,KAAK,KAAK,YAAY;AAAA,MACtB,MAAM,KAAK,YAAY;AAAA,MACvB,OAAO,KAAK,YAAY;AAAA,IAC1B,EAAE,KAAK,IAAI;AACX,QAAI,KAAK,SAAS,aAAa,KAAK;AAClC,WAAK,eAAe,KAAK,sBAAsB,KAAK,aAAa,KAAK,UAAU,GAAG,KAAK,WAAW,CAAC;AAAA,IACtG,WAAW,KAAK,SAAS,aAAa,QAAQ,KAAK,YAAY;AAC7D,WAAK,eAAe,KAAK,sBAAsB,KAAK,aAAa,KAAK,UAAU,KAAK,YAAY,KAAK,WAAW,CAAC;AAAA,IACpH,OAAO;AACL,WAAK,eAAe,KAAK,MAAM,KAAK,UAAU,CAAC,CAAC;AAAA,IAClD;AAAA,EACF;AACF;AACA,0BAA0B,OAAO,SAAS,kCAAkC,mBAAmB;AAC7F,SAAO,KAAK,qBAAqB,2BAA8B,kBAAkB,WAAW,CAAC;AAC/F;AACA,0BAA0B,OAAyB,kBAAkB;AAAA,EACnE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,uBAAuB,EAAE,CAAC;AAAA,EAC3C,cAAc,SAAS,uCAAuC,IAAI,KAAK;AACrE,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,SAAS,SAAS,qDAAqD;AACnF,eAAO,IAAI,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,EACd;AAAA,EACA,SAAS;AAAA,IACP,gBAAgB;AAAA,EAClB;AAAA,EACA,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAaH,IAAM,yBAAN,MAA6B;AAAA,EAC3B,YAAY,aAAa;AACvB,SAAK,cAAc;AAInB,SAAK,iBAAiB,IAAI,aAAa;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,SAAK,eAAe,KAAK,KAAK,YAAY,WAAW,oBAAI,KAAK,CAAC,CAAC;AAAA,EAClE;AACF;AACA,uBAAuB,OAAO,SAAS,+BAA+B,mBAAmB;AACvF,SAAO,KAAK,qBAAqB,wBAA2B,kBAAkB,WAAW,CAAC;AAC5F;AACA,uBAAuB,OAAyB,kBAAkB;AAAA,EAChE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,EACxC,cAAc,SAAS,oCAAoC,IAAI,KAAK;AAClE,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,SAAS,SAAS,kDAAkD;AAChF,eAAO,IAAI,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,UAAU;AAAA,EACZ;AAAA,EACA,SAAS;AAAA,IACP,gBAAgB;AAAA,EAClB;AAAA,EACA,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,+BAAN,MAAmC;AAAA,EACjC,YAAY,aAAa;AACvB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AAAA,IACpB;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,WAAW,MAAM,QAAQ,MAAM;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB;AAAA,IACjB;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,WAAW,MAAM,KAAK,MAAM;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AAAA,IACb;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,WAAW,MAAM,UAAU,MAAM;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAIA,qBAAqB;AAAA,IACnB;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,WAAW,MAAM,QAAQ,MAAM;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAIA,wBAAwB;AAAA,IACtB;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,WAAW,MAAM,SAAS,MAAM;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,kBAAkB,KAAK,aAAa,MAAM,cAAc,aAAa,UAAU;AACnF,UAAM,SAAS,CAAC,cAAc,aAAa,WAAW,cAAc,WAAW,WAAW,WAAW,KAAK,MAAM;AAChH,WAAO,GAAG,OAAO,WAAW,UAAU,eAAe,MAAM,QAAQ,eAAe,CAAC,CAAC,MAAM,OAAO,SAAS,IAAI,CAAC;AAAA,EACjH;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AAAA,IACX;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,WAAW,MAAM,OAAO,MAAM;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,WAAW,MAAM,OAAO,MAAM;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AAAA,IACX;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,WAAW,MAAM,mBAAmB,MAAM;AAAA,EACnD;AACF;AACA,6BAA6B,OAAO,SAAS,qCAAqC,mBAAmB;AACnG,SAAO,KAAK,qBAAqB,8BAAiC,SAAS,WAAW,CAAC;AACzF;AACA,6BAA6B,QAA0B,mBAAmB;AAAA,EACxE,OAAO;AAAA,EACP,SAAS,6BAA6B;AACxC,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AA4BH,IAAM,wBAAN,cAAoC,6BAA6B;AAAC;AAClE,sBAAsB,OAAuB,uBAAM;AACjD,MAAI;AACJ,SAAO,SAAS,8BAA8B,mBAAmB;AAC/D,YAAQ,uCAAuC,qCAAwC,sBAAsB,qBAAqB,IAAI,qBAAqB,qBAAqB;AAAA,EAClL;AACF,GAAG;AACH,sBAAsB,QAA0B,mBAAmB;AAAA,EACjE,OAAO;AAAA,EACP,SAAS,sBAAsB;AACjC,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AASH,IAAM,mBAAN,MAAuB;AAAA,EACrB,YAAY,eAAe,QAAQ;AACjC,SAAK,gBAAgB;AACrB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,UAAU,MAAM,QAAQ,SAAS,KAAK,QAAQ,eAAe,GAAG,cAAc,CAAC,GAAG,YAAY;AAC5F,QAAI,OAAO,KAAK,cAAc,MAAM,MAAM,aAAa;AACrD,YAAM,iBAAiB,OAAO,oBAAoB,OAAO,eAAe,sBAAsB,SAAS,CAAC,EAAE,OAAO,aAAW,YAAY,aAAa;AACrJ,YAAM,IAAI,MAAM,GAAG,MAAM,sDAAsD,eAAe,KAAK,IAAI,CAAC,EAAE;AAAA,IAC5G;AACA,WAAO,KAAK,cAAc,MAAM,EAAE;AAAA,MAChC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,iBAAiB,OAAO,SAAS,yBAAyB,mBAAmB;AAC3E,SAAO,KAAK,qBAAqB,kBAAqB,kBAAkB,uBAAuB,EAAE,GAAM,kBAAkB,WAAW,EAAE,CAAC;AACzI;AACA,iBAAiB,QAA0B,aAAa;AAAA,EACtD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,SAAS;AAAA,MAClB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,gBAAN,MAAoB;AAAA,EAClB,YAAY,aAAa;AACvB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,aAAa,KAAK,aAAa,IAAI;AAAA,EAC5C;AAAA,EACA,kBAAkB,MAAM;AACtB,WAAO,kBAAkB,KAAK,aAAa,IAAI;AAAA,EACjD;AAAA,EACA,YAAY,MAAM;AAChB,WAAO,YAAY,KAAK,aAAa,IAAI;AAAA,EAC3C;AACF;AACA,cAAc,OAAO,SAAS,sBAAsB,mBAAmB;AACrE,SAAO,KAAK,qBAAqB,eAAkB,SAAS,WAAW,CAAC;AAC1E;AACA,cAAc,QAA0B,mBAAmB;AAAA,EACzD,OAAO;AAAA,EACP,SAAS,cAAc;AACzB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,SAAS,IAAI,eAAe,QAAQ;AAiB1C,IAAM,8BAAN,MAAkC;AAAA;AAAA;AAAA;AAAA,EAIhC,YAAY,QAAQ,aAAa;AAC/B,SAAK,SAAS;AACd,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AAAA,IACpB;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,KAAK,OAAO,IAAI,EAAE,OAAO,MAAM,EAAE,OAAO,MAAM;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB;AAAA,IACjB;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,KAAK,OAAO,IAAI,EAAE,OAAO,MAAM,EAAE,OAAO,GAAG;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AAAA,IACb;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,KAAK,OAAO,IAAI,EAAE,OAAO,MAAM,EAAE,OAAO,WAAW;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA,EAIA,qBAAqB;AAAA,IACnB;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,KAAK,OAAO,IAAI,EAAE,OAAO,MAAM,EAAE,OAAO,MAAM;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA,EAIA,wBAAwB;AAAA,IACtB;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,KAAK,OAAO,IAAI,EAAE,OAAO,MAAM,EAAE,OAAO,OAAO;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,kBAAkB,KAAK,aAAa,MAAM,cAAc,aAAa,UAAU;AACnF,UAAM,SAAS,CAAC,cAAc,aAAa,KAAK,OAAO,YAAY,EAAE,OAAO,MAAM,EAAE,OAAO,WAAW,WAAW,WAAW,GAAG;AAC/H,WAAO,GAAG,OAAO,WAAW,UAAU,eAAe,MAAM,QAAQ,eAAe,CAAC,CAAC,MAAM,OAAO,SAAS,IAAI,CAAC;AAAA,EACjH;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AAAA,IACX;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,KAAK,OAAO,IAAI,EAAE,OAAO,MAAM,EAAE,OAAO,IAAI;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,KAAK,OAAO,IAAI,EAAE,OAAO,MAAM,EAAE,OAAO,IAAI;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AAAA,IACX;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,KAAK,OAAO,IAAI,EAAE,OAAO,MAAM,EAAE,OAAO,UAAU;AAAA,EAC3D;AAAA;AACF;AACA,4BAA4B,OAAO,SAAS,oCAAoC,mBAAmB;AACjG,SAAO,KAAK,qBAAqB,6BAAgC,SAAS,MAAM,GAAM,SAAS,WAAW,CAAC;AAC7G;AACA,4BAA4B,QAA0B,mBAAmB;AAAA,EACvE,OAAO;AAAA,EACP,SAAS,4BAA4B;AACvC,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,MAAM;AAAA,MACf,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AAOH,IAAM,8BAAN,MAAkC;AAAA,EAChC,YAAY,aAAa;AACvB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AAAA,IACpB;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,IAAI,KAAK,eAAe,QAAQ;AAAA,MACrC,SAAS;AAAA,IACX,CAAC,EAAE,OAAO,IAAI;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB;AAAA,IACjB;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,IAAI,KAAK,eAAe,QAAQ;AAAA,MACrC,KAAK;AAAA,IACP,CAAC,EAAE,OAAO,IAAI;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AAAA,IACb;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,IAAI,KAAK,eAAe,QAAQ;AAAA,MACrC,MAAM;AAAA,MACN,OAAO;AAAA,IACT,CAAC,EAAE,OAAO,IAAI;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,qBAAqB;AAAA,IACnB;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,IAAI,KAAK,eAAe,QAAQ;AAAA,MACrC,SAAS;AAAA,IACX,CAAC,EAAE,OAAO,IAAI;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,wBAAwB;AAAA,IACtB;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,IAAI,KAAK,eAAe,QAAQ;AAAA,MACrC,KAAK;AAAA,MACL,OAAO;AAAA,IACT,CAAC,EAAE,OAAO,IAAI;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,kBAAkB,KAAK,aAAa,MAAM,cAAc,aAAa,UAAU;AACnF,UAAM,SAAS,CAAC,cAAc,aAAa,IAAI,KAAK,eAAe,QAAQ;AAAA,MACzE,KAAK;AAAA,MACL,OAAO;AAAA,MACP,MAAM,WAAW,YAAY;AAAA,IAC/B,CAAC,EAAE,OAAO,YAAY;AACtB,WAAO,GAAG,OAAO,WAAW,UAAU,eAAe,MAAM,QAAQ,eAAe,CAAC,CAAC,MAAM,OAAO,SAAS,IAAI,CAAC;AAAA,EACjH;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AAAA,IACX;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,IAAI,KAAK,eAAe,QAAQ;AAAA,MACrC,MAAM;AAAA,IACR,CAAC,EAAE,OAAO,IAAI;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,IAAI,KAAK,eAAe,QAAQ;AAAA,MACrC,MAAM;AAAA,IACR,CAAC,EAAE,OAAO,IAAI;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AAAA,IACX;AAAA,IACA;AAAA,EACF,GAAG;AACD,WAAO,IAAI,KAAK,eAAe,QAAQ;AAAA,MACrC,KAAK;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACX,CAAC,EAAE,OAAO,IAAI;AAAA,EAChB;AACF;AACA,4BAA4B,OAAO,SAAS,oCAAoC,mBAAmB;AACjG,SAAO,KAAK,qBAAqB,6BAAgC,SAAS,WAAW,CAAC;AACxF;AACA,4BAA4B,QAA0B,mBAAmB;AAAA,EACvE,OAAO;AAAA,EACP,SAAS,4BAA4B;AACvC,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAI;AAAA,CACH,SAAUC,qCAAoC;AAC7C,EAAAA,oCAAmC,MAAM,IAAI;AAC7C,EAAAA,oCAAmC,MAAM,IAAI;AAC7C,EAAAA,oCAAmC,QAAQ,IAAI;AACjD,GAAG,uCAAuC,qCAAqC,CAAC,EAAE;AAkBlF,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,OAAO,QAAQ,aAAa,SAAS,CAAC,GAAG;AACvC,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,aAAa,OAAO,uBAAuB,6BAA6B,OAAO,iBAAiB,uBAAuB,OAAO,SAAS,eAAe,OAAO,QAAQ,YAAY;AAAA,IAC/L;AAAA,EACF;AACF;AACA,qBAAqB,OAAO,SAAS,6BAA6B,mBAAmB;AACnF,SAAO,KAAK,qBAAqB,sBAAsB;AACzD;AACA,qBAAqB,OAAyB,iBAAiB;AAAA,EAC7D,MAAM;AAAA,EACN,cAAc,CAAC,+BAA+B,6BAA6B,gCAAgC,0BAA0B,+BAA+B,2BAA2B,wBAAwB,kBAAkB,wBAAwB,kBAAkB,gBAAgB,qBAAqB;AAAA,EACxT,SAAS,CAAC,YAAY;AAAA,EACtB,SAAS,CAAC,+BAA+B,6BAA6B,gCAAgC,0BAA0B,+BAA+B,2BAA2B,wBAAwB,kBAAkB,wBAAwB,kBAAkB,gBAAgB,qBAAqB;AACrT,CAAC;AACD,qBAAqB,OAAyB,iBAAiB;AAAA,EAC7D,WAAW,CAAC,cAAc;AAAA,EAC1B,SAAS,CAAC,YAAY;AACxB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,+BAA+B,6BAA6B,gCAAgC,0BAA0B,+BAA+B,2BAA2B,wBAAwB,kBAAkB,wBAAwB,kBAAkB,gBAAgB,qBAAqB;AAAA,MACxT,SAAS,CAAC,YAAY;AAAA,MACtB,SAAS,CAAC,+BAA+B,6BAA6B,gCAAgC,0BAA0B,+BAA+B,2BAA2B,wBAAwB,kBAAkB,wBAAwB,kBAAkB,gBAAgB,qBAAqB;AAAA,MACnT,WAAW,CAAC,cAAc;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,6BAAN,MAAiC;AAAA,EAC/B,cAAc;AACZ,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,iBAAiB,IAAI,aAAa;AACvC,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,iBAAiB;AACtB,SAAK,eAAe;AAAA,EACtB;AACF;AACA,2BAA2B,OAAO,SAAS,mCAAmC,mBAAmB;AAC/F,SAAO,KAAK,qBAAqB,4BAA4B;AAC/D;AACA,2BAA2B,OAAyB,kBAAkB;AAAA,EACpE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,yBAAyB,CAAC;AAAA,EACvC,WAAW,CAAC,GAAG,YAAY,cAAc;AAAA,EACzC,UAAU;AAAA,EACV,cAAc,SAAS,wCAAwC,IAAI,KAAK;AACtE,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,YAAY,IAAI,IAAI,MAAM,EAAE,aAAa,IAAI,IAAI,OAAO,EAAE,cAAc,IAAI,IAAI,QAAQ,EAAE,eAAe,IAAI,IAAI,SAAS,EAAE,gBAAgB,IAAI,IAAI,OAAO,EAAE,iBAAiB,CAAC,IAAI,IAAI,OAAO,EAAE,kBAAkB,IAAI,IAAI,OAAO,SAAS,CAAC,EAAE,YAAY,IAAI,QAAQ,IAAI,OAAO,EAAE,uBAAuB,CAAC,CAAC,IAAI,IAAI,eAAe;AAAA,IACnV;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,cAAc;AAAA,EAChB;AAAA,EACA,SAAS;AAAA,IACP,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,cAAc;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,eAAe,MAAM,GAAG,CAAC,SAAS,iBAAiB,GAAG,MAAM,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC,SAAS,cAAc,GAAG,MAAM,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,SAAS,aAAa,gBAAgB,IAAI,mBAAmB,mBAAmB,GAAG,WAAW,WAAW,sBAAsB,oBAAoB,gBAAgB,mBAAmB,uBAAuB,gBAAgB,iBAAiB,YAAY,YAAY,gBAAgB,uBAAuB,cAAc,cAAc,YAAY,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,gBAAgB,IAAI,mBAAmB,mBAAmB,GAAG,aAAa,GAAG,cAAc,cAAc,YAAY,WAAW,WAAW,sBAAsB,oBAAoB,gBAAgB,mBAAmB,uBAAuB,gBAAgB,YAAY,YAAY,gBAAgB,qBAAqB,CAAC;AAAA,EAC38B,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,GAAG,mDAAmD,GAAG,IAAI,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,mDAAmD,GAAG,GAAG,eAAe,CAAC;AAAA,IAC5M;AACA,QAAI,KAAK,GAAG;AACV,YAAM,sBAAyB,YAAY,CAAC;AAC5C,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,oBAAoB,IAAI,kBAAkB,mBAAmB,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,CAAC,IAAI,KAAK,IAAI,SAAS,IAAI,QAAQ,IAAI,kBAAkB,IAAI,cAAc,IAAI,gBAAgB,IAAI,cAAc,IAAI,iBAAiB,IAAI,qBAAqB,IAAI,cAAc,IAAI,gBAAgB,IAAI,YAAY,CAAC,CAAC;AAAA,IAC5V;AAAA,EACF;AAAA,EACA,cAAc,CAAI,SAAY,SAAY,MAAS,kBAAqB,SAAY,oBAAoB,0BAA0B,gBAAgB,kBAAkB,wBAAwB,gBAAgB;AAAA,EAC5M,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA+EV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,QACrB,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,QACvB,wBAAwB;AAAA,QACxB,yBAAyB;AAAA,QACzB,0BAA0B;AAAA,QAC1B,oBAAoB;AAAA,QACpB,+BAA+B;AAAA,MACjC;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oBAAoB,QAAQ,YAAY,CAAC,MAAM,QAAQ,MAAM;AAAA,EACjE,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,eAAe;AAAA,EACf,kBAAkB;AACpB,CAAC,CAAC,GAAG,MAAM,KAAK,MAAM;AAAA,EACpB,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,eAAe;AAAA,EACf,kBAAkB;AACpB,CAAC,CAAC,GAAG,WAAW,aAAa,QAAQ,gBAAgB,CAAC,GAAG,WAAW,aAAa,QAAQ,eAAe,CAAC,CAAC,CAAC;AAC3G,IAAM,iCAAN,MAAqC;AAAA,EACnC,cAAc;AACZ,SAAK,SAAS;AACd,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,iBAAiB;AACtB,SAAK,eAAe;AAAA,EACtB;AACF;AACA,+BAA+B,OAAO,SAAS,uCAAuC,mBAAmB;AACvG,SAAO,KAAK,qBAAqB,gCAAgC;AACnE;AACA,+BAA+B,OAAyB,kBAAkB;AAAA,EACxE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,8BAA8B,CAAC;AAAA,EAC5C,QAAQ;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,IACtB,MAAM;AAAA,EACR;AAAA,EACA,SAAS;AAAA,IACP,cAAc;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,SAAS,uBAAuB,QAAQ,eAAe,GAAG,MAAM,GAAG,CAAC,QAAQ,eAAe,GAAG,qBAAqB,GAAG,CAAC,YAAY,MAAM,QAAQ,OAAO,GAAG,CAAC,YAAY,KAAK,QAAQ,UAAU,GAAG,CAAC,gBAAgB,IAAI,mBAAmB,mBAAmB,GAAG,WAAW,iBAAiB,YAAY,YAAY,gBAAgB,uBAAuB,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,gBAAgB,IAAI,mBAAmB,mBAAmB,GAAG,WAAW,YAAY,YAAY,gBAAgB,qBAAqB,GAAG,CAAC,GAAG,aAAa,GAAG,SAAS,GAAG,CAAC,QAAQ,SAAS,YAAY,KAAK,GAAG,YAAY,mBAAmB,SAAS,gBAAgB,GAAG,CAAC,GAAG,SAAS,gBAAgB,CAAC;AAAA,EAClwB,UAAU,SAAS,wCAAwC,IAAI,KAAK;AAClE,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,GAAG,uDAAuD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,uDAAuD,GAAG,GAAG,eAAe,CAAC;AAAA,IACnN;AACA,QAAI,KAAK,GAAG;AACV,YAAM,sBAAyB,YAAY,CAAC;AAC5C,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,oBAAoB,IAAI,kBAAkB,mBAAmB,EAAE,2BAA8B,gBAAgB,GAAG,MAAM,IAAI,QAAQ,IAAI,cAAc,IAAI,QAAQ,IAAI,gBAAgB,IAAI,YAAY,CAAC;AAAA,IACrN;AAAA,EACF;AAAA,EACA,cAAc,CAAI,SAAY,SAAY,MAAS,kBAAqB,SAAY,oBAAoB,+BAA+B,6BAA6B,gBAAgB,uBAAuB,gBAAgB;AAAA,EAC3N,eAAe;AAAA,EACf,MAAM;AAAA,IACJ,WAAW,CAAC,iBAAiB;AAAA,EAC/B;AACF,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gCAAgC,CAAC;AAAA,IACvG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqFV,YAAY,CAAC,iBAAiB;AAAA,IAChC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mCAAN,MAAuC;AAAA,EACrC,cAAc;AACZ,SAAK,sBAAsB,IAAI,aAAa;AAC5C,SAAK,2BAA2B;AAAA,EAClC;AACF;AACA,iCAAiC,OAAO,SAAS,yCAAyC,mBAAmB;AAC3G,SAAO,KAAK,qBAAqB,kCAAkC;AACrE;AACA,iCAAiC,OAAyB,kBAAkB;AAAA,EAC1E,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,gCAAgC,CAAC;AAAA,EAC9C,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,gBAAgB;AAAA,EAClB;AAAA,EACA,SAAS;AAAA,IACP,qBAAqB;AAAA,EACvB;AAAA,EACA,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,QAAQ,OAAO,GAAG,gBAAgB,YAAY,GAAG,CAAC,SAAS,YAAY,YAAY,KAAK,QAAQ,gBAAgB,GAAG,YAAY,aAAa,cAAc,eAAe,WAAW,SAAS,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,YAAY,KAAK,QAAQ,gBAAgB,GAAG,YAAY,GAAG,SAAS,SAAS,CAAC;AAAA,EAC3Y,UAAU,SAAS,0CAA0C,IAAI,KAAK;AACpE,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,yDAAyD,GAAG,GAAG,eAAe,CAAC;AAAA,IACvN;AACA,QAAI,KAAK,GAAG;AACV,YAAM,qBAAwB,YAAY,CAAC;AAC3C,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,oBAAoB,IAAI,kBAAkB,kBAAkB,EAAE,2BAA8B,gBAAgB,GAAG,MAAM,IAAI,MAAM,IAAI,QAAQ,IAAI,wBAAwB,CAAC;AAAA,IACxL;AAAA,EACF;AAAA,EACA,cAAc,CAAI,SAAY,SAAY,kBAAkB,gBAAgB;AAAA,EAC5E,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kCAAkC,CAAC;AAAA,IACzG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAuCZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAYH,IAAM,6BAAN,MAAiC;AAAA;AAAA;AAAA;AAAA,EAI/B,YAAY,KAAK,OAAO,QAAQ,aAAa;AAC3C,SAAK,MAAM;AACX,SAAK,QAAQ;AACb,SAAK,cAAc;AAKnB,SAAK,SAAS,CAAC;AAIf,SAAK,cAAc,CAAC;AAIpB,SAAK,kBAAkB;AAIvB,SAAK,mBAAmB;AAIxB,SAAK,sBAAsB;AAK3B,SAAK,eAAe;AAKpB,SAAK,mBAAmB,IAAI,aAAa;AAIzC,SAAK,aAAa,IAAI,aAAa;AAInC,SAAK,eAAe,IAAI,aAAa;AAIrC,SAAK,sBAAsB,IAAI,aAAa;AAI5C,SAAK,oBAAoB,IAAI,aAAa;AAI1C,SAAK,mBAAmB,CAAC,OAAO,WAAW,KAAK,KAAK,KAAK,MAAM,QAAQ,KAAK,KAAK,sBAAsB,EAAE,IAAI,SAAO,IAAI,KAAK,YAAY,CAAC,EAAE,KAAK,GAAG;AAIrJ,SAAK,cAAc,CAAC,OAAO,QAAQ,IAAI,KAAK,YAAY;AACxD,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACT,QAAI,KAAK,SAAS;AAChB,WAAK,sBAAsB,KAAK,QAAQ,UAAU,MAAM;AACtD,aAAK,WAAW;AAChB,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,SAAS;AACnB,UAAM,gBAAgB,QAAQ,YAAY,QAAQ,eAAe,QAAQ;AACzE,UAAM,cAAc,QAAQ,YAAY,QAAQ,UAAU,QAAQ,eAAe,QAAQ;AACzF,QAAI,eAAe;AACjB,WAAK,cAAc;AAAA,IACrB;AACA,QAAI,QAAQ,QAAQ;AAClB,MAAAF,gBAAe,KAAK,MAAM;AAAA,IAC5B;AACA,QAAI,aAAa;AACf,WAAK,YAAY;AAAA,IACnB;AACA,QAAI,iBAAiB,aAAa;AAChC,WAAK,qBAAqB;AAAA,IAC5B;AACA,QAAI,QAAQ,mBAAmB,QAAQ,YAAY,QAAQ,UAAU,QAAQ,eAAe,QAAQ,WAAW;AAC7G,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,YAAY;AAAA,IACvC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB,OAAO,eAAe;AACvC,SAAK,KAAK,KAAK,QAAQ,SAAO;AAC5B,UAAI,iBAAiB,IAAI,OAAO,QAAQ,KAAK,IAAI,IAAI;AACnD,YAAI,kBAAkB,MAAM,SAAS,MAAM,MAAM,aAAa;AAAA,MAChE,OAAO;AACL,eAAO,IAAI;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,WAAW,OAAO,aAAa;AAC1C,QAAI,cAAc,aAAa;AAC7B,YAAM,OAAO,KAAK,YAAY,QAAQ,UAAU,IAAI;AACpD,YAAM,QAAQ,KAAK,YAAY,SAAS,UAAU,IAAI;AACtD,YAAM,OAAO,KAAK,YAAY,QAAQ,UAAU,IAAI;AACpD,YAAM,WAAW,KAAK,YAAY,QAAQ,KAAK,YAAY,SAAS,KAAK,YAAY,QAAQ,MAAM,OAAO,IAAI,GAAG,KAAK,GAAG,IAAI;AAC7H,UAAI;AACJ,UAAI,MAAM,KAAK;AACb,cAAM,cAAc,KAAK,YAAY,oBAAoB,UAAU,MAAM,KAAK;AAC9E,iBAAS,KAAK,YAAY,WAAW,MAAM,KAAK,WAAW;AAAA,MAC7D;AACA,WAAK,kBAAkB,KAAK;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,MAAM,mCAAmC;AAAA,MAC3C,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,SAAK,gBAAgB,KAAK,MAAM,kBAAkB;AAAA,MAChD,UAAU,KAAK;AAAA,MACf,cAAc,KAAK;AAAA,MACnB,UAAU,KAAK;AAAA,MACf,aAAa,KAAK;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,KAAK,MAAM,aAAa;AAAA,MAClC,QAAQ,KAAK;AAAA,MACb,UAAU,KAAK;AAAA,MACf,cAAc,KAAK;AAAA,MACnB,UAAU,KAAK;AAAA,MACf,aAAa,KAAK;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,oBAAoB,MAAM;AACjC,YAAM,YAAY,KAAK,aAAa,KAAK;AACzC,WAAK,UAAU,KAAK,KAAK,KAAK,KAAK,SAAO,KAAK,YAAY,UAAU,IAAI,MAAM,SAAS,CAAC;AACzF,YAAM,QAAQ,KAAK,KAAK,KAAK,QAAQ,KAAK,OAAO;AACjD,WAAK,eAAe,KAAK,MAAM,QAAQ,KAAK,KAAK,sBAAsB,IAAI,KAAK,KAAK;AAAA,IACvF,OAAO;AACL,WAAK,eAAe;AACpB,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,aAAa;AACX,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,qBAAqB;AAC1B,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,iBAAiB,KAAK,MAAM;AACnC,WAAK,iBAAiB,KAAK;AAAA,QACzB,QAAQ,KAAK;AAAA,QACb,MAAM,KAAK,KAAK;AAAA,QAChB,QAAQ,KAAK,KAAK;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF;AACF;AACA,2BAA2B,OAAO,SAAS,mCAAmC,mBAAmB;AAC/F,SAAO,KAAK,qBAAqB,4BAA+B,kBAAqB,iBAAiB,GAAM,kBAAkB,aAAa,GAAM,kBAAkB,SAAS,GAAM,kBAAkB,WAAW,CAAC;AAClN;AACA,2BAA2B,OAAyB,kBAAkB;AAAA,EACpE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,yBAAyB,CAAC;AAAA,EACvC,QAAQ;AAAA,IACN,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,cAAc;AAAA,IACd,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,uBAAuB;AAAA,IACvB,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,IACtB,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,qBAAqB;AAAA,IACrB,mBAAmB;AAAA,EACrB;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,oBAAoB;AAAA,EAClC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,QAAQ,QAAQ,GAAG,gBAAgB,GAAG,CAAC,GAAG,uBAAuB,QAAQ,UAAU,gBAAgB,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,QAAQ,OAAO,GAAG,cAAc,GAAG,CAAC,QAAQ,YAAY,gBAAgB,IAAI,iBAAiB,iBAAiB,GAAG,WAAW,OAAO,WAAW,UAAU,oBAAoB,uBAAuB,mBAAmB,gBAAgB,kBAAkB,WAAW,yBAAyB,YAAY,mBAAmB,gBAAgB,kBAAkB,QAAQ,gBAAgB,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,gBAAgB,IAAI,iBAAiB,iBAAiB,GAAG,gBAAgB,QAAQ,UAAU,UAAU,UAAU,QAAQ,kBAAkB,sBAAsB,sBAAsB,GAAG,CAAC,QAAQ,YAAY,gBAAgB,IAAI,iBAAiB,iBAAiB,GAAG,YAAY,mBAAmB,gBAAgB,kBAAkB,QAAQ,gBAAgB,WAAW,OAAO,WAAW,UAAU,oBAAoB,uBAAuB,mBAAmB,gBAAgB,kBAAkB,WAAW,uBAAuB,CAAC;AAAA,EACllC,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,kCAAkC,CAAC;AACrE,MAAG,WAAW,uBAAuB,SAAS,kGAAkG,QAAQ;AACtJ,eAAO,IAAI,oBAAoB,KAAK,MAAM;AAAA,MAC5C,CAAC;AACD,MAAG,aAAa;AAChB,MAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,MAAG,WAAW,GAAG,2CAA2C,GAAG,IAAI,OAAO,CAAC;AAC3E,MAAG,aAAa,EAAE;AAAA,IACpB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,UAAU;AACb,MAAG,WAAW,QAAQ,IAAI,aAAa,EAAE,UAAU,IAAI,MAAM,EAAE,kBAAkB,IAAI,cAAc;AACnG,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,WAAW,IAAI,KAAK,UAAU,EAAE,gBAAgB,IAAI,gBAAgB;AAAA,IACpF;AAAA,EACF;AAAA,EACA,cAAc,CAAI,SAAY,SAAY,SAAY,oBAAoB,gBAAgB,uBAAuB,4BAA4B,gCAAgC,kCAAqC,WAAW,gBAAgB;AAAA,EAC7O,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAsFZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,SAAS;AAAA,MAClB,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,sBAAN,MAA0B;AAAC;AAC3B,oBAAoB,OAAO,SAAS,4BAA4B,mBAAmB;AACjF,SAAO,KAAK,qBAAqB,qBAAqB;AACxD;AACA,oBAAoB,OAAyB,iBAAiB;AAAA,EAC5D,MAAM;AAAA,EACN,cAAc,CAAC,4BAA4B,4BAA4B,gCAAgC,gCAAgC;AAAA,EACvI,SAAS,CAAC,cAAc,mBAAmB,oBAAoB;AAAA,EAC/D,SAAS,CAAC,mBAAmB,4BAA4B,4BAA4B,gCAAgC,gCAAgC;AACvJ,CAAC;AACD,oBAAoB,OAAyB,iBAAiB;AAAA,EAC5D,SAAS,CAAC,cAAc,mBAAmB,sBAAsB,iBAAiB;AACpF,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,mBAAmB,oBAAoB;AAAA,MAC/D,cAAc,CAAC,4BAA4B,4BAA4B,gCAAgC,gCAAgC;AAAA,MACvI,SAAS,CAAC,mBAAmB,4BAA4B,4BAA4B,gCAAgC,gCAAgC;AAAA,IACvJ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,qBAAN,MAAyB;AAAA,EACvB,YAAY,sBAAsB,kBAAkB;AAClD,SAAK,uBAAuB;AAC5B,SAAK,gBAAgB,iBAAiB,sBAAsB;AAAA,EAC9D;AAAA,EACA,aAAa;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM,2BAA2B,kBAAkB;AAAA,MACjD;AAAA,MACA;AAAA,IACF,CAAC,KAAK;AACN,QAAI,mBAAmB;AACrB,YAAM,QAAQ,OAAO,OAAO,CAAC,GAAG,KAAK,eAAe;AAAA,QAClD,MAAM,KAAK,cAAc,OAAO,UAAU;AAAA,QAC1C,OAAO,KAAK,cAAc,QAAQ,UAAU;AAAA,QAC5C,KAAK,KAAK,cAAc,MAAM,UAAU;AAAA,QACxC,QAAQ,KAAK,cAAc,SAAS,UAAU;AAAA,MAChD,CAAC;AACD,UAAI,0BAA0B;AAC5B,cAAM,QAAQ,KAAK,qBAAqB,sBAAsB;AAC9D,cAAM,cAAc,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM;AAC/D,cAAM,iBAAiB,MAAM,MAAM,MAAM,UAAU,MAAM,SAAS,MAAM;AACxE,eAAO,qBAAqB,OAAO,KAAK,MAAM,eAAe;AAAA,MAC/D;AAEA,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,IAAM,uBAAN,MAA2B;AAAA,EACzB,YAAY,wBAAwB,UAAU,KAAK;AACjD,SAAK,yBAAyB;AAC9B,SAAK,WAAW;AAChB,SAAK,MAAM;AAAA,EACb;AAAA,EACA,eAAe;AAAA,IACb;AAAA,IACA;AAAA,EACF,GAAG;AACD,QAAI,KAAK,KAAK;AAGZ,UAAI,OAAO,MAAM,SAAS,aAAa;AACrC,kBAAU,QAAQ,MAAM;AACxB,kBAAU,SAAS,MAAM;AAAA,MAC3B,WAAW,OAAO,MAAM,UAAU,aAAa;AAC7C,kBAAU,QAAQ,MAAM;AACxB,kBAAU,SAAS,MAAM;AAAA,MAC3B;AACA,gBAAU,QAAQ,UAAU,QAAQ,UAAU;AAAA,IAChD;AACA,QAAI,KAAK,YAAY,KAAK,KAAK,UAAU,KAAK,IAAI,KAAK,KAAK,KAAK,QAAQ,GAAG;AAC1E,aAAO;AAAA,IACT;AACA,WAAO,SAAS,KAAK,uBAAuB,sBAAsB,GAAG,SAAS;AAAA,EAChF;AACF;AACA,IAAM,kCAAN,MAAsC;AAAA,EACpC,cAAc;AACZ,SAAK,mBAAmB,IAAI,aAAa;AACzC,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,YAAY,IAAI,aAAa;AAClC,SAAK,2BAA2B;AAAA,EAClC;AACF;AACA,gCAAgC,OAAO,SAAS,wCAAwC,mBAAmB;AACzG,SAAO,KAAK,qBAAqB,iCAAiC;AACpE;AACA,gCAAgC,OAAyB,kBAAkB;AAAA,EACzE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,+BAA+B,CAAC;AAAA,EAC7C,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,gBAAgB;AAAA,EAClB;AAAA,EACA,SAAS;AAAA,IACP,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,QAAQ,OAAO,GAAG,iBAAiB,GAAG,CAAC,SAAS,cAAc,gBAAgB,IAAI,iBAAiB,iBAAiB,YAAY,KAAK,QAAQ,gBAAgB,GAAG,YAAY,aAAa,cAAc,eAAe,WAAW,YAAY,QAAQ,aAAa,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,gBAAgB,IAAI,iBAAiB,iBAAiB,YAAY,KAAK,QAAQ,gBAAgB,GAAG,cAAc,GAAG,YAAY,QAAQ,aAAa,SAAS,CAAC;AAAA,EAChiB,UAAU,SAAS,yCAAyC,IAAI,KAAK;AACnE,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,wDAAwD,GAAG,GAAG,eAAe,CAAC;AAAA,IACrN;AACA,QAAI,KAAK,GAAG;AACV,YAAM,qBAAwB,YAAY,CAAC;AAC3C,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,oBAAoB,IAAI,kBAAkB,kBAAkB,EAAE,2BAA8B,gBAAgB,GAAG,MAAM,IAAI,MAAM,IAAI,QAAQ,IAAI,kBAAkB,IAAI,cAAc,IAAI,WAAW,IAAI,wBAAwB,CAAC;AAAA,IAC/O;AAAA,EACF;AAAA,EACA,cAAc,CAAI,SAAY,SAAY,kBAAqB,oBAAoB,gBAAgB,gBAAgB;AAAA,EACnH,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iCAAiC,CAAC;AAAA,IACxG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAqDZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iCAAN,MAAqC;AAAA,EACnC,cAAc;AACZ,SAAK,eAAe,IAAI,aAAa;AAAA,EACvC;AACF;AACA,+BAA+B,OAAO,SAAS,uCAAuC,mBAAmB;AACvG,SAAO,KAAK,qBAAqB,gCAAgC;AACnE;AACA,+BAA+B,OAAyB,kBAAkB;AAAA,EACxE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,8BAA8B,CAAC;AAAA,EAC5C,QAAQ;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,kBAAkB;AAAA,IAClB,qBAAqB;AAAA,IACrB,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,IACtB,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,SAAS;AAAA,IACP,cAAc;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,YAAY,KAAK,QAAQ,eAAe,GAAG,aAAa,GAAG,YAAY,mBAAmB,WAAW,sBAAsB,oBAAoB,gBAAgB,mBAAmB,uBAAuB,cAAc,GAAG,CAAC,GAAG,SAAS,gBAAgB,GAAG,CAAC,GAAG,SAAS,kBAAkB,MAAM,CAAC;AAAA,EACvX,UAAU,SAAS,wCAAwC,IAAI,KAAK;AAClE,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,GAAG,uDAAuD,GAAG,IAAI,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,uDAAuD,GAAG,GAAG,eAAe,CAAC;AAAA,IACpN;AACA,QAAI,KAAK,GAAG;AACV,YAAM,sBAAyB,YAAY,CAAC;AAC5C,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,oBAAoB,IAAI,kBAAkB,mBAAmB,EAAE,2BAA8B,gBAAgB,GAAG,MAAM,CAAC,IAAI,WAAW,IAAI,kBAAkB,IAAI,cAAc,IAAI,iBAAiB,IAAI,qBAAqB,IAAI,iBAAiB,IAAI,cAAc,IAAI,QAAQ,IAAI,UAAU,CAAC,CAAC;AAAA,IAC/S;AAAA,EACF;AAAA,EACA,cAAc,CAAI,kBAAqB,SAAS,+BAA+B,6BAA6B,0BAA0B,gBAAgB,uBAAuB,wBAAwB,gBAAgB;AAAA,EACrN,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gCAAgC,CAAC;AAAA,IACvG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAwEZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,uCAAN,MAA2C;AAAC;AAC5C,qCAAqC,OAAO,SAAS,6CAA6C,mBAAmB;AACnH,SAAO,KAAK,qBAAqB,sCAAsC;AACzE;AACA,qCAAqC,OAAyB,kBAAkB;AAAA,EAC9E,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,qCAAqC,CAAC;AAAA,EACnD,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,gBAAgB;AAAA,EAClB;AAAA,EACA,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,oBAAoB,GAAG,SAAS,GAAG,CAAC,SAAS,YAAY,GAAG,MAAM,GAAG,CAAC,GAAG,UAAU,CAAC;AAAA,EAC9K,UAAU,SAAS,8CAA8C,IAAI,KAAK;AACxE,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,GAAG,6DAA6D,GAAG,IAAI,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,6DAA6D,GAAG,GAAG,eAAe,CAAC;AAAA,IAChO;AACA,QAAI,KAAK,GAAG;AACV,YAAM,qBAAwB,YAAY,CAAC;AAC3C,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,oBAAoB,IAAI,kBAAkB,kBAAkB,EAAE,2BAA8B,gBAAgB,GAAG,MAAM,IAAI,SAAS,IAAI,QAAQ,IAAI,eAAe,IAAI,aAAa,IAAI,UAAU,CAAC;AAAA,IACjN;AAAA,EACF;AAAA,EACA,cAAc,CAAI,SAAY,MAAS,kBAAkB,kBAAkB,gBAAgB;AAAA,EAC3F,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sCAAsC,CAAC;AAAA,IAC7G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IA6CZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,6CAAN,MAAiD;AAAA,EAC/C,YAAY,aAAa,MAAM;AAC7B,SAAK,cAAc;AACnB,SAAK,OAAO;AACZ,SAAK,cAAc,IAAI,gBAAgB,MAAS;AAChD,SAAK,UAAU,KAAK,KAAK,SAAS,KAAK,UAAU,MAAM,SAAS,KAAK,GAAI,CAAC,GAAG,UAAU,CAAC,GAAG,YAAY,KAAK,WAAW,GAAG,IAAI,gBAAc;AAC1I,YAAM,aAAa,KAAK,YAAY,WAAW,KAAK,YAAY,SAAS,YAAY,KAAK,YAAY,GAAG,KAAK,cAAc;AAC5H,YAAM,WAAW,KAAK,YAAY,WAAW,KAAK,YAAY,SAAS,YAAY,KAAK,UAAU,GAAG,KAAK,YAAY;AACtH,YAAM,qBAAqB,KAAK,eAAe,KAAK,qBAAqB,KAAK,gBAAgB;AAC9F,YAAM,MAAM,oBAAI,KAAK;AACrB,aAAO;AAAA,QACL,WAAW,KAAK,YAAY,UAAU,YAAY,GAAG,KAAK,OAAO,cAAc,OAAO;AAAA,QACtF,KAAK,KAAK,YAAY,oBAAoB,KAAK,UAAU,IAAI;AAAA,MAC/D;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,YAAY;AACtB,WAAK,YAAY,KAAK,QAAQ,WAAW,YAAY;AAAA,IACvD;AAAA,EACF;AACF;AACA,2CAA2C,OAAO,SAAS,mDAAmD,mBAAmB;AAC/H,SAAO,KAAK,qBAAqB,4CAA+C,kBAAkB,WAAW,GAAM,kBAAqB,MAAM,CAAC;AACjJ;AACA,2CAA2C,OAAyB,kBAAkB;AAAA,EACpF,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,4CAA4C,CAAC;AAAA,EAC1D,QAAQ;AAAA,IACN,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,cAAc;AAAA,IACd,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,EAClB;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,oBAAoB;AAAA,EAClC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,SAAS,2BAA2B,GAAG,OAAO,GAAG,MAAM,GAAG,CAAC,GAAG,yBAAyB,CAAC;AAAA,EAC/K,UAAU,SAAS,oDAAoD,IAAI,KAAK;AAC9E,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,GAAG,mEAAmE,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,mEAAmE,GAAG,GAAG,eAAe,CAAC;AACzO,MAAG,OAAO,GAAG,OAAO;AACpB,MAAG,OAAO,GAAG,OAAO;AAAA,IACtB;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,YAAM,qBAAwB,YAAY,CAAC;AAC3C,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,oBAAoB,IAAI,kBAAkB,kBAAkB,EAAE,2BAA8B,gBAAgB,GAAG,MAAM,IAAI,YAAY,IAAI,cAAc,IAAI,gBAAgB,IAAI,YAAY,IAAI,eAAe,UAAa,YAAY,GAAG,GAAG,IAAI,OAAO,MAAM,OAAO,OAAO,QAAQ,YAAY,UAAa,YAAY,GAAG,GAAG,IAAI,OAAO,MAAM,OAAO,OAAO,QAAQ,GAAG,CAAC;AAAA,IAC1X;AAAA,EACF;AAAA,EACA,cAAc,CAAI,MAAS,kBAAqB,SAAS;AAAA,EACzD,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4CAA4C,CAAC;AAAA,IACnH,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IA+BZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAYH,IAAM,4BAAN,MAAgC;AAAA;AAAA;AAAA;AAAA,EAI9B,YAAY,KAAK,OAAO,QAAQ,aAAa,SAAS;AACpD,SAAK,MAAM;AACX,SAAK,QAAQ;AACb,SAAK,cAAc;AACnB,SAAK,UAAU;AAKf,SAAK,SAAS,CAAC;AAIf,SAAK,cAAc,CAAC;AAIpB,SAAK,mBAAmB;AAIxB,SAAK,sBAAsB;AAK3B,SAAK,eAAe;AAKpB,SAAK,YAAY;AAIjB,SAAK,oBAAoB;AAIzB,SAAK,eAAe;AAIpB,SAAK,oBAAoB;AAIzB,SAAK,qBAAqB;AAI1B,SAAK,eAAe;AAIpB,SAAK,iBAAiB;AAItB,SAAK,aAAa;AAIlB,SAAK,eAAe;AAIpB,SAAK,mBAAmB,IAAI,aAAa;AAIzC,SAAK,eAAe,IAAI,aAAa;AAIrC,SAAK,oBAAoB,IAAI,aAAa;AAK1C,SAAK,mBAAmB,IAAI,aAAa;AAIzC,SAAK,qBAAqB,IAAI,aAAa;AAI3C,SAAK,qBAAqB,oBAAI,IAAI;AAIlC,SAAK,mBAAmB,oBAAI,IAAI;AAIhC,SAAK,uBAAuB;AAAA,MAC1B,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAIA,SAAK,aAAa;AAIlB,SAAK,mBAAmB;AAIxB,SAAK,aAAa,OAAO,+BAA+B;AAIxD,SAAK,MAAM;AAIX,SAAK,2BAA2B;AAIhC,SAAK,qBAAqB;AAI1B,SAAK,cAAc;AAInB,SAAK,yBAAyB;AAI9B,SAAK,uBAAuB;AAI5B,SAAK,oBAAoB,CAAC,OAAO,WAAW,OAAO,MAAM,CAAC,IAAI,OAAO,MAAM,CAAC,EAAE,SAAS,CAAC,EAAE,KAAK,YAAY,IAAI;AAI/G,SAAK,YAAY,CAAC,OAAO,QAAQ,IAAI;AACrC,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACT,QAAI,KAAK,SAAS;AAChB,WAAK,sBAAsB,KAAK,QAAQ,UAAU,MAAM;AACtD,aAAK,WAAW;AAChB,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,SAAS;AACnB,UAAM,gBAAgB,QAAQ,YAAY,QAAQ,eAAe,QAAQ,eAAe,QAAQ,cAAc,QAAQ;AACtH,UAAM,cAAc,QAAQ,YAAY,QAAQ,gBAAgB,QAAQ,kBAAkB,QAAQ,cAAc,QAAQ,gBAAgB,QAAQ,gBAAgB,QAAQ,gBAAgB,QAAQ,gBAAgB,QAAQ,eAAe,QAAQ,eAAe,QAAQ,qBAAqB,QAAQ,UAAU,QAAQ,cAAc,QAAQ;AAC3U,QAAI,eAAe;AACjB,WAAK,cAAc;AAAA,IACrB;AACA,QAAI,QAAQ,QAAQ;AAClB,MAAAA,gBAAe,KAAK,MAAM;AAAA,IAC5B;AACA,QAAI,aAAa;AACf,WAAK,YAAY;AAAA,IACnB;AACA,QAAI,iBAAiB,aAAa;AAChC,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,YAAY;AAAA,IACvC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB;AAChB,SAAK,MAAM,OAAO,WAAW,eAAe,iBAAiB,KAAK,QAAQ,aAAa,EAAE,cAAc;AACvG,SAAK,IAAI,cAAc;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAIA,uBAAuB,iBAAiB,WAAW,aAAa;AAC9D,SAAK,iBAAiB,IAAI,UAAU,OAAO,WAAW;AACtD,SAAK,cAAc,iBAAiB,SAAS;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,WAAW,aAAa;AACxC,SAAK,iBAAiB,IAAI,UAAU,OAAO,WAAW;AACtD,UAAM,iBAAiB,oBAAI,IAAI;AAC/B,UAAM,aAAa,CAAC,GAAG,KAAK,MAAM;AAClC,SAAK,iBAAiB,QAAQ,CAAC,iBAAiB,UAAU;AACxD,YAAM,gBAAgB,KAAK,yBAAyB,OAAO,eAAe;AAC1E,YAAM,gBAAgB,kCACjB,QACA;AAEL,qBAAe,IAAI,eAAe,KAAK;AACvC,YAAM,aAAa,WAAW,QAAQ,KAAK;AAC3C,iBAAW,UAAU,IAAI;AAAA,IAC3B,CAAC;AACD,SAAK,sBAAsB,YAAY,gBAAgB,IAAI;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA,EAIA,qBAAqB,WAAW;AAC9B,SAAK,OAAO,KAAK,YAAY,KAAK,MAAM;AACxC,UAAM,kBAAkB,KAAK,iBAAiB,IAAI,UAAU,KAAK;AACjE,QAAI,iBAAiB;AACnB,WAAK,iBAAiB,OAAO,UAAU,KAAK;AAC5C,YAAM,gBAAgB,KAAK,yBAAyB,UAAU,OAAO,eAAe;AACpF,WAAK,kBAAkB,KAAK;AAAA,QAC1B,UAAU,cAAc;AAAA,QACxB,QAAQ,cAAc;AAAA,QACtB,OAAO,UAAU;AAAA,QACjB,MAAM,mCAAmC;AAAA,MAC3C,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,yBAAyB,uBAAuB,aAAa,aAAa;AACxE,SAAK,mBAAmB,IAAI,aAAa;AAAA,MACvC,gBAAgB,YAAY;AAAA,MAC5B,cAAc,YAAY;AAAA,MAC1B,MAAM,OAAO,YAAY,MAAM,SAAS,cAAc,SAAS;AAAA,IACjE,CAAC;AACD,SAAK,cAAc,uBAAuB,aAAa,KAAK,kBAAkB,qBAAqB,CAAC;AAAA,EACtG;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB,aAAa,aAAa,UAAU;AACtD,UAAM,gBAAgB,KAAK,mBAAmB,IAAI,WAAW;AAC7D,UAAM,WAAW,KAAK,MAAM,KAAK;AACjC,QAAI,OAAO,YAAY,MAAM,SAAS,aAAa;AACjD,YAAM,OAAO,KAAK,MAAM,CAAC,YAAY,MAAM,OAAO,QAAQ,IAAI;AAC9D,kBAAY,SAAS,cAAc,iBAAiB;AACpD,kBAAY,OAAO,cAAc,eAAe;AAAA,IAClD,WAAW,OAAO,YAAY,MAAM,UAAU,aAAa;AACzD,YAAM,OAAO,KAAK,MAAM,CAAC,YAAY,MAAM,QAAQ,QAAQ,IAAI;AAC/D,kBAAY,OAAO,cAAc,eAAe;AAAA,IAClD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,uBAAuB,aAAa;AAClC,UAAM,gBAAgB,KAAK,mBAAmB,IAAI,WAAW;AAC7D,QAAI,eAAe;AACjB,YAAM,iCAAiC,cAAc,SAAS;AAC9D,UAAI;AACJ,UAAI,gCAAgC;AAClC,mBAAW,YAAY,SAAS,cAAc;AAAA,MAChD,OAAO;AACL,mBAAW,YAAY,OAAO,cAAc;AAAA,MAC9C;AACA,kBAAY,SAAS,cAAc;AACnC,kBAAY,OAAO,cAAc;AACjC,YAAM,WAAW,KAAK,2BAA2B,YAAY,OAAO,UAAU,8BAA8B;AAC5G,WAAK,kBAAkB,KAAK;AAAA,QAC1B,UAAU,SAAS;AAAA,QACnB,QAAQ,SAAS;AAAA,QACjB,OAAO,YAAY;AAAA,QACnB,MAAM,mCAAmC;AAAA,MAC3C,CAAC;AACD,WAAK,mBAAmB,OAAO,WAAW;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,mBAAmB;AACnC,WAAO,KAAK,MAAM,kBAAkB,cAAc,KAAK,KAAK,MAAM;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,MAAM;AAClB,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,WAAW,MAAM,QAAQ;AACpC,QAAI,uBAAuB,WAAW,MAAM,QAAQ,KAAK,UAAU,KAAK,KAAK,kBAAkB,QAAQ,MAAM,KAAK,QAAQ,MAAM,CAAC,KAAK,qBAAqB,UAAU,SAAS,UAAU,KAAK,mBAAmB;AAC9M,WAAK,kBAAkB,KAAK;AAAA,QAC1B,MAAM,mCAAmC;AAAA,QACzC,OAAO,UAAU,SAAS;AAAA,QAC1B,UAAU;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,MAAM;AACd,SAAK,qBAAqB,IAAI;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,MAAM;AACd,SAAK,qBAAqB,IAAI;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,wBAAwB,cAAc,OAAO,MAAM;AAC7D,SAAK,iBAAiB,KAAK,kBAAkB,sBAAsB;AACnE,UAAM,aAAa,IAAI,mBAAmB,wBAAwB,YAAY;AAC9E,SAAK,eAAe,CAAC;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,IACF,MAAM;AACJ,YAAM,YAAY,KAAK,mBAAmB,SAAS,KAAK,KAAK,iBAAiB,SAAS,KAAK,WAAW,aAAa;AAAA,QAClH;AAAA,QACA;AAAA,QACA,mBAAmB,KAAK;AAAA,QACxB,kBAAkB,KAAK;AAAA,QACvB;AAAA,MACF,CAAC;AACD,UAAI,aAAa,KAAK,2BAA2B;AAC/C,cAAM,gBAAgB,KAAK,uBAAuB,OAAO;AAAA,UACvD;AAAA,UACA;AAAA,QACF,GAAG,KAAK,gBAAgB,IAAI;AAC5B,eAAO,KAAK,0BAA0B;AAAA,UACpC,MAAM,mCAAmC;AAAA,UACzC,OAAO,MAAM;AAAA,UACb,UAAU,cAAc;AAAA,UACxB,QAAQ,cAAc;AAAA,QACxB,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AACA,SAAK,aAAa;AAClB,SAAK,mBAAmB;AACxB,SAAK,mBAAmB;AACxB,SAAK,uBAAuB;AAAA,MAC1B,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AACA,QAAI,CAAC,KAAK,qBAAqB,MAAM;AACnC,WAAK,KAAK,YAAY,QAAQ,YAAU;AACtC,cAAM,cAAc,OAAO,OAAO,KAAK,iBAAe,YAAY,UAAU,MAAM,SAAS,gBAAgB,KAAK;AAEhH,YAAI,aAAa;AACf,sBAAY,QAAQ;AACpB,sBAAY,SAAS;AAAA,QACvB;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,UAAU,WAAW;AAC5B,UAAM,gBAAgB,KAAK,uBAAuB,UAAU,WAAW,KAAK,gBAAgB,IAAI;AAChG,UAAM,gBAAgB,SAAS;AAC/B,UAAM,gBAAgB,kCACjB,gBACA;AAEL,UAAM,aAAa,KAAK,OAAO,IAAI,WAAS;AAC1C,UAAI,UAAU,eAAe;AAC3B,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC;AACD,SAAK,sBAAsB,YAAY,oBAAI,IAAI,CAAC,CAAC,eAAe,aAAa,CAAC,CAAC,GAAG,KAAK,iBAAiB;AACxG,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AACpB,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,WAAW,cAAc,UAAU,OAAO,OAAO;AACzD,SAAK,OAAO,KAAK,YAAY,KAAK,MAAM;AACxC,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK,uBAAuB,WAAW,cAAc,UAAU,IAAI;AACvE,SAAK,KAAK,qBAAqB,KAAK,qBAAqB,OAAO,SAAS,QAAQ,IAAI,MAAM,sBAAsB,OAAO,KAAK,KAAK,KAAK,MAAM,GAAG;AAC9I,WAAK,mBAAmB,UAAU;AAClC,WAAK,kBAAkB,KAAK;AAAA,QAC1B,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,OAAO,UAAU;AAAA,QACjB,MAAM,mCAAmC;AAAA,QACzC,QAAQ,CAAC;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,SAAK,OAAO,KAAK,MAAM,kBAAkB;AAAA,MACvC,UAAU,KAAK;AAAA,MACf,cAAc,KAAK;AAAA,MACnB,UAAU,KAAK;AAAA,MACf,aAAa,KAAK;AAAA,OACf,kBAAkB,KAAK,aAAa,KAAK,UAAU,KAAK,cAAc,KAAK,aAAa,KAAK,UAAU,EAC3G;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,KAAK,YAAY,KAAK,MAAM;AAAA,EAC1C;AAAA,EACA,aAAa;AACX,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,QAAQ,KAAK,MAAM;AAC1B,WAAK,iBAAiB,KAAK;AAAA,QACzB,QAAQ,KAAK;AAAA,SACV,KAAK,KACT;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY,QAAQ;AAClB,WAAO,KAAK,MAAM,YAAY;AAAA,MAC5B;AAAA,MACA,UAAU,KAAK;AAAA,MACf,cAAc,KAAK;AAAA,MACnB,UAAU,KAAK;AAAA,MACf,WAAW,KAAK;AAAA,MAChB,0BAA0B;AAAA,MAC1B,cAAc,KAAK;AAAA,MACnB,cAAc,KAAK;AAAA,MACnB,UAAU;AAAA,QACR,MAAM,KAAK;AAAA,QACX,QAAQ,KAAK;AAAA,MACf;AAAA,MACA,QAAQ;AAAA,QACN,MAAM,KAAK;AAAA,QACX,QAAQ,KAAK;AAAA,MACf;AAAA,MACA,eAAe,KAAK;AAAA,MACpB,aAAa,KAAK;AAAA,MAClB,oBAAoB,KAAK;AAAA,OACtB,kBAAkB,KAAK,aAAa,KAAK,UAAU,KAAK,cAAc,KAAK,aAAa,KAAK,UAAU,EAC3G;AAAA,EACH;AAAA,EACA,uBAAuB,WAAW,cAAc,UAAU,MAAM;AAC9D,UAAM,cAAc,eAAe,aAAa,GAAG,QAAQ,IAAI,YAAY,KAAK,MAAM,KAAK;AAC3F,UAAM,eAAe,OAAO,gBAAgB,aAAa,GAAG,KAAK,cAAc,KAAK,mBAAmB,KAAK,eAAe,KAAK,YAAY,IAAI;AAChJ,UAAM,QAAQ,KAAK,YAAY,WAAW,sBAAsB,KAAK,aAAa,UAAU,MAAM,OAAO,aAAa,KAAK,WAAW,GAAG,YAAY;AACrJ,QAAI;AACJ,QAAI,UAAU,MAAM,KAAK;AACvB,YAAM,KAAK,YAAY,WAAW,sBAAsB,KAAK,aAAa,UAAU,MAAM,KAAK,aAAa,KAAK,WAAW,GAAG,YAAY;AAAA,IAC7I;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,sBAAsB,YAAY,gBAAgB,oBAAoB,MAAM;AAC1E,UAAM,eAAe,KAAK;AAC1B,QAAI,mBAAmB;AACrB,WAAK,OAAO,KAAK,YAAY,UAAU;AAAA,IACzC;AACA,UAAM,sBAAsB,WAAW,OAAO,WAAS,eAAe,IAAI,KAAK,CAAC;AAChF,SAAK,KAAK,YAAY,QAAQ,CAAC,QAAQ,gBAAgB;AACrD,mBAAa,YAAY,WAAW,EAAE,MAAM,QAAQ,CAAC,MAAM,cAAc;AACvE,aAAK,SAAS,QAAQ,CAAC,SAAS,iBAAiB;AAC/C,iBAAO,MAAM,SAAS,EAAE,SAAS,YAAY,EAAE,WAAW,QAAQ;AAAA,QACpE,CAAC;AAAA,MACH,CAAC;AACD,0BAAoB,QAAQ,mBAAiB;AAC3C,cAAM,gBAAgB,eAAe,IAAI,aAAa;AACtD,cAAM,sBAAsB,OAAO,OAAO,KAAK,iBAAe,YAAY,WAAW,oBAAoB,gBAAgB,cAAc;AACvI,YAAI,qBAAqB;AAEvB,8BAAoB,QAAQ;AAC5B,8BAAoB,WAAW,IAAI;AACnC,cAAI,CAAC,mBAAmB;AACtB,gCAAoB,SAAS;AAC7B,gCAAoB,QAAQ;AAAA,UAC9B;AAAA,QACF,OAAO;AAEL,gBAAM,QAAQ;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,iBAAiB;AAAA,YACjB,cAAc;AAAA,YACd,WAAW;AAAA,UACb;AACA,iBAAO,OAAO,KAAK,KAAK;AAAA,QAC1B;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,mBAAe,MAAM;AAAA,EACvB;AAAA,EACA,yBAAyB,eAAe,aAAa;AACnD,UAAM,gBAAgB;AAAA,MACpB,OAAO,cAAc;AAAA,MACrB,KAAK,mBAAmB,KAAK,aAAa,eAAe,KAAK,kBAAkB;AAAA,IAClF;AACA,UAGI,oBAFF;AAAA;AAAA,IAvuJN,IAyuJQ,IADC,4BACD,IADC;AAAA,MADH;AAAA;AAGF,UAAM,kBAAkB;AAAA,MACtB,OAAO,KAAK,YAAY,WAAW,cAAc,KAAK,KAAK,qBAAqB,EAAE;AAAA,MAClF,KAAK,mBAAmB,KAAK,aAAa,iBAAiB,KAAK,kBAAkB;AAAA,IACpF;AACA,UAAM,WAAW,KAAK,MAAM,KAAK;AACjC,QAAI,OAAO,YAAY,MAAM,SAAS,aAAa;AACjD,YAAM,WAAW,KAAK,MAAM,CAAC,YAAY,MAAM,OAAO,KAAK,cAAc,IAAI;AAC7E,YAAM,WAAW,sBAAsB,KAAK,aAAa,cAAc,OAAO,UAAU,KAAK,WAAW;AACxG,UAAI,WAAW,gBAAgB,OAAO;AACpC,sBAAc,QAAQ;AAAA,MACxB,OAAO;AACL,sBAAc,QAAQ,gBAAgB;AAAA,MACxC;AAAA,IACF,WAAW,OAAO,YAAY,MAAM,UAAU,aAAa;AACzD,YAAM,WAAW,KAAK,MAAM,CAAC,YAAY,MAAM,QAAQ,KAAK,cAAc,IAAI;AAC9E,YAAM,SAAS,sBAAsB,KAAK,aAAa,cAAc,KAAK,UAAU,KAAK,WAAW;AACpG,UAAI,SAAS,gBAAgB,KAAK;AAChC,sBAAc,MAAM;AAAA,MACtB,OAAO;AACL,sBAAc,MAAM,gBAAgB;AAAA,MACtC;AAAA,IACF;AACA,QAAI,OAAO,YAAY,MAAM,QAAQ,aAAa;AAChD,YAAM,eAAe,gBAAgB,YAAY,MAAM,KAAK,KAAK,cAAc,KAAK,mBAAmB,KAAK,eAAe,KAAK,YAAY;AAC5I,YAAM,WAAW,KAAK,YAAY,WAAW,cAAc,OAAO,YAAY;AAC9E,UAAI,WAAW,gBAAgB,OAAO;AACpC,sBAAc,QAAQ;AAAA,MACxB,OAAO;AACL,sBAAc,QAAQ,gBAAgB;AAAA,MACxC;AAAA,IACF,WAAW,OAAO,YAAY,MAAM,WAAW,aAAa;AAC1D,YAAM,eAAe,gBAAgB,YAAY,MAAM,QAAQ,KAAK,cAAc,KAAK,mBAAmB,KAAK,eAAe,KAAK,YAAY;AAC/I,YAAM,SAAS,KAAK,YAAY,WAAW,cAAc,KAAK,YAAY;AAC1E,UAAI,SAAS,gBAAgB,KAAK;AAChC,sBAAc,MAAM;AAAA,MACtB,OAAO;AACL,sBAAc,MAAM,gBAAgB;AAAA,MACtC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc,iBAAiB,OAAO,UAAU;AAC9C,SAAK,iBAAiB,KAAK,kBAAkB,eAAe;AAC5D,UAAM,eAAe,IAAI,qBAAqB,iBAAiB,UAAU,KAAK,GAAG;AACjF,SAAK,iBAAiB,CAAC;AAAA,MACrB;AAAA,MACA;AAAA,IACF,MAAM;AACJ,YAAM,mBAAmB,aAAa,eAAe;AAAA,QACnD,WAAW,mBACN;AAAA,QAEL;AAAA,MACF,CAAC;AACD,UAAI,oBAAoB,KAAK,2BAA2B;AACtD,YAAI;AACJ,YAAI,CAAC,UAAU;AACb,0BAAgB,KAAK,yBAAyB,MAAM,OAAO;AAAA,YACzD;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,gBAAM,WAAW,KAAK,MAAM,KAAK;AACjC,cAAI,OAAO,MAAM,SAAS,aAAa;AACrC,kBAAM,OAAO,KAAK,MAAM,CAAC,MAAM,OAAO,QAAQ,IAAI;AAClD,4BAAgB,KAAK,2BAA2B,MAAM,OAAO,MAAM,CAAC,KAAK,GAAG;AAAA,UAC9E,OAAO;AACL,kBAAM,OAAO,KAAK,MAAM,CAAC,MAAM,QAAQ,QAAQ,IAAI;AACnD,4BAAgB,KAAK,2BAA2B,MAAM,OAAO,MAAM,KAAK,GAAG;AAAA,UAC7E;AAAA,QACF;AACA,eAAO,KAAK,0BAA0B;AAAA,UACpC,MAAM,mCAAmC;AAAA,UACzC,OAAO,MAAM;AAAA,UACb,UAAU,cAAc;AAAA,UACxB,QAAQ,cAAc;AAAA,QACxB,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AACA,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,2BAA2B,OAAO,UAAU,aAAa;AACvD,QAAI,QAAQ,MAAM;AAClB,QAAI,MAAM,MAAM,OAAO,MAAM;AAC7B,QAAI,aAAa;AACf,cAAQ,sBAAsB,KAAK,aAAa,OAAO,UAAU,KAAK,WAAW;AAAA,IACnF,OAAO;AACL,YAAM,sBAAsB,KAAK,aAAa,KAAK,UAAU,KAAK,WAAW;AAAA,IAC/E;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,0BAA0B,OAAO,SAAS,kCAAkC,mBAAmB;AAC7F,SAAO,KAAK,qBAAqB,2BAA8B,kBAAqB,iBAAiB,GAAM,kBAAkB,aAAa,GAAM,kBAAkB,SAAS,GAAM,kBAAkB,WAAW,GAAM,kBAAqB,UAAU,CAAC;AACtP;AACA,0BAA0B,OAAyB,kBAAkB;AAAA,EACnE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,wBAAwB,CAAC;AAAA,EACtC,QAAQ;AAAA,IACN,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,cAAc;AAAA,IACd,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,IACtB,WAAW;AAAA,IACX,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,cAAc;AAAA,IACd,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,qBAAqB;AAAA,IACrB,eAAe;AAAA,IACf,2BAA2B;AAAA,IAC3B,YAAY;AAAA,IACZ,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,eAAe;AAAA,EACjB;AAAA,EACA,SAAS;AAAA,IACP,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,EACtB;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,oBAAoB;AAAA,EAClC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,cAAc,EAAE,GAAG,CAAC,yBAAyB,EAAE,GAAG,CAAC,qBAAqB,EAAE,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,qBAAqB,EAAE,GAAG,CAAC,QAAQ,QAAQ,GAAG,eAAe,GAAG,CAAC,GAAG,oBAAoB,gBAAgB,aAAa,QAAQ,UAAU,gBAAgB,GAAG,CAAC,SAAS,sBAAsB,gBAAgB,IAAI,GAAG,aAAa,aAAa,GAAG,MAAM,GAAG,CAAC,gBAAgB,IAAI,GAAG,mBAAmB,GAAG,aAAa,WAAW,GAAG,CAAC,SAAS,yBAAyB,GAAG,MAAM,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,SAAS,kBAAkB,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,gBAAgB,IAAI,GAAG,sBAAsB,GAAG,aAAa,WAAW,GAAG,CAAC,GAAG,uBAAuB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,SAAS,kBAAkB,gBAAgB,IAAI,iBAAiB,iBAAiB,GAAG,QAAQ,aAAa,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,SAAS,kBAAkB,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,gBAAgB,IAAI,iBAAiB,iBAAiB,GAAG,kBAAkB,GAAG,QAAQ,WAAW,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC,SAAS,uBAAuB,gBAAgB,IAAI,gBAAgB,IAAI,mBAAmB,mBAAmB,GAAG,iBAAiB,0BAA0B,wBAAwB,WAAW,SAAS,cAAc,eAAe,iBAAiB,kBAAkB,kBAAkB,YAAY,YAAY,gBAAgB,gBAAgB,uBAAuB,eAAe,YAAY,aAAa,aAAa,YAAY,WAAW,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,gBAAgB,IAAI,gBAAgB,IAAI,mBAAmB,mBAAmB,GAAG,uBAAuB,GAAG,eAAe,YAAY,aAAa,aAAa,YAAY,WAAW,WAAW,iBAAiB,kBAAkB,kBAAkB,YAAY,YAAY,gBAAgB,gBAAgB,qBAAqB,GAAG,CAAC,SAAS,oDAAoD,mBAAmB,IAAI,GAAG,eAAe,GAAG,MAAM,GAAG,CAAC,GAAG,gBAAgB,UAAU,aAAa,oBAAoB,mBAAmB,uBAAuB,gBAAgB,kBAAkB,sBAAsB,wBAAwB,YAAY,GAAG,CAAC,SAAS,iDAAiD,mBAAmB,IAAI,GAAG,eAAe,GAAG,MAAM,GAAG,CAAC,mBAAmB,IAAI,GAAG,qBAAqB,kCAAkC,GAAG,aAAa,GAAG,CAAC,mBAAmB,IAAI,GAAG,qBAAqB,+BAA+B,GAAG,aAAa,GAAG,CAAC,SAAS,YAAY,GAAG,gBAAgB,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,UAAU,WAAW,iBAAiB,UAAU,kBAAkB,eAAe,cAAc,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,GAAG,WAAW,iBAAiB,UAAU,kBAAkB,eAAe,YAAY,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC,GAAG,cAAc,gBAAgB,kBAAkB,cAAc,gBAAgB,gBAAgB,gBAAgB,qBAAqB,gBAAgB,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,SAAS,uBAAuB,gBAAgB,IAAI,gBAAgB,IAAI,mBAAmB,mBAAmB,GAAG,iBAAiB,yBAAyB,uBAAuB,WAAW,UAAU,OAAO,UAAU,QAAQ,SAAS,iBAAiB,kBAAkB,kBAAkB,wBAAwB,YAAY,YAAY,gBAAgB,uBAAuB,oBAAoB,wBAAwB,gBAAgB,eAAe,YAAY,aAAa,aAAa,YAAY,WAAW,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,gBAAgB,IAAI,gBAAgB,IAAI,mBAAmB,mBAAmB,GAAG,uBAAuB,GAAG,eAAe,YAAY,aAAa,aAAa,YAAY,WAAW,WAAW,UAAU,iBAAiB,kBAAkB,kBAAkB,wBAAwB,YAAY,YAAY,gBAAgB,uBAAuB,oBAAoB,wBAAwB,cAAc,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,gBAAgB,UAAU,aAAa,oBAAoB,mBAAmB,uBAAuB,mBAAmB,gBAAgB,kBAAkB,sBAAsB,wBAAwB,UAAU,YAAY,GAAG,CAAC,gBAAgB,IAAI,mBAAmB,mBAAmB,GAAG,UAAU,WAAW,iBAAiB,UAAU,kBAAkB,cAAc,yBAAyB,iBAAiB,eAAe,YAAY,QAAQ,aAAa,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,gBAAgB,IAAI,mBAAmB,mBAAmB,GAAG,YAAY,QAAQ,aAAa,WAAW,iBAAiB,UAAU,kBAAkB,cAAc,yBAAyB,iBAAiB,aAAa,CAAC;AAAA,EAC3lJ,UAAU,SAAS,mCAAmC,IAAI,KAAK;AAC7D,QAAI,KAAK,GAAG;AACV,YAAM,MAAS,iBAAiB;AAChC,MAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,iCAAiC,CAAC;AACpE,MAAG,WAAW,oBAAoB,SAAS,6FAA6F,QAAQ;AAC9I,QAAG,cAAc,GAAG;AACpB,eAAU,YAAY,IAAI,iBAAiB,KAAK,MAAM,CAAC;AAAA,MACzD,CAAC,EAAE,gBAAgB,SAAS,yFAAyF,QAAQ;AAC3H,QAAG,cAAc,GAAG;AACpB,eAAU,YAAY,IAAI,aAAa;AAAA,UACrC,UAAU;AAAA,QACZ,GAAG,OAAO,UAAU,IAAI,CAAC;AAAA,MAC3B,CAAC,EAAE,aAAa,SAAS,sFAAsF,QAAQ;AACrH,QAAG,cAAc,GAAG;AACpB,eAAU,YAAY,IAAI,cAAc,OAAO,IAAI,CAAC;AAAA,MACtD,CAAC;AACD,MAAG,aAAa;AAChB,MAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,OAAO,CAAC;AACzE,MAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,MAAG,WAAW,aAAa,SAAS,8DAA8D;AAChG,QAAG,cAAc,GAAG;AACpB,eAAU,YAAY,IAAI,UAAU,MAAM,CAAC;AAAA,MAC7C,CAAC,EAAE,aAAa,SAAS,8DAA8D;AACrF,QAAG,cAAc,GAAG;AACpB,eAAU,YAAY,IAAI,UAAU,MAAM,CAAC;AAAA,MAC7C,CAAC;AACD,MAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,OAAO,CAAC;AACzE,MAAG,eAAe,GAAG,OAAO,IAAI,CAAC;AACjC,MAAG,WAAW,GAAG,0CAA0C,GAAG,IAAI,OAAO,EAAE;AAC3E,MAAG,aAAa,EAAE,EAAE;AAAA,IACtB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,UAAU;AACb,MAAG,WAAW,QAAQ,IAAI,IAAI,EAAE,UAAU,IAAI,MAAM,EAAE,kBAAkB,IAAI,cAAc;AAC1F,MAAG,UAAU;AACb,MAAG,WAAW,QAAQ,IAAI,KAAK,gBAAgB,SAAS,CAAC;AACzD,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,QAAQ,IAAI,KAAK,YAAY,SAAS,KAAK,IAAI,eAAe,CAAC;AAC7E,MAAG,UAAU;AACb,MAAG,YAAY,qBAAqB,IAAI,iBAAiB,OAAO,CAAC;AACjE,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,WAAW,IAAI,KAAK,WAAW,EAAE,gBAAgB,IAAI,iBAAiB;AAAA,IACtF;AAAA,EACF;AAAA,EACA,cAAc,CAAI,SAAY,SAAY,MAAS,kBAAqB,oBAAuB,uBAA0B,oBAAuB,oBAAoB,gBAAgB,iCAAiC,gCAAgC,sCAAsC,0CAA0C;AAAA,EACrU,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAkVZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,SAAS;AAAA,MAClB,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAyB;AAAC;AAC1B,mBAAmB,OAAO,SAAS,2BAA2B,mBAAmB;AAC/E,SAAO,KAAK,qBAAqB,oBAAoB;AACvD;AACA,mBAAmB,OAAyB,iBAAiB;AAAA,EAC3D,MAAM;AAAA,EACN,cAAc,CAAC,2BAA2B,iCAAiC,gCAAgC,sCAAsC,0CAA0C;AAAA,EAC3L,SAAS,CAAC,cAAc,iBAAiB,mBAAmB,oBAAoB;AAAA,EAChF,SAAS,CAAC,iBAAiB,mBAAmB,2BAA2B,iCAAiC,gCAAgC,sCAAsC,0CAA0C;AAC5N,CAAC;AACD,mBAAmB,OAAyB,iBAAiB;AAAA,EAC3D,SAAS,CAAC,cAAc,iBAAiB,mBAAmB,sBAAsB,iBAAiB,iBAAiB;AACtH,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,iBAAiB,mBAAmB,oBAAoB;AAAA,MAChF,cAAc,CAAC,2BAA2B,iCAAiC,gCAAgC,sCAAsC,0CAA0C;AAAA,MAC3L,SAAS,CAAC,iBAAiB,mBAAmB,2BAA2B,iCAAiC,gCAAgC,sCAAsC,0CAA0C;AAAA,IAC5N,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAYH,IAAM,2BAAN,MAA+B;AAAA,EAC7B,cAAc;AAKZ,SAAK,SAAS,CAAC;AAIf,SAAK,eAAe;AAIpB,SAAK,oBAAoB;AAIzB,SAAK,qBAAqB;AAI1B,SAAK,eAAe;AAIpB,SAAK,iBAAiB;AAItB,SAAK,aAAa;AAIlB,SAAK,eAAe;AAIpB,SAAK,mBAAmB;AAIxB,SAAK,sBAAsB;AAK3B,SAAK,eAAe;AAIpB,SAAK,oBAAoB;AAIzB,SAAK,eAAe,IAAI,aAAa;AAIrC,SAAK,qBAAqB,IAAI,aAAa;AAI3C,SAAK,oBAAoB,IAAI,aAAa;AAK1C,SAAK,mBAAmB,IAAI,aAAa;AAAA,EAC3C;AACF;AACA,yBAAyB,OAAO,SAAS,iCAAiC,mBAAmB;AAC3F,SAAO,KAAK,qBAAqB,0BAA0B;AAC7D;AACA,yBAAyB,OAAyB,kBAAkB;AAAA,EAClE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,EACrC,QAAQ;AAAA,IACN,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,cAAc;AAAA,IACd,qBAAqB;AAAA,IACrB,eAAe;AAAA,IACf,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,IACtB,mBAAmB;AAAA,IACnB,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,eAAe;AAAA,EACjB;AAAA,EACA,SAAS;AAAA,IACP,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,EACpB;AAAA,EACA,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,gBAAgB,GAAG,gBAAgB,sBAAsB,qBAAqB,oBAAoB,cAAc,YAAY,UAAU,gBAAgB,gBAAgB,qBAAqB,sBAAsB,gBAAgB,kBAAkB,cAAc,gBAAgB,WAAW,UAAU,iBAAiB,oBAAoB,mBAAmB,uBAAuB,gBAAgB,iBAAiB,uBAAuB,iBAAiB,sBAAsB,wBAAwB,qBAAqB,6BAA6B,6BAA6B,2BAA2B,CAAC;AAAA,EACpmB,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,GAAG,0BAA0B,CAAC;AAChD,MAAG,WAAW,gBAAgB,SAAS,iFAAiF,QAAQ;AAC9H,eAAO,IAAI,aAAa,KAAK,MAAM;AAAA,MACrC,CAAC,EAAE,sBAAsB,SAAS,uFAAuF,QAAQ;AAC/H,eAAO,IAAI,mBAAmB,KAAK,MAAM;AAAA,MAC3C,CAAC,EAAE,qBAAqB,SAAS,sFAAsF,QAAQ;AAC7H,eAAO,IAAI,kBAAkB,KAAK,MAAM;AAAA,MAC1C,CAAC,EAAE,oBAAoB,SAAS,qFAAqF,QAAQ;AAC3H,eAAO,IAAI,iBAAiB,KAAK,MAAM;AAAA,MACzC,CAAC;AACD,MAAG,aAAa;AAAA,IAClB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,cAAc,CAAC,EAAE,YAAY,IAAI,QAAQ,EAAE,UAAU,IAAI,MAAM,EAAE,gBAAgB,IAAI,YAAY,EAAE,gBAAgB,IAAI,YAAY,EAAE,qBAAqB,IAAI,iBAAiB,EAAE,sBAAsB,IAAI,kBAAkB,EAAE,gBAAgB,IAAI,YAAY,EAAE,kBAAkB,IAAI,cAAc,EAAE,cAAc,IAAI,UAAU,EAAE,gBAAgB,IAAI,YAAY,EAAE,WAAW,IAAI,OAAO,EAAE,UAAU,IAAI,MAAM,EAAE,iBAAiB,IAAI,aAAa,EAAE,oBAAoB,IAAI,gBAAgB,EAAE,mBAAmB,IAAI,eAAe,EAAE,uBAAuB,IAAI,mBAAmB,EAAE,gBAAgB,IAAI,YAAY,EAAE,iBAAiB,IAAI,aAAa,EAAE,uBAAuB,IAAI,mBAAmB,EAAE,iBAAiB,IAAI,aAAa,EAAE,sBAAsB,IAAI,kBAAkB,EAAE,wBAAwB,IAAI,oBAAoB,EAAE,qBAAqB,IAAI,iBAAiB,EAAE,6BAA6B,IAAI,yBAAyB,EAAE,6BAA6B,IAAI,yBAAyB,EAAE,6BAA6B,IAAI,yBAAyB;AAAA,IACniC;AAAA,EACF;AAAA,EACA,cAAc,CAAC,yBAAyB;AAAA,EACxC,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAoCZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oBAAN,MAAwB;AAAC;AACzB,kBAAkB,OAAO,SAAS,0BAA0B,mBAAmB;AAC7E,SAAO,KAAK,qBAAqB,mBAAmB;AACtD;AACA,kBAAkB,OAAyB,iBAAiB;AAAA,EAC1D,MAAM;AAAA,EACN,cAAc,CAAC,wBAAwB;AAAA,EACvC,SAAS,CAAC,cAAc,sBAAsB,kBAAkB;AAAA,EAChE,SAAS,CAAC,wBAAwB;AACpC,CAAC;AACD,kBAAkB,OAAyB,iBAAiB;AAAA,EAC1D,SAAS,CAAC,cAAc,sBAAsB,kBAAkB;AAClE,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,sBAAsB,kBAAkB;AAAA,MAChE,cAAc,CAAC,wBAAwB;AAAA,MACvC,SAAS,CAAC,wBAAwB;AAAA,IACpC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAiBH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,QAAQ,aAAa,SAAS,CAAC,GAAG;AACvC,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,aAAa,OAAO,uBAAuB,6BAA6B,OAAO,iBAAiB,uBAAuB,OAAO,SAAS,eAAe,OAAO,QAAQ,YAAY;AAAA,IAC/L;AAAA,EACF;AACF;AACA,eAAe,OAAO,SAAS,uBAAuB,mBAAmB;AACvE,SAAO,KAAK,qBAAqB,gBAAgB;AACnD;AACA,eAAe,OAAyB,iBAAiB;AAAA,EACvD,MAAM;AAAA,EACN,SAAS,CAAC,sBAAsB,qBAAqB,oBAAoB,iBAAiB;AAAA,EAC1F,SAAS,CAAC,sBAAsB,qBAAqB,oBAAoB,iBAAiB;AAC5F,CAAC;AACD,eAAe,OAAyB,iBAAiB;AAAA,EACvD,SAAS,CAAC,sBAAsB,qBAAqB,oBAAoB,mBAAmB,sBAAsB,qBAAqB,oBAAoB,iBAAiB;AAC9K,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,sBAAsB,qBAAqB,oBAAoB,iBAAiB;AAAA,MAC1F,SAAS,CAAC,sBAAsB,qBAAqB,oBAAoB,iBAAiB;AAAA,IAC5F,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["Positioning", "style", "DAYS_OF_WEEK", "i", "EventValidationErrorMessage", "document", "CalendarView", "validateEvents", "MINUTES_IN_HOUR", "CalendarEventTimesChangedEventType"]}