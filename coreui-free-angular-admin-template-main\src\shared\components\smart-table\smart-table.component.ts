import { Component, Input, Output, EventEmitter, SimpleChanges, ViewChild, ElementRef } from '@angular/core';
import { TableColumn, TableConfig, StatusConfig, ActionButton } from '../../models/table.models';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { FrenchDatePipe } from '../../pipes/french-date.pipe';
import { IconModule } from '@coreui/icons-angular';
import { SpinnerComponent } from '../spinner/spinner.component';
import { CommentModalComponent } from '../comment-modal/comment-modal.component';
import { CommentService, SessionStorageService } from '../../../services';
import { ToastrService } from 'ngx-toastr';
import { firstValueFrom } from 'rxjs';

@Component({
  standalone: true,
  selector: 'app-smart-table',
  imports: [CommonModule, FormsModule, FrenchDatePipe, IconModule,SpinnerComponent,CommentModalComponent],
  templateUrl: './smart-table.component.html',
  styleUrls: ['./smart-table.component.scss']
})
export class SmartTableComponent {
  @Input() columns: TableColumn[] = [];
  @Input() totalRows: number = 0;
  @Input() actionButtons: ActionButton[] = [];
  @Input() showActionsColumn: boolean = true;
  
  private _data: any[] = [];
  @Input() 
  get data(): any[] {
    return this._data;
  }
  set data(value: any[]) {
    this._data = value || [];
    this.applyFilter();
  }

  @Input() isLoading: boolean = false;
  
  @Output() selectionChange = new EventEmitter<any[]>();
  @Output() pageChange = new EventEmitter<number>();
  @Output() pageSizeChange = new EventEmitter<number>();

  @ViewChild('filterInput') filterInput!: ElementRef;

  showCommentModal = false;
  currentRowId: number | null = null;
  commentList: any[] = [];
  
  filteredData: any[] = [];
  paginatedData: any[] = [];
  selectedRows = new Set<any>();
  currentPage = 1;
  pageSize :number= 10;
  totalPages = 1;
  sortColumn = '';
  sortDirection: 'asc' | 'desc' = 'asc';
  filterText = '';

  private _config: Partial<TableConfig> = {};
  @Input() 
  set config(value: Partial<TableConfig>) {
    this._config = value || {};
    this.pageSize = this.getConfigValue('pageSize') || 10;
    this.currentPage = this.getConfigValue('currentPage') || 1;
  }

  get config(): TableConfig {
    return {
      ...this.getDefaultTableConfig(),
      ...this._config
    };
  }

  private getConfigValue<K extends keyof TableConfig>(key: K): TableConfig[K] {
    return this._config[key] ?? this.getDefaultTableConfig()[key];
  }

  constructor(private commentService: CommentService,
    private sessionStorageService: SessionStorageService,
    private toastr: ToastrService
  ) {}

  get displayedColumns(): string[] {
    let cols = this.columns.map(col => col.name);
    if (this.config.selectable) {
      cols = ['select', ...cols];
    }
    if (this.showActionsColumn && this.visibleActionButtons.length > 0) {
      return [...cols, 'actions'];
    }
    return cols;
  }

  get visibleActionButtons(): ActionButton[] {
    return this.actionButtons || [];
  }

  get hasAnyActions(): boolean {
    return this.visibleActionButtons.length > 0;
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['config']) {
      this.pageSize = this.getConfigValue('pageSize') || 10;
      this.currentPage = this.getConfigValue('currentPage') || 1;
    }
    if (changes['data'] || changes['totalRows']) {
      this.updatePagination();
    }
  }

  getDefaultTableConfig(): TableConfig {
  return {
    pageSizeOptions: [5, 10, 25],
    pageSize: 10,
    currentPage: 1,
    selectable: false,
    multiSelect: false,
    emptyMessage: 'No data available',
    striped: true,
    hover: true,
    bordered: false,
    small: false,
    showFilter: true,
    commentable: false
  };
}

  applyFilter() {
  if (!this.filterText) {
    this.filteredData = [...this._data];
  } else {
    const filterValue = this.filterText.toLowerCase();
    this.filteredData = this._data.filter(item => {
      return this.columns.some(column => {
        if (column.filterable === false) return false;

        const value = this.getNestedProperty(item, column.name);
        if (value === null || value === undefined) return false;

        // Handle different data types
        if (column.dataType === 'date') {
          const frenchDatePipe = new FrenchDatePipe();
          const formattedDate = frenchDatePipe.transform(value).toLowerCase();
          return formattedDate.includes(filterValue);
        } else if (column.dataType === 'boolean') {
          const boolText = value ? 'true' : 'false';
          return boolText.includes(filterValue);
        } else {
          return String(value).toLowerCase().includes(filterValue);
        }
      });
    });
  }

  // Apply sorting after filtering
  if (this.sortColumn) {
    this.sortData();
  }
  this.updatePagination();
}

  clearFilter() {
    this.filterText = '';
    this.applyFilter();
    this.filterInput.nativeElement.focus();
    }

  updatePagination() {
    // Always calculate totalPages based on filteredData
    if (this.totalRows !== 0) {
      this.totalPages = Math.ceil(this.totalRows / this.pageSize) || 1;
    }
    else {
      this.totalPages = Math.ceil(this.filteredData.length / this.pageSize) || 1;
    }
    
    // If using server-side pagination (totalRows is provided and greater than 0)
    if (this.totalRows > 0 && this.filterText === '') {
      // For server-side, only use filteredData for local filtering
      // but show data as provided by parent when no filter is applied
      this.paginatedData = this._data;
    } else {
      // Client-side pagination or filtered server-side data
      const startIndex = (this.currentPage - 1) * this.pageSize;
      this.paginatedData = this.filteredData.slice(startIndex, startIndex + this.pageSize);
    }
  }


  onPageChange(page: number) {
    if (page < 1 || page > this.totalPages) return;
    this.currentPage = page;
    let newPage = {currentPage : this.currentPage}
    if (this.pageChange.observed) {
      // If parent is listening, emit the event
      this.pageChange.emit(newPage.currentPage);
    } else {
      // Otherwise, handle pagination internally
      this.updatePagination();
    }
  }

  onPageSizeChange(size: number) {
    this.pageSize = size; 
    if (this.pageSizeChange.observed) {
      // If parent is listening, emit the event
      this.pageSizeChange.emit(this.pageSize);
    } else {
      // Otherwise, handle pagination internally
      this.updatePagination();
    }
  }
  sortData() {
    if (!this.sortColumn) return;
  
    const column = this.columns.find(c => c.name === this.sortColumn);
    if (!column) return;
  
    this.filteredData.sort((a, b) => {
      let valueA = this.getNestedProperty(a, this.sortColumn);
      let valueB = this.getNestedProperty(b, this.sortColumn);
  
      // Handle different data types
      if (column.dataType === 'date') {
        valueA = new Date(valueA).getTime();
        valueB = new Date(valueB).getTime();
      } else if (column.dataType === 'number') {
        valueA = Number(valueA);
        valueB = Number(valueB);
      } else {
        // Default to string comparison
        valueA = String(valueA).toLowerCase();
        valueB = String(valueB).toLowerCase();
      }
  
      if (valueA < valueB) {
        return this.sortDirection === 'asc' ? -1 : 1;
      }
      if (valueA > valueB) {
        return this.sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }

  onSort(column: string) {
    const col = this.columns.find(c => c.name === column);
    if (!col?.sortable) return;
  
    if (this.sortColumn === column) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortColumn = column;
      this.sortDirection = 'asc';
    }
  
    this.sortData();
    this.updatePagination();
  }

  getColumnClass(column: TableColumn): string {
    const classes: string[] = [];
    if (column.dataType) {
      classes.push(`${column.dataType}-column`);
    }
    if (column.name === 'active') {
      classes.push('active-column');
    }
    return classes.join(' ');
  }

  getStatusConfig(column: TableColumn, value: any): StatusConfig | null {
    if (column.dataType !== 'status' || !column.statusConfig) {
      return null;
    }
    return column.statusConfig.find(config => config.value === value) || null;
  }

  getStatusDisplay(column: TableColumn, value: any): { text: string; config: StatusConfig | null } {
    const config = this.getStatusConfig(column, value);
    return {
      text: config?.displayText || String(value),
      config: config
    };
  }

  getSortIcon(column: string): string {
    if (this.sortColumn !== column) return 'cilSwapVertical';
    return this.sortDirection === 'asc' ? 'cilSortAscending' : 'cilSortDescending';
  }

  getIconName(iconClass: string): string {
    // Convert old icon class names to CoreUI icon names
    const iconMap: { [key: string]: string } = {
      'cil-pencil': 'cilPencil',
      'cil-trash': 'cilTrash',
      'cil-info': 'cilInfo',
      'cil-eye': 'cilEye',
      'cil-check': 'cilCheck',
      'cil-x': 'cilX',
      'cil-plus': 'cilPlus',
      'cil-minus': 'cilMinus',
      'cil-settings': 'cilSettings',
      'cil-user': 'cilUser',
      'cil-envelope': 'cilEnvelopeClosed',
      'cil-phone': 'cilPhone',
      'cil-location-pin': 'cilLocationPin',
      'cil-calendar': 'cilCalendar',
      'cil-clock': 'cilClock',
      'cil-star': 'cilStar',
      'cil-heart': 'cilHeart',
      'cil-thumb-up': 'cilThumbUp',
      'cil-thumb-down': 'cilThumbDown',
      'cil-share': 'cilShare',
      'cil-download': 'cilCloudDownload',
      'cil-upload': 'cilCloudUpload',
      'cil-print': 'cilPrint',
      'cil-copy': 'cilCopy',
      'cil-cut': 'cilCut',
      'cil-paste': 'cilDescription'
    };

    return iconMap[iconClass] || iconClass;
  }

  getEndIndex(): number {
    return Math.min(this.currentPage * this.pageSize, this.filteredData.length);
  }

  onActionButtonClick(actionButton: ActionButton, row: any): void {
    if (actionButton.isCommentAction) {
      this.handleCommentAction(row);
    } else if (actionButton.callback) {
      actionButton.callback(row);
    }
  }

  shouldShowActionButton(actionButton: ActionButton, row: any): boolean {
    if (actionButton.condition && !actionButton.condition(row)) {
      return false;
    }
    return true;
  }

  isActionButtonDisabled(actionButton: ActionButton, row: any): boolean {
    if (actionButton.disabled) {
      return actionButton.disabled(row);
    }
    return false;
  }

  toggleRowSelection(row: any) {
    if (this.config.multiSelect) {
      if (this.selectedRows.has(row)) {
        this.selectedRows.delete(row);
      } else {
        this.selectedRows.add(row);
      }
    } else {
      this.selectedRows.clear();
      this.selectedRows.add(row);
    }
    this.selectionChange.emit(Array.from(this.selectedRows));
  }

  isSelected(row: any): boolean {
    return this.selectedRows.has(row);
  }

  masterToggle() {
    if (this.isAllSelected()) {
      this.selectedRows.clear();
    } else {
      this.paginatedData.forEach(row => this.selectedRows.add(row));
    }
    this.selectionChange.emit(Array.from(this.selectedRows));
  }

  isAllSelected(): boolean {
    return this.paginatedData.length > 0 && 
           this.selectedRows.size === this.paginatedData.length;
  }

  getPages(): number[] {
    const pages: number[] = [];
    const maxVisiblePages = 5;
    
    if (this.totalPages <= maxVisiblePages) {
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
      let endPage = startPage + maxVisiblePages - 1;
      
      if (endPage > this.totalPages) {
        endPage = this.totalPages;
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }
      
      if (startPage > 1) {
        pages.push(1);
        if (startPage > 2) {
          pages.push(-1);
        }
      }
      
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
      
      if (endPage < this.totalPages) {
        if (endPage < this.totalPages - 1) {
          pages.push(-1);
        }
        pages.push(this.totalPages);
      }
    }
    
    return pages;
  }

  async handleCommentAction(row: any) {
    this.currentRowId = row.id;
    try {
      if (this.config.commentService) {
        this.commentList = await this.config.commentService.findCommentsByLigne(row.id);
        this.showCommentModal = true;
      }
      else if (this.commentService) {
        const { firstValueFrom } = await import('rxjs');
        this.commentList = await firstValueFrom(this.commentService.findCommentsByLine(row.id));
        this.showCommentModal = true;
      }
    } catch (error) {
      console.error('Error fetching comments:', error);
    }
  }

  async onSaveComment(commentData: {id: any, comment: any}) {
    try {
      const data = {
      id_ligne: commentData.id,
      value: commentData.comment,
      id_author: this.sessionStorageService.getSessionValue("iduser")
    }

    // First save the comment
    await firstValueFrom(this.commentService.addComment(data));
    this.toastr.success('Commentaire ajouté avec succès !');
    
    // Then refresh the comments list
    if (this.config.commentService) {
      this.commentList = await this.config.commentService.findCommentsByLigne(commentData.id);
    } else {
      this.commentList = await firstValueFrom(this.commentService.findCommentsByLine(commentData.id));
      
    }
   } catch (error) {
      console.error('Error saving comment:', error);
    }
  }

  onCloseCommentModal() {
    this.showCommentModal = false;
    this.currentRowId = null;
    this.commentList = [];
  }

  getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((o, p) => o?.[p], obj);
  }
}