import { CommonModule, NgTemplateOutlet, DatePipe } from '@angular/common';
import { Component, computed, inject, input, OnInit, OnDestroy } from '@angular/core';
import { RouterLink, RouterLinkActive, Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

import {
  AvatarComponent,
  BadgeComponent,
  BreadcrumbRouterComponent,
  ColorModeService,
  ContainerComponent,
  DropdownComponent,
  DropdownDividerDirective,
  DropdownHeaderDirective,
  DropdownItemDirective,
  DropdownMenuDirective,
  DropdownToggleDirective,
  HeaderComponent,
  HeaderNavComponent,
  HeaderTogglerDirective,
  NavItemComponent,
  NavLinkDirective
} from '@coreui/angular';

import { IconDirective } from '@coreui/icons-angular';
import { AuthService } from '../../../../services/auth.service';
import { User } from '../../../../models/auth.interfaces';
import { LayoutService } from '../../../../services/layout.service';
import { NotificationService, Notification } from '../../../../services/notification.service';
import { SessionStorageService } from '../../../../services';

@Component({
  selector: 'app-default-header',
  templateUrl: './default-header.component.html',
  standalone: true,
  imports: [CommonModule, DatePipe, HeaderComponent, ContainerComponent, HeaderTogglerDirective, IconDirective, HeaderNavComponent, NavItemComponent, NavLinkDirective, RouterLink, RouterLinkActive, NgTemplateOutlet, BreadcrumbRouterComponent, DropdownComponent, DropdownToggleDirective, AvatarComponent, DropdownMenuDirective, DropdownHeaderDirective, DropdownItemDirective, BadgeComponent, DropdownDividerDirective]
})
export class DefaultHeaderComponent extends HeaderComponent implements OnInit, OnDestroy {

  readonly #colorModeService = inject(ColorModeService);
  readonly colorMode = this.#colorModeService.colorMode;

  readonly colorModes = [
    { name: 'light', text: 'Light', icon: 'cilSun' },
    { name: 'dark', text: 'Dark', icon: 'cilMoon' },
    { name: 'auto', text: 'Auto', icon: 'cilContrast' }
  ];

  readonly icons = computed(() => {
    const currentMode = this.colorMode();
    return this.colorModes.find(mode => mode.name === currentMode)?.icon ?? 'cilSun';
  });

  // Authentication properties
  currentUser: User | null = null;
  isAuthenticated = false;
  isLoggingOut = false;

  // Notification properties
  public notifications: Notification[] = [];
  public unreadCount: number = 0;
  private notificationFetchInterval: any;

  private destroy$ = new Subject<void>();

  constructor(
    private authService: AuthService,
    private router: Router,
    private layoutService: LayoutService,
    private SessionStorageService: SessionStorageService,
    private notificationService: NotificationService
  ) {
    super();
  }

  ngOnInit(): void {
    // Subscribe to authentication state
    this.authService.isAuthenticated$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(isAuth => {
      this.isAuthenticated = isAuth;
      this.initializeNotifications();

      // Initialize notifications when authenticated

    });

    // Subscribe to current user
    this.authService.currentUser$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(user => {
      this.currentUser = user;

      // Initialize notifications when user is available
      if (user && this.isAuthenticated) {
        this.initializeNotifications();
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();

    // Clean up notification resources
    if (this.notificationFetchInterval) {
      clearInterval(this.notificationFetchInterval);
    }
    this.notificationService.disconnectSSE();
  }

  /**
   * Handle logout action
   */
  onLogout(): void {
    if (this.isLoggingOut) {
      return; // Prevent multiple logout attempts
    }

    this.isLoggingOut = true;

    this.authService.logout().pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: () => {
        console.log('Logout successful');
        this.router.navigate(['/login']);
      },
      error: (error) => {
        console.error('Logout error:', error);
        // Even if logout fails, redirect to login
        this.router.navigate(['/login']);
      },
      complete: () => {
        this.isLoggingOut = false;
      }
    });
  }

  /**
   * Get user display name
   */
  getUserDisplayName(): string {
    if (!this.currentUser) {
      return 'User';
    }

    if (this.currentUser.prenom && this.currentUser.nom) {
      return `${this.currentUser.prenom} ${this.currentUser.nom}`;
    }

    if (this.currentUser.nom) {
      return this.currentUser.nom;
    }

    return this.currentUser.email || 'User';
  }

  /**
   * Get user email for display
   */
  getUserEmail(): string {
    return this.currentUser?.email || '';
  }

  /**
   * Toggle theme customizer panel
   */
  toggleCustomizer(): void {
    this.layoutService.emitCustomizerChange('toggle');
  }

  /**
   * Toggle sidebar between expanded and narrow/compact states
   */
  toggleSidebarNarrow(): void {
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
      if (sidebar.classList.contains('sidebar-narrow')) {
        // Currently narrow, expand it
        sidebar.classList.remove('sidebar-narrow');
      } else {
        // Currently expanded, make it narrow
        sidebar.classList.add('sidebar-narrow');
      }
    }
  }

  /**
   * Initialize notification functionality
   */
  private initializeNotifications(): void {
    // if (!this.currentUser?.id) {
    //   return;
    // }
    const userId = this.SessionStorageService.getSessionValue('iduser') || '';
    this.fetchNotifications(userId);
    this.startFetchingNotifications(userId);
    this.startSSEConnection();
  }

  /**
   * Fetch notifications for the current user
   */
  private fetchNotifications(userId: string): void {
    this.notificationService.getByIdUser(userId).then(
      data => {
        console.log(data)
        this.notifications = data || [];
        console.log('Notifications fetched:', this.notifications);
        this.unreadCount = this.notifications.filter(notification => !notification.readed && !notification.read).length;
        console.log('Unread notifications count:', this.unreadCount);
      },
      error => {
        console.error('Error fetching notifications:', error);
        this.notifications = [];
        this.unreadCount = 0;
      }
    );
  }

  /**
   * Start periodic notification fetching
   */
  private startFetchingNotifications(userId: string): void {
    // Clear existing interval if any
    if (this.notificationFetchInterval) {
      clearInterval(this.notificationFetchInterval);
    }

    // Fetch notifications every 3 minutes
    this.notificationFetchInterval = setInterval(() => {
      this.fetchNotifications(userId);
    }, 3 * 60 * 1000);
  }

  /**
   * Start Server-Sent Events connection for real-time notifications
   */
  private startSSEConnection(): void {
    this.notificationService.connectToSSE();

    // Handle new notifications from SSE
    this.notificationService.handleNewNotification = (notification: Notification) => {
      this.notifications.unshift(notification); // Add to beginning of array
      this.unreadCount++; // Increment unread count
    };
  }

  /**
   * Reset unread count and mark notifications as read
   */
  resetUnreadCount(): void {
    // Collect the IDs of unread notifications
    const unreadNotificationIds = this.notifications
      .filter(notification => !notification.read && !notification.readed)
      .map(notification => notification.id);

    if (unreadNotificationIds.length > 0) {
      // Call the service to update the read status
      this.notificationService.updateNotificationStatus(unreadNotificationIds)
        .then(response => {
          console.log('Notifications marked as read:', response);
          // Reset the unread count
          this.unreadCount = 0;
          // Update the local state
          this.notifications.forEach(notification => {
            if (unreadNotificationIds.includes(notification.id)) {
              notification.read = true;
              notification.readed = true;
            }
          });
        })
        .catch(error => {
          console.error('Error marking notifications as read:', error);
        });
    } else {
      // If there are no unread notifications, just reset the count
      this.unreadCount = 0;
    }
  }

  /**
   * Handle notification click - navigate to notification URL
   */
  handleNotificationClick(notification: Notification): void {
    if (notification.url) {
      this.router.navigate([notification.url]);
    } else {
      console.log("No URL defined for this notification");
    }
  }

  sidebarId = input('sidebar1');

  public newMessages = [
    {
      id: 0,
      from: 'Jessica Williams',
      avatar: '7.jpg',
      status: 'success',
      title: 'Urgent: System Maintenance Tonight',
      time: 'Just now',
      link: 'apps/email/inbox/message',
      message: 'Attention team, we\'ll be conducting critical system maintenance tonight from 10 PM to 2 AM. Plan accordingly...'
    },
    {
      id: 1,
      from: 'Richard Johnson',
      avatar: '6.jpg',
      status: 'warning',
      title: 'Project Update: Milestone Achieved',
      time: '5 minutes ago',
      link: 'apps/email/inbox/message',
      message: 'Kudos on hitting sales targets last quarter! Let\'s keep the momentum. New goals, new victories ahead...'
    },
    {
      id: 2,
      from: 'Angela Rodriguez',
      avatar: '5.jpg',
      status: 'danger',
      title: 'Social Media Campaign Launch',
      time: '1:52 PM',
      link: 'apps/email/inbox/message',
      message: 'Exciting news! Our new social media campaign goes live tomorrow. Brace yourselves for engagement...'
    },
    {
      id: 3,
      from: 'Jane Lewis',
      avatar: '4.jpg',
      status: 'info',
      title: 'Inventory Checkpoint',
      time: '4:03 AM',
      link: 'apps/email/inbox/message',
      message: 'Team, it\'s time for our monthly inventory check. Accurate counts ensure smooth operations. Let\'s nail it...'
    },
    {
      id: 3,
      from: 'Ryan Miller',
      avatar: '4.jpg',
      status: 'info',
      title: 'Customer Feedback Results',
      time: '3 days ago',
      link: 'apps/email/inbox/message',
      message: 'Our latest customer feedback is in. Let\'s analyze and discuss improvements for an even better service...'
    }
  ];

  public newNotifications = [
    { id: 0, title: 'New user registered', icon: 'cilUserFollow', color: 'success' },
    { id: 1, title: 'User deleted', icon: 'cilUserUnfollow', color: 'danger' },
    { id: 2, title: 'Sales report is ready', icon: 'cilChartPie', color: 'info' },
    { id: 3, title: 'New client', icon: 'cilBasket', color: 'primary' },
    { id: 4, title: 'Server overloaded', icon: 'cilSpeedometer', color: 'warning' }
  ];

  public newStatus = [
    { id: 0, title: 'CPU Usage', value: 25, color: 'info', details: '348 Processes. 1/4 Cores.' },
    { id: 1, title: 'Memory Usage', value: 70, color: 'warning', details: '11444GB/16384MB' },
    { id: 2, title: 'SSD 1 Usage', value: 90, color: 'danger', details: '243GB/256GB' }
  ];

  public newTasks = [
    { id: 0, title: 'Upgrade NPM', value: 0, color: 'info' },
    { id: 1, title: 'ReactJS Version', value: 25, color: 'danger' },
    { id: 2, title: 'VueJS Version', value: 50, color: 'warning' },
    { id: 3, title: 'Add new layouts', value: 75, color: 'info' },
    { id: 4, title: 'Angular Version', value: 100, color: 'success' }
  ];

}
