import { Component, OnInit } from '@angular/core';
import { CommentService } from '../../../../../services/comment.service';
import { ActionButton, TableColumn, TableConfig } from '../../../../../shared/models/table.models';
import { ToastrService } from 'ngx-toastr';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';

@Component({
  selector: 'app-reclamation-list',
  imports: [SmartContainerComponent,SmartTableComponent],
  templateUrl: './reclamation-list.component.html',
  styleUrl: './reclamation-list.component.scss'
})
export class ReclamationListComponent implements OnInit {
  listReclamations: any[] = [];
  loading = true;
  constructor(private commentService: CommentService,
    private toastr: ToastrService
  ) {this.loadComments(); }
  ngOnInit(): void {
    this.loadComments();
  }

  tableColumns : TableColumn[] = [
    { name:'nom_depart',displayName:'Destination Depart',sortable:true,dataType:'text',filterable:true},
    { name:'nom_arrivee',displayName:'Destination Arrivee',sortable:true,dataType:'text',filterable:true},
    { name:'type_ligne',displayName:'Type',sortable:true,dataType:'text',filterable:true},
    { name:'volume',displayName:'Volume (m3)',sortable:true,dataType:'number',filterable:true},
    { name:'estimation',displayName:'Estimation',sortable:true,dataType:'number',filterable:true},
  ]
  tableConfig: TableConfig = {
    commentable: true,
    emptyMessage: 'Aucune reclamation',
    showFilter: true
  }
  actionButtons: ActionButton[] = [
      {
        label: 'Commentaires',
        icon: 'cil-comment-bubble',
        color: 'info',
        isCommentAction: true,
        tooltip: 'Voir les commentaires'
      }
    ]
  
  loadComments() {
    this.loading = true;
    this.commentService.findAllComments().subscribe({
      next: (data) => {
        this.listReclamations = data;
        console.log('Comments loaded:', this.listReclamations);
      },
      error: (error) => {
        console.error('Error loading comments:', error);
      },
      complete: () => {
        this.loading = false;
      }

    });
  }

}
