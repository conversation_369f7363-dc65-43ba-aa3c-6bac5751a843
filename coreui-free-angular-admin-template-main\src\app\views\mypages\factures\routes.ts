import { Routes } from '@angular/router';
import { authGuard } from '../../../../guards/auth.guard';
import { roleGuard } from '../../../../guards/role.guard';

export const routes: Routes = [
    {
        path: 'factures-list',
        loadComponent: () => import('./facture-list/facture-list.component').then((c) => c.FactureListComponent),
        canActivate: [authGuard, roleGuard],
        data: {
            title: 'Liste Factures',
            roles: ['FACTURATION', 'SuperAdmin', 'Administrateur']
        }
    },
    {
        path:'transport-facturation',
        loadComponent: () => import('./facturation-transport/facturation-transport.component').then((c) => c.FacturationTransportComponent),
        canActivate: [authGuard, roleGuard],
        data: {
            title: 'Facturation Transport',
            roles: ['FACTURATION', 'SuperAdmin', 'Administrateur']
        }
    }

]