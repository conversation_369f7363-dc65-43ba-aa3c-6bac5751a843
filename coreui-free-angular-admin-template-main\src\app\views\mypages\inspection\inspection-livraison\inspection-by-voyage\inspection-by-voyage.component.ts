import { Component, ViewChild } from '@angular/core';
import { VoyageService } from 'src/services';
import { FormConfig } from 'src/shared/models/form.models';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';
import { DynamicExportButtonComponent } from 'src/shared/components/dynamic-export-button/dynamic-export-button.component';
import { ActionButton, TableColumn, TableConfig } from 'src/shared/models/table.models';
import { CommonModule } from '@angular/common';
import { DynamicFormComponent } from 'src/shared/components/dynamic-form/dynamic-form.component';
import { ToastrService } from 'ngx-toastr';
import { ConfirmationDialogService } from 'src/services/confirmation-dialog.service';
import { InspectionSharedModalComponent } from '../shared/inspection-shared-modal/inspection-shared-modal.component';


@Component({
  selector: 'app-inspection-by-voyage',
  standalone: true,
  imports: [SmartContainerComponent,SmartTableComponent,DynamicExportButtonComponent,CommonModule,DynamicFormComponent,InspectionSharedModalComponent],
  templateUrl: './inspection-by-voyage.component.html',
  styleUrl: './inspection-by-voyage.component.scss'
})
export class InspectionByVoyageComponent {
  @ViewChild('detailModal') detailModal!: InspectionSharedModalComponent; 
  loading = false;
  showTable = false;
  Date !: {
    date_debut: string,
    date_fin: string
  } 
  lignes: any[] = [];
  constructor(
    private voyageService: VoyageService,
    private toastr: ToastrService,
    private confirmDialogService: ConfirmationDialogService
  ){}


  formConfig: FormConfig ={
    fields: [
      {
        name:'startDate',
        type:'date',
        label:'Date de début',
        required:true
      },
      {
        name:'endDate',
        type:'date',
        label:'Date de fin',
        required:true
      }
    ],
    buttons: [
      {
        label:'Rechercher',
        color:'primary',
        icon:'cil-search',
        onClick: (formData: any) => {this.onSubmit(formData)}
      }
    ]
  }

  tableColumn :TableColumn[] =[
  {
    name: 'nom_voyage',
    displayName: 'Nom Voyage',
    sortable: true,
    filterable: true,
  },
  
  {
    name: 'sell_price',
    displayName: 'Montant de vente',
    sortable: true,
    filterable: true,
  },
  {
    name: 'purchase_price',
    displayName: 'Montant à payer',
    sortable: true,
    filterable: true,
  },
  {
    name: 'date_voyage',
    displayName: 'Date du voyage',
    dataType: 'date',
    sortable: true,
    filterable: true,
  }
]
  tableActions: ActionButton[] = [
    {
      icon: 'cil-trash',
      color: 'danger',
      callback: (row: any) => {this.handleVoyageCancellation(row)}
    },
    {
      icon: 'cil-zoom',
      color: 'primary',
      callback: (row: any) => {this.ViewDetails(row)}
    }
  ]

  tableConfig: TableConfig = {
    emptyMessage: 'Aucune ligne trouvée', 
  }
  handleVoyageCancellation(row: any): void {
    const voyageId = row.id;
    this.confirmDialogService.confirmDelete("Voulez-vous vraiment annuler ce voyage ?").then(
      (data: any) => {
        this.voyageService.cancelVoyage(voyageId).subscribe({
            next: (data: any) => {
              this.toastr.success('Voyage annulé avec succès');
              this.lignes = this.lignes.filter((ligne: any) => ligne.id !== voyageId);
            },
            error: (error: any) => {
              console.error(error);
            },
            complete: () => {
              this.loading = false;
            }
          })
        }
    )  
  }

  onSubmit(formData: any) {
    console.log(formData);
    this.loading = true;
    const data = {
      date_debut: formData.startDate,
      date_fin: formData.endDate
    }
    this.Date = data;
    console.log(data);
    this.voyageService.searchVoyageByDate(data).subscribe({
      next: (data: any) => {
        this.lignes = data;
        console.log(this.lignes);
        this.showTable = true;
      },
      error: (error: any) => {
        console.error(error);
      },
      complete: () => {
        this.loading = false;
      }
    })
  }

  ViewDetails(row: any): void {
    this.detailModal.VoyageId = row.id;
    this.detailModal.openModal();
  }
}
