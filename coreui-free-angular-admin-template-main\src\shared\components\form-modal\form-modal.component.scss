// dynamic-modal.component.scss
.modal-overlay {
  position: fixed; // Changed from absolute to fixed
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050; // Ensure this is higher than your sidebar's z-index
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;

  &.show {
    opacity: 1;
    visibility: visible;
  }

  &.backdrop {
    background-color: rgba(0, 0, 0, 0.5);
  }
}

.modal-content {
  background: #fff;
  border-radius: 0.3rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  width: 90%; // Changed from 100% to 90% for better mobile behavior
  margin: 1rem; // Added margin for smaller screens
  transition: transform 0.3s ease, opacity 0.3s ease;

  // Responsive sizing
  @media (min-width: 576px) {
    &.modal-sm {
      max-width: 300px;
    }
  
    &.modal-md {
      max-width: 500px;
    }
  
    &.modal-lg {
      max-width: 800px;
    }
  
    &.modal-xl {
      max-width: 1140px;
    }
  }

  &.modal-centered {
    align-self: center;
  }

  &.modal-scrollable {
    overflow-y: auto;
  }

  &.modal-fade {
    opacity: 0;
    transform: translateY(-20px);

    .show & {
      opacity: 1;
      transform: translateY(0);
    }
  }

  &.modal-slide {
    transform: translateY(50px);

    .show & {
      transform: translateY(0);
    }
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
}

.modal-title {
  margin-bottom: 0;
  word-break: break-word; // Prevent long titles from breaking layout
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 1rem;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; // Better scrolling on mobile
}

.modal-footer {
  display: flex;
  flex-wrap: wrap; // Allow buttons to wrap on small screens
  justify-content: flex-end;
  align-items: center;
  padding: 1rem;
  border-top: 1px solid #dee2e6;
  gap: 0.5rem;
}

.close {
  float: right;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.5;
  background: transparent;
  border: 0;
  cursor: pointer;
  padding: 0.5rem; // Larger tap target for mobile
  margin-left: auto; // Better positioning

  &:hover {
    opacity: 0.75;
  }
}

// Mobile-specific adjustments
@media (max-width: 575.98px) {
  .modal-content {
    width: 95%;
    max-height: 85vh;
    margin: 0.5rem;
  }

  .modal-footer {
    justify-content: center; // Center buttons on small screens
    > * {
      flex: 1 1 auto; // Make buttons take available space
      min-width: 45%; // Ensure reasonable button sizing
    }
  }
}

// Ensure modal appears above other elements
.modal-overlay {
  z-index: 9999; // Very high to ensure it's above everything
}

// Prevent body scrolling when modal is open
body.modal-open {
  overflow: hidden;
  position: fixed;
  width: 100%;
}