import { Component, Input, Output, EventEmitter } from '@angular/core';
import { ExcelExportService } from '../../../services/excel-export.service';
import { 
  ButtonDirective,
  DropdownComponent,
  DropdownToggleDirective,
  DropdownMenuDirective,
  DropdownItemDirective
} from '@coreui/angular';
import { IconModule } from '@coreui/icons-angular';

@Component({
  selector: 'app-export-button',
  templateUrl: './dynamic-export-button.component.html',
  styleUrls: ['./dynamic-export-button.component.scss'],
  imports: [
    ButtonDirective,
    DropdownComponent,
    DropdownToggleDirective,
    DropdownMenuDirective,
    DropdownItemDirective,
    IconModule
  ],
  standalone: true
})
export class DynamicExportButtonComponent {
  @Input() data: any[] = [];
  @Input() fileName: string = 'export';
  @Input() buttonText: string = 'Export';
  @Input() buttonColor: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark' = 'primary';
  @Input() buttonSize: '' | 'sm' | 'lg' = ''; // default is empty string for medium
  @Input() buttonIcon: string = 'cil-cloud-download';
  @Input() showDropdown: boolean = false;
  @Input() exportOptions: {
    label: string;
    options: {
      sheetName?: string;
      columnHeaders?: Record<string, string>;
      excludeColumns?: string[];
      dateFields?: string[];
      dateFormat?: string;
    };
  }[] = [];

  @Output() exportStarted = new EventEmitter<void>();
  @Output() exportCompleted = new EventEmitter<void>();
  @Output() exportFailed = new EventEmitter<Error>();

  constructor(private excelExportService: ExcelExportService) {}

  setSize(size: 'sm' | 'md' | 'lg'): void {
    this.buttonSize = size === 'md' ? '' : size; // convert 'md' to ''
  }

  exportData(options: any = {}): void {
    this.exportStarted.emit();
    
    try {
      this.excelExportService.exportToExcel(
        this.data,
        this.fileName,
        options
      );
      this.exportCompleted.emit();
    } catch (error) {
      console.error('Export failed:', error);
      this.exportFailed.emit(error as Error);
    }
  }
}
