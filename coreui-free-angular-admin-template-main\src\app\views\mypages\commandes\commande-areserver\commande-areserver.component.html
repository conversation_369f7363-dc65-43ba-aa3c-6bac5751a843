<app-smart-container [title]="'Commandes à Expédier'">
  <div slot="content">
    <app-dynamic-form 
      *ngIf="camionList.length > 0 && conducteurList.length > 0 && formConfig" 
      [config]="formConfig"
      (formSubmit)="onFormSubmit($event)">
    </app-dynamic-form>

    <app-smart-container>
      <div slot="actions">
        <app-export-button [data]="SourceData" [fileName]="'commande-areserver-data'"
          [buttonText]="'Exporter Commande A Réserver'"></app-export-button>
      </div>
      <div slot="content">
        <app-smart-table [data]="SourceData" [columns]="TableColumn" [config]="tableConfig"
          (selectionChange)="handleSelectionChange($event)">
        </app-smart-table>
      </div>
    </app-smart-container>
  </div>

</app-smart-container>

<app-dynamic-modal [title]="'Total des volumes'" [buttonLabel]="'Suivant'" [show]="showFirstModal"
  [buttonVariant]="'primary'" [size]="'md'" (onSubmit)="next()">
  <p>Le total des volumes dans les lignes sélectionnées, y compris les réservations précédentes si existe, est de {{
    totalVolume }} m³</p>

</app-dynamic-modal>


<div *ngIf="showHoraireModal">
  <app-dynamic-modal [title]="'Horaire'" [buttonLabel]="'Réserver'" [show]="showHoraireModal"
    [buttonVariant]="'primary'" [size]="'lg'" (onSubmit)="res()">
    <table class="table table-bordered table-hover align-middle bg-white shadow-sm rounded" style="min-width: 700px;">
      <thead class="table-light">
        <tr>
          <th class="text-center">Départ</th>
          <th class="text-center">Heure Départ</th>
          <th class="text-center">Arrivé</th>
          <th class="text-center">Heure Arrivé</th>
          <th class="text-center">Tolérance</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let destination of filteredRows">
          <td class="text-center fw-semibold">{{ destination.nom_depart }}</td>
          <td class="text-center">
            <select class="form-select form-select-sm w-auto mx-auto" [(ngModel)]="destination.plage_horaire_heure_dep"
              required>
              <option value="" disabled selected>Heure</option>
              <option *ngFor="let hour of hours" [value]="hour">{{ hour }}</option>
            </select>
          </td>
          <td class="text-center fw-semibold">{{ destination.nom_arrivee }}</td>
          <td class="text-center">
            <select class="form-select form-select-sm w-auto mx-auto" [(ngModel)]="destination.plage_horaire_heure_arr"
              required>
              <option value="" disabled selected>Heure</option>
              <option *ngFor="let hour of hours" [value]="hour">{{ hour }}</option>
            </select>
          </td>
          <td class="d-flex align-items-center justify-content-center">
            <b class="me-2" style="font-size: x-large;">±</b>
            <select class="form-select form-select-sm w-auto" [(ngModel)]="destination.tolerance" required>
              <option value="" disabled selected>Tolérance</option>
              <option [value]="0.5">30 min</option>
              <option [value]="1">1 Heure</option>
              <option [value]="2">2 Heures</option>
            </select>
          </td>
        </tr>
      </tbody>
    </table>
  </app-dynamic-modal>
</div>