import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { environment } from '../environments/environment';
import { Observable } from 'rxjs';
const httpOptions = {
  headers: new HttpHeaders({
    'Content-Type': 'application/json',
    'Authorization': 'Basic ' + btoa('med:123456')
  })
};
@Injectable({
  providedIn: 'root'
})
export class CommandeService {
  private apiURL = environment.apiURL;
  constructor(private http: HttpClient) { }

  findAllCommandesAReserver(): Observable<any[]> {
    return this.http.get<any>(this.apiURL + `ptchargement/valid/`, httpOptions);
  }

  updateStatusReserved(data: { updates: any[] }): Observable<any> {
    const url = `${this.apiURL}ptchargement/reserved`;

    return this.http.post(url, data, httpOptions);
  }
  getAllSaison(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'saison', httpOptions);
  }

  updateQteSaison(id: number, qte: number): Observable<any> {
    return this.http.put<any>(`${this.apiURL}saison/${id}/${qte}`, {}, httpOptions);
  }

  findEnrgCommandeModele(id:any): Observable<any> {
    return this.http.get<any>(this.apiURL + 'getAllCommande/' + id, httpOptions);
  }

  findEnrgCommandeModeleByChef(idChef:any): Observable<any> {
    return this.http.get<any>(this.apiURL + `findEnrgBychef/${idChef}`, httpOptions)
  }
  
  deleteCommandModele(id: number): Observable<any> {
    return this.http.put<any>(`${this.apiURL}comm/deleteEnr/${id}`, null, httpOptions)
  }
  getAllCommands(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'commande', httpOptions);
  }
  getAllCommandsByChef(idChef : any): Observable<any> {
    return this.http.get<any>(this.apiURL + `getAllCommandeByChef/${idChef}`, httpOptions);   
  }
  getAllOrdersByUser(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'comm/' + id, httpOptions);
  }

  addCharDecharByIdCmd(id_commande : any): Observable<any> {
    return this.http.get<any>(this.apiURL + 'ptchargDecharg/' + id_commande, httpOptions);
  }
  getComToValidateByChef(chefId: string): Observable<any> {
    return this.http.get<any>(this.apiURL + `getCommandeToValidateByChef/${chefId}`, httpOptions)
  }
  findComToValidateByUser(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'commande/exp/' + id, httpOptions);
  }
  findAllComValidByUser(id :any): Observable<any> {
    return this.http.get<any>(this.apiURL + 'com/' + id, httpOptions)
  }
  getAllComValidByChef(idChef : any): Observable<any> {
    return  this.http.get<any>(this.apiURL + `getAllComValidByChef/${idChef}`, httpOptions)
  }
}
