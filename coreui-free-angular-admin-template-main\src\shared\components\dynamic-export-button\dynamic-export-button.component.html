<!-- export-button.component.html -->
@if (!showDropdown) {
  <button
    cButton
    [color]="buttonColor"
    [size]="buttonSize"
    (click)="exportData()"
    [attr.aria-label]="'Export to Excel'"
  >
    @if (buttonIcon) {
       <c-icon [name]="buttonIcon" class="me-2"></c-icon>
    }
    {{ buttonText }}
  </button>
} @else {
  <c-dropdown>
    <button cButton [color]="buttonColor" [size]="buttonSize" cDropdownToggle>
      @if (buttonIcon) {
         <c-icon [name]="buttonIcon" class="me-2"></c-icon>
      }
      {{ buttonText }}
    </button>
    <ul cDropdownMenu>
      @for (option of exportOptions; track $index) {
        <li>
          <button cDropdownItem (click)="exportData(option.options)">
            {{ option.label }}
          </button>
        </li>
      }
      @if (exportOptions.length === 0) {
        <li>
          <button cDropdownItem (click)="exportData()">
            Default Export
          </button>
        </li>
      }
    </ul>
  </c-dropdown>
}