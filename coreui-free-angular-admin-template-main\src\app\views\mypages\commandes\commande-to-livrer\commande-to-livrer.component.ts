import { Component } from '@angular/core';
import { TableColumn,TableConfig } from 'src/shared/models/table.models';
import { FormConfig } from 'src/shared/models/form.models';
import { ConducteurService } from 'src/services/conducteur.service';
import { CamionService } from 'src/services/camion.service';
import { LigneCmdService } from 'src/services/ligne-cmd.service';
import { OrderLineService } from 'src/services';
import { PriceService } from 'src/services/price.service';
import { CircuitService } from 'src/services/circuit.service';
import { ToastrService } from 'ngx-toastr';
import { CommonModule } from '@angular/common';
import { SmartTableComponent } from '../../../../../shared/components/smart-table/smart-table.component';
import { SmartContainerComponent } from '../../../../../shared/components/smart-container/smart-container.component';
import { DynamicFormComponent } from '../../../../../shared/components/dynamic-form/dynamic-form.component';
import { SmartButtonComponent } from '../../../../../shared/components/smart-button/smart-button.component';
import { ActionButton } from '../../../../../shared/models/table.models';
import { ConfirmationDialogService } from '../../../../../services/confirmation-dialog.service';
import { DestinationService, MailService } from '../../../../../services';

@Component({
  selector: 'app-commande-to-livrer',
  standalone: true,
  imports: [
    CommonModule,
    SmartTableComponent,
    SmartContainerComponent,
    DynamicFormComponent,
    SmartButtonComponent
  ],
  templateUrl: './commande-to-livrer.component.html',
  styleUrl: './commande-to-livrer.component.scss'
})
export class CommandeToLivrerComponent {

    loadingPrimeTable:boolean = false
    showDetailsTable:boolean = false
    loadingDetailsTable:boolean = false
    initialData: any = {};
    selectedSource: any ={};
    selectedDetails:any[] = [];
    conducteurList: any[] = [];
    circuitList: any[] = [];
    camionList: any[] = [];
    source:any[] = [];
    
    disableButton :boolean = true;
  
    sourceDetails:any = [];
  
    tableColumn: TableColumn[] = [
      { name: 'id', displayName: 'ID', sortable: true ,filterable: true},
      { name:'nom',displayName: 'Nom Transporteur',sortable: true ,filterable: true},
      { name:'prenom',displayName: 'Prenom Transporteur',sortable: true ,filterable: true},
      { name:'immatriculation',displayName: 'Immatriculation',sortable: true ,filterable: true},
      { name:'date_voyage',displayName: 'Date Voyage',sortable: true ,filterable: true},
    ];
  
    tableConfig: TableConfig = {
      selectable: true,
      multiSelect: false   
    }
    formConfig: FormConfig ={
      fieldGroups : [
        {
          fields: [
            {
              name: 'selectedCamionId',
  
              type: 'select',
              required: true,
              disabled: true,
              options: {objectArray: 
               this.camionList,
              valueAttribute: 'id',
              labelAttribute: 'immatriculation'}
            },
            {
              name: 'selectedConducteurId',
              type: 'select',
              required: true,
              disabled: true,
              options: {objectArray: 
                this.conducteurList,
              valueAttribute: 'id',
              labelAttribute: 'fullName'}
            },
            {
              name : 'selectedDate',
              type: 'date',
              disabled: true,
              required: true
            }
          ]
        }
      ]
    }
  
    tableDetailsColumn: TableColumn[] = [
      { name: 'id', displayName: 'ID', sortable: true ,filterable: true},
      { name: 'nom_depart', displayName: 'Nom Depart', sortable: true ,filterable: true},
      { name: 'nom_arrivee', displayName: 'Nom Arrivee', sortable: true ,filterable: true},
      { name: 'type_ligne', displayName: 'Type Ligne', sortable: true ,filterable: true},
      { name: 'kilometrage', displayName: 'Kilometrage', sortable: true ,filterable: true},
      { name: 'nom_client', displayName: 'Nom Client', sortable: true ,filterable: true},
      { name: 'volume', displayName: 'Volume', sortable: true ,filterable: true},
      { name: 'status', displayName: 'Status', sortable: true ,filterable: true},
    ];
  
    tableDetailsConfig: TableConfig = {
      selectable: true,
      multiSelect: true,
      commentable: true   
    }
    tableDetailsAction: ActionButton[] = [
      {
        icon: 'cil-comment-bubble',
        color: 'info',
        isCommentAction: true
      }
    ]
  
  
    constructor(
      private conducteurService: ConducteurService,
      private destinationService: DestinationService,
      private camionService: CamionService,
      private ligneCmdService: LigneCmdService,
      private orderLigneService :OrderLineService,
      private mailService: MailService,
      private priceService: PriceService,
      private circuitService: CircuitService,
      private confirmationDialogService: ConfirmationDialogService,
      private toastr: ToastrService
    ) {}
  
    ngOnInit(): void {
      this.loadSource();
      this.loadConducteurs();
      this.loadCircuit();
      this.loadCamion();
    }
  
    loadDetails(){
      this.loadingDetailsTable = true;
      this.showDetailsTable = true;
      let data ={
        id_camion : this.initialData.selectedCamionId,
        id_conducteur : this.initialData.selectedConducteurId,
        date : this.initialData.selectedDate,
      }
      this.orderLigneService.findExpeditedByDateAndTruck(data).subscribe({
        next: (data:any) => {
          this.sourceDetails = data;
          this.loadingDetailsTable = false;
        },
        error: (error:any) => {
          this.toastr.error('Erreur lors de la chargement des lignes de voyage');
          this.loadingDetailsTable = false;
        }
      });
    }
    onSelectChange(event: any) {
      this.selectedSource = event[0];
      this.initialData = {
        selectedCamionId: this.selectedSource.id_camion,
        selectedConducteurId: this.selectedSource.id_conducteur,
        selectedDate: this.selectedSource.date_voyage,
      };
      
      this.loadDetails();
  
    }
    onSelectDetailsChange(event: any) {
      this.selectedDetails = event;
      this.UpdateButtonDisableState();
      

    }
    UpdateButtonDisableState(){
      if (this.selectedDetails.length > 0) {
        this.disableButton = false;
      } else {
        this.disableButton = true;
      }
    }
    EnPanne(){
      let selectedIds :number[] =[]
      this.selectedDetails.forEach((detail:any) => {
        selectedIds.push(detail.id);
      })
      this.confirmationDialogService.confirmDelete(`Voulez-vous vraiment mettre ces lignes de voyage en panne ?`)
      .then((confirmed) => {
        if (confirmed) {
          selectedIds.map((id:number) => {
            this.ligneCmdService.updatePanne(id).subscribe({
              next: (data:any) => {
                this.toastr.success('Ligne de voyage mise en panne avec succès');
                this.sendAnnulation(selectedIds)
                this.sourceDetails = this.sourceDetails.filter((item:any ) => !selectedIds.includes(item.id));
                if (this.sourceDetails.length == 0) {
                  this.showDetailsTable = false;
                  this.source = this.source.filter((item:any) => item != this.selectedSource);
                }
              },
              error: (error:any) => {
                this.toastr.error('Erreur lors de la mise en panne de la ligne de voyage');
              }
            });
          })
        }
      });
      
    }
    Livrer(){
      let currentDate = new Date();
      let previousDate = new Date(this.initialData.selectedDate);
      if (currentDate < previousDate) {
        this.toastr.error('Erreur lors de la vérification des lignes de voyage');
        return;
      }
      let selectedIds :number[] =[]
      this.selectedDetails.forEach((detail:any) => {
        selectedIds.push(detail.id);
      })
      selectedIds.map((id:number) => {
        this.orderLigneService.updateDeliveryStatus(id).subscribe({
          next: (data:any) => {
            this.toastr.success('Ligne de voyage livrée avec succès');
            this.sourceDetails = this.sourceDetails.filter((item:any ) => !selectedIds.includes(item.id));
            if (this.sourceDetails.length == 0) {
              this.showDetailsTable = false;
              this.source = this.source.filter((item:any) => item != this.selectedSource);
            }
          },
          error: (error:any) => {
            this.toastr.error('Erreur lors de la livraison de la ligne de voyage');
          }
        })
      })

    }

    loadConducteurs() {
      this.conducteurService.findConducteur().subscribe({
        next: (data:any) => {
          this.conducteurList = data;
          this.conducteurList.forEach((conducteur:any) =>
          {conducteur.fullName = conducteur.nom + ' ' + conducteur.prenom}
          )
          if (this.formConfig.fieldGroups && this.formConfig.fieldGroups[0].fields[1].options) {
            this.formConfig.fieldGroups[0].fields[1].options.objectArray = this.conducteurList;
          }
        },
        error: (error:any) => {
          this.toastr.error('Erreur lors de la chargement des conducteurs');
        }
      });
    }
    loadCircuit() {
      this.circuitService.getAllCircuits().subscribe({
        next: (data:any) => {
          this.circuitList = data;
          if (this.formConfig.fieldGroups && this.formConfig.fieldGroups[0].fields[3].options) {
            this.formConfig.fieldGroups[0].fields[3].options.objectArray = this.circuitList;
          }
        },
        error: (error:any) => {
          this.toastr.error('Erreur lors de la chargement des circuits');
        }
      });
    }
    loadCamion() {
      this.camionService.findAllCamion().subscribe({
        next: (data:any) => {
          this.camionList = data;
          if (this.formConfig.fieldGroups && this.formConfig.fieldGroups[0].fields[0].options) {
            this.formConfig.fieldGroups[0].fields[0].options.objectArray = this.camionList;
          }
        },
        error: (error:any) => {
          this.toastr.error('Erreur lors de la chargement des camions');
        }
      });
    }
    loadSource() {
      this.loadingPrimeTable = true;
      this.orderLigneService.findVoyageListExpedited().subscribe({
        next: (data:any) => {
          this.source = data;
          this.loadingPrimeTable = false;
        },
        error: (error:any) => {
          this.toastr.error('Erreur lors de la chargement des lignes de voyage');
          this.loadingPrimeTable = false;
        }
      });
    }
  
    async sendAnnulation(selectedIds :number[]) {
      const destinationsData:any = {}; 
  
      for (const id of selectedIds) {
  
        const destination = this.sourceDetails.find((item:any) => item.id == id);
  
        if (destination) {
          try {
            await this.destinationService.findDestinationById(destination.to_destination).subscribe({
              next: (data:any) => {
                console.log(data);
                let mail = data.destination.email;
                let nom_locale = data.destination.nom_locale;
                destinationsData[destination.to_destination] = {
                  mail: mail,
                  nom_locale: nom_locale
                };
                console.log(destinationsData);
              },
              error: (error:any) => {
                console.error(`Une erreur s'est produite lors de la récupération des données pour l'ID ${id}:`, error);
              }
            })
          } catch (error) {
            console.error(`Une erreur s'est produite lors de la récupération des données pour l'ID ${id}:`, error);
          }
        }
      }
  
      const destinationsUniques = Object.values(destinationsData);
  
      for (const data of destinationsUniques) {
        try {
          await this.mailService.sendCancellationMail(data);
        } catch (error) {
          console.error(`Une erreur s'est produite lors de l'envoi de l'e-mail d'annulation pour la destination ${data}:`, error);
        }
      }
    }
}
