@use "@coreui/coreui/scss/variables";
@use "@coreui/coreui/scss/mixins";

// Fallback variables in case CoreUI isn't available
$font-family-base: var(--cui-body-font-family, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif) !default;
$border-radius: var(--cui-border-radius, 0.25rem) !default;
$box-shadow-sm: var(--cui-box-shadow-sm, 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075)) !default;
$font-weight-semibold: var(--cui-font-weight-semibold, 600) !default;
$white: var(--cui-white, #fff) !default;


.invoice-container {
  max-width: 800px;
  margin: 2rem auto;
  font-family: $font-family-base;

  .invoice-card {
    background-color: var(--cui-body-bg);
    border: 1px solid var(--cui-border-color);
    border-radius: $border-radius;
    box-shadow: $box-shadow-sm;
    overflow: hidden;
    padding: 2rem;
  }

  .invoice-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--cui-border-color);

    .company-info {
      .company-logo {
        height: 60px;
        margin-bottom: 1rem;
      }

      .company-name {
        color: var(--cui-heading-color);
        font-size: 1.5rem;
        margin-bottom: 0.75rem;
      }

      .company-details {
        color: var(--cui-body-color);
        line-height: 1.5;
        font-size: 0.9rem;
      }
    }

    .invoice-title-section {
      text-align: right;

      .invoice-title {
        background-color: var(--cui-primary);
        color: $white;
        padding: 0.5rem 1rem;
        border-radius: $border-radius;
        display: inline-block;
        margin-bottom: 1rem;
        font-size: 1.25rem;
      }

      .client-info {
        background-color: var(--cui-light-bg-subtle);
        border-radius: $border-radius;
        padding: 1rem;
        border: 1px solid var(--cui-border-color);

        h3 {
          color: var(--cui-heading-color);
          margin-top: 0;
          margin-bottom: 0.75rem;
          font-size: 1.1rem;
        }

        p {
          margin-bottom: 0.25rem;
          font-size: 0.9rem;
        }
      }
    }
  }

  .invoice-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--cui-light-bg-subtle);
    padding: 1rem;
    border-radius: $border-radius;
    margin-bottom: 1.5rem;

    .invoice-number {
      h3 {
        color: var(--cui-heading-color);
        margin: 0;
        font-size: 1.1rem;
      }
    }

    .invoice-date {
      p {
        margin: 0;
        color: var(--cui-body-color);
      }
    }
  }

  .invoice-table-container {
    margin-bottom: 2rem;

    .invoice-table {
      width: 100%;
      border-collapse: collapse;

      thead {
        background-color: var(--cui-primary);
        color: $white;

        th {
          padding: 0.75rem;
          text-align: left;
          font-weight: $font-weight-semibold;
        }
      }

      tbody {
        tr {
          border-bottom: 1px solid var(--cui-border-color);

          &:nth-child(even) {
            background-color: var(--cui-light-bg-subtle);
          }

          td {
            padding: 0.75rem;
          }
        }
      }
    }
  }

  .invoice-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;

    .amount-in-words {
      flex: 1;
      margin-right: 2rem;
      background-color: var(--cui-light-bg-subtle);
      padding: 1rem;
      border-radius: $border-radius;

      h4 {
        color: var(--cui-heading-color);
        margin-top: 0;
        margin-bottom: 0.5rem;
        border-bottom: 1px solid var(--cui-border-color);
        padding-bottom: 0.5rem;
      }

      p {
        font-style: italic;
        color: var(--cui-body-color);
        margin: 0;
      }
    }

    .invoice-totals {
      width: 250px;

      .totals-table {
        width: 100%;
        border-collapse: collapse;

        tr {
          &:not(.total-ttc) {
            background-color: var(--cui-light-bg-subtle);
          }

          th, td {
            padding: 0.75rem;
            text-align: left;
          }

          td {
            text-align: right;
          }
        }

        .total-ttc {
          background-color: var(--cui-primary);
          color: $white;
          font-weight: $font-weight-semibold;
        }
      }
    }
  }

  .invoice-notes {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--cui-border-color);
    text-align: center;
    color: var(--cui-body-color);
    font-size: 0.9rem;
  }
}

// Print-specific styles
@media print {
  :root {
    --cui-body-bg: #fff !important;
    --cui-body-color: #000 !important;
    --cui-heading-color: #000 !important;
    --cui-border-color: #ddd !important;
    --cui-light-bg-subtle: #f9f9f9 !important;
    --cui-primary: #2c3e50 !important;
  }

  body {
    background-color: white !important;
    color: black !important;
  }

  .invoice-container {
    margin: 0 !important;
    padding: 0 !important;
    max-width: 100% !important;
    box-shadow: none !important;

    .invoice-card {
      border: none !important;
      box-shadow: none !important;
      padding: 0 !important;
    }

    .invoice-header, 
    .invoice-meta, 
    .invoice-footer, 
    .invoice-table-container {
      page-break-inside: avoid;
    }

    .invoice-table {
      thead {
        display: table-header-group;
      }

      tbody {
        tr {
          page-break-inside: avoid;
        }
      }
    }

    // Ensure colors are print-friendly
    .invoice-title,
    .invoice-table thead,
    .total-ttc {
      background-color: #2c3e50 !important;
      color: white !important;
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
    }

    // Hide unnecessary elements for print
    .no-print {
      display: none !important;
    }
  }

  @page {
    size: A4;
    margin: 1cm;
  }
}

// Dark theme adjustments
.c-dark-theme {
  .invoice-container {
    .invoice-card {
      background-color: var(--cui-dark-bg);
    }

    .invoice-table {
      thead {
        background-color: var(--cui-primary);
      }
    }

    .total-ttc {
      background-color: var(--cui-primary) !important;
    }
  }
}

@media print {
    :root {
      --cui-body-bg: #fff !important;
      --cui-body-color: #000 !important;
      --cui-heading-color: #000 !important;
      --cui-border-color: #ddd !important;
      --cui-light-bg-subtle: #f9f9f9 !important;
      --cui-primary: #2c3e50 !important;
    }
  
    body {
      background: white !important;
      color: black !important;
    }
  
    .invoice-container {
      margin: 0 !important;
      padding: 0 !important;
      width: 100% !important;
      
      .invoice-card {
        box-shadow: none !important;
        border: none !important;
        padding: 0 !important;
      }
    }
  
    // Hide elements that shouldn't print
    .no-print {
      display: none !important;
    }
  }