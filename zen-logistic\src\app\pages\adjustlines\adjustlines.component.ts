import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CircuitService } from 'app/services/circuit.service';
import { CommentService } from 'app/services/commentaire.service';
import { DestinationService } from 'app/services/destination.service';
import { LigneCmdService } from 'app/services/ligneCmd.service';
import { PricesService } from 'app/services/prices.service';
import { RegisterServiceService } from 'app/services/register-service.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-adjustlines',
  templateUrl: './adjustlines.component.html',
  styleUrls: ['./adjustlines.component.scss']
})
export class AdjustlinesComponent implements OnInit {

  @ViewChild('popupTemplate', { static: true }) popupTemplate: TemplateRef<any>;

  camionList: any = []
  selectedDate = ""
  selectedCamionId = ""
  reservationList: any = []
  selectedIds: number[] = [];
  previousCamionId = ""
  minDate = ""
  previousDate = ""
  Show = false
  selectedCamion: string = ""; // ou tout autre type qui convient à votre modèle
  selectedConducteur: any = "";
  selectedDateUpdate: any = ""
  conducteurList: any = []
  searchConducteur = ""
  previousConducteur = ""
  initialVolume: number;
  selectAll: boolean = false;
  source: any = []
  nouveauCommentaire=""
  id_Comment
  commentList : any =[]
  modalRef : any
  toVerify: any = {}
  destinationInfoMap: any
  mail = ""
  nom_locale = ""
  circuit= [];
  selectedCircuit=""
  constructor(private listcamionService: RegisterServiceService,
    private listconducteurService: RegisterServiceService,
    private ligneCmdService: LigneCmdService,
    private commentService : CommentService,
    private destinationService: DestinationService,
    private modalService: NgbModal,
    private priceService : PricesService,
    private ciruitService : CircuitService,
    private toastr: ToastrService) { }

  async ngOnInit() {


    await this.listcamionService.findAllCamion().subscribe(data => {
      this.camionList = data;
      //console.log("camion:", this.camionList)
    })

    this.listconducteurService.findConducteur().subscribe(data => {
      this.conducteurList = data;
      //console.log("conducteurList:", this.conducteurList)

    })

    await this.ligneCmdService.findVoyageListToAdjust().subscribe(res => {
      this.source = res
    })

    await this.priceService.getAllPrices().subscribe(res => {
      this.circuit = res
    })



  }

  settings = {
    actions: {
      add: false,
      edit: false,
      delete: false,


    },
    columns: {
      nom: {
        title: "Nom Transporteur"
      },
      prenom: {
        title: 'Prenom Transporteur'
      },
      immatriculation: {
        title: 'Immatriculation'
      },
      date_voyage: {
        title: 'Date Voyage '
      }

    },
    // attr : les lignes dans le tableau 
    attr: {
      class: "table table-responsive"
    },

    delete: {
      confirmDelete: false,
    }


  }

  async search() {
    this.Show = false;
    this.selectAll=false
    this.selectedIds=[]
    this.selectedCircuit=""
    // Vérifiez si les valeurs ont changé depuis la dernière recherche
    if (this.selectedCamionId !== this.previousCamionId || this.selectedDate !== this.previousDate || this.searchConducteur !== this.previousConducteur) {
      if (this.selectedCamionId !== "" && this.selectedDate !== "" && this.searchConducteur !== "") {
        const data = {
          id_camion: this.selectedCamionId,
          date: this.selectedDate,
          id_conducteur: this.searchConducteur
        };

        this.toVerify = data


        try {
          const result = await this.ligneCmdService.findLineToAdjust(data).toPromise();
          this.reservationList = result;
          //console.log(result);

          // Mettez à jour les valeurs précédentes après une recherche réussie
          this.previousCamionId = this.selectedCamionId;
          this.previousDate = this.selectedDate;
          this.previousConducteur = this.searchConducteur;
        } catch (error) {
          console.error("Erreur lors de la recherche : ", error);
          this.toastr.error('Erreur lors de la recherche : ' + error.message);
        }
      } else {
        this.toastr.error('Veuillez saisir les champs obligatoire');
      }
    }
  }



  toggleSelectAll() {
    for (let item of this.reservationList) {
      item.selected = this.selectAll;
    }

    this.updateSelectedIds(); // Met à jour la liste des IDs sélectionnés
    //console.log(this.selectedIds); // Affiche la liste dans la console
  }

  getSelectedIds(): number[] {
    return this.reservationList
      .filter(item => item.selected)
      .map(item => item.id);
  }

  updateSelectedIds(): void {
    const updatedSelectedIds = this.getSelectedIds();
    this.selectedIds = updatedSelectedIds;
    //console.log(this.selectedIds); // Affiche la liste dans la console
  }

  toggleItemSelection(item): void {
    item.selected = !item.selected;
    this.updateSelectedIds(); // Met à jour la liste des IDs sélectionnés
  }






  onRowSelect(event) {
    //console.log('Ligne sélectionnée:', event.data);
    // Faites ce que vous voulez avec les données de la ligne sélectionnée

    this.selectedCamionId = event.data.id_camion
    this.searchConducteur = event.data.id_conducteur
    this.selectedDate = event.data.date_voyage

    this.search()
  }

  async envoyerCommentaire() {
    if (this.nouveauCommentaire) {
      const data = {
        id_ligne: this.id_Comment,
        value: this.nouveauCommentaire,
        id_author: sessionStorage.getItem("iduser")
      };
  
  
      try {
        const response = await this.commentService.addComment(data);
        this.toastr.success("Commentaire Envoyer");
  
        const result = await this.ligneCmdService.findExpediedByDateAndCamion(this.toVerify).toPromise();
        this.reservationList = result;

        this.nouveauCommentaire = "";

        this.commentList = await this.commentService.findCommentsByLigne(this.id_Comment);
      } catch (error) {
        this.toastr.error("Problème de connexion");
        console.error("Erreur:", error);
        // Gérer l'erreur ici, si nécessaire
      }
    }
  
  
  
  }
  

  async openComment(id: number) {
    try {
      // Réinitialiser le nouveau commentaire et récupérer les commentaires associés à l'identifiant spécifié
      this.nouveauCommentaire = "";
      this.commentList = await this.commentService.findCommentsByLigne(id);
  
      // Afficher les commentaires récupérés dans la console pour le débogage
      //console.log("Commentaires récupérés:", this.commentList);
  
      // Ouvrir le modal avec les commentaires récupérés
      this.id_Comment = id;
      this.modalRef = this.modalService.open(this.popupTemplate, {
        ariaLabelledBy: 'modal-basic-title',
        windowClass: 'custom-modal-style' // Utilisez la classe CSS spécifique définie dans votre fichier SCSS de composant
      });
    } catch (error) {
      // Gérer les erreurs éventuelles lors de la récupération des commentaires
      console.error("Erreur lors de la récupération des commentaires:", error);
    }
  }

  truncateComment(commentaire: string): string {
    const maxLength = 15; // Limite de longueur du commentaire
    if (commentaire.length > maxLength) {
        return commentaire.substring(0, maxLength) + '...'; // Retourne les premiers maxLength caractères avec '...' ajouté à la fin
    } else {
        return commentaire; // Retourne le commentaire complet s'il est inférieur ou égal à la limite
    }
}

async affectation() {
  try {
    if (!this.checkLigneTableConditions()) {
      return;
    }

    const data = {
      id_circuit: this.selectedCircuit,
      lignes: this.selectedIds
    };

    //console.log(data);

    const res = await this.ciruitService.adjustVoyage(data);
    this.reservationList = await this.reservationList.filter((item:any) => !this.selectedIds.includes(item.id));


    if (this.reservationList.length == 0) {
      console.log("---------------------------", this.toVerify);
    
      // Supprimer directement la ligne de `source` correspondant aux critères
      this.source = this.source.filter((row) =>
        !(row.id_camion == this.toVerify.id_camion &&
        row.date_voyage == this.toVerify.date &&  // Utilisation de 'date_voyage' ici
        row.id_conducteur == this.toVerify.id_conducteur)
      );
    
      // Afficher un message pour confirmer la suppression
      console.log('Ligne supprimée pour:', this.toVerify);
    }


      this.selectedIds = [];

      this.toastr.success('voyage mis en place avec succès');
    
  } catch (error) {
    this.toastr.error('Une erreur est survenue lors de l\'ajustement du voyage', error);
  }
}





checkLigneTableConditions() {
  // Filtrer les lignes avec les IDs sélectionnés
  const lignesFiltrees = this.reservationList.filter(ligne => this.selectedIds.includes(ligne.id));

  const typesQuantiteSeule = [
    'Livraison fourniture',
    'Transfert Administratif inter magasin',
    'Transfert administratif technique et matériel informatique',
    'Transfert administratif ZEN Group-Magasin ou dépôt'
  ];

  for (const ligne of lignesFiltrees) {
    // Exclure la vérification si nom_arrivee ou nom_depart contient "aramex"
    if (
      ligne.nom_arrivee.toLowerCase().includes('aramex') || 
      ligne.nom_depart.toLowerCase().includes('aramex')
    ) {
      continue; // Le point-virgule est ajouté ici
    }

    // Vérification spécifique pour les types de 'typesQuantiteSeule'
    if (typesQuantiteSeule.includes(ligne.type_ligne)) {
      if (ligne.quantite <= 0) {
        this.toastr.error(`La quantité doit être supérieure à 0 pour le type ${ligne.type_ligne}`);
        return false;
      }
    } else {
      // Vérification pour les autres types : quantite et volume > 0
      if (ligne.quantite <= 0 || ligne.volume <= 0) {
        this.toastr.error(`Quantité et volume doivent être supérieurs à 0 pour le type ${ligne.type_ligne}`);
        return false;
      }
    }
  }

  return true;
}





}
