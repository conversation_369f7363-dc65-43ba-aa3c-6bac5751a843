import { Component, OnInit } from '@angular/core';
import { callback } from 'node_modules/chart.js/dist/helpers/helpers.core';
import { PdfUploadService } from 'src/services/pdf-upload.service';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from 'src/shared/components/smart-table/smart-table.component';
import { ActionButton, TableColumn, TableConfig } from 'src/shared/models/table.models';

@Component({
  selector: 'app-list-colisage',
  standalone: true,
  imports: [SmartContainerComponent,SmartTableComponent],
  templateUrl: './list-colisage.component.html',
  styleUrl: './list-colisage.component.scss'
})
export class ListColisageComponent implements OnInit {
  listColisage: any[] = [];
  totalItems: number = 0;
  currentPage: number = 1;
  pageSize: number = 25;
  totalPages: number = 0;

  tableConfig: TableConfig = {
    pageSize: this.pageSize,
    currentPage: this.currentPage,
    pageSizeOptions: [10, 25, 50, 100],
  }

  tableColumns: TableColumn[] = [
    {
        name: 'date_creation',
        displayName: 'Date creation',
        dataType: 'date',
        sortable: true,
        filterable: true,
    },
    {
      name: 'depotEmetteur',
      displayName: 'Depot emetteur',
      sortable: true,
      filterable: true,
    },
    {
      name: 'depotRecepteur',
      displayName: 'Depot recepteur',
      sortable: true,
      filterable: true,
    },
    {
      name: 'date_voyage',
      displayName: 'Date voyage',
      sortable: true,
      filterable: true,
    },
    {
      name: 'volume',
      displayName: 'Volume',
      sortable: true,
      filterable: true,
    },
    {
      name: 'status',
      displayName: 'Status',
      sortable: true,
      filterable: true,
    },
    {
      name: 'nom_demandeur',
      displayName: 'Nom demandeur',
      sortable: true,
      filterable: true,
    },    
  ]
  tableActions: ActionButton[] = [
    {
      icon: 'cilFile',
      callback: (row: any) => this.ViewPDF(row)
    },
  ]
  constructor(private pdfUploadService: PdfUploadService) {}
  ngOnInit(): void {
    this.loadColisage();
  }

  loadColisage() {
    this.pdfUploadService.getColisages(this.currentPage, this.pageSize).subscribe({
      next: (response: any) => {
        console.log(response);
        this.listColisage = response.data;
        console.log(this.listColisage);
        this.totalItems = response.total;
        this.totalPages = Math.ceil(this.totalItems / this.pageSize);
      },
      error: (error) => {
        console.error(error);
      }
    });
  }

  handlePageChange(page: number) {
    this.currentPage = page;
    console.log('Page change:', this.currentPage);
    this.loadColisage();
  }

  handlePageSizeChange(pageSize: number) {
    this.pageSize = pageSize;
    console.log('Page size change:', this.pageSize);
    this.loadColisage();
  }

  ViewPDF(row: any) {
    console.log(row);
    window.open(row.fileName, '_blank');
  }
}
