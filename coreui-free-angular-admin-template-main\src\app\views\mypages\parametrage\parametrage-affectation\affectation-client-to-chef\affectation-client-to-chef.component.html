<app-smart-container [title]="'Affecter Client au Chef'">
    
    <div slot="content">
        <app-smart-container
        [title]="'Affecter un Utilisateur au chef'"
        [subtitle]="'Choisir un chef pour afficher la liste des utilisateurs affectés'">
            <div slot="content">
                <app-dynamic-form
                    [config]="formConfig"

                >
                </app-dynamic-form>
            </div>
        </app-smart-container>
        <div *ngIf="showTable">
            <app-smart-container [title]="'Utilisateur(s) affecté au chef ' + selectedChef.nom_utilisateur">
                <div slot="content">
                    <app-smart-table
                    [data]="clientsUnderChef"
                    [columns]="tableColumns"
                    [actionButtons]="tableActions"
                    [config]="{emptyMessage:'aucun utilisateur affecté au chef ' + selectedChef.nom_utilisateur}"
                    [isLoading]="loading"
                    >
                    </app-smart-table>
                </div>
            </app-smart-container>
            
        
        </div>
    </div>

    

</app-smart-container>
