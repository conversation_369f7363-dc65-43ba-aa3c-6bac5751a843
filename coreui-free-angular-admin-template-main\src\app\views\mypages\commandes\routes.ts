import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    data: {
      title: 'Commandes'
    },
    children: [
      {
        path: 'commandes-aexpedier',
        loadComponent: () => import('./commande-aexpedier/commande-aexpedier.component').then(m => m.CommandeAExpedierComponent),
        data: {
          title: 'Commandes à expédier'
        }
      },
      {
        path: 'commandes-areserver',
        loadComponent: () => import('./commande-areserver/commande-areserver.component').then(m => m.CommandeAReserverComponent),
        data: {
          title: 'Commandes à réserver'
        }
      },
      {
        path: 'commandes-a-livrer',
        loadComponent: () => import('./commande-to-livrer/commande-to-livrer.component').then(m => m.CommandeToLivrerComponent),
        data: {
          title: 'Commandes à livrer'
        }
      },
      {
        path: 'ligne-afacturer',
        loadComponent: () => import('./ligne-to-facture/ligne-to-facture.component').then(m => m.LigneToFactureComponent),
        data: {
          title: 'Commandes à facturer'
        }
      },
      {
        path: 'assign-ligne-cmd',
        loadComponent: () => import('./ajouter-ligne-cmd/ajouter-ligne-cmd.component').then(m => m.AjouterLigneCmdComponent),
        data: {
          title: 'Assigner ligne de commande'
        }
      },
      {
        path: 'suivis-commandes',
        loadComponent: () => import('./suivis-commande/wrapper-suivis-commandes/wrapper-suivis-commandes.component').then(m => m.WrapperSuivisCommandesComponent),
        data: {
          title: 'Suivis des commandes',
          roles: ['GS', 'GE', 'Client','Chef Departement']
        }
      },
      {
        path: 'suivis-ligne',
        loadComponent: () => import('./suivis-ligne/suivis-ligne.component').then(m => m.SuivisLigneComponent),
        data: {
          title: 'Suivis des lignes',
          roles: ['GS', 'GE', 'Client','Chef Departement']
        }
      }
    ]
  }
];
