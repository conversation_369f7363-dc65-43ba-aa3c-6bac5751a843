import { Component, ViewChild } from '@angular/core';
import { ActionButton, TableColumn, TableConfig } from '../../../../../../shared/models/table.models';
import { DetailContainerComponent } from '../shared/detail-container/detail-container.component';
import { ContextService } from '../../../../../../services/strategy/access-context.service';
import { ToastrService } from 'ngx-toastr';
import { SmartContainerComponent } from '../../../../../../shared/components/smart-container/smart-container.component';
import { SmartTableComponent } from '../../../../../../shared/components/smart-table/smart-table.component';
import { OrderService } from '../../../../../../services';
import { ConfirmationDialogService } from '../../../../../../services/confirmation-dialog.service';

@Component({
  selector: 'app-my-commands-to-validate',
  imports: [DetailContainerComponent, SmartContainerComponent, SmartTableComponent],
  templateUrl: './my-commands-to-validate.component.html',
  styleUrl: './my-commands-to-validate.component.scss'
})
export class MyCommandsToValidateComponent {
  @ViewChild('DetailContainer') DetailContainer !: DetailContainerComponent;

  loading : boolean = false;

  source : any[] = [];

  constructor(
    private contextService : ContextService,
    private orderService : OrderService,
    private confiramationDialogService : ConfirmationDialogService,
    private toastr : ToastrService
  ) { }

  TableColumns: TableColumn[] = [
    {
      name: 'id',
      displayName: 'ID',
      sortable: true,
      filterable: true,
      
    },
    {
      name: 'date_depart',
      displayName: 'Date de depart',
      sortable: true,
      filterable: true,
    },
    {
      name: 'date_arrivee',
      displayName: 'Date d\'arrivée',
      sortable: true,
      filterable: true,
    },
    {
      name: 'statut',
      displayName: 'Statut',
      sortable: true,
      filterable: true,
      dataType: 'status',
      statusConfig: [
        {
          value: 'en attente de confirmation',
          displayText: 'En attente de confirmation',
          badgeColor: 'warning',
          icon : 'cil-clock'
        },
        {
          value: 'Valide',
          displayText: 'Validé',
          badgeColor: 'success',
          icon : 'cil-check'
        }
      ]
    },
    {
      name: 'type_conditionnement',
      displayName: 'Type de conditionnement',
      sortable: true,
      filterable: true,
    },
    {
      name: 'type_marchandise',
      displayName: 'Type de marchandise',
      sortable: true,
      filterable: true,
    }, 
    {
      name: 'nom_utilisateur',
      displayName: 'Ajouté Par',
      sortable: true,
      filterable: true,
    },
    {
      name:'mention',
      displayName: 'Mention',
      sortable: true,
      filterable: true,
    }
  ]
  TableActions: ActionButton[] = [
    {
      tooltip: 'valider',
      color: 'success',
      icon: 'cilCheck',
      callback: (row: any) => {
        this.validateCommand(row);
      }
    },
    {
      tooltip: 'supprimer',
      color: 'danger',
      icon: 'cilXCircle',
      callback: (row: any) => {
        this.deleteCommand(row);
      }
    },
    {
      tooltip: 'detail',
      color: 'info',
      icon: 'cilZoom',
      callback: (row: any) => {
        this.showDetail(row);
      }
    }
  ] 

  tableConfig: TableConfig = {
    emptyMessage: 'Aucune commande trouvée',
    
  }

  ngOnInit(): void {
    this.loadMyCommands();
  }

  ScrollToTop() {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }
  showDetail(row: any) {
    this.DetailContainer.source = row;
    this.DetailContainer.open();
    setTimeout(() => {
      this.ScrollToTop();
    }, 100);
  }

  validateCommand(row: any) {
    this.confiramationDialogService.confirmSave('Voulez-vous valider cette commande ?').then((result) => {
      if (result) {
        this.orderService.updateOrderExpeditor({ "statut": "Valide" }, row.id).subscribe({
          next: (data:any) => {
            this.toastr.success('Commande validée');
            this.loadMyCommands();
          },
          error: (error:any) => {
            this.toastr.error('Error validating command');
            console.error(error);
          }
        })
      }
    })
  }

  deleteCommand(row: any) {
    this.confiramationDialogService.confirmDelete('Voulez-vous supprimer cette commande ?').then((result) => {
      if (result) {
        this.orderService.updateOrderExpeditor({ "statut": "Invalide"}, row.id).subscribe({
          next: (data:any) => {
            this.toastr.success('Commande supprimée');
            this.loadMyCommands();
          },
          error: (error:any) => {
            this.toastr.error('Error deleting command');
            console.error(error);
          }
        })
      }
    })
  }

  loadMyCommands() {
    this.loading = true;
    this.contextService.findCommandsToValidate().subscribe({
      next: (data:any) => {
        this.source = data;
        console.log('data', this.source);
        this.loading = false;
      },
      error: (error:any) => {
        this.toastr.error('Error loading commands');
        console.error(error);
        this.loading = false;
      }
    })
  }
}
