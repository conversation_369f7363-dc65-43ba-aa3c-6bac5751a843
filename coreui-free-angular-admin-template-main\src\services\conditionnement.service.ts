import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, Observable, throwError } from 'rxjs';
import { environment } from '../environments/environment';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};
@Injectable({
  providedIn: 'root'
})
export class ConditionnementService {
  private apiURL = environment.apiURL;;
  constructor(private http: HttpClient) { }

  /**
   * Add condition type
   * @param conditionName - Condition name
   * @returns Observable with creation result
   * @originalName addTypeCondition
   */
  addConditionType(conditionName: string): Observable<any> {
    const data = { nom_condition: conditionName };
    return this.http.post<any>(this.apiURL + 'condition', data, httpOptions);
  }

  /**
   * Get all condition types
   * @returns Observable with condition types list
   * @originalName getAllCondition
   */
  getAllConditionTypes(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'condition', httpOptions);
  }

  /**
   * Delete condition type
   * @param id - Condition type ID
   * @returns Observable with deletion result
   */
  deleteConditionType(id: number): Observable<any> {
    const url = `${this.apiURL}condition/${id}`;
    return this.http.delete<any>(url, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  private handleError(error: any): Observable<never> {
      console.error('An error occurred:', error);
      return throwError(() => new Error(error.message || 'Server error'));
    }
}
