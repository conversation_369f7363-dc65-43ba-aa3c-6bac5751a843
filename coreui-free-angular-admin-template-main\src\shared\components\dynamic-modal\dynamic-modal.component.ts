// dynamic-modal.component.ts
import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter, HostListener, ElementRef, TemplateRef } from '@angular/core';

@Component({
  selector: 'app-dynamic-modal',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './dynamic-modal.component.html',
  styleUrls: ['./dynamic-modal.component.scss']
})
export class DynamicModalComponent {
  @Input() title: string = 'Modal Title';
  @Input() buttonLabel: string = 'Submit';
  @Input() showCloseButton: boolean = true;
  @Input() closeOnOutsideClick: boolean = true;
  @Input() closeOnEsc: boolean = true;
  @Input() size: 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() buttonVariant: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark' = 'primary';
  @Input() show: boolean = false;
  @Input() submitExist: boolean = true;
  @Input() disableSubmit: boolean = false;
  @Input() centered: boolean = true;
  @Input() scrollable: boolean = true;
  @Input() backdrop: boolean = true;
  @Input() animation: 'fade' | 'slide' | 'none' = 'fade';
  @Input() headerContent?: TemplateRef<any>;
  @Input() footerContent?: TemplateRef<any>;

  @Output() onClose = new EventEmitter<void>();
  @Output() showChange = new EventEmitter<boolean>();
  @Output() onSubmit = new EventEmitter<void>();

  @HostListener('document:keydown.escape', ['$event'])
  handleEscapeKey(event: KeyboardEvent | Event) {
    if (this.closeOnEsc && this.show) {
      this.close();
    }
  }

  constructor(private elementRef: ElementRef) {}

  close() {
    this.show = false;
    this.onClose.emit();
    this.showChange.emit(this.show);
  }

  submit() {
    this.onSubmit.emit();
    this.close();
  }

  onBackdropClick(event: MouseEvent) {
    if (this.closeOnOutsideClick && this.show) {
      const clickedInside = this.elementRef.nativeElement.contains(event.target);
      if (!clickedInside) {
        this.close();
      }
    }
  }
}