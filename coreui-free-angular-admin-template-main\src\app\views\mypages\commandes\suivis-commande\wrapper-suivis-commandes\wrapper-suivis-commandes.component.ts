import { Component } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { MyCommandsComponent } from '../my-commands/my-commands.component';
import { MyCommandsToValidateComponent } from '../my-commands-to-validate/my-commands-to-validate.component';
import { MyValidatedCommandsComponent } from '../my-validated-commands/my-validated-commands.component';
import { CommonModule } from '@angular/common';
import { ButtonGroupComponent } from '@coreui/angular';
import { FormCheckLabelDirective } from '@coreui/angular';
import { ButtonDirective } from '@coreui/angular';
import { SmartContainerComponent } from 'src/shared/components/smart-container/smart-container.component';

@Component({
  selector: 'app-wrapper-suivis-commandes',
  standalone: true,
  imports: [
    SmartContainerComponent,
    MyCommandsComponent,
    MyCommandsToValidateComponent,
    MyValidatedCommandsComponent,
    FormsModule,
    CommonModule,
    ReactiveFormsModule,
    ButtonGroupComponent,
    FormCheckLabelDirective,
    ButtonDirective,
  ],
  templateUrl: './wrapper-suivis-commandes.component.html',
  styleUrl: './wrapper-suivis-commandes.component.scss'
})
export class WrapperSuivisCommandesComponent {

  title: string = 'Mes Commandes';
  formRadio = new UntypedFormGroup({
    radio: new UntypedFormControl('Mes Commandes')
  });

  constructor(
    private formBuilder: UntypedFormBuilder
  ) { }

  setRadioValue(value: string): void {
    this.formRadio.setValue({ radio: value });
    console.log(this.formRadio.value)
    this.title = this.formRadio.value['radio']
  }


}

