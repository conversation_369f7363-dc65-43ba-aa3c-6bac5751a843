// data-export.component.ts
import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DynamicExportButtonComponent } from '../../../../../shared/components/dynamic-export-button/dynamic-export-button.component';
import { SmartContainerComponent } from "../../../../../shared/components/smart-container/smart-container.component";


@Component({
  selector: 'app-data-export',
  templateUrl: 'data-export.component.html',
  styles: [`
    .container {
      max-width: 800px;
      margin: 20px auto;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 8px;
      font-family: Arial, sans-serif;
    }
    .data-preview {
      margin: 20px 0;
      overflow-x: auto;
    }
    table {
      width: 100%;
      border-collapse: collapse;
    }
    th, td {
      padding: 8px 12px;
      border: 1px solid #ddd;
      text-align: left;
    }
    th {
      background-color: #f2f2f2;
    }
    .more-rows {
      text-align: center;
      font-style: italic;
      color: #666;
    }
    .export-controls {
      margin: 20px 0;
      text-align: center;
    }
    .status {
      padding: 10px;
      margin-top: 15px;
      border-radius: 4px;
      text-align: center;
    }
    .success {
      background-color: #dff0d8;
      color: #3c763d;
    }
    .error {
      background-color: #f2dede;
      color: #a94442;
    }
  `],
  standalone: true,
  imports: [CommonModule, DynamicExportButtonComponent, SmartContainerComponent]
})
export class DataExportComponent {
  sampleData = [
    { id: 1, name: 'Laptop', category: 'Electronics', price: 999.99, stock: 25 },
    { id: 2, name: 'Smartphone', category: 'Electronics', price: 699.99, stock: 50 },
    { id: 3, name: 'Headphones', category: 'Electronics', price: 149.99, stock: 100 },
    { id: 4, name: 'Desk Chair', category: 'Furniture', price: 199.99, stock: 15 },
    { id: 5, name: 'Coffee Mug', category: 'Kitchen', price: 9.99, stock: 200 },
    { id: 6, name: 'Notebook', category: 'Office', price: 4.99, stock: 150 },
    { id: 7, name: 'Pen Set', category: 'Office', price: 12.99, stock: 75 }
  ];

  exportStatus: 'success' | 'error' | null = null;
  statusMessage = '';
  today = new Date().toISOString().slice(0, 10);

  getHeaders(): string[] {
    return this.sampleData.length > 0 ? Object.keys(this.sampleData[0]) : [];
  }

  onExportStart() {
    this.exportStatus = null;
    this.statusMessage = 'Preparing export...';
  }

  onExportSuccess() {
    this.exportStatus = 'success';
    this.statusMessage = 'Export completed successfully!';
    setTimeout(() => this.resetStatus(), 3000);
  }

  onExportError(error: Error) {
    this.exportStatus = 'error';
    this.statusMessage = `Export failed: ${error.message}`;
    setTimeout(() => this.resetStatus(), 5000);
  }

  resetStatus() {
    this.exportStatus = null;
    this.statusMessage = '';
  }
}